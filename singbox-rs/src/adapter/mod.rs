//! Adapter module for sing-box
//! 
//! This module contains adapter interfaces and implementations for various
//! sing-box components including inbounds, outbounds, routers, and services.

pub mod lifecycle;

// Re-export commonly used items
pub use lifecycle::*;

use std::net::SocketAddr;
use std::collections::HashMap;
use crate::common::interrupt::Context;

/// Basic adapter trait
pub trait Adapter: Send + Sync {
    fn adapter_type(&self) -> &str;
    fn tag(&self) -> &str;
}

/// Inbound adapter trait
pub trait Inbound: Adapter + Lifecycle {
    // Inherits type() and tag() from Adapter
    // Inherits start() and close() from Lifecycle
}

/// Outbound adapter trait
pub trait Outbound: Adapter + Send + Sync {
    fn network(&self) -> Vec<String>;
    fn dependencies(&self) -> Vec<String>;
    
    // Dialer functionality (simplified)
    fn dial(&self, network: &str, destination: &str) -> Result<(), String>;
}

/// Router adapter trait
pub trait Router: Send + Sync {
    fn route(&self, metadata: &InboundContext) -> Result<String, String>;
}

/// Inbound context structure
#[derive(Debug, Clone)]
pub struct InboundContext {
    pub inbound: String,
    pub inbound_type: String,
    /// Inbound tag (alias for inbound)
    pub inbound_tag: String,
    pub ip_version: u8,
    pub network: String,
    pub source: SocketAddr,
    pub destination: Option<crate::route::rule::Destination>,
    pub user: String,
    pub outbound: String,
    pub domain: String,
    pub protocol: String,
    pub port_mapping: Option<u16>,
    pub process_info: Option<ProcessInfo>,
}

impl Default for InboundContext {
    fn default() -> Self {
        Self {
            inbound: String::new(),
            inbound_type: String::new(),
            inbound_tag: String::new(),
            ip_version: 4,
            network: "tcp".to_string(),
            source: "0.0.0.0:0".parse().unwrap(),
            destination: None,
            user: String::new(),
            outbound: String::new(),
            domain: String::new(),
            protocol: String::new(),
            port_mapping: None,
            process_info: None,
        }
    }
}

/// Process information structure
#[derive(Debug, Clone)]
pub struct ProcessInfo {
    pub process_path: String,
    pub package_name: String,
    pub user: String,
    pub user_id: Option<u32>,
    pub group_id: Option<u32>,
    /// Process name (for compatibility)
    pub name: String,
    /// Process path (alias for process_path)
    pub path: String,
}

impl Default for ProcessInfo {
    fn default() -> Self {
        Self {
            process_path: String::new(),
            package_name: String::new(),
            user: String::new(),
            user_id: None,
            group_id: None,
            name: String::new(),
            path: String::new(),
        }
    }
}

/// Registry trait for creating adapters
pub trait Registry<T>: Send + Sync {
    fn create(&self, 
              ctx: &Context, 
              tag: &str, 
              adapter_type: &str, 
              options: &HashMap<String, serde_json::Value>) -> Result<T, String>;
}

/// Manager trait for managing collections of adapters
pub trait Manager<T>: Lifecycle {
    fn get(&self, tag: &str) -> Option<&T>;
    fn get_mut(&mut self, tag: &str) -> Option<&mut T>;
    fn add(&mut self, tag: String, adapter: T) -> Result<(), String>;
    fn remove(&mut self, tag: &str) -> Result<(), String>;
    fn list(&self) -> Vec<&T>;
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;

    struct TestAdapter {
        adapter_type: String,
        tag: String,
    }

    impl TestAdapter {
        fn new(adapter_type: &str, tag: &str) -> Self {
            Self {
                adapter_type: adapter_type.to_string(),
                tag: tag.to_string(),
            }
        }
    }

    impl Adapter for TestAdapter {
        fn adapter_type(&self) -> &str {
            &self.adapter_type
        }

        fn tag(&self) -> &str {
            &self.tag
        }
    }

    #[async_trait]
    impl Lifecycle for TestAdapter {
        async fn start(&self, _stage: StartStage) -> Result<(), String> {
            Ok(())
        }

        async fn close(&self) -> Result<(), String> {
            Ok(())
        }
    }

    impl Inbound for TestAdapter {}

    struct TestOutbound {
        adapter: TestAdapter,
        networks: Vec<String>,
        deps: Vec<String>,
    }

    impl TestOutbound {
        fn new(tag: &str) -> Self {
            Self {
                adapter: TestAdapter::new("test", tag),
                networks: vec!["tcp".to_string(), "udp".to_string()],
                deps: Vec::new(),
            }
        }
    }

    impl Adapter for TestOutbound {
        fn adapter_type(&self) -> &str {
            self.adapter.adapter_type()
        }

        fn tag(&self) -> &str {
            self.adapter.tag()
        }
    }

    impl Outbound for TestOutbound {
        fn network(&self) -> Vec<String> {
            self.networks.clone()
        }

        fn dependencies(&self) -> Vec<String> {
            self.deps.clone()
        }

        fn dial(&self, _network: &str, _destination: &str) -> Result<(), String> {
            Ok(())
        }
    }

    #[test]
    fn test_inbound_context_default() {
        let ctx = InboundContext::default();
        assert_eq!(ctx.ip_version, 4);
        assert_eq!(ctx.network, "tcp");
        assert!(ctx.inbound.is_empty());
        assert!(ctx.domain.is_empty());
    }

    #[test]
    fn test_process_info_default() {
        let info = ProcessInfo::default();
        assert!(info.process_path.is_empty());
        assert!(info.package_name.is_empty());
        assert!(info.user_id.is_none());
        assert!(info.group_id.is_none());
    }

    #[test]
    fn test_adapter_trait() {
        let adapter = TestAdapter::new("http", "test-inbound");
        assert_eq!(adapter.adapter_type(), "http");
        assert_eq!(adapter.tag(), "test-inbound");
    }

    #[tokio::test]
    async fn test_inbound_trait() {
        let inbound = TestAdapter::new("http", "test-inbound");

        // Test lifecycle methods
        assert!(inbound.start(StartStage::Initialize).await.is_ok());
        assert!(inbound.start(StartStage::Start).await.is_ok());
        assert!(inbound.close().await.is_ok());
    }

    #[test]
    fn test_outbound_trait() {
        let outbound = TestOutbound::new("test-outbound");
        
        assert_eq!(outbound.adapter_type(), "test");
        assert_eq!(outbound.tag(), "test-outbound");
        assert_eq!(outbound.network(), vec!["tcp", "udp"]);
        assert!(outbound.dependencies().is_empty());
        assert!(outbound.dial("tcp", "example.com:80").is_ok());
    }

    #[tokio::test]
    async fn test_start_services() {
        let service1 = TestAdapter::new("test", "service1");
        let service2 = TestAdapter::new("test", "service2");

        let services: Vec<&dyn Lifecycle> = vec![&service1, &service2];

        assert!(crate::adapter::lifecycle::start_services(StartStage::Start, &services).await.is_ok());
    }
}
