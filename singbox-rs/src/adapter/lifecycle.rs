// Lifecycle management for sing-box adapters
//
// This module defines lifecycle interfaces and stages for managing
// the startup and shutdown of various sing-box components.

use std::fmt;
use async_trait::async_trait;

/// Simple lifecycle interface for basic start/close operations
pub trait SimpleLifecycle: Send + Sync {
    fn start(&mut self) -> Result<(), String>;
    fn close(&mut self) -> Result<(), String>;
}

/// Start stage enumeration
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash)]
#[repr(u8)]
pub enum StartStage {
    Initialize = 0,
    Start = 1,
    PostStart = 2,
    Started = 3,
}

impl StartStage {
    /// Get all start stages as a vector
    pub fn all() -> Vec<StartStage> {
        vec![
            StartStage::Initialize,
            StartStage::Start,
            StartStage::PostStart,
            StartStage::Started,
        ]
    }
}

impl fmt::Display for StartStage {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let s = match self {
            StartStage::Initialize => "initialize",
            StartStage::Start => "start",
            StartStage::PostStart => "post-start",
            StartStage::Started => "finish-start",
        };
        write!(f, "{}", s)
    }
}

/// Advanced lifecycle interface with staged startup
#[async_trait]
pub trait Lifecycle: Send + Sync {
    async fn start(&self, stage: StartStage) -> Result<(), String>;
    async fn close(&self) -> Result<(), String>;
}

/// Lifecycle service interface (extends Lifecycle with name)
#[async_trait]
pub trait LifecycleService: Lifecycle {
    fn name(&self) -> &str;
}

/// Start multiple lifecycle services at a specific stage
pub async fn start_services(stage: StartStage, services: &[&dyn Lifecycle]) -> Result<(), String> {
    for service in services {
        service.start(stage).await?;
    }
    Ok(())
}

/// Start multiple named lifecycle services at a specific stage
pub async fn start_named_services(stage: StartStage, services: &[&dyn LifecycleService]) -> Result<(), String> {
    for service in services {
        service.start(stage).await.map_err(|err| {
            format!("{} {}: {}", stage, service.name(), err)
        })?;
    }
    Ok(())
}

/// Close multiple lifecycle services
pub async fn close_services(services: &[&dyn Lifecycle]) -> Result<(), String> {
    let mut errors = Vec::new();
    
    for service in services {
        if let Err(err) = service.close().await {
            errors.push(err);
        }
    }
    
    if errors.is_empty() {
        Ok(())
    } else {
        Err(errors.join("; "))
    }
}

/// Close multiple named lifecycle services
pub async fn close_named_services(services: &[&dyn LifecycleService]) -> Result<(), String> {
    let mut errors = Vec::new();
    
    for service in services {
        if let Err(err) = service.close().await {
            errors.push(format!("{}: {}", service.name(), err));
        }
    }
    
    if errors.is_empty() {
        Ok(())
    } else {
        Err(errors.join("; "))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    struct TestService {
        name: String,
        started: std::sync::atomic::AtomicBool,
        closed: std::sync::atomic::AtomicBool,
        should_fail: bool,
    }

    impl TestService {
        fn new(name: &str) -> Self {
            Self {
                name: name.to_string(),
                started: std::sync::atomic::AtomicBool::new(false),
                closed: std::sync::atomic::AtomicBool::new(false),
                should_fail: false,
            }
        }

        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }
    }

    #[async_trait]
    impl Lifecycle for TestService {
        async fn start(&self, _stage: StartStage) -> Result<(), String> {
            if self.should_fail {
                return Err("test failure".to_string());
            }
            self.started.store(true, std::sync::atomic::Ordering::Relaxed);
            Ok(())
        }

        async fn close(&self) -> Result<(), String> {
            if self.should_fail {
                return Err("test failure".to_string());
            }
            self.closed.store(true, std::sync::atomic::Ordering::Relaxed);
            Ok(())
        }
    }

    impl LifecycleService for TestService {
        fn name(&self) -> &str {
            &self.name
        }
    }

    #[test]
    fn test_start_stage_display() {
        assert_eq!(format!("{}", StartStage::Initialize), "initialize");
        assert_eq!(format!("{}", StartStage::Start), "start");
        assert_eq!(format!("{}", StartStage::PostStart), "post-start");
        assert_eq!(format!("{}", StartStage::Started), "finish-start");
    }

    #[test]
    fn test_start_stage_values() {
        assert_eq!(StartStage::Initialize as u8, 0);
        assert_eq!(StartStage::Start as u8, 1);
        assert_eq!(StartStage::PostStart as u8, 2);
        assert_eq!(StartStage::Started as u8, 3);
    }

    #[test]
    fn test_start_stage_ordering() {
        assert!(StartStage::Initialize < StartStage::Start);
        assert!(StartStage::Start < StartStage::PostStart);
        assert!(StartStage::PostStart < StartStage::Started);
    }

    #[test]
    fn test_start_stage_all() {
        let all_stages = StartStage::all();
        assert_eq!(all_stages.len(), 4);
        assert_eq!(all_stages[0], StartStage::Initialize);
        assert_eq!(all_stages[3], StartStage::Started);
    }

    #[tokio::test]
    async fn test_lifecycle_service() {
        let service = TestService::new("test_service");

        assert_eq!(service.name(), "test_service");
        assert!(!service.started.load(std::sync::atomic::Ordering::Relaxed));
        assert!(!service.closed.load(std::sync::atomic::Ordering::Relaxed));

        assert!(service.start(StartStage::Start).await.is_ok());
        assert!(service.started.load(std::sync::atomic::Ordering::Relaxed));

        assert!(service.close().await.is_ok());
        assert!(service.closed.load(std::sync::atomic::Ordering::Relaxed));
    }

    #[tokio::test]
    async fn test_start_services_success() {
        let service1 = TestService::new("service1");
        let service2 = TestService::new("service2");

        let services: Vec<&dyn Lifecycle> = vec![&service1, &service2];

        assert!(start_services(StartStage::Start, &services).await.is_ok());
        assert!(service1.started.load(std::sync::atomic::Ordering::Relaxed));
        assert!(service2.started.load(std::sync::atomic::Ordering::Relaxed));
    }

    #[tokio::test]
    async fn test_start_services_failure() {
        let service1 = TestService::new("service1");
        let service2 = TestService::new("service2").with_failure();

        let services: Vec<&dyn Lifecycle> = vec![&service1, &service2];

        assert!(start_services(StartStage::Start, &services).await.is_err());
        assert!(service1.started.load(std::sync::atomic::Ordering::Relaxed)); // First service should have started
        assert!(!service2.started.load(std::sync::atomic::Ordering::Relaxed)); // Second service should have failed
    }

    #[tokio::test]
    async fn test_close_services() {
        let service1 = TestService::new("service1");
        let service2 = TestService::new("service2");

        let services: Vec<&dyn Lifecycle> = vec![&service1, &service2];

        assert!(close_services(&services).await.is_ok());
        assert!(service1.closed.load(std::sync::atomic::Ordering::Relaxed));
        assert!(service2.closed.load(std::sync::atomic::Ordering::Relaxed));
    }
}
