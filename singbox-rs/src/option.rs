//! Configuration options for sing-box
//!
//! This module provides configuration structures for all sing-box components.

use std::collections::HashMap;
use std::time::Duration;
use serde::{Deserialize, Serialize};

/// Main configuration options
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Options {
    /// Log configuration
    pub log: Option<LogOptions>,
    
    /// DNS configuration
    pub dns: Option<DNSOptions>,
    
    /// Inbound configurations
    pub inbounds: Vec<Inbound>,
    
    /// Outbound configurations
    pub outbounds: Vec<Outbound>,
    
    /// Route configuration
    pub route: Option<RouteOptions>,
    
    /// Experimental features
    pub experimental: Option<ExperimentalOptions>,
}

/// Log configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LogOptions {
    /// Log level
    pub level: String,
    
    /// Log output
    pub output: Option<String>,
    
    /// Disable color
    pub disable_color: bool,
    
    /// Timestamp format
    pub timestamp: bool,
}

/// DNS configuration options
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DNSOptions {
    /// DNS servers
    pub servers: Vec<DNSServer>,
    
    /// DNS rules
    pub rules: Vec<DNSRule>,
    
    /// Final DNS server
    pub final_: Option<String>,
    
    /// Strategy
    pub strategy: Option<String>,
    
    /// Disable cache
    pub disable_cache: bool,
    
    /// Disable expire
    pub disable_expire: bool,
    
    /// Independent cache
    pub independent_cache: bool,
    
    /// Reverse mapping
    pub reverse_mapping: bool,
    
    /// Fake IP
    pub fakeip: Option<FakeIPOptions>,
}

/// DNS server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DNSServer {
    /// Server tag
    pub tag: String,
    
    /// Server address
    pub address: String,
    
    /// Address resolver
    pub address_resolver: Option<String>,
    
    /// Address strategy
    pub address_strategy: Option<String>,
    
    /// Strategy
    pub strategy: Option<String>,
    
    /// Detour
    pub detour: Option<String>,
}

/// DNS rule configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DNSRule {
    /// Rule type
    #[serde(flatten)]
    pub rule_type: DNSRuleType,
    
    /// Server
    pub server: Option<String>,
    
    /// Disable cache
    pub disable_cache: bool,
    
    /// Rewrite TTL
    pub rewrite_ttl: Option<u32>,
}

/// DNS rule types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum DNSRuleType {
    #[serde(rename = "default")]
    Default,
    
    #[serde(rename = "logical")]
    Logical {
        mode: String,
        rules: Vec<DNSRule>,
    },
}

/// Fake IP configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FakeIPOptions {
    /// Enable fake IP
    pub enabled: bool,
    
    /// IPv4 range
    pub inet4_range: Option<String>,
    
    /// IPv6 range
    pub inet6_range: Option<String>,
}

/// Inbound configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Inbound {
    /// Inbound type
    #[serde(rename = "type")]
    pub inbound_type: String,
    
    /// Inbound tag
    pub tag: String,
    
    /// Listen address
    pub listen: Option<String>,
    
    /// Listen port
    pub listen_port: Option<u16>,
    
    /// TCP fast open
    pub tcp_fast_open: Option<bool>,
    
    /// TCP multi path
    pub tcp_multi_path: Option<bool>,
    
    /// UDP fragment
    pub udp_fragment: Option<bool>,
    
    /// UDP timeout
    pub udp_timeout: Option<Duration>,
    
    /// Proxy protocol
    pub proxy_protocol: Option<bool>,
    
    /// Proxy protocol accept no header
    pub proxy_protocol_accept_no_header: Option<bool>,
    
    /// Network
    pub network: Option<String>,
    
    /// Override address
    pub override_address: Option<String>,
    
    /// Override port
    pub override_port: Option<u16>,
    
    /// Additional configuration
    #[serde(flatten)]
    pub config: HashMap<String, serde_json::Value>,
}

/// Outbound configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Outbound {
    /// Outbound type
    #[serde(rename = "type")]
    pub outbound_type: String,
    
    /// Outbound tag
    pub tag: String,
    
    /// Server address
    pub server: Option<String>,
    
    /// Server port
    pub server_port: Option<u16>,
    
    /// Local address
    pub local_address: Option<String>,
    
    /// Network
    pub network: Option<String>,
    
    /// Domain strategy
    pub domain_strategy: Option<String>,
    
    /// Fallback delay
    pub fallback_delay: Option<Duration>,
    
    /// Bind interface
    pub bind_interface: Option<String>,
    
    /// Inet4 bind address
    pub inet4_bind_address: Option<String>,
    
    /// Inet6 bind address
    pub inet6_bind_address: Option<String>,
    
    /// Routing mark
    pub routing_mark: Option<u32>,
    
    /// Reuse address
    pub reuse_addr: Option<bool>,
    
    /// Connect timeout
    pub connect_timeout: Option<Duration>,
    
    /// TCP fast open
    pub tcp_fast_open: Option<bool>,
    
    /// TCP multi path
    pub tcp_multi_path: Option<bool>,
    
    /// UDP fragment
    pub udp_fragment: Option<bool>,
    
    /// UDP timeout
    pub udp_timeout: Option<Duration>,
    
    /// Additional configuration
    #[serde(flatten)]
    pub config: HashMap<String, serde_json::Value>,
}

/// Route configuration options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RouteOptions {
    /// GeoIP configuration
    pub geoip: Option<GeoIPOptions>,
    
    /// GeoSite configuration
    pub geosite: Option<GeoSiteOptions>,
    
    /// Rules
    pub rules: Vec<RouteRule>,
    
    /// Rule set
    pub rule_set: Vec<RuleSet>,
    
    /// Final outbound
    pub final_: Option<String>,
    
    /// Auto detect interface
    pub auto_detect_interface: Option<bool>,
    
    /// Override Android VPN
    pub override_android_vpn: Option<bool>,
    
    /// Default interface
    pub default_interface: Option<String>,
    
    /// Default mark
    pub default_mark: Option<u32>,
}

/// GeoIP configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeoIPOptions {
    /// Path to GeoIP database
    pub path: Option<String>,
    
    /// Download URL
    pub download_url: Option<String>,
    
    /// Download detour
    pub download_detour: Option<String>,
}

/// GeoSite configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeoSiteOptions {
    /// Path to GeoSite database
    pub path: Option<String>,
    
    /// Download URL
    pub download_url: Option<String>,
    
    /// Download detour
    pub download_detour: Option<String>,
}

/// Route rule configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RouteRule {
    /// Rule type
    #[serde(flatten)]
    pub rule_type: RouteRuleType,
    
    /// Outbound
    pub outbound: Option<String>,
    
    /// Server
    pub server: Option<String>,
    
    /// Disable cache
    pub disable_cache: Option<bool>,
    
    /// Rewrite TTL
    pub rewrite_ttl: Option<u32>,
}

/// Route rule types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum RouteRuleType {
    #[serde(rename = "default")]
    Default,
    
    #[serde(rename = "logical")]
    Logical {
        mode: String,
        rules: Vec<RouteRule>,
    },
}

/// Rule set configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleSet {
    /// Rule set tag
    pub tag: String,
    
    /// Rule set type
    #[serde(rename = "type")]
    pub rule_set_type: String,
    
    /// Format
    pub format: Option<String>,
    
    /// Path
    pub path: Option<String>,
    
    /// URL
    pub url: Option<String>,
    
    /// Download detour
    pub download_detour: Option<String>,
    
    /// Update interval
    pub update_interval: Option<Duration>,
}

/// Experimental options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExperimentalOptions {
    /// Clash API configuration
    pub clash_api: Option<ClashAPIOptions>,
    
    /// V2Ray API configuration
    pub v2ray_api: Option<V2RayAPIOptions>,
    
    /// Cache file configuration
    pub cache_file: Option<CacheFileOptions>,
}

/// Clash API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClashAPIOptions {
    /// External controller
    pub external_controller: Option<String>,
    
    /// External UI
    pub external_ui: Option<String>,
    
    /// External UI download URL
    pub external_ui_download_url: Option<String>,
    
    /// External UI download detour
    pub external_ui_download_detour: Option<String>,
    
    /// Secret
    pub secret: Option<String>,
    
    /// Default mode
    pub default_mode: Option<String>,
    
    /// Store mode
    pub store_mode: Option<bool>,
    
    /// Store selected
    pub store_selected: Option<bool>,
    
    /// Store fake IP
    pub store_fakeip: Option<bool>,
    
    /// Cache file
    pub cache_file: Option<String>,
    
    /// Cache ID
    pub cache_id: Option<String>,
}

/// V2Ray API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct V2RayAPIOptions {
    /// Listen address
    pub listen: String,
    
    /// Statistics configuration
    pub stats: Option<V2RayStatsOptions>,
}

/// V2Ray statistics configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct V2RayStatsOptions {
    /// Enable statistics
    pub enabled: bool,
    
    /// Inbounds
    pub inbounds: Vec<String>,
    
    /// Outbounds
    pub outbounds: Vec<String>,
    
    /// Users
    pub users: Vec<String>,
}

/// Cache file configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheFileOptions {
    /// Enable cache file
    pub enabled: bool,
    
    /// Cache file path
    pub path: Option<String>,
    
    /// Cache ID
    pub cache_id: Option<String>,
    
    /// Store RDRC
    pub store_rdrc: Option<bool>,
    
    /// Store fake IP
    pub store_fakeip: Option<bool>,
    
    /// Fake IP filter
    pub fakeip_filter: Option<Vec<String>>,
}

impl Default for Options {
    fn default() -> Self {
        Self {
            log: Some(LogOptions::default()),
            dns: None,
            inbounds: Vec::new(),
            outbounds: Vec::new(),
            route: None,
            experimental: None,
        }
    }
}

impl Default for LogOptions {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            output: None,
            disable_color: false,
            timestamp: true,
        }
    }
}

impl Default for DNSOptions {
    fn default() -> Self {
        Self {
            servers: Vec::new(),
            rules: Vec::new(),
            final_: None,
            strategy: Some("prefer_ipv4".to_string()),
            disable_cache: false,
            disable_expire: false,
            independent_cache: false,
            reverse_mapping: false,
            fakeip: None,
        }
    }
}

impl Default for RouteOptions {
    fn default() -> Self {
        Self {
            geoip: None,
            geosite: None,
            rules: Vec::new(),
            rule_set: Vec::new(),
            final_: Some("direct".to_string()),
            auto_detect_interface: None,
            override_android_vpn: None,
            default_interface: None,
            default_mark: None,
        }
    }
}

/// Validate configuration options
pub fn validate_options(options: &Options) -> Result<(), String> {
    // Validate log options
    if let Some(log) = &options.log {
        let valid_levels = ["trace", "debug", "info", "warn", "error"];
        if !valid_levels.contains(&log.level.as_str()) {
            return Err(format!("Invalid log level: {}", log.level));
        }
    }

    // Validate inbounds
    for inbound in &options.inbounds {
        if inbound.tag.is_empty() {
            return Err("Inbound tag cannot be empty".to_string());
        }
        if inbound.inbound_type.is_empty() {
            return Err("Inbound type cannot be empty".to_string());
        }
    }

    // Validate outbounds
    for outbound in &options.outbounds {
        if outbound.tag.is_empty() {
            return Err("Outbound tag cannot be empty".to_string());
        }
        if outbound.outbound_type.is_empty() {
            return Err("Outbound type cannot be empty".to_string());
        }
    }

    // Validate route options
    if let Some(route) = &options.route {
        if let Some(final_outbound) = &route.final_ {
            if !options.outbounds.iter().any(|o| &o.tag == final_outbound) {
                return Err(format!("Final outbound '{}' not found in outbounds", final_outbound));
            }
        }
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_options_default() {
        let options = Options::default();
        assert!(options.log.is_some());
        assert!(options.dns.is_none());
        assert!(options.inbounds.is_empty());
        assert!(options.outbounds.is_empty());
    }
    
    #[test]
    fn test_log_options_default() {
        let log_options = LogOptions::default();
        assert_eq!(log_options.level, "info");
        assert!(log_options.timestamp);
        assert!(!log_options.disable_color);
    }
    
    #[test]
    fn test_dns_options_default() {
        let dns_options = DNSOptions::default();
        assert!(dns_options.servers.is_empty());
        assert!(dns_options.rules.is_empty());
        assert_eq!(dns_options.strategy, Some("prefer_ipv4".to_string()));
        assert!(!dns_options.disable_cache);
    }
    
    #[test]
    fn test_route_options_default() {
        let route_options = RouteOptions::default();
        assert!(route_options.geoip.is_none());
        assert!(route_options.geosite.is_none());
        assert!(route_options.rules.is_empty());
        assert_eq!(route_options.final_, Some("direct".to_string()));
    }
}
