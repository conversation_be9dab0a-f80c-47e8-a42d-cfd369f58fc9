//! Protocol sniffing module
//!
//! This module provides protocol detection capabilities for automatic
//! protocol identification and routing decisions.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::time::{timeout, Duration};

/// Protocol sniffing result
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum SniffResult {
    /// HTTP protocol detected
    Http {
        method: String,
        host: Option<String>,
        path: String,
        version: String,
    },
    
    /// HTTPS/TLS protocol detected
    Tls {
        sni: Option<String>,
        version: String,
        cipher_suite: Option<String>,
    },
    
    /// SSH protocol detected
    Ssh {
        version: String,
        software: String,
    },
    
    /// FTP protocol detected
    Ftp {
        response_code: u16,
        message: String,
    },
    
    /// SMTP protocol detected
    Smtp {
        response_code: u16,
        message: String,
    },
    
    /// POP3 protocol detected
    Pop3 {
        response: String,
    },
    
    /// IMAP protocol detected
    Imap {
        response: String,
    },
    
    /// DNS protocol detected
    Dns {
        query_type: String,
        domain: Option<String>,
    },
    
    /// Unknown protocol
    Unknown,
}

impl SniffResult {
    /// Get protocol name
    pub fn protocol_name(&self) -> &str {
        match self {
            SniffResult::Http { .. } => "http",
            SniffResult::Tls { .. } => "tls",
            SniffResult::Ssh { .. } => "ssh",
            SniffResult::Ftp { .. } => "ftp",
            SniffResult::Smtp { .. } => "smtp",
            SniffResult::Pop3 { .. } => "pop3",
            SniffResult::Imap { .. } => "imap",
            SniffResult::Dns { .. } => "dns",
            SniffResult::Unknown => "unknown",
        }
    }
    
    /// Get host/domain information if available
    pub fn host(&self) -> Option<&str> {
        match self {
            SniffResult::Http { host, .. } => host.as_deref(),
            SniffResult::Tls { sni, .. } => sni.as_deref(),
            SniffResult::Dns { domain, .. } => domain.as_deref(),
            _ => None,
        }
    }
}

/// Protocol sniffer trait
pub trait ProtocolSniffer: Send + Sync {
    /// Sniff protocol from data
    fn sniff(&self, data: &[u8]) -> Option<SniffResult>;
    
    /// Get sniffer name
    fn name(&self) -> &str;
    
    /// Get priority (higher = more priority)
    fn priority(&self) -> u8;
}

/// HTTP protocol sniffer
pub struct HttpSniffer;

impl ProtocolSniffer for HttpSniffer {
    fn sniff(&self, data: &[u8]) -> Option<SniffResult> {
        let text = std::str::from_utf8(data).ok()?;
        let lines: Vec<&str> = text.lines().collect();
        
        if lines.is_empty() {
            return None;
        }
        
        let first_line = lines[0];
        let parts: Vec<&str> = first_line.split_whitespace().collect();
        
        if parts.len() < 3 {
            return None;
        }
        
        // Check if it's an HTTP method
        let method = parts[0];
        if !matches!(method, "GET" | "POST" | "PUT" | "DELETE" | "HEAD" | "OPTIONS" | "PATCH" | "CONNECT" | "TRACE") {
            return None;
        }
        
        let path = parts[1].to_string();
        let version = parts[2].to_string();
        
        // Extract Host header
        let host = lines.iter()
            .find(|line| line.to_lowercase().starts_with("host:"))
            .and_then(|line| line.split(':').nth(1))
            .map(|host| host.trim().to_string());
        
        Some(SniffResult::Http {
            method: method.to_string(),
            host,
            path,
            version,
        })
    }
    
    fn name(&self) -> &str {
        "http"
    }
    
    fn priority(&self) -> u8 {
        100
    }
}

/// TLS protocol sniffer
pub struct TlsSniffer;

impl ProtocolSniffer for TlsSniffer {
    fn sniff(&self, data: &[u8]) -> Option<SniffResult> {
        if data.len() < 6 {
            return None;
        }
        
        // Check TLS record header
        if data[0] != 0x16 || data[1] != 0x03 {
            return None;
        }
        
        let version = match data[2] {
            0x01 => "TLS 1.0",
            0x02 => "TLS 1.1", 
            0x03 => "TLS 1.2",
            0x04 => "TLS 1.3",
            _ => "Unknown",
        };
        
        // Try to extract SNI
        let sni = self.extract_sni(data);
        
        Some(SniffResult::Tls {
            sni,
            version: version.to_string(),
            cipher_suite: None,
        })
    }
    
    fn name(&self) -> &str {
        "tls"
    }
    
    fn priority(&self) -> u8 {
        90
    }
}

impl TlsSniffer {
    /// Extract SNI from TLS Client Hello
    fn extract_sni(&self, data: &[u8]) -> Option<String> {
        if data.len() < 43 {
            return None;
        }
        
        // Skip TLS record header (5 bytes) and handshake header (4 bytes)
        let mut offset = 9;
        
        // Skip version (2 bytes) and random (32 bytes)
        offset += 34;
        
        if offset >= data.len() {
            return None;
        }
        
        // Skip session ID
        let session_id_len = data[offset] as usize;
        offset += 1 + session_id_len;
        
        if offset + 2 >= data.len() {
            return None;
        }
        
        // Skip cipher suites
        let cipher_suites_len = u16::from_be_bytes([data[offset], data[offset + 1]]) as usize;
        offset += 2 + cipher_suites_len;
        
        if offset >= data.len() {
            return None;
        }
        
        // Skip compression methods
        let compression_methods_len = data[offset] as usize;
        offset += 1 + compression_methods_len;
        
        if offset + 2 >= data.len() {
            return None;
        }
        
        // Parse extensions
        let extensions_len = u16::from_be_bytes([data[offset], data[offset + 1]]) as usize;
        offset += 2;
        
        let extensions_end = offset + extensions_len;
        
        while offset + 4 < extensions_end && offset + 4 < data.len() {
            let ext_type = u16::from_be_bytes([data[offset], data[offset + 1]]);
            let ext_len = u16::from_be_bytes([data[offset + 2], data[offset + 3]]) as usize;
            offset += 4;
            
            // SNI extension type is 0
            if ext_type == 0 && offset + ext_len <= data.len() {
                return self.parse_sni_extension(&data[offset..offset + ext_len]);
            }
            
            offset += ext_len;
        }
        
        None
    }
    
    /// Parse SNI extension
    fn parse_sni_extension(&self, data: &[u8]) -> Option<String> {
        if data.len() < 5 {
            return None;
        }
        
        // Skip server name list length (2 bytes)
        let mut offset = 2;
        
        // Check server name type (should be 0 for hostname)
        if data[offset] != 0 {
            return None;
        }
        offset += 1;
        
        // Get server name length
        let name_len = u16::from_be_bytes([data[offset], data[offset + 1]]) as usize;
        offset += 2;
        
        if offset + name_len > data.len() {
            return None;
        }
        
        // Extract server name
        String::from_utf8(data[offset..offset + name_len].to_vec()).ok()
    }
}

/// SSH protocol sniffer
pub struct SshSniffer;

impl ProtocolSniffer for SshSniffer {
    fn sniff(&self, data: &[u8]) -> Option<SniffResult> {
        let text = std::str::from_utf8(data).ok()?;
        
        if text.starts_with("SSH-2.0-") || text.starts_with("SSH-1.99-") {
            let parts: Vec<&str> = text.trim().split('-').collect();
            if parts.len() >= 3 {
                let version = format!("{}-{}", parts[0], parts[1]);
                let software = parts[2..].join("-");
                
                return Some(SniffResult::Ssh {
                    version,
                    software,
                });
            }
        }
        
        None
    }
    
    fn name(&self) -> &str {
        "ssh"
    }
    
    fn priority(&self) -> u8 {
        80
    }
}

/// DNS protocol sniffer
pub struct DnsSniffer;

impl ProtocolSniffer for DnsSniffer {
    fn sniff(&self, data: &[u8]) -> Option<SniffResult> {
        if data.len() < 12 {
            return None;
        }

        // Check if it looks like a DNS query
        let flags = u16::from_be_bytes([data[2], data[3]]);
        let questions = u16::from_be_bytes([data[4], data[5]]);
        let answers = u16::from_be_bytes([data[6], data[7]]);
        let authority = u16::from_be_bytes([data[8], data[9]]);
        let additional = u16::from_be_bytes([data[10], data[11]]);

        // Check if it's a query (QR bit = 0) and has at least one question
        // Also check that the packet structure makes sense for DNS
        if (flags & 0x8000) == 0 && questions > 0 && questions <= 10 {
            // Try to extract domain name to validate DNS structure
            let domain = self.extract_domain_name(&data[12..]);

            // Only proceed if we successfully extracted a domain name
            if let Some(ref domain_name) = domain {
                // Validate that the domain name looks reasonable
                if domain_name.is_empty() || domain_name.len() > 253 {
                    return None;
                }

                // Check if domain contains valid characters
                if !domain_name.chars().all(|c| c.is_ascii_alphanumeric() || c == '.' || c == '-') {
                    return None;
                }
            } else {
                return None;
            }

            // Get query type
            let query_type = if data.len() >= 14 + domain.as_ref().map_or(0, |d| d.len() + 1) {
                let offset = 12 + domain.as_ref().map_or(0, |d| d.len() + 1);
                if offset + 2 <= data.len() {
                    let qtype = u16::from_be_bytes([data[offset], data[offset + 1]]);
                    match qtype {
                        1 => "A",
                        2 => "NS",
                        5 => "CNAME",
                        15 => "MX",
                        16 => "TXT",
                        28 => "AAAA",
                        _ => "OTHER",
                    }.to_string()
                } else {
                    "UNKNOWN".to_string()
                }
            } else {
                "UNKNOWN".to_string()
            };

            return Some(SniffResult::Dns {
                query_type,
                domain,
            });
        }

        None
    }
    
    fn name(&self) -> &str {
        "dns"
    }
    
    fn priority(&self) -> u8 {
        70
    }
}

impl DnsSniffer {
    /// Extract domain name from DNS query
    fn extract_domain_name(&self, data: &[u8]) -> Option<String> {
        let mut offset = 0;
        let mut domain_parts = Vec::new();
        
        while offset < data.len() {
            let len = data[offset] as usize;
            if len == 0 {
                break;
            }
            
            offset += 1;
            if offset + len > data.len() {
                return None;
            }
            
            let part = String::from_utf8(data[offset..offset + len].to_vec()).ok()?;
            domain_parts.push(part);
            offset += len;
        }
        
        if domain_parts.is_empty() {
            None
        } else {
            Some(domain_parts.join("."))
        }
    }
}

/// Protocol sniffing engine
pub struct SniffingEngine {
    sniffers: Vec<Arc<dyn ProtocolSniffer>>,
    timeout_duration: Duration,
}

impl SniffingEngine {
    /// Create a new sniffing engine
    pub fn new() -> Self {
        let mut engine = Self {
            sniffers: Vec::new(),
            timeout_duration: Duration::from_millis(100),
        };
        
        // Register default sniffers
        engine.register_sniffer(Arc::new(HttpSniffer));
        engine.register_sniffer(Arc::new(TlsSniffer));
        engine.register_sniffer(Arc::new(SshSniffer));
        engine.register_sniffer(Arc::new(DnsSniffer));
        
        // Sort by priority (highest first)
        engine.sniffers.sort_by(|a, b| b.priority().cmp(&a.priority()));
        
        engine
    }
    
    /// Register a protocol sniffer
    pub fn register_sniffer(&mut self, sniffer: Arc<dyn ProtocolSniffer>) {
        self.sniffers.push(sniffer);
    }
    
    /// Set sniffing timeout
    pub fn set_timeout(&mut self, timeout: Duration) {
        self.timeout_duration = timeout;
    }
    
    /// Sniff protocol from data
    pub async fn sniff(&self, data: &[u8]) -> SniffResult {
        // Try each sniffer in priority order
        for sniffer in &self.sniffers {
            if let Some(result) = sniffer.sniff(data) {
                return result;
            }
        }
        
        SniffResult::Unknown
    }
    
    /// Sniff protocol with timeout
    pub async fn sniff_with_timeout(&self, data: &[u8]) -> SniffResult {
        match timeout(self.timeout_duration, self.sniff(data)).await {
            Ok(result) => result,
            Err(_) => SniffResult::Unknown,
        }
    }
    
    /// Get registered sniffers
    pub fn sniffers(&self) -> &[Arc<dyn ProtocolSniffer>] {
        &self.sniffers
    }
}

impl Default for SniffingEngine {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_http_sniffer() {
        let sniffer = HttpSniffer;
        let data = b"GET /path HTTP/1.1\r\nHost: example.com\r\n\r\n";
        
        let result = sniffer.sniff(data).unwrap();
        match result {
            SniffResult::Http { method, host, path, version } => {
                assert_eq!(method, "GET");
                assert_eq!(host, Some("example.com".to_string()));
                assert_eq!(path, "/path");
                assert_eq!(version, "HTTP/1.1");
            },
            _ => panic!("Expected HTTP result"),
        }
    }
    
    #[test]
    fn test_tls_sniffer() {
        let sniffer = TlsSniffer;
        // Valid TLS Client Hello with enough bytes
        let data = &[0x16, 0x03, 0x03, 0x00, 0x10, 0x01];

        let result = sniffer.sniff(data).unwrap();
        match result {
            SniffResult::Tls { version, .. } => {
                assert_eq!(version, "TLS 1.2");
            },
            _ => panic!("Expected TLS result"),
        }
    }
    
    #[test]
    fn test_ssh_sniffer() {
        let sniffer = SshSniffer;
        let data = b"SSH-2.0-OpenSSH_7.4";
        
        let result = sniffer.sniff(data).unwrap();
        match result {
            SniffResult::Ssh { version, software } => {
                assert_eq!(version, "SSH-2.0");
                assert_eq!(software, "OpenSSH_7.4");
            },
            _ => panic!("Expected SSH result"),
        }
    }
    
    #[tokio::test]
    async fn test_sniffing_engine() {
        let engine = SniffingEngine::new();
        
        // Test HTTP sniffing
        let http_data = b"GET / HTTP/1.1\r\nHost: test.com\r\n\r\n";
        let result = engine.sniff(http_data).await;
        assert_eq!(result.protocol_name(), "http");
        assert_eq!(result.host(), Some("test.com"));
        
        // Test unknown protocol
        let unknown_data = b"unknown protocol data";
        let result = engine.sniff(unknown_data).await;
        assert_eq!(result.protocol_name(), "unknown");
    }
}
