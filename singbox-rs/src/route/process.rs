/// Process-based routing implementation
/// 
/// Provides routing rules based on the process that initiated the connection

use std::collections::HashMap;
use std::path::PathBuf;
use serde::{Deserialize, Serialize};
use regex::Regex;

/// Process information
#[derive(Debug, Clone)]
pub struct ProcessInfo {
    /// Process ID
    pub pid: u32,
    /// Process name
    pub name: String,
    /// Executable path
    pub path: PathBuf,
    /// Command line arguments
    pub args: Vec<String>,
    /// Parent process ID
    pub parent_pid: Option<u32>,
    /// User ID that owns the process
    pub uid: Option<u32>,
    /// Group ID that owns the process
    pub gid: Option<u32>,
}

impl ProcessInfo {
    /// Create new process info
    pub fn new(pid: u32, name: String, path: PathBuf) -> Self {
        Self {
            pid,
            name,
            path,
            args: Vec::new(),
            parent_pid: None,
            uid: None,
            gid: None,
        }
    }

    /// Get process name without extension
    pub fn name_without_extension(&self) -> String {
        if let Some(stem) = self.path.file_stem() {
            stem.to_string_lossy().to_string()
        } else {
            self.name.clone()
        }
    }

    /// Get full command line
    pub fn command_line(&self) -> String {
        if self.args.is_empty() {
            self.path.to_string_lossy().to_string()
        } else {
            format!("{} {}", self.path.to_string_lossy(), self.args.join(" "))
        }
    }
}

/// Process matching types
#[derive(Debug, Clone, PartialEq)]
pub enum ProcessMatchType {
    /// Match by process name (exact)
    Name,
    /// Match by process name (case insensitive)
    NameIgnoreCase,
    /// Match by executable path (exact)
    Path,
    /// Match by executable path (case insensitive)
    PathIgnoreCase,
    /// Match by command line (contains)
    CommandLine,
    /// Match by regular expression on name
    NameRegex,
    /// Match by regular expression on path
    PathRegex,
    /// Match by regular expression on command line
    CommandLineRegex,
}

/// Process rule for matching
#[derive(Debug, Clone)]
pub struct ProcessRule {
    pub pattern: String,
    pub match_type: ProcessMatchType,
    pub compiled_regex: Option<Regex>,
}

impl ProcessRule {
    /// Create a new process rule
    pub fn new(pattern: String, match_type: ProcessMatchType) -> Result<Self, ProcessError> {
        let compiled_regex = match match_type {
            ProcessMatchType::NameRegex | ProcessMatchType::PathRegex | ProcessMatchType::CommandLineRegex => {
                Some(Regex::new(&pattern)
                    .map_err(|e| ProcessError::InvalidRegex(pattern.clone(), e.to_string()))?)
            }
            _ => None,
        };

        Ok(Self {
            pattern,
            match_type,
            compiled_regex,
        })
    }

    /// Check if process matches this rule
    pub fn matches(&self, process: &ProcessInfo) -> bool {
        match self.match_type {
            ProcessMatchType::Name => process.name == self.pattern,
            ProcessMatchType::NameIgnoreCase => process.name.to_lowercase() == self.pattern.to_lowercase(),
            ProcessMatchType::Path => process.path.to_string_lossy() == self.pattern,
            ProcessMatchType::PathIgnoreCase => {
                process.path.to_string_lossy().to_lowercase() == self.pattern.to_lowercase()
            }
            ProcessMatchType::CommandLine => process.command_line().contains(&self.pattern),
            ProcessMatchType::NameRegex => {
                if let Some(regex) = &self.compiled_regex {
                    regex.is_match(&process.name)
                } else {
                    false
                }
            }
            ProcessMatchType::PathRegex => {
                if let Some(regex) = &self.compiled_regex {
                    regex.is_match(&process.path.to_string_lossy())
                } else {
                    false
                }
            }
            ProcessMatchType::CommandLineRegex => {
                if let Some(regex) = &self.compiled_regex {
                    regex.is_match(&process.command_line())
                } else {
                    false
                }
            }
        }
    }
}

/// Process routing rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessRoutingRule {
    /// Process rules to match
    pub rules: Vec<String>, // Serialized process rules
    /// Whether to invert the match
    pub invert: bool,
    /// Match any rule (OR) or all rules (AND)
    pub match_any: bool,
}

impl ProcessRoutingRule {
    /// Create a new process routing rule
    pub fn new(rules: Vec<String>) -> Self {
        Self {
            rules,
            invert: false,
            match_any: true,
        }
    }

    /// Create a new inverted process routing rule
    pub fn new_inverted(rules: Vec<String>) -> Self {
        Self {
            rules,
            invert: true,
            match_any: true,
        }
    }

    /// Set match mode (true for OR, false for AND)
    pub fn set_match_any(mut self, match_any: bool) -> Self {
        self.match_any = match_any;
        self
    }
}

/// Process matcher for checking if processes match rules
pub struct ProcessMatcher {
    rules: Vec<ProcessRule>,
}

impl ProcessMatcher {
    /// Create a new process matcher
    pub fn new() -> Self {
        Self {
            rules: Vec::new(),
        }
    }

    /// Add a process rule
    pub fn add_rule(&mut self, rule: ProcessRule) {
        self.rules.push(rule);
    }

    /// Add a name match rule
    pub fn add_name_rule(&mut self, name: &str, case_sensitive: bool) -> Result<(), ProcessError> {
        let match_type = if case_sensitive {
            ProcessMatchType::Name
        } else {
            ProcessMatchType::NameIgnoreCase
        };
        let rule = ProcessRule::new(name.to_string(), match_type)?;
        self.add_rule(rule);
        Ok(())
    }

    /// Add a path match rule
    pub fn add_path_rule(&mut self, path: &str, case_sensitive: bool) -> Result<(), ProcessError> {
        let match_type = if case_sensitive {
            ProcessMatchType::Path
        } else {
            ProcessMatchType::PathIgnoreCase
        };
        let rule = ProcessRule::new(path.to_string(), match_type)?;
        self.add_rule(rule);
        Ok(())
    }

    /// Add a regex rule
    pub fn add_regex_rule(&mut self, pattern: &str, target: ProcessMatchType) -> Result<(), ProcessError> {
        let rule = ProcessRule::new(pattern.to_string(), target)?;
        self.add_rule(rule);
        Ok(())
    }

    /// Check if process matches any rule
    pub fn matches_any(&self, process: &ProcessInfo) -> bool {
        self.rules.iter().any(|rule| rule.matches(process))
    }

    /// Check if process matches all rules
    pub fn matches_all(&self, process: &ProcessInfo) -> bool {
        if self.rules.is_empty() {
            return false;
        }
        self.rules.iter().all(|rule| rule.matches(process))
    }

    /// Check if process matches routing rule
    pub fn matches_routing_rule(&self, process: &ProcessInfo, rule: &ProcessRoutingRule) -> bool {
        // For simplicity, we'll assume the routing rule contains process names
        // In a real implementation, this would parse the serialized rules
        let matches = if rule.match_any {
            rule.rules.iter().any(|name| process.name == *name || process.name_without_extension() == *name)
        } else {
            rule.rules.iter().all(|name| process.name == *name || process.name_without_extension() == *name)
        };

        if rule.invert { !matches } else { matches }
    }
}

impl Default for ProcessMatcher {
    fn default() -> Self {
        Self::new()
    }
}

/// Process manager for tracking and querying processes
pub struct ProcessManager {
    processes: HashMap<u32, ProcessInfo>,
    matcher: ProcessMatcher,
}

impl ProcessManager {
    /// Create a new process manager
    pub fn new() -> Self {
        Self {
            processes: HashMap::new(),
            matcher: ProcessMatcher::new(),
        }
    }

    /// Add or update process information
    pub fn update_process(&mut self, process: ProcessInfo) {
        self.processes.insert(process.pid, process);
    }

    /// Remove process information
    pub fn remove_process(&mut self, pid: u32) {
        self.processes.remove(&pid);
    }

    /// Get process information by PID
    pub fn get_process(&self, pid: u32) -> Option<&ProcessInfo> {
        self.processes.get(&pid)
    }

    /// Get all processes
    pub fn get_all_processes(&self) -> Vec<&ProcessInfo> {
        self.processes.values().collect()
    }

    /// Find processes by name
    pub fn find_by_name(&self, name: &str) -> Vec<&ProcessInfo> {
        self.processes.values()
            .filter(|p| p.name == name || p.name_without_extension() == name)
            .collect()
    }

    /// Find processes matching a rule
    pub fn find_matching(&self, rule: &ProcessRule) -> Vec<&ProcessInfo> {
        self.processes.values()
            .filter(|p| rule.matches(p))
            .collect()
    }

    /// Check if any process matches routing rule
    pub fn matches_routing_rule(&self, rule: &ProcessRoutingRule) -> bool {
        self.processes.values()
            .any(|process| self.matcher.matches_routing_rule(process, rule))
    }

    /// Get process statistics
    pub fn get_stats(&self) -> ProcessStats {
        ProcessStats {
            total_processes: self.processes.len(),
            unique_names: self.processes.values()
                .map(|p| p.name_without_extension())
                .collect::<std::collections::HashSet<_>>()
                .len(),
        }
    }

    /// Cleanup old processes (placeholder - would integrate with system APIs)
    pub fn cleanup_dead_processes(&mut self) {
        // In a real implementation, this would check if processes are still alive
        // and remove dead ones from the cache
    }
}

impl Default for ProcessManager {
    fn default() -> Self {
        Self::new()
    }
}

/// Process statistics
#[derive(Debug, Clone)]
pub struct ProcessStats {
    pub total_processes: usize,
    pub unique_names: usize,
}

/// Process-related errors
#[derive(Debug, Clone)]
pub enum ProcessError {
    InvalidRegex(String, String),
    SystemError(String),
    ProcessNotFound(u32),
}

impl std::fmt::Display for ProcessError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ProcessError::InvalidRegex(pattern, error) => write!(f, "Invalid regex '{}': {}", pattern, error),
            ProcessError::SystemError(msg) => write!(f, "System error: {}", msg),
            ProcessError::ProcessNotFound(pid) => write!(f, "Process not found: {}", pid),
        }
    }
}

impl std::error::Error for ProcessError {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_process_info() {
        let process = ProcessInfo::new(
            1234,
            "chrome.exe".to_string(),
            PathBuf::from("/usr/bin/chrome"),
        );
        
        assert_eq!(process.pid, 1234);
        assert_eq!(process.name, "chrome.exe");
        assert_eq!(process.name_without_extension(), "chrome");
    }

    #[test]
    fn test_process_rule_name_match() {
        let rule = ProcessRule::new("chrome.exe".to_string(), ProcessMatchType::Name).unwrap();
        let process = ProcessInfo::new(
            1234,
            "chrome.exe".to_string(),
            PathBuf::from("/usr/bin/chrome.exe"),
        );
        
        assert!(rule.matches(&process));
        
        let other_process = ProcessInfo::new(
            5678,
            "firefox.exe".to_string(),
            PathBuf::from("/usr/bin/firefox.exe"),
        );
        
        assert!(!rule.matches(&other_process));
    }

    #[test]
    fn test_process_matcher() {
        let mut matcher = ProcessMatcher::new();
        matcher.add_name_rule("chrome", false).unwrap();
        matcher.add_name_rule("firefox", false).unwrap();

        let chrome_process = ProcessInfo::new(
            1234,
            "chrome".to_string(), // 修复：使用不带扩展名的名称
            PathBuf::from("/usr/bin/chrome"),
        );

        assert!(matcher.matches_any(&chrome_process));

        let other_process = ProcessInfo::new(
            5678,
            "notepad".to_string(),
            PathBuf::from("/usr/bin/notepad"),
        );

        assert!(!matcher.matches_any(&other_process));
    }

    #[test]
    fn test_process_manager() {
        let mut manager = ProcessManager::new();
        
        let process = ProcessInfo::new(
            1234,
            "chrome.exe".to_string(),
            PathBuf::from("/usr/bin/chrome.exe"),
        );
        
        manager.update_process(process);
        
        assert!(manager.get_process(1234).is_some());
        assert_eq!(manager.get_process(1234).unwrap().name, "chrome.exe");
        
        let found = manager.find_by_name("chrome");
        assert_eq!(found.len(), 1);
        
        manager.remove_process(1234);
        assert!(manager.get_process(1234).is_none());
    }
}
