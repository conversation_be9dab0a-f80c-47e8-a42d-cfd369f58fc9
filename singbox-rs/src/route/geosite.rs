/// GeoSite routing implementation
/// 
/// Provides domain-based routing rules using categorized domain lists

use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use serde::{Deserialize, Serialize};
use regex::Regex;

/// Domain matching types
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum DomainMatchType {
    /// Exact domain match
    Full,
    /// Suffix match (includes subdomains)
    Suffix,
    /// Keyword match (contains)
    Keyword,
    /// Regular expression match
    Regex,
}

/// Domain rule entry
#[derive(Debug, <PERSON><PERSON>)]
pub struct DomainRule {
    pub pattern: String,
    pub match_type: DomainMatchType,
    pub compiled_regex: Option<Regex>,
}

impl DomainRule {
    /// Create a new domain rule
    pub fn new(pattern: String, match_type: DomainMatchType) -> Result<Self, GeoSiteError> {
        let compiled_regex = if match_type == DomainMatchType::Regex {
            Some(Regex::new(&pattern)
                .map_err(|e| GeoSiteError::InvalidRegex(pattern.clone(), e.to_string()))?)
        } else {
            None
        };

        Ok(Self {
            pattern,
            match_type,
            compiled_regex,
        })
    }

    /// Check if a domain matches this rule
    pub fn matches(&self, domain: &str) -> bool {
        let domain_lower = domain.to_lowercase();
        let pattern_lower = self.pattern.to_lowercase();

        match self.match_type {
            DomainMatchType::Full => domain_lower == pattern_lower,
            DomainMatchType::Suffix => {
                domain_lower == pattern_lower || domain_lower.ends_with(&format!(".{}", pattern_lower))
            }
            DomainMatchType::Keyword => domain_lower.contains(&pattern_lower),
            DomainMatchType::Regex => {
                if let Some(regex) = &self.compiled_regex {
                    regex.is_match(&domain_lower)
                } else {
                    false
                }
            }
        }
    }
}

/// GeoSite category containing domain rules
#[derive(Debug, Clone)]
pub struct GeoSiteCategory {
    pub name: String,
    pub description: String,
    pub rules: Vec<DomainRule>,
}

impl GeoSiteCategory {
    /// Create a new category
    pub fn new(name: String, description: String) -> Self {
        Self {
            name,
            description,
            rules: Vec::new(),
        }
    }

    /// Add a domain rule
    pub fn add_rule(&mut self, rule: DomainRule) {
        self.rules.push(rule);
    }

    /// Add a full domain match
    pub fn add_full(&mut self, domain: &str) -> Result<(), GeoSiteError> {
        let rule = DomainRule::new(domain.to_string(), DomainMatchType::Full)?;
        self.add_rule(rule);
        Ok(())
    }

    /// Add a suffix match (includes subdomains)
    pub fn add_suffix(&mut self, domain: &str) -> Result<(), GeoSiteError> {
        let rule = DomainRule::new(domain.to_string(), DomainMatchType::Suffix)?;
        self.add_rule(rule);
        Ok(())
    }

    /// Add a keyword match
    pub fn add_keyword(&mut self, keyword: &str) -> Result<(), GeoSiteError> {
        let rule = DomainRule::new(keyword.to_string(), DomainMatchType::Keyword)?;
        self.add_rule(rule);
        Ok(())
    }

    /// Add a regex match
    pub fn add_regex(&mut self, pattern: &str) -> Result<(), GeoSiteError> {
        let rule = DomainRule::new(pattern.to_string(), DomainMatchType::Regex)?;
        self.add_rule(rule);
        Ok(())
    }

    /// Check if a domain matches any rule in this category
    pub fn matches(&self, domain: &str) -> bool {
        self.rules.iter().any(|rule| rule.matches(domain))
    }
}

/// GeoSite database
#[derive(Debug, Clone)]
pub struct GeoSiteDatabase {
    categories: HashMap<String, GeoSiteCategory>,
}

impl GeoSiteDatabase {
    /// Create a new empty database
    pub fn new() -> Self {
        Self {
            categories: HashMap::new(),
        }
    }

    /// Add a category
    pub fn add_category(&mut self, category: GeoSiteCategory) {
        self.categories.insert(category.name.clone(), category);
    }

    /// Get a category by name
    pub fn get_category(&self, name: &str) -> Option<&GeoSiteCategory> {
        self.categories.get(name)
    }

    /// Get all category names
    pub fn get_category_names(&self) -> Vec<&str> {
        self.categories.keys().map(|s| s.as_str()).collect()
    }

    /// Check if domain matches any category
    pub fn matches_categories(&self, domain: &str, categories: &[String]) -> bool {
        categories.iter().any(|cat_name| {
            if let Some(category) = self.get_category(cat_name) {
                category.matches(domain)
            } else {
                false
            }
        })
    }

    /// Load built-in GeoSite database
    pub fn load_builtin() -> Self {
        let mut db = Self::new();

        // Google services
        let mut google = GeoSiteCategory::new("google".to_string(), "Google services".to_string());
        google.add_suffix("google.com").unwrap();
        google.add_suffix("googleapis.com").unwrap();
        google.add_suffix("googleusercontent.com").unwrap();
        google.add_suffix("youtube.com").unwrap();
        google.add_suffix("ytimg.com").unwrap();
        google.add_suffix("gmail.com").unwrap();
        db.add_category(google);

        // Facebook/Meta services
        let mut facebook = GeoSiteCategory::new("facebook".to_string(), "Facebook/Meta services".to_string());
        facebook.add_suffix("facebook.com").unwrap();
        facebook.add_suffix("instagram.com").unwrap();
        facebook.add_suffix("whatsapp.com").unwrap();
        facebook.add_suffix("fbcdn.net").unwrap();
        db.add_category(facebook);

        // Microsoft services
        let mut microsoft = GeoSiteCategory::new("microsoft".to_string(), "Microsoft services".to_string());
        microsoft.add_suffix("microsoft.com").unwrap();
        microsoft.add_suffix("office.com").unwrap();
        microsoft.add_suffix("outlook.com").unwrap();
        microsoft.add_suffix("live.com").unwrap();
        microsoft.add_suffix("xbox.com").unwrap();
        db.add_category(microsoft);

        // Apple services
        let mut apple = GeoSiteCategory::new("apple".to_string(), "Apple services".to_string());
        apple.add_suffix("apple.com").unwrap();
        apple.add_suffix("icloud.com").unwrap();
        apple.add_suffix("itunes.com").unwrap();
        apple.add_suffix("appstore.com").unwrap();
        db.add_category(apple);

        // Chinese sites
        let mut cn = GeoSiteCategory::new("cn".to_string(), "Chinese websites".to_string());
        cn.add_suffix("baidu.com").unwrap();
        cn.add_suffix("qq.com").unwrap();
        cn.add_suffix("weibo.com").unwrap();
        cn.add_suffix("taobao.com").unwrap();
        cn.add_suffix("alipay.com").unwrap();
        cn.add_suffix("163.com").unwrap();
        cn.add_suffix("sina.com.cn").unwrap();
        db.add_category(cn);

        // Streaming services
        let mut streaming = GeoSiteCategory::new("streaming".to_string(), "Streaming services".to_string());
        streaming.add_suffix("netflix.com").unwrap();
        streaming.add_suffix("hulu.com").unwrap();
        streaming.add_suffix("disney.com").unwrap();
        streaming.add_suffix("primevideo.com").unwrap();
        streaming.add_suffix("twitch.tv").unwrap();
        db.add_category(streaming);

        // Social media
        let mut social = GeoSiteCategory::new("social".to_string(), "Social media".to_string());
        social.add_suffix("twitter.com").unwrap();
        social.add_suffix("reddit.com").unwrap();
        social.add_suffix("linkedin.com").unwrap();
        social.add_suffix("tiktok.com").unwrap();
        social.add_suffix("snapchat.com").unwrap();
        db.add_category(social);

        // Ad and tracking domains
        let mut ads = GeoSiteCategory::new("ads".to_string(), "Advertisement and tracking".to_string());
        ads.add_keyword("ads").unwrap();
        ads.add_keyword("analytics").unwrap();
        ads.add_keyword("tracking").unwrap();
        ads.add_suffix("doubleclick.net").unwrap();
        ads.add_suffix("googleadservices.com").unwrap();
        db.add_category(ads);

        db
    }
}

impl Default for GeoSiteDatabase {
    fn default() -> Self {
        Self::load_builtin()
    }
}

/// GeoSite rule for routing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeoSiteRule {
    /// Categories to match
    pub categories: Vec<String>,
    /// Whether to invert the match
    pub invert: bool,
}

impl GeoSiteRule {
    /// Create a new GeoSite rule
    pub fn new(categories: Vec<String>) -> Self {
        Self {
            categories,
            invert: false,
        }
    }

    /// Create a new inverted GeoSite rule
    pub fn new_inverted(categories: Vec<String>) -> Self {
        Self {
            categories,
            invert: true,
        }
    }

    /// Check if a domain matches this rule
    pub fn matches(&self, domain: &str, database: &GeoSiteDatabase) -> bool {
        let matches = database.matches_categories(domain, &self.categories);
        if self.invert { !matches } else { matches }
    }
}

/// GeoSite manager
pub struct GeoSiteManager {
    database: Arc<GeoSiteDatabase>,
}

impl GeoSiteManager {
    /// Create a new GeoSite manager
    pub fn new() -> Self {
        Self {
            database: Arc::new(GeoSiteDatabase::load_builtin()),
        }
    }

    /// Create with custom database
    pub fn with_database(database: GeoSiteDatabase) -> Self {
        Self {
            database: Arc::new(database),
        }
    }

    /// Check if domain matches rule
    pub fn matches_rule(&self, domain: &str, rule: &GeoSiteRule) -> bool {
        rule.matches(domain, &self.database)
    }

    /// Get matching categories for a domain
    pub fn get_matching_categories(&self, domain: &str) -> Vec<String> {
        self.database.categories.iter()
            .filter(|(_, category)| category.matches(domain))
            .map(|(name, _)| name.clone())
            .collect()
    }

    /// Get database reference
    pub fn database(&self) -> &GeoSiteDatabase {
        &self.database
    }

    /// Update database
    pub fn update_database(&mut self, database: GeoSiteDatabase) {
        self.database = Arc::new(database);
    }
}

impl Default for GeoSiteManager {
    fn default() -> Self {
        Self::new()
    }
}

/// GeoSite errors
#[derive(Debug, Clone)]
pub enum GeoSiteError {
    InvalidRegex(String, String),
    CategoryNotFound(String),
    DatabaseError(String),
}

impl std::fmt::Display for GeoSiteError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            GeoSiteError::InvalidRegex(pattern, error) => write!(f, "Invalid regex '{}': {}", pattern, error),
            GeoSiteError::CategoryNotFound(name) => write!(f, "Category not found: {}", name),
            GeoSiteError::DatabaseError(msg) => write!(f, "Database error: {}", msg),
        }
    }
}

impl std::error::Error for GeoSiteError {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_domain_rule_full_match() {
        let rule = DomainRule::new("example.com".to_string(), DomainMatchType::Full).unwrap();
        assert!(rule.matches("example.com"));
        assert!(rule.matches("EXAMPLE.COM"));
        assert!(!rule.matches("sub.example.com"));
        assert!(!rule.matches("example.org"));
    }

    #[test]
    fn test_domain_rule_suffix_match() {
        let rule = DomainRule::new("example.com".to_string(), DomainMatchType::Suffix).unwrap();
        assert!(rule.matches("example.com"));
        assert!(rule.matches("sub.example.com"));
        assert!(rule.matches("deep.sub.example.com"));
        assert!(!rule.matches("example.org"));
        assert!(!rule.matches("notexample.com"));
    }

    #[test]
    fn test_domain_rule_keyword_match() {
        let rule = DomainRule::new("ads".to_string(), DomainMatchType::Keyword).unwrap();
        assert!(rule.matches("ads.example.com"));
        assert!(rule.matches("example-ads.com"));
        assert!(rule.matches("googleads.com"));
        assert!(!rule.matches("example.com"));
    }

    #[test]
    fn test_geosite_database() {
        let db = GeoSiteDatabase::load_builtin();
        
        // Test Google category
        assert!(db.matches_categories("google.com", &vec!["google".to_string()]));
        assert!(db.matches_categories("mail.google.com", &vec!["google".to_string()]));
        assert!(!db.matches_categories("example.com", &vec!["google".to_string()]));
        
        // Test ads category
        assert!(db.matches_categories("ads.example.com", &vec!["ads".to_string()]));
        assert!(db.matches_categories("analytics.google.com", &vec!["ads".to_string()]));
    }

    #[test]
    fn test_geosite_rule() {
        let rule = GeoSiteRule::new(vec!["google".to_string(), "facebook".to_string()]);
        let db = GeoSiteDatabase::load_builtin();
        
        assert!(rule.matches("google.com", &db));
        assert!(rule.matches("facebook.com", &db));
        assert!(!rule.matches("example.com", &db));
    }

    #[test]
    fn test_inverted_geosite_rule() {
        let rule = GeoSiteRule::new_inverted(vec!["ads".to_string()]);
        let db = GeoSiteDatabase::load_builtin();
        
        assert!(!rule.matches("ads.example.com", &db));
        assert!(rule.matches("google.com", &db));
    }
}
