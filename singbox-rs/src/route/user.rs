/// User-based routing implementation
/// 
/// Provides routing rules based on user authentication and identification

use std::collections::{HashMap, HashSet};
use serde::{Deserialize, Serialize};
use regex::Regex;

/// User information
#[derive(Debug, <PERSON>lone)]
pub struct UserInfo {
    /// User ID
    pub id: String,
    /// Username
    pub username: String,
    /// User groups
    pub groups: Vec<String>,
    /// User roles
    pub roles: Vec<String>,
    /// User attributes/metadata
    pub attributes: HashMap<String, String>,
    /// Authentication method used
    pub auth_method: String,
    /// Authentication timestamp
    pub auth_time: std::time::SystemTime,
}

impl UserInfo {
    /// Create new user info
    pub fn new(id: String, username: String) -> Self {
        Self {
            id,
            username,
            groups: Vec::new(),
            roles: Vec::new(),
            attributes: HashMap::new(),
            auth_method: "unknown".to_string(),
            auth_time: std::time::SystemTime::now(),
        }
    }

    /// Add user to a group
    pub fn add_group(&mut self, group: String) {
        if !self.groups.contains(&group) {
            self.groups.push(group);
        }
    }

    /// Add role to user
    pub fn add_role(&mut self, role: String) {
        if !self.roles.contains(&role) {
            self.roles.push(role);
        }
    }

    /// Set user attribute
    pub fn set_attribute(&mut self, key: String, value: String) {
        self.attributes.insert(key, value);
    }

    /// Check if user has a specific group
    pub fn has_group(&self, group: &str) -> bool {
        self.groups.contains(&group.to_string())
    }

    /// Check if user has a specific role
    pub fn has_role(&self, role: &str) -> bool {
        self.roles.contains(&role.to_string())
    }

    /// Get user attribute
    pub fn get_attribute(&self, key: &str) -> Option<&String> {
        self.attributes.get(key)
    }

    /// Check if user has any of the specified groups
    pub fn has_any_group(&self, groups: &[String]) -> bool {
        groups.iter().any(|g| self.has_group(g))
    }

    /// Check if user has any of the specified roles
    pub fn has_any_role(&self, roles: &[String]) -> bool {
        roles.iter().any(|r| self.has_role(r))
    }
}

/// User matching types
#[derive(Debug, Clone, PartialEq)]
pub enum UserMatchType {
    /// Match by user ID (exact)
    UserId,
    /// Match by username (exact)
    Username,
    /// Match by username (case insensitive)
    UsernameIgnoreCase,
    /// Match by group membership
    Group,
    /// Match by role
    Role,
    /// Match by attribute key-value pair
    Attribute,
    /// Match by regular expression on username
    UsernameRegex,
    /// Match by authentication method
    AuthMethod,
}

/// User rule for matching
#[derive(Debug, Clone)]
pub struct UserRule {
    pub pattern: String,
    pub match_type: UserMatchType,
    pub attribute_key: Option<String>, // For attribute matching
    pub compiled_regex: Option<Regex>,
}

impl UserRule {
    /// Create a new user rule
    pub fn new(pattern: String, match_type: UserMatchType) -> Result<Self, UserError> {
        let compiled_regex = if match_type == UserMatchType::UsernameRegex {
            Some(Regex::new(&pattern)
                .map_err(|e| UserError::InvalidRegex(pattern.clone(), e.to_string()))?)
        } else {
            None
        };

        Ok(Self {
            pattern,
            match_type,
            attribute_key: None,
            compiled_regex,
        })
    }

    /// Create a new attribute rule
    pub fn new_attribute(key: String, value: String) -> Self {
        Self {
            pattern: value,
            match_type: UserMatchType::Attribute,
            attribute_key: Some(key),
            compiled_regex: None,
        }
    }

    /// Check if user matches this rule
    pub fn matches(&self, user: &UserInfo) -> bool {
        match self.match_type {
            UserMatchType::UserId => user.id == self.pattern,
            UserMatchType::Username => user.username == self.pattern,
            UserMatchType::UsernameIgnoreCase => {
                user.username.to_lowercase() == self.pattern.to_lowercase()
            }
            UserMatchType::Group => user.has_group(&self.pattern),
            UserMatchType::Role => user.has_role(&self.pattern),
            UserMatchType::Attribute => {
                if let Some(key) = &self.attribute_key {
                    user.get_attribute(key).map_or(false, |v| v == &self.pattern)
                } else {
                    false
                }
            }
            UserMatchType::UsernameRegex => {
                if let Some(regex) = &self.compiled_regex {
                    regex.is_match(&user.username)
                } else {
                    false
                }
            }
            UserMatchType::AuthMethod => user.auth_method == self.pattern,
        }
    }
}

/// User routing rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserRoutingRule {
    /// User IDs to match
    pub user_ids: Vec<String>,
    /// Usernames to match
    pub usernames: Vec<String>,
    /// Groups to match
    pub groups: Vec<String>,
    /// Roles to match
    pub roles: Vec<String>,
    /// Authentication methods to match
    pub auth_methods: Vec<String>,
    /// Whether to invert the match
    pub invert: bool,
    /// Match any condition (OR) or all conditions (AND)
    pub match_any: bool,
}

impl UserRoutingRule {
    /// Create a new user routing rule
    pub fn new() -> Self {
        Self {
            user_ids: Vec::new(),
            usernames: Vec::new(),
            groups: Vec::new(),
            roles: Vec::new(),
            auth_methods: Vec::new(),
            invert: false,
            match_any: true,
        }
    }

    /// Add user ID to match
    pub fn add_user_id(mut self, user_id: String) -> Self {
        self.user_ids.push(user_id);
        self
    }

    /// Add username to match
    pub fn add_username(mut self, username: String) -> Self {
        self.usernames.push(username);
        self
    }

    /// Add group to match
    pub fn add_group(mut self, group: String) -> Self {
        self.groups.push(group);
        self
    }

    /// Add role to match
    pub fn add_role(mut self, role: String) -> Self {
        self.roles.push(role);
        self
    }

    /// Add authentication method to match
    pub fn add_auth_method(mut self, auth_method: String) -> Self {
        self.auth_methods.push(auth_method);
        self
    }

    /// Set invert flag
    pub fn invert(mut self) -> Self {
        self.invert = true;
        self
    }

    /// Set match mode (true for OR, false for AND)
    pub fn set_match_any(mut self, match_any: bool) -> Self {
        self.match_any = match_any;
        self
    }

    /// Check if user matches this rule
    pub fn matches(&self, user: &UserInfo) -> bool {
        let conditions = vec![
            (!self.user_ids.is_empty(), self.user_ids.contains(&user.id)),
            (!self.usernames.is_empty(), self.usernames.contains(&user.username)),
            (!self.groups.is_empty(), user.has_any_group(&self.groups)),
            (!self.roles.is_empty(), user.has_any_role(&self.roles)),
            (!self.auth_methods.is_empty(), self.auth_methods.contains(&user.auth_method)),
        ];

        let active_conditions: Vec<bool> = conditions
            .into_iter()
            .filter(|(active, _)| *active)
            .map(|(_, result)| result)
            .collect();

        if active_conditions.is_empty() {
            return false;
        }

        let matches = if self.match_any {
            active_conditions.iter().any(|&result| result)
        } else {
            active_conditions.iter().all(|&result| result)
        };

        if self.invert { !matches } else { matches }
    }
}

impl Default for UserRoutingRule {
    fn default() -> Self {
        Self::new()
    }
}

/// User manager for tracking authenticated users
pub struct UserManager {
    users: HashMap<String, UserInfo>,
    session_users: HashMap<String, String>, // session_id -> user_id
}

impl UserManager {
    /// Create a new user manager
    pub fn new() -> Self {
        Self {
            users: HashMap::new(),
            session_users: HashMap::new(),
        }
    }

    /// Add or update user information
    pub fn add_user(&mut self, user: UserInfo) {
        self.users.insert(user.id.clone(), user);
    }

    /// Remove user
    pub fn remove_user(&mut self, user_id: &str) {
        self.users.remove(user_id);
        // Remove associated sessions
        self.session_users.retain(|_, uid| uid != user_id);
    }

    /// Get user by ID
    pub fn get_user(&self, user_id: &str) -> Option<&UserInfo> {
        self.users.get(user_id)
    }

    /// Get user by username
    pub fn get_user_by_username(&self, username: &str) -> Option<&UserInfo> {
        self.users.values().find(|u| u.username == username)
    }

    /// Associate session with user
    pub fn create_session(&mut self, session_id: String, user_id: String) {
        if self.users.contains_key(&user_id) {
            self.session_users.insert(session_id, user_id);
        }
    }

    /// Remove session
    pub fn remove_session(&mut self, session_id: &str) {
        self.session_users.remove(session_id);
    }

    /// Get user by session ID
    pub fn get_user_by_session(&self, session_id: &str) -> Option<&UserInfo> {
        if let Some(user_id) = self.session_users.get(session_id) {
            self.get_user(user_id)
        } else {
            None
        }
    }

    /// Get all users
    pub fn get_all_users(&self) -> Vec<&UserInfo> {
        self.users.values().collect()
    }

    /// Find users by group
    pub fn find_by_group(&self, group: &str) -> Vec<&UserInfo> {
        self.users.values()
            .filter(|u| u.has_group(group))
            .collect()
    }

    /// Find users by role
    pub fn find_by_role(&self, role: &str) -> Vec<&UserInfo> {
        self.users.values()
            .filter(|u| u.has_role(role))
            .collect()
    }

    /// Check if user matches routing rule
    pub fn matches_routing_rule(&self, user_id: &str, rule: &UserRoutingRule) -> bool {
        if let Some(user) = self.get_user(user_id) {
            rule.matches(user)
        } else {
            false
        }
    }

    /// Check if session matches routing rule
    pub fn session_matches_routing_rule(&self, session_id: &str, rule: &UserRoutingRule) -> bool {
        if let Some(user) = self.get_user_by_session(session_id) {
            rule.matches(user)
        } else {
            false
        }
    }

    /// Get user statistics
    pub fn get_stats(&self) -> UserStats {
        let all_groups: HashSet<String> = self.users.values()
            .flat_map(|u| u.groups.iter().cloned())
            .collect();
        
        let all_roles: HashSet<String> = self.users.values()
            .flat_map(|u| u.roles.iter().cloned())
            .collect();

        UserStats {
            total_users: self.users.len(),
            active_sessions: self.session_users.len(),
            unique_groups: all_groups.len(),
            unique_roles: all_roles.len(),
        }
    }

    /// Cleanup expired sessions (placeholder)
    pub fn cleanup_expired_sessions(&mut self) {
        // In a real implementation, this would check session expiration
        // and remove expired sessions
    }
}

impl Default for UserManager {
    fn default() -> Self {
        Self::new()
    }
}

/// User statistics
#[derive(Debug, Clone)]
pub struct UserStats {
    pub total_users: usize,
    pub active_sessions: usize,
    pub unique_groups: usize,
    pub unique_roles: usize,
}

/// User-related errors
#[derive(Debug, Clone)]
pub enum UserError {
    InvalidRegex(String, String),
    UserNotFound(String),
    SessionNotFound(String),
    AuthenticationFailed(String),
}

impl std::fmt::Display for UserError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            UserError::InvalidRegex(pattern, error) => write!(f, "Invalid regex '{}': {}", pattern, error),
            UserError::UserNotFound(id) => write!(f, "User not found: {}", id),
            UserError::SessionNotFound(id) => write!(f, "Session not found: {}", id),
            UserError::AuthenticationFailed(msg) => write!(f, "Authentication failed: {}", msg),
        }
    }
}

impl std::error::Error for UserError {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_info() {
        let mut user = UserInfo::new("user123".to_string(), "john_doe".to_string());
        user.add_group("admin".to_string());
        user.add_role("moderator".to_string());
        user.set_attribute("department".to_string(), "engineering".to_string());
        
        assert!(user.has_group("admin"));
        assert!(user.has_role("moderator"));
        assert_eq!(user.get_attribute("department"), Some(&"engineering".to_string()));
    }

    #[test]
    fn test_user_routing_rule() {
        let rule = UserRoutingRule::new()
            .add_group("admin".to_string())
            .add_role("moderator".to_string());
        
        let mut user = UserInfo::new("user123".to_string(), "john_doe".to_string());
        user.add_group("admin".to_string());
        
        assert!(rule.matches(&user));
        
        let mut other_user = UserInfo::new("user456".to_string(), "jane_doe".to_string());
        other_user.add_group("user".to_string());
        
        assert!(!rule.matches(&other_user));
    }

    #[test]
    fn test_user_manager() {
        let mut manager = UserManager::new();
        
        let user = UserInfo::new("user123".to_string(), "john_doe".to_string());
        manager.add_user(user);
        
        assert!(manager.get_user("user123").is_some());
        assert!(manager.get_user_by_username("john_doe").is_some());
        
        manager.create_session("session456".to_string(), "user123".to_string());
        assert!(manager.get_user_by_session("session456").is_some());
        
        manager.remove_session("session456");
        assert!(manager.get_user_by_session("session456").is_none());
    }
}
