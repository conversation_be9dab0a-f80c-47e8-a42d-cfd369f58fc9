//! Rule set module
//!
//! This module provides external rule set loading, updating, and compilation
//! functionality for advanced routing rules.

use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use regex::Regex;

use crate::adapter::InboundContext;
use crate::route::rule::{RouteRule, Destination};

/// Rule set format
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum RuleSetFormat {
    /// Source format (JSON/YAML)
    Source,
    /// Binary format (compiled)
    Binary,
}

/// Rule set type
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum RuleSetType {
    /// Local file
    Local,
    /// Remote URL
    Remote,
    /// Inline rules
    Inline,
}

/// Rule set entry
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "snake_case")]
pub enum RuleSetEntry {
    /// Domain exact match
    Domain {
        value: String,
    },
    /// Domain suffix match
    DomainSuffix {
        value: String,
    },
    /// Domain keyword match
    DomainKeyword {
        value: String,
    },
    /// Domain regex match
    DomainRegex {
        value: String,
    },
    /// IP CIDR match
    IpCidr {
        value: String,
    },
    /// GeoIP match
    Geoip {
        country: String,
    },
    /// GeoSite match
    Geosite {
        category: String,
    },
    /// Process name match
    ProcessName {
        value: String,
    },
    /// Process path match
    ProcessPath {
        value: String,
    },
    /// Port match
    Port {
        value: u16,
    },
    /// Port range match
    PortRange {
        start: u16,
        end: u16,
    },
}

impl RuleSetEntry {
    /// Check if entry matches the given context
    pub fn matches(&self, ctx: &InboundContext) -> bool {
        match self {
            RuleSetEntry::Domain { value } => {
                if let Some(ref dest) = ctx.destination {
                    if let Some(domain) = dest.domain() {
                        return domain.eq_ignore_ascii_case(value);
                    }
                }
                false
            },
            RuleSetEntry::DomainSuffix { value } => {
                if let Some(ref dest) = ctx.destination {
                    if let Some(domain) = dest.domain() {
                        return domain.to_lowercase().ends_with(&value.to_lowercase());
                    }
                }
                false
            },
            RuleSetEntry::DomainKeyword { value } => {
                if let Some(ref dest) = ctx.destination {
                    if let Some(domain) = dest.domain() {
                        return domain.to_lowercase().contains(&value.to_lowercase());
                    }
                }
                false
            },
            RuleSetEntry::DomainRegex { value } => {
                if let Some(ref dest) = ctx.destination {
                    if let Some(domain) = dest.domain() {
                        if let Ok(regex) = Regex::new(value) {
                            return regex.is_match(domain);
                        }
                    }
                }
                false
            },
            RuleSetEntry::IpCidr { value } => {
                if let Some(ref dest) = ctx.destination {
                    if let Some(ip) = dest.ip() {
                        // Simplified CIDR matching - in real implementation would use proper CIDR parsing
                        return value.contains(&ip.to_string());
                    }
                }
                false
            },
            RuleSetEntry::Geoip { country: _ } => {
                // GeoIP matching would require GeoIP database
                false
            },
            RuleSetEntry::Geosite { category: _ } => {
                // GeoSite matching would require GeoSite database
                false
            },
            RuleSetEntry::ProcessName { value } => {
                if let Some(ref process_info) = ctx.process_info {
                    return process_info.name.eq_ignore_ascii_case(value);
                }
                false
            },
            RuleSetEntry::ProcessPath { value } => {
                if let Some(ref process_info) = ctx.process_info {
                    return process_info.path.contains(value);
                }
                false
            },
            RuleSetEntry::Port { value } => {
                if let Some(ref dest) = ctx.destination {
                    return dest.port() == *value;
                }
                false
            },
            RuleSetEntry::PortRange { start, end } => {
                if let Some(ref dest) = ctx.destination {
                    let port = dest.port();
                    return port >= *start && port <= *end;
                }
                false
            },
        }
    }
}

/// Rule set configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleSetConfig {
    /// Rule set tag
    pub tag: String,
    
    /// Rule set type
    pub r#type: RuleSetType,
    
    /// Rule set format
    pub format: RuleSetFormat,
    
    /// Local file path (for Local type)
    pub path: Option<PathBuf>,
    
    /// Remote URL (for Remote type)
    pub url: Option<String>,
    
    /// Download detour (for Remote type)
    pub download_detour: Option<String>,
    
    /// Update interval in seconds (for Remote type)
    pub update_interval: Option<u64>,
    
    /// Inline rules (for Inline type)
    pub rules: Option<Vec<RuleSetEntry>>,
}

/// Compiled rule set
#[derive(Debug, Clone)]
pub struct CompiledRuleSet {
    /// Rule set tag
    pub tag: String,
    
    /// Compiled entries
    pub entries: Vec<RuleSetEntry>,
    
    /// Compilation time
    pub compiled_at: SystemTime,
    
    /// Source file hash (for change detection)
    pub source_hash: Option<String>,
}

impl CompiledRuleSet {
    /// Check if rule set matches the given context
    pub fn matches(&self, ctx: &InboundContext) -> bool {
        self.entries.iter().any(|entry| entry.matches(ctx))
    }
    
    /// Get entry count
    pub fn entry_count(&self) -> usize {
        self.entries.len()
    }
}

/// Rule set manager
pub struct RuleSetManager {
    /// Loaded rule sets
    rule_sets: Arc<RwLock<HashMap<String, Arc<CompiledRuleSet>>>>,
    
    /// Rule set configurations
    configs: Arc<RwLock<HashMap<String, RuleSetConfig>>>,
    
    /// Update tasks
    update_tasks: Arc<RwLock<HashMap<String, tokio::task::JoinHandle<()>>>>,
}

impl RuleSetManager {
    /// Create a new rule set manager
    pub fn new() -> Self {
        Self {
            rule_sets: Arc::new(RwLock::new(HashMap::new())),
            configs: Arc::new(RwLock::new(HashMap::new())),
            update_tasks: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// Load rule set from configuration
    pub async fn load_rule_set(&self, config: RuleSetConfig) -> Result<(), String> {
        let tag = config.tag.clone();
        
        // Store configuration
        self.configs.write().await.insert(tag.clone(), config.clone());
        
        // Compile rule set
        let compiled = self.compile_rule_set(&config).await?;
        
        // Store compiled rule set
        self.rule_sets.write().await.insert(tag.clone(), Arc::new(compiled));
        
        // Start update task for remote rule sets
        if config.r#type == RuleSetType::Remote {
            self.start_update_task(config).await;
        }
        
        Ok(())
    }
    
    /// Compile rule set from configuration
    async fn compile_rule_set(&self, config: &RuleSetConfig) -> Result<CompiledRuleSet, String> {
        let entries = match &config.r#type {
            RuleSetType::Local => {
                let path = config.path.as_ref()
                    .ok_or("Local rule set requires path")?;
                self.load_from_file(path, &config.format).await?
            },
            RuleSetType::Remote => {
                let url = config.url.as_ref()
                    .ok_or("Remote rule set requires URL")?;
                self.load_from_url(url, &config.format, config.download_detour.as_deref()).await?
            },
            RuleSetType::Inline => {
                config.rules.clone().unwrap_or_default()
            },
        };
        
        let source_hash = self.calculate_hash(&entries);
        
        Ok(CompiledRuleSet {
            tag: config.tag.clone(),
            entries,
            compiled_at: SystemTime::now(),
            source_hash: Some(source_hash),
        })
    }
    
    /// Load rule set from file
    async fn load_from_file(&self, path: &Path, format: &RuleSetFormat) -> Result<Vec<RuleSetEntry>, String> {
        let content = tokio::fs::read_to_string(path).await
            .map_err(|e| format!("Failed to read rule set file: {}", e))?;
        
        match format {
            RuleSetFormat::Source => {
                // Try JSON first, then YAML
                if let Ok(entries) = serde_json::from_str::<Vec<RuleSetEntry>>(&content) {
                    Ok(entries)
                } else {
                    // In a real implementation, would try YAML parsing here
                    Err("Failed to parse rule set file as JSON".to_string())
                }
            },
            RuleSetFormat::Binary => {
                // In a real implementation, would parse binary format
                Err("Binary rule set format not yet implemented".to_string())
            },
        }
    }
    
    /// Load rule set from URL
    async fn load_from_url(&self, url: &str, format: &RuleSetFormat, _detour: Option<&str>) -> Result<Vec<RuleSetEntry>, String> {
        // In a real implementation, would use HTTP client with optional detour
        let response = reqwest::get(url).await
            .map_err(|e| format!("Failed to download rule set: {}", e))?;
        
        let content = response.text().await
            .map_err(|e| format!("Failed to read rule set content: {}", e))?;
        
        match format {
            RuleSetFormat::Source => {
                serde_json::from_str::<Vec<RuleSetEntry>>(&content)
                    .map_err(|e| format!("Failed to parse rule set: {}", e))
            },
            RuleSetFormat::Binary => {
                Err("Binary rule set format not yet implemented".to_string())
            },
        }
    }
    
    /// Calculate hash of rule set entries
    fn calculate_hash(&self, entries: &[RuleSetEntry]) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        format!("{:?}", entries).hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }
    
    /// Start update task for remote rule set
    async fn start_update_task(&self, config: RuleSetConfig) {
        let interval = Duration::from_secs(config.update_interval.unwrap_or(3600)); // Default 1 hour
        let tag = config.tag.clone();
        let tag_for_task = tag.clone();
        let rule_sets = Arc::clone(&self.rule_sets);
        
        let task = tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);
            
            loop {
                interval_timer.tick().await;
                
                // Try to update rule set
                if let Ok(entries) = Self::load_from_url_static(&config.url.as_ref().unwrap(), &config.format, config.download_detour.as_deref()).await {
                    let compiled = CompiledRuleSet {
                        tag: tag_for_task.clone(),
                        entries,
                        compiled_at: SystemTime::now(),
                        source_hash: None,
                    };

                    rule_sets.write().await.insert(tag_for_task.clone(), Arc::new(compiled));
                    println!("Updated rule set: {}", tag_for_task);
                }
            }
        });
        
        self.update_tasks.write().await.insert(tag, task);
    }
    
    /// Static version of load_from_url for use in async task
    async fn load_from_url_static(url: &str, format: &RuleSetFormat, _detour: Option<&str>) -> Result<Vec<RuleSetEntry>, String> {
        let response = reqwest::get(url).await
            .map_err(|e| format!("Failed to download rule set: {}", e))?;
        
        let content = response.text().await
            .map_err(|e| format!("Failed to read rule set content: {}", e))?;
        
        match format {
            RuleSetFormat::Source => {
                serde_json::from_str::<Vec<RuleSetEntry>>(&content)
                    .map_err(|e| format!("Failed to parse rule set: {}", e))
            },
            RuleSetFormat::Binary => {
                Err("Binary rule set format not yet implemented".to_string())
            },
        }
    }
    
    /// Get rule set by tag
    pub async fn get_rule_set(&self, tag: &str) -> Option<Arc<CompiledRuleSet>> {
        self.rule_sets.read().await.get(tag).cloned()
    }
    
    /// Check if rule set matches context
    pub async fn matches(&self, tag: &str, ctx: &InboundContext) -> bool {
        if let Some(rule_set) = self.get_rule_set(tag).await {
            rule_set.matches(ctx)
        } else {
            false
        }
    }
    
    /// List all loaded rule sets
    pub async fn list_rule_sets(&self) -> Vec<String> {
        self.rule_sets.read().await.keys().cloned().collect()
    }
    
    /// Reload rule set
    pub async fn reload_rule_set(&self, tag: &str) -> Result<(), String> {
        let config = self.configs.read().await.get(tag).cloned()
            .ok_or_else(|| format!("Rule set not found: {}", tag))?;
        
        let compiled = self.compile_rule_set(&config).await?;
        self.rule_sets.write().await.insert(tag.to_string(), Arc::new(compiled));
        
        Ok(())
    }
    
    /// Remove rule set
    pub async fn remove_rule_set(&self, tag: &str) -> Result<(), String> {
        // Stop update task if exists
        if let Some(task) = self.update_tasks.write().await.remove(tag) {
            task.abort();
        }
        
        // Remove rule set and config
        self.rule_sets.write().await.remove(tag);
        self.configs.write().await.remove(tag);
        
        Ok(())
    }
}

impl Default for RuleSetManager {
    fn default() -> Self {
        Self::new()
    }
}

/// Rule set rule for routing
pub struct RuleSetRule {
    /// Rule name
    pub name: String,
    
    /// Target outbound
    pub outbound: String,
    
    /// Rule set tag
    pub rule_set_tag: String,
    
    /// Rule set manager
    pub manager: Arc<RuleSetManager>,
    
    /// Invert match result
    pub invert: bool,
}

impl RouteRule for RuleSetRule {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn outbound(&self) -> &str {
        &self.outbound
    }
    
    fn matches(&self, ctx: &InboundContext) -> bool {
        // This would need to be async in a real implementation
        // For now, return false as a placeholder
        false
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr};
    
    #[test]
    fn test_rule_set_entry_domain() {
        let entry = RuleSetEntry::Domain {
            value: "example.com".to_string(),
        };
        
        let ctx = InboundContext {
            inbound_tag: "test".to_string(),
            destination: Some(Destination::Domain("example.com".to_string(), 80)),
            ..Default::default()
        };
        
        assert!(entry.matches(&ctx));
        
        let ctx2 = InboundContext {
            inbound_tag: "test".to_string(),
            destination: Some(Destination::Domain("other.com".to_string(), 80)),
            ..Default::default()
        };
        
        assert!(!entry.matches(&ctx2));
    }
    
    #[test]
    fn test_rule_set_entry_port() {
        let entry = RuleSetEntry::Port { value: 80 };
        
        let ctx = InboundContext {
            inbound_tag: "test".to_string(),
            destination: Some(Destination::Domain("example.com".to_string(), 80)),
            ..Default::default()
        };
        
        assert!(entry.matches(&ctx));
        
        let ctx2 = InboundContext {
            inbound_tag: "test".to_string(),
            destination: Some(Destination::Domain("example.com".to_string(), 443)),
            ..Default::default()
        };
        
        assert!(!entry.matches(&ctx2));
    }
    
    #[tokio::test]
    async fn test_rule_set_manager() {
        let manager = RuleSetManager::new();
        
        let config = RuleSetConfig {
            tag: "test".to_string(),
            r#type: RuleSetType::Inline,
            format: RuleSetFormat::Source,
            path: None,
            url: None,
            download_detour: None,
            update_interval: None,
            rules: Some(vec![
                RuleSetEntry::Domain {
                    value: "example.com".to_string(),
                },
                RuleSetEntry::Port { value: 80 },
            ]),
        };
        
        assert!(manager.load_rule_set(config).await.is_ok());
        
        let rule_sets = manager.list_rule_sets().await;
        assert!(rule_sets.contains(&"test".to_string()));
        
        let rule_set = manager.get_rule_set("test").await.unwrap();
        assert_eq!(rule_set.entry_count(), 2);
    }
}
