/// GeoIP routing implementation
/// 
/// Provides IP geolocation-based routing rules

use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
use std::sync::Arc;
use serde::{Deserialize, Serialize};

/// IP range for CIDR matching
#[derive(Debug, Clone, PartialEq)]
pub struct IpRange {
    pub network: IpAddr,
    pub prefix_len: u8,
}

impl IpRange {
    /// Create a new IP range from CIDR notation
    pub fn from_cidr(cidr: &str) -> Result<Self, GeoIpError> {
        let parts: Vec<&str> = cidr.split('/').collect();
        if parts.len() != 2 {
            return Err(GeoIpError::InvalidCidr(cidr.to_string()));
        }

        let network: IpAddr = parts[0].parse()
            .map_err(|_| GeoIpError::InvalidIpAddress(parts[0].to_string()))?;
        let prefix_len: u8 = parts[1].parse()
            .map_err(|_| GeoIpError::InvalidPrefixLength(parts[1].to_string()))?;

        // Validate prefix length
        match network {
            IpAddr::V4(_) if prefix_len > 32 => {
                return Err(GeoIpError::InvalidPrefixLength(prefix_len.to_string()));
            }
            IpAddr::V6(_) if prefix_len > 128 => {
                return Err(GeoIpError::InvalidPrefixLength(prefix_len.to_string()));
            }
            _ => {}
        }

        Ok(Self { network, prefix_len })
    }

    /// Check if an IP address is within this range
    pub fn contains(&self, ip: &IpAddr) -> bool {
        match (self.network, ip) {
            (IpAddr::V4(net), IpAddr::V4(addr)) => {
                let net_bits = u32::from(net);
                let addr_bits = u32::from(*addr);
                let mask = !((1u32 << (32 - self.prefix_len)) - 1);
                (net_bits & mask) == (addr_bits & mask)
            }
            (IpAddr::V6(net), IpAddr::V6(addr)) => {
                let net_bits = u128::from(net);
                let addr_bits = u128::from(*addr);
                let mask = !((1u128 << (128 - self.prefix_len)) - 1);
                (net_bits & mask) == (addr_bits & mask)
            }
            _ => false, // Different IP versions
        }
    }
}

/// GeoIP database entry
#[derive(Debug, Clone)]
pub struct GeoIpEntry {
    pub country_code: String,
    pub country_name: String,
    pub ranges: Vec<IpRange>,
}

/// GeoIP database
#[derive(Debug, Clone)]
pub struct GeoIpDatabase {
    entries: HashMap<String, GeoIpEntry>,
    ip_to_country: Vec<(IpRange, String)>,
}

impl GeoIpDatabase {
    /// Create a new empty GeoIP database
    pub fn new() -> Self {
        Self {
            entries: HashMap::new(),
            ip_to_country: Vec::new(),
        }
    }

    /// Add a GeoIP entry
    pub fn add_entry(&mut self, entry: GeoIpEntry) {
        for range in &entry.ranges {
            self.ip_to_country.push((range.clone(), entry.country_code.clone()));
        }
        self.entries.insert(entry.country_code.clone(), entry);
    }

    /// Load GeoIP database from built-in data
    pub fn load_builtin() -> Self {
        let mut db = Self::new();
        
        // Add some common country IP ranges (simplified for demo)
        // In a real implementation, this would load from a comprehensive database
        
        // United States
        let us_ranges = vec![
            IpRange::from_cidr("*******/24").unwrap(),
            IpRange::from_cidr("*******/24").unwrap(),
        ];
        db.add_entry(GeoIpEntry {
            country_code: "US".to_string(),
            country_name: "United States".to_string(),
            ranges: us_ranges,
        });

        // China
        let cn_ranges = vec![
            IpRange::from_cidr("*************/24").unwrap(),
            IpRange::from_cidr("*********/24").unwrap(),
        ];
        db.add_entry(GeoIpEntry {
            country_code: "CN".to_string(),
            country_name: "China".to_string(),
            ranges: cn_ranges,
        });

        // Private networks
        let private_ranges = vec![
            IpRange::from_cidr("10.0.0.0/8").unwrap(),
            IpRange::from_cidr("**********/12").unwrap(),
            IpRange::from_cidr("***********/16").unwrap(),
            IpRange::from_cidr("*********/8").unwrap(),
        ];
        db.add_entry(GeoIpEntry {
            country_code: "PRIVATE".to_string(),
            country_name: "Private Network".to_string(),
            ranges: private_ranges,
        });

        db
    }

    /// Lookup country code for an IP address
    pub fn lookup(&self, ip: &IpAddr) -> Option<&str> {
        for (range, country_code) in &self.ip_to_country {
            if range.contains(ip) {
                return Some(country_code);
            }
        }
        None
    }

    /// Get all country codes
    pub fn get_countries(&self) -> Vec<&str> {
        self.entries.keys().map(|s| s.as_str()).collect()
    }

    /// Get entry for a country code
    pub fn get_entry(&self, country_code: &str) -> Option<&GeoIpEntry> {
        self.entries.get(country_code)
    }

    /// Check if IP matches any of the specified countries
    pub fn matches_countries(&self, ip: &IpAddr, countries: &[String]) -> bool {
        if let Some(country) = self.lookup(ip) {
            countries.iter().any(|c| c == country)
        } else {
            false
        }
    }
}

impl Default for GeoIpDatabase {
    fn default() -> Self {
        Self::load_builtin()
    }
}

/// GeoIP rule for routing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeoIpRule {
    /// Country codes to match
    pub countries: Vec<String>,
    /// Whether to invert the match
    pub invert: bool,
}

impl GeoIpRule {
    /// Create a new GeoIP rule
    pub fn new(countries: Vec<String>) -> Self {
        Self {
            countries,
            invert: false,
        }
    }

    /// Create a new inverted GeoIP rule
    pub fn new_inverted(countries: Vec<String>) -> Self {
        Self {
            countries,
            invert: true,
        }
    }

    /// Check if an IP matches this rule
    pub fn matches(&self, ip: &IpAddr, database: &GeoIpDatabase) -> bool {
        let matches = database.matches_countries(ip, &self.countries);
        if self.invert { !matches } else { matches }
    }
}

/// GeoIP manager
pub struct GeoIpManager {
    database: Arc<GeoIpDatabase>,
}

impl GeoIpManager {
    /// Create a new GeoIP manager
    pub fn new() -> Self {
        Self {
            database: Arc::new(GeoIpDatabase::load_builtin()),
        }
    }

    /// Create with custom database
    pub fn with_database(database: GeoIpDatabase) -> Self {
        Self {
            database: Arc::new(database),
        }
    }

    /// Lookup country for IP
    pub fn lookup_country(&self, ip: &IpAddr) -> Option<String> {
        self.database.lookup(ip).map(|s| s.to_string())
    }

    /// Check if IP matches rule
    pub fn matches_rule(&self, ip: &IpAddr, rule: &GeoIpRule) -> bool {
        rule.matches(ip, &self.database)
    }

    /// Get database reference
    pub fn database(&self) -> &GeoIpDatabase {
        &self.database
    }

    /// Update database
    pub fn update_database(&mut self, database: GeoIpDatabase) {
        self.database = Arc::new(database);
    }
}

impl Default for GeoIpManager {
    fn default() -> Self {
        Self::new()
    }
}

/// GeoIP errors
#[derive(Debug, Clone)]
pub enum GeoIpError {
    InvalidCidr(String),
    InvalidIpAddress(String),
    InvalidPrefixLength(String),
    DatabaseError(String),
}

impl std::fmt::Display for GeoIpError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            GeoIpError::InvalidCidr(cidr) => write!(f, "Invalid CIDR: {}", cidr),
            GeoIpError::InvalidIpAddress(ip) => write!(f, "Invalid IP address: {}", ip),
            GeoIpError::InvalidPrefixLength(len) => write!(f, "Invalid prefix length: {}", len),
            GeoIpError::DatabaseError(msg) => write!(f, "Database error: {}", msg),
        }
    }
}

impl std::error::Error for GeoIpError {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ip_range_from_cidr() {
        let range = IpRange::from_cidr("***********/24").unwrap();
        assert_eq!(range.network, IpAddr::V4(Ipv4Addr::new(192, 168, 1, 0)));
        assert_eq!(range.prefix_len, 24);
    }

    #[test]
    fn test_ip_range_contains() {
        let range = IpRange::from_cidr("***********/24").unwrap();
        
        assert!(range.contains(&IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1))));
        assert!(range.contains(&IpAddr::V4(Ipv4Addr::new(192, 168, 1, 255))));
        assert!(!range.contains(&IpAddr::V4(Ipv4Addr::new(192, 168, 2, 1))));
    }

    #[test]
    fn test_geoip_database() {
        let db = GeoIpDatabase::load_builtin();
        
        // Test private IP
        let private_ip = IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1));
        assert_eq!(db.lookup(&private_ip), Some("PRIVATE"));
        
        // Test public IP (Google DNS)
        let google_ip = IpAddr::V4(Ipv4Addr::new(8, 8, 8, 8));
        assert_eq!(db.lookup(&google_ip), Some("US"));
    }

    #[test]
    fn test_geoip_rule() {
        let rule = GeoIpRule::new(vec!["US".to_string(), "CN".to_string()]);
        let db = GeoIpDatabase::load_builtin();
        
        let us_ip = IpAddr::V4(Ipv4Addr::new(8, 8, 8, 8));
        assert!(rule.matches(&us_ip, &db));
        
        let private_ip = IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1));
        assert!(!rule.matches(&private_ip, &db));
    }

    #[test]
    fn test_inverted_geoip_rule() {
        let rule = GeoIpRule::new_inverted(vec!["US".to_string()]);
        let db = GeoIpDatabase::load_builtin();
        
        let us_ip = IpAddr::V4(Ipv4Addr::new(8, 8, 8, 8));
        assert!(!rule.matches(&us_ip, &db));
        
        let private_ip = IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1));
        assert!(rule.matches(&private_ip, &db));
    }
}
