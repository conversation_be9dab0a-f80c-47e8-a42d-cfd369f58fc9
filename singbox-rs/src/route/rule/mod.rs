//! Route rule module
//!
//! This module provides various types of routing rules for traffic matching
//! and routing decisions.

pub mod advanced;

use crate::adapter::InboundContext;

/// Route rule trait
pub trait RouteRule: Send + Sync {
    /// Get rule name
    fn name(&self) -> &str;
    
    /// Get target outbound
    fn outbound(&self) -> &str;
    
    /// Check if the rule matches the given context
    fn matches(&self, ctx: &InboundContext) -> bool;
}

/// Rule condition trait for composable conditions
pub trait RuleCondition: Send + Sync {
    /// Evaluate the condition against the given context
    fn evaluate(&self, ctx: &InboundContext) -> bool;
}

/// Destination type for routing
#[derive(Debug, Clone, PartialEq)]
pub enum Destination {
    /// Domain name and port
    Domain(String, u16),
    
    /// IP address and port
    Ip(std::net::IpAddr, u16),
}

impl Destination {
    /// Get the port number
    pub fn port(&self) -> u16 {
        match self {
            Destination::Domain(_, port) => *port,
            Destination::Ip(_, port) => *port,
        }
    }
    
    /// Get domain name if this is a domain destination
    pub fn domain(&self) -> Option<&str> {
        match self {
            Destination::Domain(domain, _) => Some(domain),
            Destination::Ip(_, _) => None,
        }
    }
    
    /// Get IP address if this is an IP destination
    pub fn ip(&self) -> Option<&std::net::IpAddr> {
        match self {
            Destination::Domain(_, _) => None,
            Destination::Ip(ip, _) => Some(ip),
        }
    }
}

/// Rule builder for creating complex routing rules
pub struct RuleBuilder {
    name: String,
    outbound: String,
    conditions: Vec<Box<dyn RuleCondition>>,
    invert: bool,
}

impl RuleBuilder {
    /// Create a new rule builder
    pub fn new(name: String, outbound: String) -> Self {
        Self {
            name,
            outbound,
            conditions: Vec::new(),
            invert: false,
        }
    }
    
    /// Add a condition to the rule
    pub fn add_condition(mut self, condition: Box<dyn RuleCondition>) -> Self {
        self.conditions.push(condition);
        self
    }
    
    /// Set invert flag
    pub fn invert(mut self, invert: bool) -> Self {
        self.invert = invert;
        self
    }
    
    /// Build the composite rule
    pub fn build(self) -> CompositeRule {
        CompositeRule {
            name: self.name,
            outbound: self.outbound,
            conditions: self.conditions,
            invert: self.invert,
        }
    }
}

/// Composite rule that combines multiple conditions
pub struct CompositeRule {
    name: String,
    outbound: String,
    conditions: Vec<Box<dyn RuleCondition>>,
    invert: bool,
}

impl RouteRule for CompositeRule {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn outbound(&self) -> &str {
        &self.outbound
    }
    
    fn matches(&self, ctx: &InboundContext) -> bool {
        // All conditions must match (AND logic)
        let result = self.conditions.iter().all(|condition| condition.evaluate(ctx));
        
        if self.invert {
            !result
        } else {
            result
        }
    }
}

/// Domain condition for matching domain names
pub struct DomainCondition {
    domains: std::collections::HashSet<String>,
}

impl DomainCondition {
    /// Create a new domain condition
    pub fn new() -> Self {
        Self {
            domains: std::collections::HashSet::new(),
        }
    }
    
    /// Add a domain to match
    pub fn add_domain(&mut self, domain: String) {
        self.domains.insert(domain.to_lowercase());
    }
}

impl RuleCondition for DomainCondition {
    fn evaluate(&self, ctx: &InboundContext) -> bool {
        if let Some(ref dest) = ctx.destination {
            if let Some(domain) = dest.domain() {
                return self.domains.contains(&domain.to_lowercase());
            }
        }
        false
    }
}

/// IP condition for matching IP addresses
pub struct IpCondition {
    ips: std::collections::HashSet<std::net::IpAddr>,
}

impl IpCondition {
    /// Create a new IP condition
    pub fn new() -> Self {
        Self {
            ips: std::collections::HashSet::new(),
        }
    }
    
    /// Add an IP to match
    pub fn add_ip(&mut self, ip: std::net::IpAddr) {
        self.ips.insert(ip);
    }
}

impl RuleCondition for IpCondition {
    fn evaluate(&self, ctx: &InboundContext) -> bool {
        if let Some(ref dest) = ctx.destination {
            if let Some(ip) = dest.ip() {
                return self.ips.contains(ip);
            }
        }
        false
    }
}

/// Port condition for matching port numbers
pub struct PortCondition {
    ports: std::collections::HashSet<u16>,
}

impl PortCondition {
    /// Create a new port condition
    pub fn new() -> Self {
        Self {
            ports: std::collections::HashSet::new(),
        }
    }
    
    /// Add a port to match
    pub fn add_port(&mut self, port: u16) {
        self.ports.insert(port);
    }
}

impl RuleCondition for PortCondition {
    fn evaluate(&self, ctx: &InboundContext) -> bool {
        if let Some(ref dest) = ctx.destination {
            return self.ports.contains(&dest.port());
        }
        false
    }
}

/// Inbound condition for matching inbound tags
pub struct InboundCondition {
    inbounds: std::collections::HashSet<String>,
}

impl InboundCondition {
    /// Create a new inbound condition
    pub fn new() -> Self {
        Self {
            inbounds: std::collections::HashSet::new(),
        }
    }
    
    /// Add an inbound tag to match
    pub fn add_inbound(&mut self, inbound: String) {
        self.inbounds.insert(inbound);
    }
}

impl RuleCondition for InboundCondition {
    fn evaluate(&self, ctx: &InboundContext) -> bool {
        self.inbounds.contains(&ctx.inbound_tag)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr};
    
    #[test]
    fn test_destination() {
        let domain_dest = Destination::Domain("example.com".to_string(), 80);
        assert_eq!(domain_dest.port(), 80);
        assert_eq!(domain_dest.domain(), Some("example.com"));
        assert!(domain_dest.ip().is_none());
        
        let ip_dest = Destination::Ip(IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1)), 443);
        assert_eq!(ip_dest.port(), 443);
        assert!(ip_dest.domain().is_none());
        assert_eq!(ip_dest.ip(), Some(&IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1))));
    }
    
    #[test]
    fn test_domain_condition() {
        let mut condition = DomainCondition::new();
        condition.add_domain("example.com".to_string());
        condition.add_domain("test.org".to_string());
        
        let mut ctx = InboundContext {
            inbound_tag: "test".to_string(),
            destination: Some(Destination::Domain("example.com".to_string(), 80)),
            ..Default::default()
        };
        
        assert!(condition.evaluate(&ctx));
        
        ctx.destination = Some(Destination::Domain("other.com".to_string(), 80));
        assert!(!condition.evaluate(&ctx));
    }
    
    #[test]
    fn test_composite_rule() {
        let mut domain_condition = DomainCondition::new();
        domain_condition.add_domain("example.com".to_string());
        
        let mut port_condition = PortCondition::new();
        port_condition.add_port(80);
        
        let rule = RuleBuilder::new("test".to_string(), "proxy".to_string())
            .add_condition(Box::new(domain_condition))
            .add_condition(Box::new(port_condition))
            .build();
        
        let ctx = InboundContext {
            inbound_tag: "test".to_string(),
            destination: Some(Destination::Domain("example.com".to_string(), 80)),
            ..Default::default()
        };
        
        assert!(rule.matches(&ctx));
        assert_eq!(rule.name(), "test");
        assert_eq!(rule.outbound(), "proxy");
    }
    
    #[test]
    fn test_composite_rule_invert() {
        let mut domain_condition = DomainCondition::new();
        domain_condition.add_domain("example.com".to_string());
        
        let rule = RuleBuilder::new("test".to_string(), "proxy".to_string())
            .add_condition(Box::new(domain_condition))
            .invert(true)
            .build();
        
        let ctx = InboundContext {
            inbound_tag: "test".to_string(),
            destination: Some(Destination::Domain("example.com".to_string(), 80)),
            ..Default::default()
        };
        
        // Should not match because of invert
        assert!(!rule.matches(&ctx));
        
        let ctx2 = InboundContext {
            inbound_tag: "test".to_string(),
            destination: Some(Destination::Domain("other.com".to_string(), 80)),
            ..Default::default()
        };
        
        // Should match because of invert
        assert!(rule.matches(&ctx2));
    }
}
