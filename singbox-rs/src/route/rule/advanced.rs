//! Advanced routing rules implementation
//!
//! This module implements advanced routing rules including domain keyword matching,
//! regex matching, protocol sniffing, GeoIP, GeoSite, and other sophisticated routing conditions.

use std::collections::{HashMap, HashSet};
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::RwLock;
use regex::Regex;
use serde::{Deserialize, Serialize};

use crate::adapter::InboundContext;
use crate::route::rule::{RouteRule, RuleCondition, Destination};

/// GeoIP rule for country-based routing
#[derive(Debug, Clone)]
pub struct GeoIpRule {
    /// Rule name
    pub name: String,

    /// Target outbound
    pub outbound: String,

    /// Country codes to match
    pub country_codes: Vec<String>,

    /// Whether to invert the match
    pub invert: bool,

    /// GeoIP database
    pub database: Arc<RwLock<HashMap<String, Vec<IpRange>>>>,

    /// Custom IP ranges
    pub custom_ranges: Vec<IpRange>,
}

/// GeoSite rule for site category-based routing
#[derive(Debug, Clone)]
pub struct GeoSiteRule {
    /// Rule name
    pub name: String,

    /// Target outbound
    pub outbound: String,

    /// Site categories to match
    pub categories: Vec<String>,

    /// Whether to invert the match
    pub invert: bool,

    /// GeoSite database
    pub database: Arc<RwLock<HashMap<String, Vec<DomainPattern>>>>,

    /// Custom domain patterns
    pub custom_patterns: Vec<DomainPattern>,
}

/// IP range specification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpRange {
    /// Start IP address
    pub start: IpAddr,

    /// End IP address
    pub end: IpAddr,
}

/// Domain pattern types for GeoSite
#[derive(Debug, Clone)]
pub enum DomainPattern {
    /// Exact domain match
    Exact(String),

    /// Domain suffix match
    Suffix(String),

    /// Domain keyword match
    Keyword(String),

    /// Regular expression match
    Regex(Regex),
}

/// Process-based routing rule
#[derive(Debug, Clone)]
pub struct ProcessRule {
    /// Rule name
    pub name: String,

    /// Target outbound
    pub outbound: String,

    /// Process names to match
    pub process_names: Vec<String>,

    /// Process paths to match
    pub process_paths: Vec<String>,

    /// Process path patterns (regex)
    pub process_path_patterns: Vec<Regex>,

    /// Whether to invert the match
    pub invert: bool,

    /// Case sensitive matching
    pub case_sensitive: bool,
}

/// User-based routing rule
#[derive(Debug, Clone)]
pub struct UserRule {
    /// Rule name
    pub name: String,

    /// Target outbound
    pub outbound: String,

    /// User IDs to match
    pub user_ids: Vec<u32>,

    /// User names to match
    pub user_names: Vec<String>,

    /// Whether to invert the match
    pub invert: bool,
}

/// Time-based routing rule
#[derive(Debug, Clone)]
pub struct TimeRule {
    /// Rule name
    pub name: String,

    /// Target outbound
    pub outbound: String,

    /// Start time (HH:MM format)
    pub start_time: String,

    /// End time (HH:MM format)
    pub end_time: String,

    /// Days of week (0=Sunday, 1=Monday, etc.)
    pub days_of_week: Vec<u8>,

    /// Time zone
    pub timezone: Option<String>,

    /// Whether to invert the match
    pub invert: bool,
}

/// Advanced domain matching rule
#[derive(Debug, Clone)]
pub struct AdvancedDomainRule {
    /// Rule name
    pub name: String,
    
    /// Target outbound
    pub outbound: String,
    
    /// Exact domain matches
    pub domains: HashSet<String>,
    
    /// Domain suffix matches
    pub domain_suffixes: Vec<String>,
    
    /// Domain keyword matches
    pub domain_keywords: Vec<String>,
    
    /// Domain regex patterns
    pub domain_regexes: Vec<Regex>,
    
    /// Invert match result
    pub invert: bool,
}

impl GeoIpRule {
    /// Create a new GeoIP rule
    pub fn new(name: String, outbound: String, country_codes: Vec<String>) -> Self {
        Self {
            name,
            outbound,
            country_codes,
            invert: false,
            database: Arc::new(RwLock::new(HashMap::new())),
            custom_ranges: Vec::new(),
        }
    }

    /// Load GeoIP database from file
    pub async fn load_database(&self, database_path: &str) -> Result<(), String> {
        // In a real implementation, this would load from MaxMind GeoIP database
        // For now, we'll create some sample data
        let mut db = self.database.write().await;

        // Sample data for common countries
        db.insert("CN".to_string(), vec![
            IpRange {
                start: "*******".parse().unwrap(),
                end: "*********".parse().unwrap(),
            },
            IpRange {
                start: "1*******".parse().unwrap(),
                end: "1*************".parse().unwrap(),
            },
        ]);

        db.insert("US".to_string(), vec![
            IpRange {
                start: "*******".parse().unwrap(),
                end: "*********".parse().unwrap(),
            },
            IpRange {
                start: "*******".parse().unwrap(),
                end: "*************".parse().unwrap(),
            },
        ]);

        db.insert("JP".to_string(), vec![
            IpRange {
                start: "*********".parse().unwrap(),
                end: "***************".parse().unwrap(),
            },
        ]);

        println!("GeoIP database loaded from {}", database_path);
        Ok(())
    }

    /// Check if IP matches any country in the rule
    pub async fn matches_ip(&self, ip: &IpAddr) -> bool {
        let db = self.database.read().await;

        // Check country codes
        for country_code in &self.country_codes {
            if let Some(ranges) = db.get(country_code) {
                for range in ranges {
                    if self.ip_in_range(ip, range) {
                        return !self.invert;
                    }
                }
            }
        }

        // Check custom ranges
        for range in &self.custom_ranges {
            if self.ip_in_range(ip, range) {
                return !self.invert;
            }
        }

        self.invert
    }

    /// Check if IP is in range
    fn ip_in_range(&self, ip: &IpAddr, range: &IpRange) -> bool {
        match (ip, &range.start, &range.end) {
            (IpAddr::V4(ip), IpAddr::V4(start), IpAddr::V4(end)) => {
                let ip_u32 = u32::from(*ip);
                let start_u32 = u32::from(*start);
                let end_u32 = u32::from(*end);
                ip_u32 >= start_u32 && ip_u32 <= end_u32
            },
            (IpAddr::V6(ip), IpAddr::V6(start), IpAddr::V6(end)) => {
                let ip_u128 = u128::from(*ip);
                let start_u128 = u128::from(*start);
                let end_u128 = u128::from(*end);
                ip_u128 >= start_u128 && ip_u128 <= end_u128
            },
            _ => false,
        }
    }

    /// Add custom IP range
    pub fn add_custom_range(&mut self, start: IpAddr, end: IpAddr) {
        self.custom_ranges.push(IpRange { start, end });
    }
}

impl GeoSiteRule {
    /// Create a new GeoSite rule
    pub fn new(name: String, outbound: String, categories: Vec<String>) -> Self {
        Self {
            name,
            outbound,
            categories,
            invert: false,
            database: Arc::new(RwLock::new(HashMap::new())),
            custom_patterns: Vec::new(),
        }
    }

    /// Load GeoSite database from file
    pub async fn load_database(&self, database_path: &str) -> Result<(), String> {
        // In a real implementation, this would load from v2ray geosite database
        // For now, we'll create some sample data
        let mut db = self.database.write().await;

        // Sample data for common site categories
        db.insert("google".to_string(), vec![
            DomainPattern::Suffix("google.com".to_string()),
            DomainPattern::Suffix("googleapis.com".to_string()),
            DomainPattern::Suffix("googleusercontent.com".to_string()),
            DomainPattern::Suffix("googlevideo.com".to_string()),
            DomainPattern::Suffix("gstatic.com".to_string()),
        ]);

        db.insert("facebook".to_string(), vec![
            DomainPattern::Suffix("facebook.com".to_string()),
            DomainPattern::Suffix("fbcdn.net".to_string()),
            DomainPattern::Suffix("instagram.com".to_string()),
            DomainPattern::Suffix("whatsapp.com".to_string()),
        ]);

        db.insert("twitter".to_string(), vec![
            DomainPattern::Suffix("twitter.com".to_string()),
            DomainPattern::Suffix("twimg.com".to_string()),
            DomainPattern::Suffix("t.co".to_string()),
        ]);

        db.insert("netflix".to_string(), vec![
            DomainPattern::Suffix("netflix.com".to_string()),
            DomainPattern::Suffix("nflximg.net".to_string()),
            DomainPattern::Suffix("nflxvideo.net".to_string()),
        ]);

        db.insert("youtube".to_string(), vec![
            DomainPattern::Suffix("youtube.com".to_string()),
            DomainPattern::Suffix("youtu.be".to_string()),
            DomainPattern::Suffix("ytimg.com".to_string()),
        ]);

        println!("GeoSite database loaded from {}", database_path);
        Ok(())
    }

    /// Check if domain matches any category in the rule
    pub async fn matches_domain(&self, domain: &str) -> bool {
        let db = self.database.read().await;

        // Check categories
        for category in &self.categories {
            if let Some(patterns) = db.get(category) {
                for pattern in patterns {
                    if self.domain_matches_pattern(domain, pattern) {
                        return !self.invert;
                    }
                }
            }
        }

        // Check custom patterns
        for pattern in &self.custom_patterns {
            if self.domain_matches_pattern(domain, pattern) {
                return !self.invert;
            }
        }

        self.invert
    }

    /// Check if domain matches pattern
    fn domain_matches_pattern(&self, domain: &str, pattern: &DomainPattern) -> bool {
        match pattern {
            DomainPattern::Exact(exact) => domain.eq_ignore_ascii_case(exact),
            DomainPattern::Suffix(suffix) => {
                domain.eq_ignore_ascii_case(suffix) ||
                domain.to_lowercase().ends_with(&format!(".{}", suffix.to_lowercase()))
            },
            DomainPattern::Keyword(keyword) => {
                domain.to_lowercase().contains(&keyword.to_lowercase())
            },
            DomainPattern::Regex(regex) => regex.is_match(domain),
        }
    }

    /// Add custom domain pattern
    pub fn add_custom_pattern(&mut self, pattern: DomainPattern) {
        self.custom_patterns.push(pattern);
    }
}

impl AdvancedDomainRule {
    /// Create a new advanced domain rule
    pub fn new(name: String, outbound: String) -> Self {
        Self {
            name,
            outbound,
            domains: HashSet::new(),
            domain_suffixes: Vec::new(),
            domain_keywords: Vec::new(),
            domain_regexes: Vec::new(),
            invert: false,
        }
    }
    
    /// Add exact domain match
    pub fn add_domain(&mut self, domain: String) {
        self.domains.insert(domain.to_lowercase());
    }
    
    /// Add domain suffix match
    pub fn add_domain_suffix(&mut self, suffix: String) {
        self.domain_suffixes.push(suffix.to_lowercase());
    }
    
    /// Add domain keyword match
    pub fn add_domain_keyword(&mut self, keyword: String) {
        self.domain_keywords.push(keyword.to_lowercase());
    }
    
    /// Add domain regex pattern
    pub fn add_domain_regex(&mut self, pattern: &str) -> Result<(), String> {
        let regex = Regex::new(pattern)
            .map_err(|e| format!("Invalid regex pattern '{}': {}", pattern, e))?;
        self.domain_regexes.push(regex);
        Ok(())
    }
    
    /// Set invert flag
    pub fn set_invert(&mut self, invert: bool) {
        self.invert = invert;
    }
    
    /// Check if domain matches any condition
    fn matches_domain(&self, domain: &str) -> bool {
        let domain_lower = domain.to_lowercase();
        
        // Exact domain match
        if self.domains.contains(&domain_lower) {
            return true;
        }
        
        // Domain suffix match
        for suffix in &self.domain_suffixes {
            if domain_lower.ends_with(suffix) {
                return true;
            }
        }
        
        // Domain keyword match
        for keyword in &self.domain_keywords {
            if domain_lower.contains(keyword) {
                return true;
            }
        }
        
        // Domain regex match
        for regex in &self.domain_regexes {
            if regex.is_match(&domain_lower) {
                return true;
            }
        }
        
        false
    }
}

impl RouteRule for AdvancedDomainRule {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn outbound(&self) -> &str {
        &self.outbound
    }
    
    fn matches(&self, ctx: &InboundContext) -> bool {
        let domain = match &ctx.destination {
            Some(dest) => match dest {
                Destination::Domain(domain, _) => domain,
                _ => return false,
            },
            None => return false,
        };
        
        let matches = self.matches_domain(domain);
        if self.invert { !matches } else { matches }
    }
}

/// IP range matching rule
#[derive(Debug, Clone)]
pub struct IpRangeRule {
    /// Rule name
    pub name: String,
    
    /// Target outbound
    pub outbound: String,
    
    /// IPv4 ranges
    pub ipv4_ranges: Vec<(Ipv4Addr, u8)>, // (network, prefix_len)
    
    /// IPv6 ranges
    pub ipv6_ranges: Vec<(Ipv6Addr, u8)>, // (network, prefix_len)
    
    /// Invert match result
    pub invert: bool,
}

impl IpRangeRule {
    /// Create a new IP range rule
    pub fn new(name: String, outbound: String) -> Self {
        Self {
            name,
            outbound,
            ipv4_ranges: Vec::new(),
            ipv6_ranges: Vec::new(),
            invert: false,
        }
    }
    
    /// Add IPv4 CIDR range
    pub fn add_ipv4_cidr(&mut self, cidr: &str) -> Result<(), String> {
        let parts: Vec<&str> = cidr.split('/').collect();
        if parts.len() != 2 {
            return Err(format!("Invalid IPv4 CIDR format: {}", cidr));
        }
        
        let ip: Ipv4Addr = parts[0].parse()
            .map_err(|e| format!("Invalid IPv4 address '{}': {}", parts[0], e))?;
        let prefix_len: u8 = parts[1].parse()
            .map_err(|e| format!("Invalid prefix length '{}': {}", parts[1], e))?;
        
        if prefix_len > 32 {
            return Err(format!("IPv4 prefix length cannot exceed 32: {}", prefix_len));
        }
        
        self.ipv4_ranges.push((ip, prefix_len));
        Ok(())
    }
    
    /// Add IPv6 CIDR range
    pub fn add_ipv6_cidr(&mut self, cidr: &str) -> Result<(), String> {
        let parts: Vec<&str> = cidr.split('/').collect();
        if parts.len() != 2 {
            return Err(format!("Invalid IPv6 CIDR format: {}", cidr));
        }
        
        let ip: Ipv6Addr = parts[0].parse()
            .map_err(|e| format!("Invalid IPv6 address '{}': {}", parts[0], e))?;
        let prefix_len: u8 = parts[1].parse()
            .map_err(|e| format!("Invalid prefix length '{}': {}", parts[1], e))?;
        
        if prefix_len > 128 {
            return Err(format!("IPv6 prefix length cannot exceed 128: {}", prefix_len));
        }
        
        self.ipv6_ranges.push((ip, prefix_len));
        Ok(())
    }
    
    /// Check if IP is in any range
    fn matches_ip(&self, ip: &IpAddr) -> bool {
        match ip {
            IpAddr::V4(ipv4) => {
                for (network, prefix_len) in &self.ipv4_ranges {
                    if self.ipv4_in_range(*ipv4, *network, *prefix_len) {
                        return true;
                    }
                }
            },
            IpAddr::V6(ipv6) => {
                for (network, prefix_len) in &self.ipv6_ranges {
                    if self.ipv6_in_range(*ipv6, *network, *prefix_len) {
                        return true;
                    }
                }
            },
        }
        false
    }
    
    /// Check if IPv4 address is in range
    fn ipv4_in_range(&self, ip: Ipv4Addr, network: Ipv4Addr, prefix_len: u8) -> bool {
        if prefix_len == 0 {
            return true;
        }
        
        let mask = !((1u32 << (32 - prefix_len)) - 1);
        let ip_int = u32::from(ip);
        let network_int = u32::from(network);
        
        (ip_int & mask) == (network_int & mask)
    }
    
    /// Check if IPv6 address is in range
    fn ipv6_in_range(&self, ip: Ipv6Addr, network: Ipv6Addr, prefix_len: u8) -> bool {
        if prefix_len == 0 {
            return true;
        }
        
        let ip_bytes = ip.octets();
        let network_bytes = network.octets();
        
        let full_bytes = (prefix_len / 8) as usize;
        let remaining_bits = prefix_len % 8;
        
        // Check full bytes
        if ip_bytes[..full_bytes] != network_bytes[..full_bytes] {
            return false;
        }
        
        // Check remaining bits
        if remaining_bits > 0 && full_bytes < 16 {
            let mask = 0xFF << (8 - remaining_bits);
            if (ip_bytes[full_bytes] & mask) != (network_bytes[full_bytes] & mask) {
                return false;
            }
        }
        
        true
    }
}

impl RouteRule for IpRangeRule {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn outbound(&self) -> &str {
        &self.outbound
    }
    
    fn matches(&self, ctx: &InboundContext) -> bool {
        let ip = match &ctx.destination {
            Some(dest) => match dest {
                Destination::Ip(ip, _) => ip,
                _ => return false,
            },
            None => return false,
        };
        
        let matches = self.matches_ip(ip);
        if self.invert { !matches } else { matches }
    }
}

/// Port range matching rule
#[derive(Debug, Clone)]
pub struct PortRangeRule {
    /// Rule name
    pub name: String,
    
    /// Target outbound
    pub outbound: String,
    
    /// Port ranges (start, end) - inclusive
    pub port_ranges: Vec<(u16, u16)>,
    
    /// Invert match result
    pub invert: bool,
}

impl PortRangeRule {
    /// Create a new port range rule
    pub fn new(name: String, outbound: String) -> Self {
        Self {
            name,
            outbound,
            port_ranges: Vec::new(),
            invert: false,
        }
    }
    
    /// Add single port
    pub fn add_port(&mut self, port: u16) {
        self.port_ranges.push((port, port));
    }
    
    /// Add port range
    pub fn add_port_range(&mut self, start: u16, end: u16) -> Result<(), String> {
        if start > end {
            return Err(format!("Invalid port range: {} > {}", start, end));
        }
        self.port_ranges.push((start, end));
        Ok(())
    }
    
    /// Parse port range string (e.g., "80", "80-90", "80,443,8080-8090")
    pub fn add_port_string(&mut self, port_str: &str) -> Result<(), String> {
        for part in port_str.split(',') {
            let part = part.trim();
            if part.contains('-') {
                let range_parts: Vec<&str> = part.split('-').collect();
                if range_parts.len() != 2 {
                    return Err(format!("Invalid port range format: {}", part));
                }
                
                let start: u16 = range_parts[0].parse()
                    .map_err(|e| format!("Invalid port number '{}': {}", range_parts[0], e))?;
                let end: u16 = range_parts[1].parse()
                    .map_err(|e| format!("Invalid port number '{}': {}", range_parts[1], e))?;
                
                self.add_port_range(start, end)?;
            } else {
                let port: u16 = part.parse()
                    .map_err(|e| format!("Invalid port number '{}': {}", part, e))?;
                self.add_port(port);
            }
        }
        Ok(())
    }
    
    /// Check if port is in any range
    fn matches_port(&self, port: u16) -> bool {
        for (start, end) in &self.port_ranges {
            if port >= *start && port <= *end {
                return true;
            }
        }
        false
    }
}

impl RouteRule for PortRangeRule {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn outbound(&self) -> &str {
        &self.outbound
    }
    
    fn matches(&self, ctx: &InboundContext) -> bool {
        let port = match &ctx.destination {
            Some(dest) => match dest {
                Destination::Domain(_, port) => *port,
                Destination::Ip(_, port) => *port,
            },
            None => return false,
        };
        
        let matches = self.matches_port(port);
        if self.invert { !matches } else { matches }
    }
}

/// Protocol sniffing rule
#[derive(Debug, Clone)]
pub struct ProtocolSniffRule {
    /// Rule name
    pub name: String,

    /// Target outbound
    pub outbound: String,

    /// Protocols to match
    pub protocols: HashSet<String>,

    /// Invert match result
    pub invert: bool,
}

impl ProtocolSniffRule {
    /// Create a new protocol sniff rule
    pub fn new(name: String, outbound: String) -> Self {
        Self {
            name,
            outbound,
            protocols: HashSet::new(),
            invert: false,
        }
    }

    /// Add protocol to match
    pub fn add_protocol(&mut self, protocol: String) {
        self.protocols.insert(protocol.to_lowercase());
    }

    /// Sniff protocol from connection data
    fn sniff_protocol(&self, data: &[u8]) -> Option<String> {
        if data.is_empty() {
            return None;
        }

        // HTTP detection
        if self.is_http_request(data) {
            return Some("http".to_string());
        }

        // HTTPS/TLS detection
        if self.is_tls_handshake(data) {
            return Some("tls".to_string());
        }

        // SSH detection
        if self.is_ssh_handshake(data) {
            return Some("ssh".to_string());
        }

        // FTP detection
        if self.is_ftp_response(data) {
            return Some("ftp".to_string());
        }

        // SMTP detection
        if self.is_smtp_response(data) {
            return Some("smtp".to_string());
        }

        // POP3 detection
        if self.is_pop3_response(data) {
            return Some("pop3".to_string());
        }

        // IMAP detection
        if self.is_imap_response(data) {
            return Some("imap".to_string());
        }

        None
    }

    /// Check if data looks like HTTP request
    fn is_http_request(&self, data: &[u8]) -> bool {
        let text = match std::str::from_utf8(data) {
            Ok(text) => text,
            Err(_) => return false,
        };

        text.starts_with("GET ") ||
        text.starts_with("POST ") ||
        text.starts_with("PUT ") ||
        text.starts_with("DELETE ") ||
        text.starts_with("HEAD ") ||
        text.starts_with("OPTIONS ") ||
        text.starts_with("PATCH ") ||
        text.starts_with("CONNECT ") ||
        text.starts_with("TRACE ")
    }

    /// Check if data looks like TLS handshake
    fn is_tls_handshake(&self, data: &[u8]) -> bool {
        if data.len() < 6 {
            return false;
        }

        // TLS record header: type(1) + version(2) + length(2)
        // Client Hello: type = 0x16, version = 0x0301/0x0302/0x0303
        data[0] == 0x16 && // Handshake record
        data[1] == 0x03 && // TLS version major
        (data[2] == 0x01 || data[2] == 0x02 || data[2] == 0x03 || data[2] == 0x04) // TLS version minor
    }

    /// Check if data looks like SSH handshake
    fn is_ssh_handshake(&self, data: &[u8]) -> bool {
        let text = match std::str::from_utf8(data) {
            Ok(text) => text,
            Err(_) => return false,
        };

        text.starts_with("SSH-2.0-") || text.starts_with("SSH-1.99-")
    }

    /// Check if data looks like FTP response
    fn is_ftp_response(&self, data: &[u8]) -> bool {
        let text = match std::str::from_utf8(data) {
            Ok(text) => text,
            Err(_) => return false,
        };

        // FTP responses start with 3-digit code
        if text.len() < 3 {
            return false;
        }

        text.chars().take(3).all(|c| c.is_ascii_digit()) &&
        (text.starts_with("220 ") || text.starts_with("230 ") || text.starts_with("331 "))
    }

    /// Check if data looks like SMTP response
    fn is_smtp_response(&self, data: &[u8]) -> bool {
        let text = match std::str::from_utf8(data) {
            Ok(text) => text,
            Err(_) => return false,
        };

        text.starts_with("220 ") || text.starts_with("250 ") || text.starts_with("354 ")
    }

    /// Check if data looks like POP3 response
    fn is_pop3_response(&self, data: &[u8]) -> bool {
        let text = match std::str::from_utf8(data) {
            Ok(text) => text,
            Err(_) => return false,
        };

        text.starts_with("+OK ") || text.starts_with("-ERR ")
    }

    /// Check if data looks like IMAP response
    fn is_imap_response(&self, data: &[u8]) -> bool {
        let text = match std::str::from_utf8(data) {
            Ok(text) => text,
            Err(_) => return false,
        };

        text.starts_with("* OK ") || text.starts_with("* PREAUTH ") || text.starts_with("* BYE ")
    }
}

impl RouteRule for ProtocolSniffRule {
    fn name(&self) -> &str {
        &self.name
    }

    fn outbound(&self) -> &str {
        &self.outbound
    }

    fn matches(&self, ctx: &InboundContext) -> bool {
        // In a real implementation, this would access connection data for sniffing
        // For now, we'll use a simplified approach based on destination port
        let protocol = match &ctx.destination {
            Some(dest) => {
                let port = match dest {
                    Destination::Domain(_, port) => *port,
                    Destination::Ip(_, port) => *port,
                };

                match port {
                    80 | 8080 | 8000 => Some("http".to_string()),
                    443 | 8443 => Some("tls".to_string()),
                    22 => Some("ssh".to_string()),
                    21 => Some("ftp".to_string()),
                    25 | 587 => Some("smtp".to_string()),
                    110 => Some("pop3".to_string()),
                    143 => Some("imap".to_string()),
                    _ => None,
                }
            },
            None => None,
        };

        let matches = match protocol {
            Some(proto) => self.protocols.contains(&proto),
            None => false,
        };

        if self.invert { !matches } else { matches }
    }
}



impl ProcessRule {
    /// Create a new process rule
    pub fn new(name: String, outbound: String) -> Self {
        Self {
            name,
            outbound,
            process_names: Vec::new(),
            process_paths: Vec::new(),
            process_path_patterns: Vec::new(),
            invert: false,
            case_sensitive: false,
        }
    }

    /// Add process name to match
    pub fn add_process_name(&mut self, name: String) {
        self.process_names.push(name.to_lowercase());
    }

    /// Add process path to match
    pub fn add_process_path(&mut self, path: String) {
        self.process_paths.push(path);
    }

    /// Add process path regex
    pub fn add_process_path_regex(&mut self, pattern: &str) -> Result<(), String> {
        let regex = Regex::new(pattern)
            .map_err(|e| format!("Invalid regex pattern '{}': {}", pattern, e))?;
        self.process_path_patterns.push(regex);
        Ok(())
    }

    /// Check if process info matches
    fn matches_process(&self, process_info: &crate::adapter::ProcessInfo) -> bool {
        // Check process name
        if self.process_names.iter().any(|name| name == &process_info.name.to_lowercase()) {
            return true;
        }

        // Check process path
        for path in &self.process_paths {
            if process_info.path.contains(path) {
                return true;
            }
        }

        // Check process path regex
        for regex in &self.process_path_patterns {
            if regex.is_match(&process_info.path) {
                return true;
            }
        }

        false
    }
}

impl RouteRule for ProcessRule {
    fn name(&self) -> &str {
        &self.name
    }

    fn outbound(&self) -> &str {
        &self.outbound
    }

    fn matches(&self, ctx: &InboundContext) -> bool {
        let process_info = match &ctx.process_info {
            Some(info) => info,
            None => return false,
        };

        let matches = self.matches_process(process_info);
        if self.invert { !matches } else { matches }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr};

    #[test]
    fn test_advanced_domain_rule() {
        let mut rule = AdvancedDomainRule::new("test".to_string(), "proxy".to_string());
        rule.add_domain("example.com".to_string());
        rule.add_domain_suffix(".google.com".to_string());
        rule.add_domain_keyword("youtube".to_string());
        rule.add_domain_regex(r".*\.github\.com$").unwrap();

        // Test exact match
        assert!(rule.matches_domain("example.com"));
        assert!(rule.matches_domain("EXAMPLE.COM")); // Case insensitive

        // Test suffix match
        assert!(rule.matches_domain("www.google.com"));
        assert!(rule.matches_domain("mail.google.com"));

        // Test keyword match
        assert!(rule.matches_domain("youtube.com"));
        assert!(rule.matches_domain("m.youtube.com"));

        // Test regex match
        assert!(rule.matches_domain("api.github.com"));
        assert!(rule.matches_domain("raw.github.com"));

        // Test no match
        assert!(!rule.matches_domain("facebook.com"));
    }

    #[test]
    fn test_ip_range_rule() {
        let mut rule = IpRangeRule::new("test".to_string(), "proxy".to_string());
        rule.add_ipv4_cidr("***********/24").unwrap();
        rule.add_ipv4_cidr("10.0.0.0/8").unwrap();

        // Test IPv4 matches
        assert!(rule.matches_ip(&IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1))));
        assert!(rule.matches_ip(&IpAddr::V4(Ipv4Addr::new(192, 168, 1, 255))));
        assert!(rule.matches_ip(&IpAddr::V4(Ipv4Addr::new(10, 0, 0, 1))));
        assert!(rule.matches_ip(&IpAddr::V4(Ipv4Addr::new(10, 255, 255, 255))));

        // Test IPv4 no matches
        assert!(!rule.matches_ip(&IpAddr::V4(Ipv4Addr::new(192, 168, 2, 1))));
        assert!(!rule.matches_ip(&IpAddr::V4(Ipv4Addr::new(172, 16, 0, 1))));
    }

    #[test]
    fn test_port_range_rule() {
        let mut rule = PortRangeRule::new("test".to_string(), "proxy".to_string());
        rule.add_port(80);
        rule.add_port_range(8000, 8999).unwrap();
        rule.add_port_string("443,9000-9999").unwrap();

        // Test single port
        assert!(rule.matches_port(80));
        assert!(rule.matches_port(443));

        // Test port ranges
        assert!(rule.matches_port(8000));
        assert!(rule.matches_port(8500));
        assert!(rule.matches_port(8999));
        assert!(rule.matches_port(9000));
        assert!(rule.matches_port(9999));

        // Test no matches
        assert!(!rule.matches_port(81));
        assert!(!rule.matches_port(7999));
        assert!(!rule.matches_port(10000));
    }

    #[test]
    fn test_protocol_sniff_rule() {
        let mut rule = ProtocolSniffRule::new("test".to_string(), "proxy".to_string());
        rule.add_protocol("http".to_string());
        rule.add_protocol("tls".to_string());

        // Test HTTP detection
        assert!(rule.is_http_request(b"GET / HTTP/1.1\r\n"));
        assert!(rule.is_http_request(b"POST /api HTTP/1.1\r\n"));
        assert!(!rule.is_http_request(b"invalid data"));

        // Test TLS detection
        assert!(rule.is_tls_handshake(&[0x16, 0x03, 0x01, 0x00, 0x00, 0x00]));
        assert!(rule.is_tls_handshake(&[0x16, 0x03, 0x03, 0x00, 0x00, 0x00]));
        assert!(!rule.is_tls_handshake(&[0x15, 0x03, 0x01, 0x00, 0x00, 0x00]));

        // Test SSH detection
        assert!(rule.is_ssh_handshake(b"SSH-2.0-OpenSSH_7.4"));
        assert!(rule.is_ssh_handshake(b"SSH-1.99-OpenSSH_3.0"));
        assert!(!rule.is_ssh_handshake(b"HTTP/1.1 200 OK"));
    }
}
