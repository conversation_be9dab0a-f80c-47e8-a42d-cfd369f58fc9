//! Route module for sing-box
//! 
//! This module provides routing functionality including rule matching,
//! connection routing, and traffic management.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use async_trait::async_trait;
use crate::adapter::{InboundContext, Lifecycle, StartStage, Router as RouterTrait};

pub mod router;
pub mod geoip;
pub mod geosite;
pub mod process;
pub mod user;
pub mod rule;
pub mod sniff;
pub mod rule_set;

/// Route errors
#[derive(Debug, Clone)]
pub enum RouteError {
    NoRoute,
    RuleNotFound(String),
    InvalidRule(String),
    MatchFailed(String),
}

impl std::fmt::Display for RouteError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RouteError::NoRoute => write!(f, "no route found"),
            RouteError::RuleNotFound(rule) => write!(f, "rule not found: {}", rule),
            RouteError::InvalidRule(rule) => write!(f, "invalid rule: {}", rule),
            RouteError::MatchFailed(reason) => write!(f, "match failed: {}", reason),
        }
    }
}

impl std::error::Error for RouteError {}

/// Rule trait for route matching
pub trait Rule: Send + Sync {
    fn rule_type(&self) -> &str;
    fn matches(&self, context: &InboundContext) -> bool;
    fn action(&self) -> &str;
    fn outbound(&self) -> Option<&str>;
}

/// Basic rule implementation
#[derive(Debug, Clone)]
pub struct BasicRule {
    rule_type: String,
    action: String,
    outbound: Option<String>,
    inbound: Vec<String>,
    ip_version: Option<u8>,
    network: Vec<String>,
    domain: Vec<String>,
    domain_suffix: Vec<String>,
    domain_keyword: Vec<String>,
    domain_regex: Vec<String>,
    source_ip_cidr: Vec<String>,
    ip_cidr: Vec<String>,
    source_port: Vec<u16>,
    source_port_range: Vec<String>,
    port: Vec<u16>,
    port_range: Vec<String>,
    process_name: Vec<String>,
    process_path: Vec<String>,
    package_name: Vec<String>,
    user: Vec<String>,
    user_id: Vec<u32>,
    protocol: Vec<String>,
    auth_user: Vec<String>,
    clash_mode: String,
    invert: bool,
}

impl BasicRule {
    pub fn new(rule_type: &str, action: &str) -> Self {
        Self {
            rule_type: rule_type.to_string(),
            action: action.to_string(),
            outbound: None,
            inbound: Vec::new(),
            ip_version: None,
            network: Vec::new(),
            domain: Vec::new(),
            domain_suffix: Vec::new(),
            domain_keyword: Vec::new(),
            domain_regex: Vec::new(),
            source_ip_cidr: Vec::new(),
            ip_cidr: Vec::new(),
            source_port: Vec::new(),
            source_port_range: Vec::new(),
            port: Vec::new(),
            port_range: Vec::new(),
            process_name: Vec::new(),
            process_path: Vec::new(),
            package_name: Vec::new(),
            user: Vec::new(),
            user_id: Vec::new(),
            protocol: Vec::new(),
            auth_user: Vec::new(),
            clash_mode: String::new(),
            invert: false,
        }
    }

    pub fn with_outbound(mut self, outbound: &str) -> Self {
        self.outbound = Some(outbound.to_string());
        self
    }

    pub fn with_inbound(mut self, inbound: &str) -> Self {
        self.inbound.push(inbound.to_string());
        self
    }

    pub fn with_domain(mut self, domain: &str) -> Self {
        self.domain.push(domain.to_string());
        self
    }

    pub fn with_network(mut self, network: &str) -> Self {
        self.network.push(network.to_string());
        self
    }

    fn matches_inbound(&self, context: &InboundContext) -> bool {
        if self.inbound.is_empty() {
            return true;
        }
        self.inbound.contains(&context.inbound)
    }

    fn matches_network(&self, context: &InboundContext) -> bool {
        if self.network.is_empty() {
            return true;
        }
        self.network.contains(&context.network)
    }

    fn matches_domain(&self, context: &InboundContext) -> bool {
        if self.domain.is_empty() {
            return true;
        }
        self.domain.contains(&context.domain)
    }

    fn matches_ip_version(&self, context: &InboundContext) -> bool {
        if let Some(version) = self.ip_version {
            return context.ip_version == version;
        }
        true
    }
}

impl Rule for BasicRule {
    fn rule_type(&self) -> &str {
        &self.rule_type
    }

    fn matches(&self, context: &InboundContext) -> bool {
        let result = self.matches_inbound(context) &&
                    self.matches_network(context) &&
                    self.matches_domain(context) &&
                    self.matches_ip_version(context);

        if self.invert {
            !result
        } else {
            result
        }
    }

    fn action(&self) -> &str {
        &self.action
    }

    fn outbound(&self) -> Option<&str> {
        self.outbound.as_deref()
    }
}

/// Router implementation
pub struct Router {
    rules: Arc<RwLock<Vec<Box<dyn Rule>>>>,
    default_outbound: String,
    rule_map: Arc<RwLock<HashMap<String, usize>>>,
}

impl Router {
    pub fn new(default_outbound: &str) -> Self {
        Self {
            rules: Arc::new(RwLock::new(Vec::new())),
            default_outbound: default_outbound.to_string(),
            rule_map: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    pub async fn add_rule(&self, rule: Box<dyn Rule>) {
        let rule_type = rule.rule_type().to_string();
        let mut rules = self.rules.write().await;
        let index = rules.len();
        rules.push(rule);
        self.rule_map.write().await.insert(rule_type, index);
    }

    pub async fn find_outbound(&self, context: &InboundContext) -> String {
        for rule in self.rules.read().await.iter() {
            if rule.matches(context) {
                if let Some(outbound) = rule.outbound() {
                    return outbound.to_string();
                }
            }
        }
        self.default_outbound.clone()
    }
}

impl RouterTrait for Router {
    fn route(&self, metadata: &InboundContext) -> Result<String, String> {
        // Synchronous routing - check rules without async operations
        // This is a simplified version for the trait implementation

        // Try to get rules without blocking
        if let Ok(rules) = self.rules.try_read() {
            for rule in rules.iter() {
                if rule.matches(metadata) {
                    if let Some(outbound) = rule.outbound() {
                        return Ok(outbound.to_string());
                    }
                }
            }
        }

        // Return default outbound if no rules match or can't access rules
        Ok(self.default_outbound.clone())
    }
}

#[async_trait]
impl Lifecycle for Router {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        // Router initialization logic
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        // Router cleanup logic
        self.rules.write().await.clear();
        self.rule_map.write().await.clear();
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_route_error_display() {
        assert_eq!(format!("{}", RouteError::NoRoute), "no route found");
        assert_eq!(format!("{}", RouteError::RuleNotFound("test".to_string())), "rule not found: test");
        assert_eq!(format!("{}", RouteError::InvalidRule("bad".to_string())), "invalid rule: bad");
        assert_eq!(format!("{}", RouteError::MatchFailed("reason".to_string())), "match failed: reason");
    }

    #[test]
    fn test_basic_rule_creation() {
        let rule = BasicRule::new("default", "direct");
        assert_eq!(rule.rule_type(), "default");
        assert_eq!(rule.action(), "direct");
        assert!(rule.outbound().is_none());
    }

    #[test]
    fn test_basic_rule_with_outbound() {
        let rule = BasicRule::new("default", "route")
            .with_outbound("proxy");
        
        assert_eq!(rule.outbound(), Some("proxy"));
    }

    #[test]
    fn test_basic_rule_matching() {
        let rule = BasicRule::new("default", "direct")
            .with_inbound("http-in")
            .with_network("tcp")
            .with_domain("example.com");

        let mut context = InboundContext::default();
        context.inbound = "http-in".to_string();
        context.network = "tcp".to_string();
        context.domain = "example.com".to_string();

        assert!(rule.matches(&context));
    }

    #[test]
    fn test_basic_rule_no_match() {
        let rule = BasicRule::new("default", "direct")
            .with_inbound("http-in");

        let mut context = InboundContext::default();
        context.inbound = "socks-in".to_string();

        assert!(!rule.matches(&context));
    }

    #[tokio::test]
    async fn test_router_creation() {
        let router = Router::new("direct");
        assert_eq!(router.default_outbound, "direct");
        assert!(router.rules.read().await.is_empty());
    }

    #[tokio::test]
    async fn test_router_add_rule() {
        let router = Router::new("direct");
        let rule = Box::new(BasicRule::new("test", "proxy").with_outbound("proxy"));

        router.add_rule(rule).await;
        assert_eq!(router.rules.read().await.len(), 1);
        assert!(router.rule_map.read().await.contains_key("test"));
    }

    #[tokio::test]
    async fn test_router_find_outbound_default() {
        let router = Router::new("direct");
        let context = InboundContext::default();

        let outbound = router.find_outbound(&context).await;
        assert_eq!(outbound, "direct");
    }

    #[tokio::test]
    async fn test_router_find_outbound_rule_match() {
        let router = Router::new("direct");
        let rule = Box::new(BasicRule::new("test", "proxy")
            .with_outbound("proxy")
            .with_domain("example.com"));

        router.add_rule(rule).await;

        let mut context = InboundContext::default();
        context.domain = "example.com".to_string();

        let outbound = router.find_outbound(&context).await;
        assert_eq!(outbound, "proxy");
    }

    #[tokio::test]
    async fn test_router_trait() {
        let router = Router::new("direct");
        let rule = Box::new(BasicRule::new("test", "proxy")
            .with_outbound("proxy")
            .with_domain("example.com"));
        router.add_rule(rule).await;

        let mut context = InboundContext::default();
        context.domain = "example.com".to_string();
        let result = router.route(&context);

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "proxy");
    }

    #[tokio::test]
    async fn test_router_lifecycle() {
        let router = Router::new("direct");

        assert!(router.start(StartStage::Initialize).await.is_ok());
        assert!(router.start(StartStage::Start).await.is_ok());
        assert!(router.close().await.is_ok());

        assert!(router.rules.read().await.is_empty());
        assert!(router.rule_map.read().await.is_empty());
    }
}
