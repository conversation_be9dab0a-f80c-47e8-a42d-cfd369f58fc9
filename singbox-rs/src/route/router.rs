use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use rand::seq::SliceRandom;

use async_trait::async_trait;
use crate::adapter::{Adapter, Inbound, Outbound, InboundContext, Lifecycle, StartStage};
use crate::route::{Rule, RouterTrait};

/// Core router implementation for sing-box
pub struct Router {
    rules: Arc<RwLock<Vec<Box<dyn Rule + Send + Sync>>>>,
    default_outbound: String,
    outbounds: Arc<RwLock<HashMap<String, Box<dyn Outbound + Send + Sync>>>>,
    inbounds: Arc<RwLock<HashMap<String, Box<dyn Inbound + Send + Sync>>>>,

    /// Router configuration
    config: RouterConfig,

    /// Routing statistics
    stats: Arc<RwLock<RouterStats>>,

    /// Rule cache for performance
    rule_cache: Arc<RwLock<HashMap<String, CachedRouteResult>>>,

    /// Load balancer for outbounds
    load_balancer: Arc<RwLock<LoadBalancer>>,

    /// Failover manager
    failover_manager: Arc<RwLock<FailoverManager>>,
}

impl Router {
    pub fn new(default_outbound: &str) -> Self {
        Self {
            rules: Arc::new(RwLock::new(Vec::new())),
            default_outbound: default_outbound.to_string(),
            outbounds: Arc::new(RwLock::new(HashMap::new())),
            inbounds: Arc::new(RwLock::new(HashMap::new())),
            config: RouterConfig::default(),
            stats: Arc::new(RwLock::new(RouterStats::default())),
            rule_cache: Arc::new(RwLock::new(HashMap::new())),
            load_balancer: Arc::new(RwLock::new(LoadBalancer::new())),
            failover_manager: Arc::new(RwLock::new(FailoverManager::new(FailoverConfig::default()))),
        }
    }

    /// Create router with configuration
    pub fn with_config(default_outbound: &str, config: RouterConfig) -> Self {
        Self {
            rules: Arc::new(RwLock::new(Vec::new())),
            default_outbound: default_outbound.to_string(),
            outbounds: Arc::new(RwLock::new(HashMap::new())),
            inbounds: Arc::new(RwLock::new(HashMap::new())),
            config: config.clone(),
            stats: Arc::new(RwLock::new(RouterStats::default())),
            rule_cache: Arc::new(RwLock::new(HashMap::new())),
            load_balancer: Arc::new(RwLock::new(LoadBalancer::new())),
            failover_manager: Arc::new(RwLock::new(FailoverManager::new(config.failover))),
        }
    }

    /// Add a routing rule
    pub async fn add_rule(&self, rule: Box<dyn Rule + Send + Sync>) {
        self.rules.write().await.push(rule);
    }

    /// Add an outbound adapter
    pub async fn add_outbound(&self, outbound: Box<dyn Outbound + Send + Sync>) {
        let tag = outbound.tag().to_string();
        let mut outbounds = self.outbounds.write().await;
        outbounds.insert(tag, outbound);
    }

    /// Add an inbound adapter
    pub async fn add_inbound(&self, inbound: Box<dyn Inbound + Send + Sync>) {
        let tag = inbound.tag().to_string();
        let mut inbounds = self.inbounds.write().await;
        inbounds.insert(tag, inbound);
    }

    /// Get outbound by tag
    pub async fn get_outbound(&self, tag: &str) -> Option<String> {
        let outbounds = self.outbounds.read().await;
        if outbounds.contains_key(tag) {
            Some(tag.to_string())
        } else {
            None
        }
    }

    /// Get inbound by tag
    pub async fn get_inbound(&self, tag: &str) -> Option<String> {
        let inbounds = self.inbounds.read().await;
        if inbounds.contains_key(tag) {
            Some(tag.to_string())
        } else {
            None
        }
    }

    /// Find the best outbound for a given context
    pub async fn find_outbound(&self, context: &InboundContext) -> String {
        // Check rules in order
        let rules = self.rules.read().await;
        for rule in rules.iter() {
            if rule.matches(context) {
                if let Some(outbound) = rule.outbound() {
                    return outbound.to_string();
                }
            }
        }

        // Return default outbound if no rules match
        self.default_outbound.clone()
    }

    /// Route a connection based on metadata
    pub async fn route_connection(&self, context: &InboundContext) -> Result<String, String> {
        let outbound_tag = self.find_outbound(context).await;
        
        // Verify outbound exists
        let outbounds = self.outbounds.read().await;
        if outbounds.contains_key(&outbound_tag) || outbound_tag == self.default_outbound {
            Ok(outbound_tag)
        } else {
            Err(format!("outbound '{}' not found", outbound_tag))
        }
    }

    /// Get all outbound tags
    pub async fn list_outbounds(&self) -> Vec<String> {
        let outbounds = self.outbounds.read().await;
        outbounds.keys().cloned().collect()
    }

    /// Get all inbound tags
    pub async fn list_inbounds(&self) -> Vec<String> {
        let inbounds = self.inbounds.read().await;
        inbounds.keys().cloned().collect()
    }

    /// Get router statistics
    pub async fn stats(&self) -> RouterStats {
        let outbounds = self.outbounds.read().await;
        let inbounds = self.inbounds.read().await;
        
        RouterStats {
            total_routes: 0,
            cache_hits: 0,
            cache_misses: 0,
            avg_routing_time: std::time::Duration::ZERO,
            routes_by_outbound: std::collections::HashMap::new(),
            routes_by_rule: std::collections::HashMap::new(),
            failed_routes: 0,
            failover_events: 0,
            load_balancing_decisions: 0,
            rules_count: self.rules.read().await.len(),
            outbounds_count: outbounds.len(),
            inbounds_count: inbounds.len(),
            default_outbound: self.default_outbound.clone(),
        }
    }

    /// Remove an outbound
    pub async fn remove_outbound(&self, tag: &str) -> Result<(), String> {
        let mut outbounds = self.outbounds.write().await;
        if outbounds.remove(tag).is_some() {
            Ok(())
        } else {
            Err(format!("outbound '{}' not found", tag))
        }
    }

    /// Remove an inbound
    pub async fn remove_inbound(&self, tag: &str) -> Result<(), String> {
        let mut inbounds = self.inbounds.write().await;
        if inbounds.remove(tag).is_some() {
            Ok(())
        } else {
            Err(format!("inbound '{}' not found", tag))
        }
    }

    /// Clear all rules
    pub async fn clear_rules(&self) {
        self.rules.write().await.clear();
    }

    /// Get rules count
    pub async fn rules_count(&self) -> usize {
        self.rules.read().await.len()
    }
}

impl RouterTrait for Router {
    fn route(&self, metadata: &InboundContext) -> Result<String, String> {
        // Synchronous routing - check rules without async operations
        // This is a simplified version for the trait implementation

        // Try to get rules without blocking
        if let Ok(rules) = self.rules.try_read() {
            for rule in rules.iter() {
                if rule.matches(metadata) {
                    if let Some(outbound) = rule.outbound() {
                        return Ok(outbound.to_string());
                    }
                }
            }
        }

        // Return default outbound if no rules match or can't access rules
        Ok(self.default_outbound.clone())
    }
}

#[async_trait]
impl Lifecycle for Router {
    async fn start(&self, stage: StartStage) -> Result<(), String> {
        match stage {
            StartStage::Initialize => {
                // Initialize router components
                println!("Router initializing...");
            }
            StartStage::Start => {
                // Start router services
                println!("Router starting...");
            }
            StartStage::PostStart => {
                // Post-start initialization
                println!("Router post-start...");
            }
            StartStage::Started => {
                // Router fully started
                println!("Router started");
            }
        }
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        // Clean up router resources
        self.rules.write().await.clear();
        println!("Router closed");
        Ok(())
    }
}

/// Basic router statistics
#[derive(Debug, Clone)]
pub struct BasicRouterStats {
    pub rules_count: usize,
    pub outbounds_count: usize,
    pub inbounds_count: usize,
    pub default_outbound: String,
}

/// Router builder for easier configuration
pub struct RouterBuilder {
    default_outbound: String,
    rules: Vec<Box<dyn Rule + Send + Sync>>,
}

impl RouterBuilder {
    pub fn new(default_outbound: &str) -> Self {
        Self {
            default_outbound: default_outbound.to_string(),
            rules: Vec::new(),
        }
    }

    pub fn add_rule(mut self, rule: Box<dyn Rule + Send + Sync>) -> Self {
        self.rules.push(rule);
        self
    }

    pub async fn build(self) -> Router {
        let router = Router::new(&self.default_outbound);
        for rule in self.rules {
            router.add_rule(rule).await;
        }
        router
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::route::BasicRule;

    #[tokio::test]
    async fn test_router_creation() {
        let router = Router::new("direct");
        assert_eq!(router.default_outbound, "direct");
        assert_eq!(router.rules.read().await.len(), 0);
    }

    #[tokio::test]
    async fn test_router_add_rule() {
        let router = Router::new("direct");
        let rule = Box::new(BasicRule::new("test", "proxy").with_outbound("proxy"));

        router.add_rule(rule).await;
        assert_eq!(router.rules.read().await.len(), 1);
    }

    #[tokio::test]
    async fn test_router_find_outbound_default() {
        let router = Router::new("direct");
        let context = InboundContext::default();

        let outbound = router.find_outbound(&context).await;
        assert_eq!(outbound, "direct");
    }

    #[tokio::test]
    async fn test_router_find_outbound_rule_match() {
        let router = Router::new("direct");
        let rule = Box::new(BasicRule::new("test", "proxy")
            .with_outbound("proxy")
            .with_domain("example.com"));

        router.add_rule(rule).await;

        let mut context = InboundContext::default();
        context.domain = "example.com".to_string();

        let outbound = router.find_outbound(&context).await;
        assert_eq!(outbound, "proxy");
    }

    #[tokio::test]
    async fn test_router_builder() {
        let rule = Box::new(BasicRule::new("test", "proxy").with_outbound("proxy"));
        let router = RouterBuilder::new("direct")
            .add_rule(rule)
            .build()
            .await;

        assert_eq!(router.default_outbound, "direct");
        assert_eq!(router.rules.read().await.len(), 1);
    }

    #[tokio::test]
    async fn test_router_stats() {
        let router = Router::new("direct");
        let stats = router.stats().await;
        
        assert_eq!(stats.rules_count, 0);
        assert_eq!(stats.outbounds_count, 0);
        assert_eq!(stats.inbounds_count, 0);
        assert_eq!(stats.default_outbound, "direct");
    }

    #[tokio::test]
    async fn test_router_lifecycle() {
        let router = Router::new("direct");

        assert!(router.start(StartStage::Initialize).await.is_ok());
        assert!(router.start(StartStage::Start).await.is_ok());
        assert!(router.close().await.is_ok());

        assert_eq!(router.rules.read().await.len(), 0);
    }
}

/// Router configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RouterConfig {
    /// Enable rule caching
    pub enable_cache: bool,

    /// Cache size
    pub cache_size: usize,

    /// Cache TTL
    pub cache_ttl: Duration,

    /// Load balancing strategy
    pub load_balancing: LoadBalancingStrategy,

    /// Failover configuration
    pub failover: FailoverConfig,

    /// Route optimization
    pub optimization: RouteOptimizationConfig,

    /// Concurrent routing
    pub concurrent_routing: bool,

    /// Maximum routing time
    pub max_routing_time: Duration,
}

/// Load balancing strategies
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LoadBalancingStrategy {
    /// Round robin
    RoundRobin,

    /// Least connections
    LeastConnections,

    /// Weighted round robin
    WeightedRoundRobin(HashMap<String, u32>),

    /// Random selection
    Random,

    /// Latency-based
    LatencyBased,

    /// Health-based
    HealthBased,
}

/// Failover configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FailoverConfig {
    /// Enable failover
    pub enabled: bool,

    /// Health check interval
    pub health_check_interval: Duration,

    /// Health check timeout
    pub health_check_timeout: Duration,

    /// Maximum failures before failover
    pub max_failures: u32,

    /// Recovery check interval
    pub recovery_interval: Duration,

    /// Failover delay
    pub failover_delay: Duration,
}

/// Route optimization configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RouteOptimizationConfig {
    /// Enable route optimization
    pub enabled: bool,

    /// Learning rate for optimization
    pub learning_rate: f64,

    /// Optimization interval
    pub optimization_interval: Duration,

    /// Performance metrics to consider
    pub metrics: Vec<OptimizationMetric>,

    /// Minimum samples for optimization
    pub min_samples: u32,
}

/// Optimization metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OptimizationMetric {
    /// Connection latency
    Latency,

    /// Throughput
    Throughput,

    /// Success rate
    SuccessRate,

    /// Connection time
    ConnectionTime,

    /// Error rate
    ErrorRate,
}

/// Router statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RouterStats {
    /// Total routing decisions
    pub total_routes: u64,

    /// Cache hits
    pub cache_hits: u64,

    /// Cache misses
    pub cache_misses: u64,

    /// Average routing time
    pub avg_routing_time: Duration,

    /// Routes by outbound
    pub routes_by_outbound: HashMap<String, u64>,

    /// Routes by rule
    pub routes_by_rule: HashMap<String, u64>,

    /// Failed routes
    pub failed_routes: u64,

    /// Failover events
    pub failover_events: u64,

    /// Load balancing decisions
    pub load_balancing_decisions: u64,

    /// Rules count
    pub rules_count: usize,

    /// Outbounds count
    pub outbounds_count: usize,

    /// Inbounds count
    pub inbounds_count: usize,

    /// Default outbound
    pub default_outbound: String,
}

/// Cached route result
#[derive(Debug, Clone)]
pub struct CachedRouteResult {
    /// Selected outbound
    pub outbound: String,

    /// Matched rule
    pub rule: Option<String>,

    /// Cache timestamp
    pub timestamp: Instant,

    /// Hit count
    pub hit_count: u64,

    /// Success rate
    pub success_rate: f64,
}

/// Load balancer
pub struct LoadBalancer {
    /// Current strategy
    strategy: LoadBalancingStrategy,

    /// Outbound weights
    weights: HashMap<String, u32>,

    /// Current round robin index
    round_robin_index: usize,

    /// Connection counts
    connection_counts: HashMap<String, u32>,

    /// Latency measurements
    latency_measurements: HashMap<String, Vec<Duration>>,

    /// Health status
    health_status: HashMap<String, bool>,
}

/// Failover manager
pub struct FailoverManager {
    /// Failover configuration
    config: FailoverConfig,

    /// Outbound health status
    health_status: HashMap<String, OutboundHealth>,

    /// Failover history
    failover_history: Vec<FailoverEvent>,

    /// Active health checks
    active_checks: HashMap<String, tokio::task::JoinHandle<()>>,
}

/// Outbound health information
#[derive(Debug, Clone)]
pub struct OutboundHealth {
    /// Is healthy
    pub healthy: bool,

    /// Consecutive failures
    pub consecutive_failures: u32,

    /// Last check time
    pub last_check: SystemTime,

    /// Last failure time
    pub last_failure: Option<SystemTime>,

    /// Average response time
    pub avg_response_time: Duration,

    /// Success rate
    pub success_rate: f64,
}

/// Failover event
#[derive(Debug, Clone)]
pub struct FailoverEvent {
    /// Event timestamp
    pub timestamp: SystemTime,

    /// Failed outbound
    pub failed_outbound: String,

    /// Backup outbound
    pub backup_outbound: String,

    /// Failure reason
    pub reason: String,

    /// Recovery time
    pub recovery_time: Option<SystemTime>,
}

impl LoadBalancer {
    /// Create a new load balancer
    pub fn new() -> Self {
        Self {
            strategy: LoadBalancingStrategy::RoundRobin,
            weights: HashMap::new(),
            round_robin_index: 0,
            connection_counts: HashMap::new(),
            latency_measurements: HashMap::new(),
            health_status: HashMap::new(),
        }
    }

    /// Select outbound using load balancing strategy
    pub fn select_outbound(&mut self, outbounds: &[String]) -> Option<String> {
        if outbounds.is_empty() {
            return None;
        }

        // Filter healthy outbounds
        let healthy_outbounds: Vec<_> = outbounds.iter()
            .filter(|outbound| *self.health_status.get(*outbound).unwrap_or(&true))
            .cloned()
            .collect();

        if healthy_outbounds.is_empty() {
            // No healthy outbounds, use first available
            return Some(outbounds[0].clone());
        }

        match &self.strategy {
            LoadBalancingStrategy::RoundRobin => {
                let selected = &healthy_outbounds[self.round_robin_index % healthy_outbounds.len()];
                self.round_robin_index += 1;
                Some(selected.clone())
            },
            LoadBalancingStrategy::LeastConnections => {
                healthy_outbounds.iter()
                    .min_by_key(|outbound| self.connection_counts.get(*outbound).unwrap_or(&0))
                    .cloned()
            },
            LoadBalancingStrategy::WeightedRoundRobin(weights) => {
                // Weighted round robin implementation
                let total_weight: u32 = healthy_outbounds.iter()
                    .map(|outbound| weights.get(outbound).unwrap_or(&1))
                    .sum();

                if total_weight == 0 {
                    return Some(healthy_outbounds[0].clone());
                }

                let mut current_weight = 0;
                let target_weight = (self.round_robin_index % total_weight as usize) as u32;

                for outbound in &healthy_outbounds {
                    current_weight += weights.get(outbound).unwrap_or(&1);
                    if current_weight > target_weight {
                        self.round_robin_index += 1;
                        return Some(outbound.clone());
                    }
                }

                Some(healthy_outbounds[0].clone())
            },
            LoadBalancingStrategy::Random => {
                if healthy_outbounds.is_empty() {
                    None
                } else {
                    use rand::Rng;
                    let mut rng = rand::thread_rng();
                    let index = rng.gen_range(0..healthy_outbounds.len());
                    Some(healthy_outbounds[index].clone())
                }
            },
            LoadBalancingStrategy::LatencyBased => {
                healthy_outbounds.iter()
                    .min_by_key(|outbound| {
                        self.latency_measurements.get(*outbound)
                            .and_then(|measurements| measurements.last())
                            .copied()
                            .unwrap_or(Duration::from_secs(999))
                    })
                    .cloned()
            },
            LoadBalancingStrategy::HealthBased => {
                // Select based on health score (combination of latency and success rate)
                healthy_outbounds.iter()
                    .min_by(|a, b| {
                        let score_a = self.calculate_health_score(a);
                        let score_b = self.calculate_health_score(b);
                        score_a.partial_cmp(&score_b).unwrap_or(std::cmp::Ordering::Equal)
                    })
                    .cloned()
            },
        }
    }

    /// Update connection count
    pub fn update_connection_count(&mut self, outbound: &str, delta: i32) {
        let count = self.connection_counts.entry(outbound.to_string()).or_insert(0);
        *count = (*count as i32 + delta).max(0) as u32;
    }

    /// Record latency measurement
    pub fn record_latency(&mut self, outbound: &str, latency: Duration) {
        let measurements = self.latency_measurements.entry(outbound.to_string()).or_insert_with(Vec::new);
        measurements.push(latency);

        // Keep only last 100 measurements
        if measurements.len() > 100 {
            measurements.remove(0);
        }
    }

    /// Update health status
    pub fn update_health_status(&mut self, outbound: &str, healthy: bool) {
        self.health_status.insert(outbound.to_string(), healthy);
    }

    /// Calculate health score for outbound
    fn calculate_health_score(&self, outbound: &str) -> f64 {
        let latency_score = if let Some(measurements) = self.latency_measurements.get(outbound) {
            if let Some(avg_latency) = measurements.last() {
                1.0 / (avg_latency.as_millis() as f64 + 1.0)
            } else {
                0.0
            }
        } else {
            0.0
        };

        let connection_score = 1.0 / (*self.connection_counts.get(outbound).unwrap_or(&1) as f64 + 1.0);

        // Combine scores (can be weighted)
        (latency_score + connection_score) / 2.0
    }
}

impl FailoverManager {
    /// Create a new failover manager
    pub fn new(config: FailoverConfig) -> Self {
        Self {
            config,
            health_status: HashMap::new(),
            failover_history: Vec::new(),
            active_checks: HashMap::new(),
        }
    }

    /// Start health checks for outbound
    pub async fn start_health_check(&mut self, outbound: String) {
        if !self.config.enabled {
            return;
        }

        let config = self.config.clone();
        let health_status = Arc::new(RwLock::new(self.health_status.clone()));
        let outbound_clone = outbound.clone();

        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.health_check_interval);

            loop {
                interval.tick().await;

                // Perform health check
                let is_healthy = Self::perform_health_check(&outbound_clone, &config).await;

                // Update health status
                let mut status_guard = health_status.write().await;
                let health = status_guard.entry(outbound_clone.clone()).or_insert_with(|| OutboundHealth {
                    healthy: true,
                    consecutive_failures: 0,
                    last_check: SystemTime::now(),
                    last_failure: None,
                    avg_response_time: Duration::ZERO,
                    success_rate: 1.0,
                });

                health.last_check = SystemTime::now();

                if is_healthy {
                    health.healthy = true;
                    health.consecutive_failures = 0;
                } else {
                    health.consecutive_failures += 1;
                    health.last_failure = Some(SystemTime::now());

                    if health.consecutive_failures >= config.max_failures {
                        health.healthy = false;
                    }
                }
            }
        });

        self.active_checks.insert(outbound, task);
    }

    /// Perform health check for outbound
    async fn perform_health_check(outbound: &str, config: &FailoverConfig) -> bool {
        // Simple TCP connection test
        let timeout = config.health_check_timeout;

        // Try to parse outbound as address:port
        if let Ok(addr) = outbound.parse::<std::net::SocketAddr>() {
            match tokio::time::timeout(timeout, tokio::net::TcpStream::connect(addr)).await {
                Ok(Ok(_)) => true,
                _ => false,
            }
        } else {
            // For named outbounds, assume healthy (would need more complex logic)
            true
        }
    }

    /// Check if outbound is healthy
    pub fn is_healthy(&self, outbound: &str) -> bool {
        self.health_status.get(outbound)
            .map(|health| health.healthy)
            .unwrap_or(true)
    }

    /// Record failover event
    pub fn record_failover(&mut self, failed_outbound: String, backup_outbound: String, reason: String) {
        let event = FailoverEvent {
            timestamp: SystemTime::now(),
            failed_outbound,
            backup_outbound,
            reason,
            recovery_time: None,
        };

        self.failover_history.push(event);

        // Keep only last 1000 events
        if self.failover_history.len() > 1000 {
            self.failover_history.remove(0);
        }
    }
}

impl Default for RouterConfig {
    fn default() -> Self {
        Self {
            enable_cache: true,
            cache_size: 1000,
            cache_ttl: Duration::from_secs(300),
            load_balancing: LoadBalancingStrategy::RoundRobin,
            failover: FailoverConfig::default(),
            optimization: RouteOptimizationConfig::default(),
            concurrent_routing: true,
            max_routing_time: Duration::from_secs(5),
        }
    }
}

impl Default for FailoverConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            health_check_interval: Duration::from_secs(30),
            health_check_timeout: Duration::from_secs(5),
            max_failures: 3,
            recovery_interval: Duration::from_secs(60),
            failover_delay: Duration::from_millis(100),
        }
    }
}

impl Default for RouteOptimizationConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            learning_rate: 0.01,
            optimization_interval: Duration::from_secs(300),
            metrics: vec![
                OptimizationMetric::Latency,
                OptimizationMetric::SuccessRate,
            ],
            min_samples: 100,
        }
    }
}

impl Default for RouterStats {
    fn default() -> Self {
        Self {
            total_routes: 0,
            cache_hits: 0,
            cache_misses: 0,
            avg_routing_time: Duration::ZERO,
            routes_by_outbound: HashMap::new(),
            routes_by_rule: HashMap::new(),
            failed_routes: 0,
            failover_events: 0,
            load_balancing_decisions: 0,
            rules_count: 0,
            outbounds_count: 0,
            inbounds_count: 0,
            default_outbound: String::new(),
        }
    }
}
