/// Statistics and monitoring module
/// 
/// Provides real-time traffic statistics, connection monitoring, and performance metrics

use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::broadcast;

/// Traffic statistics for a specific adapter or connection
#[derive(Debu<PERSON>, Clone)]
pub struct TrafficStats {
    /// Total bytes uploaded
    pub upload_bytes: u64,
    /// Total bytes downloaded
    pub download_bytes: u64,
    /// Upload speed in bytes per second
    pub upload_speed: u64,
    /// Download speed in bytes per second
    pub download_speed: u64,
    /// Number of active connections
    pub connections: u32,
    /// Total number of connections established
    pub total_connections: u64,
    /// Number of failed connections
    pub failed_connections: u64,
    /// Last update timestamp
    pub last_update: Instant,
}

impl Default for TrafficStats {
    fn default() -> Self {
        Self {
            upload_bytes: 0,
            download_bytes: 0,
            upload_speed: 0,
            download_speed: 0,
            connections: 0,
            total_connections: 0,
            failed_connections: 0,
            last_update: Instant::now(),
        }
    }
}

impl TrafficStats {
    pub fn new() -> Self {
        Self {
            last_update: Instant::now(),
            ..Default::default()
        }
    }

    /// Add upload bytes and update speed
    pub fn add_upload(&mut self, bytes: u64) {
        self.upload_bytes += bytes;
        self.update_speed();
    }

    /// Add download bytes and update speed
    pub fn add_download(&mut self, bytes: u64) {
        self.download_bytes += bytes;
        self.update_speed();
    }

    /// Increment connection count
    pub fn add_connection(&mut self) {
        self.connections += 1;
        self.total_connections += 1;
    }

    /// Decrement connection count
    pub fn remove_connection(&mut self) {
        if self.connections > 0 {
            self.connections -= 1;
        }
    }

    /// Increment failed connection count
    pub fn add_failed_connection(&mut self) {
        self.failed_connections += 1;
    }

    /// Update speed calculations
    fn update_speed(&mut self) {
        let now = Instant::now();
        let duration = now.duration_since(self.last_update);
        
        if duration >= Duration::from_secs(1) {
            // Calculate speed based on bytes transferred in the last second
            // This is a simplified calculation - in practice, you'd want a sliding window
            self.upload_speed = (self.upload_bytes as f64 / duration.as_secs_f64()) as u64;
            self.download_speed = (self.download_bytes as f64 / duration.as_secs_f64()) as u64;
            self.last_update = now;
        }
    }

    /// Get total bytes transferred
    pub fn total_bytes(&self) -> u64 {
        self.upload_bytes + self.download_bytes
    }

    /// Get connection success rate
    pub fn success_rate(&self) -> f64 {
        if self.total_connections == 0 {
            0.0
        } else {
            (self.total_connections - self.failed_connections) as f64 / self.total_connections as f64
        }
    }
}

/// System-wide statistics
#[derive(Debug, Clone, Default)]
pub struct SystemStats {
    /// Memory usage in bytes
    pub memory_usage: u64,
    /// CPU usage percentage
    pub cpu_usage: f64,
    /// Number of goroutines/tasks
    pub goroutines: u32,
    /// Uptime in seconds
    pub uptime: u64,
    /// System load average
    pub load_average: f64,
}

/// Performance metrics
#[derive(Debug, Clone, Default)]
pub struct PerformanceMetrics {
    /// Average connection establishment time
    pub avg_connection_time: Duration,
    /// Average request latency
    pub avg_latency: Duration,
    /// 95th percentile latency
    pub p95_latency: Duration,
    /// 99th percentile latency
    pub p99_latency: Duration,
    /// Requests per second
    pub requests_per_second: f64,
    /// Error rate percentage
    pub error_rate: f64,
}

/// Statistics event types
#[derive(Debug, Clone)]
pub enum StatsEvent {
    /// Traffic update for a specific adapter
    TrafficUpdate {
        adapter: String,
        stats: TrafficStats,
    },
    /// Connection established
    ConnectionEstablished {
        adapter: String,
        remote_addr: String,
        timestamp: SystemTime,
    },
    /// Connection closed
    ConnectionClosed {
        adapter: String,
        remote_addr: String,
        duration: Duration,
        upload_bytes: u64,
        download_bytes: u64,
    },
    /// Error occurred
    Error {
        adapter: String,
        error: String,
        timestamp: SystemTime,
    },
    /// System metrics update
    SystemUpdate {
        stats: SystemStats,
    },
}

/// Statistics manager
pub struct StatsManager {
    /// Per-adapter traffic statistics
    adapter_stats: Arc<RwLock<HashMap<String, TrafficStats>>>,
    /// System statistics
    system_stats: Arc<RwLock<SystemStats>>,
    /// Performance metrics
    performance_metrics: Arc<RwLock<PerformanceMetrics>>,
    /// Event broadcaster
    event_sender: broadcast::Sender<StatsEvent>,
    /// Start time for uptime calculation
    start_time: Instant,
}

impl StatsManager {
    /// Create a new statistics manager
    pub fn new() -> Self {
        let (event_sender, _) = broadcast::channel(1000);
        
        Self {
            adapter_stats: Arc::new(RwLock::new(HashMap::new())),
            system_stats: Arc::new(RwLock::new(SystemStats::default())),
            performance_metrics: Arc::new(RwLock::new(PerformanceMetrics::default())),
            event_sender,
            start_time: Instant::now(),
        }
    }

    /// Get traffic statistics for an adapter
    pub fn get_adapter_stats(&self, adapter: &str) -> Option<TrafficStats> {
        self.adapter_stats.read().ok()?.get(adapter).cloned()
    }

    /// Get all adapter statistics
    pub fn get_all_adapter_stats(&self) -> HashMap<String, TrafficStats> {
        self.adapter_stats.read().unwrap_or_else(|_| panic!("Lock poisoned")).clone()
    }

    /// Update traffic statistics for an adapter
    pub fn update_traffic(&self, adapter: &str, upload_bytes: u64, download_bytes: u64) {
        if let Ok(mut stats) = self.adapter_stats.write() {
            let adapter_stats = stats.entry(adapter.to_string()).or_insert_with(TrafficStats::new);
            
            if upload_bytes > 0 {
                adapter_stats.add_upload(upload_bytes);
            }
            if download_bytes > 0 {
                adapter_stats.add_download(download_bytes);
            }

            // Send event
            let _ = self.event_sender.send(StatsEvent::TrafficUpdate {
                adapter: adapter.to_string(),
                stats: adapter_stats.clone(),
            });
        }
    }

    /// Record connection establishment
    pub fn connection_established(&self, adapter: &str, remote_addr: &str) {
        if let Ok(mut stats) = self.adapter_stats.write() {
            let adapter_stats = stats.entry(adapter.to_string()).or_insert_with(TrafficStats::new);
            adapter_stats.add_connection();
        }

        let _ = self.event_sender.send(StatsEvent::ConnectionEstablished {
            adapter: adapter.to_string(),
            remote_addr: remote_addr.to_string(),
            timestamp: SystemTime::now(),
        });
    }

    /// Record connection closure
    pub fn connection_closed(&self, adapter: &str, remote_addr: &str, duration: Duration, upload_bytes: u64, download_bytes: u64) {
        if let Ok(mut stats) = self.adapter_stats.write() {
            let adapter_stats = stats.entry(adapter.to_string()).or_insert_with(TrafficStats::new);
            adapter_stats.remove_connection();
        }

        let _ = self.event_sender.send(StatsEvent::ConnectionClosed {
            adapter: adapter.to_string(),
            remote_addr: remote_addr.to_string(),
            duration,
            upload_bytes,
            download_bytes,
        });
    }

    /// Record connection failure
    pub fn connection_failed(&self, adapter: &str, error: &str) {
        if let Ok(mut stats) = self.adapter_stats.write() {
            let adapter_stats = stats.entry(adapter.to_string()).or_insert_with(TrafficStats::new);
            adapter_stats.add_failed_connection();
        }

        let _ = self.event_sender.send(StatsEvent::Error {
            adapter: adapter.to_string(),
            error: error.to_string(),
            timestamp: SystemTime::now(),
        });
    }

    /// Get system statistics
    pub fn get_system_stats(&self) -> SystemStats {
        let mut stats = self.system_stats.read().unwrap_or_else(|_| panic!("Lock poisoned")).clone();
        stats.uptime = self.start_time.elapsed().as_secs();
        stats
    }

    /// Update system statistics
    pub fn update_system_stats(&self, stats: SystemStats) {
        if let Ok(mut system_stats) = self.system_stats.write() {
            *system_stats = stats.clone();
        }

        let _ = self.event_sender.send(StatsEvent::SystemUpdate { stats });
    }

    /// Get performance metrics
    pub fn get_performance_metrics(&self) -> PerformanceMetrics {
        self.performance_metrics.read().unwrap_or_else(|_| panic!("Lock poisoned")).clone()
    }

    /// Subscribe to statistics events
    pub fn subscribe(&self) -> broadcast::Receiver<StatsEvent> {
        self.event_sender.subscribe()
    }

    /// Get total statistics across all adapters
    pub fn get_total_stats(&self) -> TrafficStats {
        let stats = self.adapter_stats.read().unwrap_or_else(|_| panic!("Lock poisoned"));
        let mut total = TrafficStats::new();

        for adapter_stats in stats.values() {
            total.upload_bytes += adapter_stats.upload_bytes;
            total.download_bytes += adapter_stats.download_bytes;
            total.connections += adapter_stats.connections;
            total.total_connections += adapter_stats.total_connections;
            total.failed_connections += adapter_stats.failed_connections;
        }

        total.update_speed();
        total
    }

    /// Reset statistics for an adapter
    pub fn reset_adapter_stats(&self, adapter: &str) {
        if let Ok(mut stats) = self.adapter_stats.write() {
            stats.remove(adapter);
        }
    }

    /// Reset all statistics
    pub fn reset_all_stats(&self) {
        if let Ok(mut stats) = self.adapter_stats.write() {
            stats.clear();
        }
    }

    /// Start background statistics collection
    pub fn start_background_collection(&self, interval: Duration) {
        let system_stats = self.system_stats.clone();
        let event_sender = self.event_sender.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(interval);
            loop {
                interval.tick().await;
                
                // Collect system metrics (simplified)
                let stats = SystemStats {
                    memory_usage: get_memory_usage(),
                    cpu_usage: get_cpu_usage(),
                    goroutines: get_goroutine_count(),
                    uptime: 0, // Will be set by get_system_stats
                    load_average: get_load_average(),
                };

                if let Ok(mut system_stats) = system_stats.write() {
                    *system_stats = stats.clone();
                }

                let _ = event_sender.send(StatsEvent::SystemUpdate { stats });
            }
        });
    }
}

impl Default for StatsManager {
    fn default() -> Self {
        Self::new()
    }
}

// Placeholder functions for system metrics collection
// In a real implementation, these would use system APIs
fn get_memory_usage() -> u64 {
    // Placeholder - would use system APIs to get actual memory usage
    0
}

fn get_cpu_usage() -> f64 {
    // Placeholder - would use system APIs to get actual CPU usage
    0.0
}

fn get_goroutine_count() -> u32 {
    // Placeholder - would count active tasks/threads
    0
}

fn get_load_average() -> f64 {
    // Placeholder - would get system load average
    0.0
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_traffic_stats() {
        let mut stats = TrafficStats::new();
        assert_eq!(stats.upload_bytes, 0);
        assert_eq!(stats.download_bytes, 0);
        assert_eq!(stats.connections, 0);

        stats.add_upload(1024);
        stats.add_download(2048);
        stats.add_connection();

        assert_eq!(stats.upload_bytes, 1024);
        assert_eq!(stats.download_bytes, 2048);
        assert_eq!(stats.total_bytes(), 3072);
        assert_eq!(stats.connections, 1);
        assert_eq!(stats.total_connections, 1);
    }

    #[test]
    fn test_stats_manager() {
        let manager = StatsManager::new();
        
        manager.update_traffic("test", 1024, 2048);
        
        let stats = manager.get_adapter_stats("test").unwrap();
        assert_eq!(stats.upload_bytes, 1024);
        assert_eq!(stats.download_bytes, 2048);
    }

    #[test]
    fn test_success_rate() {
        let mut stats = TrafficStats::new();
        stats.total_connections = 10;
        stats.failed_connections = 2;
        
        assert_eq!(stats.success_rate(), 0.8);
    }
}
