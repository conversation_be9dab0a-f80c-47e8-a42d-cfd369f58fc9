//! Clash API implementation
//!
//! This module provides Clash-compatible API for external clients.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

use crate::experimental::ExperimentalFeature;

/// Clash connection information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ClashConnection {
    /// Connection ID
    pub id: String,

    /// Metadata
    pub metadata: ClashConnectionMetadata,

    /// Upload bytes
    pub upload: u64,

    /// Download bytes
    pub download: u64,

    /// Start time
    pub start: String,

    /// Chain of proxies
    pub chains: Vec<String>,

    /// Rule used
    pub rule: String,

    /// Rule payload
    pub rule_payload: String,
}

/// Clash connection metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClashConnectionMetadata {
    /// Network type (tcp/udp)
    pub network: String,

    /// Connection type
    pub r#type: String,

    /// Source IP
    pub source_ip: String,

    /// Destination IP
    pub destination_ip: String,

    /// Source port
    pub source_port: String,

    /// Destination port
    pub destination_port: String,

    /// Host (for HTTP)
    pub host: Option<String>,

    /// DNS mode
    pub dns_mode: Option<String>,

    /// Process path
    pub process_path: Option<String>,
}

/// Clash traffic information
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ClashTraffic {
    /// Upload speed (bytes/s)
    pub up: u64,

    /// Download speed (bytes/s)
    pub down: u64,
}

/// Clash proxy group
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClashProxyGroup {
    /// Group name
    pub name: String,

    /// Group type (select, url-test, fallback, load-balance)
    pub r#type: String,

    /// Proxies in the group
    pub proxies: Vec<String>,

    /// Currently selected proxy
    pub now: Option<String>,

    /// URL for testing
    pub url: Option<String>,

    /// Test interval
    pub interval: Option<u64>,

    /// Test timeout
    pub timeout: Option<u64>,

    /// Load balance strategy
    pub strategy: Option<String>,
}

/// Clash proxy information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClashProxy {
    /// Proxy name
    pub name: String,

    /// Proxy type
    pub r#type: String,

    /// Server address
    pub server: Option<String>,

    /// Server port
    pub port: Option<u16>,

    /// History of delay tests
    pub history: Vec<ClashDelayHistory>,

    /// Alive status
    pub alive: Option<bool>,

    /// Extra proxy-specific fields
    #[serde(flatten)]
    pub extra: HashMap<String, serde_json::Value>,
}

/// Clash delay history
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClashDelayHistory {
    /// Test time
    pub time: String,

    /// Delay in milliseconds
    pub delay: u64,

    /// Mean deviation
    pub mean_deviation: Option<u64>,
}

/// Clash API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClashApiConfig {
    /// External controller address
    pub external_controller: String,
    
    /// External UI path
    pub external_ui: Option<String>,
    
    /// Secret for authentication
    pub secret: Option<String>,
    
    /// Default mode
    pub default_mode: Option<String>,
    
    /// Store selected outbound
    pub store_selected: Option<bool>,
    
    /// Cache file path
    pub cache_file: Option<String>,
}

impl Default for ClashApiConfig {
    fn default() -> Self {
        Self {
            external_controller: "127.0.0.1:9090".to_string(),
            external_ui: None,
            secret: None,
            default_mode: Some("rule".to_string()),
            store_selected: Some(true),
            cache_file: None,
        }
    }
}

/// Clash API server (alias for compatibility)
pub type ClashApiServer = ClashApi;

/// Clash API service
pub struct ClashApi {
    config: ClashApiConfig,
    enabled: bool,

    /// HTTP server handle
    server_handle: std::sync::Mutex<Option<tokio::task::JoinHandle<()>>>,

    /// Current connections
    connections: Arc<RwLock<HashMap<String, ClashConnection>>>,

    /// Traffic statistics
    traffic_stats: Arc<RwLock<ClashTraffic>>,

    /// Proxy groups
    proxy_groups: Arc<RwLock<HashMap<String, ClashProxyGroup>>>,

    /// Proxies information
    proxies: Arc<RwLock<HashMap<String, ProxyInfo>>>,
}

impl ClashApi {
    /// Create a new Clash API instance
    pub fn new(config: ClashApiConfig) -> Self {
        Self {
            config,
            enabled: true,
            server_handle: std::sync::Mutex::new(None),
            connections: Arc::new(RwLock::new(HashMap::new())),
            traffic_stats: Arc::new(RwLock::new(ClashTraffic::default())),
            proxy_groups: Arc::new(RwLock::new(HashMap::new())),
            proxies: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// Get external controller address
    pub fn external_controller(&self) -> &str {
        &self.config.external_controller
    }
    
    /// Get secret
    pub fn secret(&self) -> Option<&str> {
        self.config.secret.as_deref()
    }
    
    /// Add proxy information
    pub async fn add_proxy(&self, name: String, info: ProxyInfo) {
        self.proxies.write().await.insert(name, info);
    }
    
    /// Remove proxy
    pub async fn remove_proxy(&self, name: &str) -> Option<ProxyInfo> {
        self.proxies.write().await.remove(name)
    }
    
    /// Get all proxies
    pub async fn get_proxies(&self) -> HashMap<String, ProxyInfo> {
        self.proxies.read().await.clone()
    }
    
    /// Add connection
    pub async fn add_connection(&self, conn: ConnectionInfo) {
        let clash_conn = ClashConnection {
            id: conn.id.clone(),
            metadata: ClashConnectionMetadata {
                network: conn.metadata.network,
                r#type: conn.metadata.r#type,
                source_ip: conn.metadata.source_ip,
                destination_ip: conn.metadata.destination_ip,
                source_port: conn.metadata.source_port,
                destination_port: conn.metadata.destination_port,
                host: Some(conn.metadata.host),
                dns_mode: Some(conn.metadata.dns_mode),
                process_path: Some(conn.metadata.process_path),
            },
            upload: conn.upload,
            download: conn.download,
            start: conn.start,
            chains: conn.chains,
            rule: conn.rule,
            rule_payload: conn.rule_payload,
        };
        self.connections.write().await.insert(conn.id, clash_conn);
    }
    
    /// Get connections
    pub async fn get_connections(&self) -> Vec<ClashConnection> {
        self.connections.read().await.values().cloned().collect()
    }
    
    /// Clear connections
    pub async fn clear_connections(&self) {
        self.connections.write().await.clear();
    }
    
    /// Start HTTP server
    async fn start_server(&self) -> Result<(), String> {
        // This would start an HTTP server with Clash-compatible endpoints
        // For now, we'll just simulate it
        Ok(())
    }
    
    /// Stop HTTP server
    async fn stop_server(&self) -> Result<(), String> {
        if let Some(handle) = self.server_handle.lock().unwrap().take() {
            handle.abort();
        }
        Ok(())
    }
}

impl ExperimentalFeature for ClashApi {
    fn feature_name(&self) -> &str {
        "clash_api"
    }
    
    fn is_enabled(&self) -> bool {
        self.enabled
    }
    
    fn initialize(&mut self) -> Result<(), String> {
        if !self.enabled {
            return Ok(());
        }
        
        // Initialize Clash API server
        // This would set up HTTP routes and handlers
        Ok(())
    }
    
    fn cleanup(&mut self) -> Result<(), String> {
        if !self.enabled {
            return Ok(());
        }
        
        // Cleanup resources
        Ok(())
    }
}

/// Proxy information for Clash API
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyInfo {
    pub name: String,
    pub r#type: String,
    pub history: Vec<DelayInfo>,
    pub alive: bool,
    pub extra: HashMap<String, serde_json::Value>,
}

/// Delay information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DelayInfo {
    pub time: String,
    pub delay: u32,
}

/// Connection information for Clash API
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionInfo {
    pub id: String,
    pub metadata: ConnectionMetadata,
    pub upload: u64,
    pub download: u64,
    pub start: String,
    pub chains: Vec<String>,
    pub rule: String,
    pub rule_payload: String,
}

/// Connection metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionMetadata {
    pub network: String,
    pub r#type: String,
    pub source_ip: String,
    pub destination_ip: String,
    pub source_port: String,
    pub destination_port: String,
    pub host: String,
    pub dns_mode: String,
    pub process_path: String,
    pub special_proxy: String,
}
