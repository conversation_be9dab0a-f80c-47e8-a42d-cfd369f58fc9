//! Debug API for development and troubleshooting
//!
//! This module provides a debug API server that exposes internal state
//! and debugging information for development and troubleshooting purposes.

use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

/// Debug API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DebugApiConfig {
    /// Listen address
    pub listen: SocketAddr,
    
    /// Enable debug API
    pub enabled: bool,
    
    /// API secret for authentication
    pub secret: Option<String>,
    
    /// Enable CORS
    pub cors: bool,
    
    /// Debug endpoints to enable
    pub endpoints: Vec<String>,
    
    /// Rate limiting
    pub rate_limit: Option<RateLimitConfig>,
}

/// Rate limiting configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// Requests per minute
    pub requests_per_minute: u32,
    
    /// Burst size
    pub burst_size: u32,
}

/// Debug information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DebugInfo {
    /// System information
    pub system: SystemInfo,
    
    /// Runtime information
    pub runtime: RuntimeInfo,
    
    /// Performance metrics
    pub performance: PerformanceInfo,
    
    /// Configuration snapshot
    pub config: ConfigInfo,
    
    /// Connection information
    pub connections: ConnectionInfo,
    
    /// Memory information
    pub memory: MemoryInfo,
}

/// System information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfo {
    /// Operating system
    pub os: String,
    
    /// Architecture
    pub arch: String,
    
    /// CPU count
    pub cpu_count: usize,
    
    /// Total memory (MB)
    pub total_memory: u64,
    
    /// Available memory (MB)
    pub available_memory: u64,
    
    /// System uptime (seconds)
    pub uptime: u64,
}

/// Runtime information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuntimeInfo {
    /// Application version
    pub version: String,
    
    /// Build timestamp
    pub build_time: String,
    
    /// Git commit hash
    pub git_commit: String,
    
    /// Start time
    pub start_time: SystemTime,
    
    /// Runtime duration (seconds)
    pub runtime_duration: u64,
    
    /// Thread count
    pub thread_count: usize,
}

/// Performance information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceInfo {
    /// CPU usage percentage
    pub cpu_usage: f64,
    
    /// Memory usage (MB)
    pub memory_usage: u64,
    
    /// Network I/O (bytes/sec)
    pub network_io: NetworkIoInfo,
    
    /// Request statistics
    pub requests: RequestStats,
    
    /// Error statistics
    pub errors: ErrorStats,
}

/// Network I/O information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkIoInfo {
    /// Bytes received per second
    pub bytes_received_per_sec: u64,
    
    /// Bytes sent per second
    pub bytes_sent_per_sec: u64,
    
    /// Packets received per second
    pub packets_received_per_sec: u64,
    
    /// Packets sent per second
    pub packets_sent_per_sec: u64,
}

/// Request statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RequestStats {
    /// Total requests
    pub total_requests: u64,
    
    /// Requests per second
    pub requests_per_second: f64,
    
    /// Average response time (ms)
    pub avg_response_time: f64,
    
    /// Success rate percentage
    pub success_rate: f64,
}

/// Error statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorStats {
    /// Total errors
    pub total_errors: u64,
    
    /// Error rate percentage
    pub error_rate: f64,
    
    /// Errors by type
    pub errors_by_type: HashMap<String, u64>,
    
    /// Recent errors
    pub recent_errors: Vec<ErrorInfo>,
}

/// Error information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorInfo {
    /// Error timestamp
    pub timestamp: SystemTime,
    
    /// Error type
    pub error_type: String,
    
    /// Error message
    pub message: String,
    
    /// Error context
    pub context: HashMap<String, String>,
}

/// Configuration information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigInfo {
    /// Configuration file path
    pub config_file: String,
    
    /// Configuration hash
    pub config_hash: String,
    
    /// Last modified time
    pub last_modified: SystemTime,
    
    /// Configuration size (bytes)
    pub size: u64,
    
    /// Validation status
    pub valid: bool,
}

/// Connection information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionInfo {
    /// Active connections
    pub active_connections: u64,
    
    /// Total connections
    pub total_connections: u64,
    
    /// Connections by protocol
    pub connections_by_protocol: HashMap<String, u64>,
    
    /// Connections by outbound
    pub connections_by_outbound: HashMap<String, u64>,
    
    /// Connection pool status
    pub pool_status: PoolStatus,
}

/// Pool status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStatus {
    /// Pool size
    pub pool_size: usize,
    
    /// Active connections in pool
    pub active_in_pool: usize,
    
    /// Idle connections in pool
    pub idle_in_pool: usize,
    
    /// Pool hit rate
    pub hit_rate: f64,
}

/// Memory information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryInfo {
    /// Heap allocated (bytes)
    pub heap_allocated: u64,
    
    /// Heap in use (bytes)
    pub heap_in_use: u64,
    
    /// Stack in use (bytes)
    pub stack_in_use: u64,
    
    /// GC statistics
    pub gc_stats: GcStats,
}

/// Garbage collection statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GcStats {
    /// Number of GC cycles
    pub gc_cycles: u64,
    
    /// Total GC time (ms)
    pub total_gc_time: u64,
    
    /// Average GC time (ms)
    pub avg_gc_time: f64,
    
    /// Memory freed by GC (bytes)
    pub memory_freed: u64,
}

/// Debug API server
pub struct DebugApiServer {
    /// Configuration
    config: DebugApiConfig,
    
    /// Server handle
    server_handle: Option<tokio::task::JoinHandle<()>>,
    
    /// Debug information cache
    debug_info: Arc<RwLock<Option<DebugInfo>>>,
    
    /// Request statistics
    request_stats: Arc<RwLock<RequestStats>>,
    
    /// Error statistics
    error_stats: Arc<RwLock<ErrorStats>>,
}

impl DebugApiServer {
    /// Create a new debug API server
    pub fn new(config: DebugApiConfig) -> Self {
        Self {
            config,
            server_handle: None,
            debug_info: Arc::new(RwLock::new(None)),
            request_stats: Arc::new(RwLock::new(RequestStats {
                total_requests: 0,
                requests_per_second: 0.0,
                avg_response_time: 0.0,
                success_rate: 100.0,
            })),
            error_stats: Arc::new(RwLock::new(ErrorStats {
                total_errors: 0,
                error_rate: 0.0,
                errors_by_type: HashMap::new(),
                recent_errors: Vec::new(),
            })),
        }
    }
    
    /// Start the debug API server
    pub async fn start(&mut self) -> Result<(), String> {
        if !self.config.enabled {
            return Ok(());
        }
        
        println!("🐛 Starting Debug API server on {}", self.config.listen);
        
        // Start debug info collection task
        self.start_debug_collection().await;
        
        // Start HTTP server (simplified implementation)
        let listen_addr = self.config.listen;
        let debug_info = Arc::clone(&self.debug_info);
        let request_stats = Arc::clone(&self.request_stats);
        let error_stats = Arc::clone(&self.error_stats);
        let endpoints = self.config.endpoints.clone();
        
        let server_task = tokio::spawn(async move {
            // In a real implementation, this would start an HTTP server
            // For now, we'll simulate the server running
            println!("Debug API server listening on {}", listen_addr);
            
            let mut interval = tokio::time::interval(Duration::from_secs(5));
            loop {
                interval.tick().await;
                
                // Update debug information
                let debug_info_data = Self::collect_debug_info().await;
                *debug_info.write().await = Some(debug_info_data);
                
                // Simulate some requests
                let mut stats = request_stats.write().await;
                stats.total_requests += 1;
                stats.requests_per_second = 0.2; // 1 request per 5 seconds
                stats.avg_response_time = 10.0; // 10ms average
            }
        });
        
        self.server_handle = Some(server_task);
        
        Ok(())
    }
    
    /// Stop the debug API server
    pub async fn stop(&self) {
        if let Some(ref handle) = self.server_handle {
            handle.abort();
        }
        
        println!("🐛 Debug API server stopped");
    }
    
    /// Start debug information collection
    async fn start_debug_collection(&self) {
        // In a real implementation, this would start background tasks
        // to collect system metrics, performance data, etc.
        println!("🔍 Started debug information collection");
    }
    
    /// Collect debug information
    async fn collect_debug_info() -> DebugInfo {
        DebugInfo {
            system: SystemInfo {
                os: std::env::consts::OS.to_string(),
                arch: std::env::consts::ARCH.to_string(),
                cpu_count: num_cpus::get(),
                total_memory: 8192, // 8GB (mock)
                available_memory: 4096, // 4GB (mock)
                uptime: 3600, // 1 hour (mock)
            },
            runtime: RuntimeInfo {
                version: env!("CARGO_PKG_VERSION").to_string(),
                build_time: "2024-01-01T00:00:00Z".to_string(),
                git_commit: "abc123def456".to_string(),
                start_time: SystemTime::now() - Duration::from_secs(3600),
                runtime_duration: 3600,
                thread_count: 8,
            },
            performance: PerformanceInfo {
                cpu_usage: 25.5,
                memory_usage: 512,
                network_io: NetworkIoInfo {
                    bytes_received_per_sec: 1024000,
                    bytes_sent_per_sec: 2048000,
                    packets_received_per_sec: 1000,
                    packets_sent_per_sec: 1500,
                },
                requests: RequestStats {
                    total_requests: 10000,
                    requests_per_second: 100.0,
                    avg_response_time: 15.5,
                    success_rate: 99.5,
                },
                errors: ErrorStats {
                    total_errors: 50,
                    error_rate: 0.5,
                    errors_by_type: {
                        let mut map = HashMap::new();
                        map.insert("network".to_string(), 30);
                        map.insert("timeout".to_string(), 15);
                        map.insert("auth".to_string(), 5);
                        map
                    },
                    recent_errors: Vec::new(),
                },
            },
            config: ConfigInfo {
                config_file: "/etc/sing-box/config.json".to_string(),
                config_hash: "sha256:abc123...".to_string(),
                last_modified: SystemTime::now() - Duration::from_secs(1800),
                size: 4096,
                valid: true,
            },
            connections: ConnectionInfo {
                active_connections: 150,
                total_connections: 10000,
                connections_by_protocol: {
                    let mut map = HashMap::new();
                    map.insert("tcp".to_string(), 100);
                    map.insert("udp".to_string(), 50);
                    map
                },
                connections_by_outbound: {
                    let mut map = HashMap::new();
                    map.insert("direct".to_string(), 80);
                    map.insert("proxy".to_string(), 70);
                    map
                },
                pool_status: PoolStatus {
                    pool_size: 50,
                    active_in_pool: 30,
                    idle_in_pool: 20,
                    hit_rate: 85.0,
                },
            },
            memory: MemoryInfo {
                heap_allocated: 536870912, // 512MB
                heap_in_use: 268435456,    // 256MB
                stack_in_use: 8388608,     // 8MB
                gc_stats: GcStats {
                    gc_cycles: 100,
                    total_gc_time: 500,
                    avg_gc_time: 5.0,
                    memory_freed: 1073741824, // 1GB
                },
            },
        }
    }
    
    /// Get current debug information
    pub async fn get_debug_info(&self) -> Option<DebugInfo> {
        self.debug_info.read().await.clone()
    }
    
    /// Get request statistics
    pub async fn get_request_stats(&self) -> RequestStats {
        self.request_stats.read().await.clone()
    }
    
    /// Get error statistics
    pub async fn get_error_stats(&self) -> ErrorStats {
        self.error_stats.read().await.clone()
    }
    
    /// Record an error
    pub async fn record_error(&self, error_type: String, message: String, context: HashMap<String, String>) {
        let mut stats = self.error_stats.write().await;
        
        stats.total_errors += 1;
        *stats.errors_by_type.entry(error_type.clone()).or_insert(0) += 1;
        
        let error_info = ErrorInfo {
            timestamp: SystemTime::now(),
            error_type,
            message,
            context,
        };
        
        stats.recent_errors.push(error_info);
        
        // Keep only recent errors (last 100)
        if stats.recent_errors.len() > 100 {
            stats.recent_errors.remove(0);
        }
        
        // Update error rate (simplified calculation)
        let total_requests = self.request_stats.read().await.total_requests;
        if total_requests > 0 {
            stats.error_rate = (stats.total_errors as f64 / total_requests as f64) * 100.0;
        }
    }
}

impl Default for DebugApiConfig {
    fn default() -> Self {
        Self {
            listen: "127.0.0.1:9090".parse().unwrap(),
            enabled: false,
            secret: None,
            cors: true,
            endpoints: vec![
                "info".to_string(),
                "stats".to_string(),
                "config".to_string(),
                "connections".to_string(),
                "memory".to_string(),
            ],
            rate_limit: Some(RateLimitConfig {
                requests_per_minute: 60,
                burst_size: 10,
            }),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_debug_api_config_default() {
        let config = DebugApiConfig::default();
        
        assert!(!config.enabled);
        assert!(config.cors);
        assert_eq!(config.endpoints.len(), 5);
        assert!(config.rate_limit.is_some());
    }
    
    #[tokio::test]
    async fn test_debug_api_server_creation() {
        let config = DebugApiConfig::default();
        let server = DebugApiServer::new(config);
        
        let debug_info = server.get_debug_info().await;
        assert!(debug_info.is_none());
        
        let stats = server.get_request_stats().await;
        assert_eq!(stats.total_requests, 0);
    }
    
    #[tokio::test]
    async fn test_error_recording() {
        let config = DebugApiConfig::default();
        let server = DebugApiServer::new(config);
        
        let mut context = HashMap::new();
        context.insert("source".to_string(), "test".to_string());
        
        server.record_error(
            "test_error".to_string(),
            "Test error message".to_string(),
            context,
        ).await;
        
        let error_stats = server.get_error_stats().await;
        assert_eq!(error_stats.total_errors, 1);
        assert_eq!(error_stats.recent_errors.len(), 1);
        assert!(error_stats.errors_by_type.contains_key("test_error"));
    }
    
    #[tokio::test]
    async fn test_debug_info_collection() {
        let debug_info = DebugApiServer::collect_debug_info().await;
        
        assert!(!debug_info.system.os.is_empty());
        assert!(!debug_info.runtime.version.is_empty());
        assert!(debug_info.performance.cpu_usage >= 0.0);
        assert!(debug_info.config.valid);
        assert!(debug_info.connections.active_connections > 0);
    }
}
