//! Experimental features module
//!
//! This module contains experimental features that may not be stable
//! or fully implemented yet.

use serde::{Deserialize, Serialize};

pub mod clash_api;
pub mod v2ray_api;
pub mod cache_file;
pub mod debug_api;

/// Experimental configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct ExperimentalConfig {
    /// Clash API configuration
    pub clash_api: Option<clash_api::ClashApiConfig>,

    /// V2Ray API configuration
    pub v2ray_api: Option<v2ray_api::V2RayApiConfig>,

    /// Cache file configuration
    pub cache_file: Option<cache_file::CacheFileConfig>,

    /// Debug API configuration
    pub debug_api: Option<debug_api::DebugApiConfig>,

    /// Enable experimental features warning
    pub show_warning: bool,

    /// Feature flags
    pub feature_flags: std::collections::HashMap<String, bool>,
}

/// Experimental feature trait
pub trait ExperimentalFeature: Send + Sync {
    /// Get the feature name
    fn feature_name(&self) -> &str;
    
    /// Check if the feature is enabled
    fn is_enabled(&self) -> bool;
    
    /// Initialize the feature
    fn initialize(&mut self) -> Result<(), String>;
    
    /// Cleanup the feature
    fn cleanup(&mut self) -> Result<(), String>;
}

/// Experimental feature manager
pub struct ExperimentalManager {
    /// Configuration
    config: ExperimentalConfig,

    /// Registered features
    features: Vec<Box<dyn ExperimentalFeature>>,

    /// Clash API server
    clash_api: Option<std::sync::Arc<clash_api::ClashApiServer>>,

    /// V2Ray API server
    v2ray_api: Option<std::sync::Arc<v2ray_api::V2RayApiServer>>,

    /// Cache file manager
    cache_file: Option<std::sync::Arc<cache_file::CacheFileManager>>,

    /// Debug API server
    debug_api: Option<std::sync::Arc<debug_api::DebugApiServer>>,
}

impl ExperimentalManager {
    /// Create a new experimental manager
    pub fn new(config: ExperimentalConfig) -> Self {
        if config.show_warning {
            println!("⚠️  WARNING: Experimental features are enabled!");
            println!("   These features are not stable and may change or be removed in future versions.");
        }

        Self {
            config,
            features: Vec::new(),
            clash_api: None,
            v2ray_api: None,
            cache_file: None,
            debug_api: None,
        }
    }
    
    /// Start experimental features
    pub async fn start(&mut self) -> Result<(), String> {
        // Start Clash API
        if let Some(ref clash_config) = self.config.clash_api {
            let mut clash_api = clash_api::ClashApi::new(clash_config.clone());
            clash_api.initialize()?;
            self.clash_api = Some(std::sync::Arc::new(clash_api));
            println!("✅ Clash API started");
        }

        // Start V2Ray API
        if let Some(ref v2ray_config) = self.config.v2ray_api {
            let mut v2ray_api = v2ray_api::V2RayApi::new(v2ray_config.clone());
            v2ray_api.initialize()?;
            self.v2ray_api = Some(std::sync::Arc::new(v2ray_api));
            println!("✅ V2Ray API started");
        }

        // Start cache file manager
        if let Some(ref cache_config) = self.config.cache_file {
            let mut cache_file = cache_file::CacheFile::new(cache_config.clone());
            cache_file.initialize()?;
            self.cache_file = Some(std::sync::Arc::new(cache_file));
            println!("✅ Cache file manager started");
        }

        // Start debug API
        if let Some(ref debug_config) = self.config.debug_api {
            let debug_api = debug_api::DebugApiServer::new(debug_config.clone());
            // Debug API will be started when needed
            self.debug_api = Some(std::sync::Arc::new(debug_api));
            println!("✅ Debug API initialized");
        }

        println!("🧪 Experimental features manager started");
        Ok(())
    }

    /// Stop experimental features
    pub async fn stop(&mut self) {
        if self.clash_api.is_some() {
            println!("🛑 Stopping Clash API");
        }

        if self.v2ray_api.is_some() {
            println!("🛑 Stopping V2Ray API");
        }

        if self.cache_file.is_some() {
            println!("🛑 Stopping Cache File");
        }

        if self.debug_api.is_some() {
            println!("🛑 Stopping Debug API");
        }

        println!("🧪 Experimental features manager stopped");
    }

    /// Register an experimental feature
    pub fn register_feature(&mut self, feature: Box<dyn ExperimentalFeature>) {
        self.features.push(feature);
    }
    
    /// Initialize all enabled features
    pub fn initialize_all(&mut self) -> Result<(), String> {
        for feature in &mut self.features {
            if feature.is_enabled() {
                feature.initialize()?;
            }
        }
        Ok(())
    }
    
    /// Cleanup all features
    pub fn cleanup_all(&mut self) -> Result<(), String> {
        for feature in &mut self.features {
            if feature.is_enabled() {
                feature.cleanup()?;
            }
        }
        Ok(())
    }
    
    /// Get feature by name
    pub fn get_feature(&self, name: &str) -> Option<&dyn ExperimentalFeature> {
        self.features.iter()
            .find(|f| f.feature_name() == name)
            .map(|f| f.as_ref())
    }
    
    /// List all features
    pub fn list_features(&self) -> Vec<&str> {
        self.features.iter()
            .map(|f| f.feature_name())
            .collect()
    }
    
    /// List enabled features
    pub fn list_enabled_features(&self) -> Vec<&str> {
        self.features.iter()
            .filter(|f| f.is_enabled())
            .map(|f| f.feature_name())
            .collect()
    }
}

impl Default for ExperimentalManager {
    fn default() -> Self {
        Self::new(ExperimentalConfig::default())
    }
}
