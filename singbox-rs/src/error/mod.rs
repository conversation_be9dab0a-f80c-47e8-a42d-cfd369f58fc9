//! Error handling module for sing-box
//!
//! This module provides comprehensive error handling including basic error types,
//! production-grade error handling, and error recovery mechanisms.

use std::fmt;
use thiserror::Error;

pub mod production;

pub use production::{
    ProductionError, ProductionErrorHandler, ErrorContext, ErrorStats,
    ErrorSeverity, NetworkErrorCode, ProtocolErrorType, ThreatLevel,
    RecoveryStrategy, RetryDelay, CircuitBreakerConfig, FallbackAction,
};

/// Basic sing-box error types
#[derive(Debug, Error)]
pub enum SingBoxError {
    #[error("Configuration error: {0}")]
    Config(String),
    
    #[error("Network error: {0}")]
    Network(String),
    
    #[error("Protocol error: {0}")]
    Protocol(String),
    
    #[error("DNS error: {0}")]
    Dns(String),
    
    #[error("TLS error: {0}")]
    Tls(String),
    
    #[error("Authentication error: {0}")]
    Auth(String),
    
    #[error("Route error: {0}")]
    Route(String),
    
    #[error("Adapter error: {0}")]
    Adapter(String),
    
    #[error("Service error: {0}")]
    Service(String),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("JSON error: {0}")]
    Json(#[from] serde_json::Error),
    
    #[error("YAML error: {0}")]
    Yaml(#[from] serde_yaml::Error),
    
    #[error("Regex error: {0}")]
    Regex(#[from] regex::Error),
    
    #[error("Parse error: {0}")]
    Parse(String),
    
    #[error("Timeout error: {0}")]
    Timeout(String),
    
    #[error("Internal error: {0}")]
    Internal(String),
}

/// Result type alias for sing-box operations
pub type SingBoxResult<T> = Result<T, SingBoxError>;

/// Error conversion utilities
impl SingBoxError {
    /// Convert to production error with context
    pub fn to_production_error(&self, component: &str, _operation: &str) -> ProductionError {
        match self {
            SingBoxError::Config(msg) => ProductionError::Configuration {
                message: msg.clone(),
                component: component.to_string(),
                severity: ErrorSeverity::High,
                suggestion: None,
            },
            SingBoxError::Network(msg) => ProductionError::Network {
                message: msg.clone(),
                code: NetworkErrorCode::ConnectionTimeout, // Default
                recoverable: true,
                retry_after: Some(std::time::Duration::from_secs(5)),
            },
            SingBoxError::Protocol(msg) => ProductionError::Protocol {
                message: msg.clone(),
                protocol: "unknown".to_string(),
                error_type: ProtocolErrorType::InvalidMessage,
                recoverable: false,
            },
            SingBoxError::Auth(msg) => ProductionError::Authentication {
                message: msg.clone(),
                auth_type: "unknown".to_string(),
                retry_allowed: false,
            },
            SingBoxError::Service(msg) => ProductionError::Service {
                message: msg.clone(),
                service: component.to_string(),
                error_code: "UNKNOWN".to_string(),
                upstream_error: None,
            },
            _ => ProductionError::Service {
                message: self.to_string(),
                service: component.to_string(),
                error_code: "GENERIC".to_string(),
                upstream_error: None,
            },
        }
    }
    
    /// Check if error is recoverable
    pub fn is_recoverable(&self) -> bool {
        match self {
            SingBoxError::Network(_) => true,
            SingBoxError::Timeout(_) => true,
            SingBoxError::Service(_) => true,
            SingBoxError::Io(_) => true,
            _ => false,
        }
    }
    
    /// Get error category
    pub fn category(&self) -> &'static str {
        match self {
            SingBoxError::Config(_) => "configuration",
            SingBoxError::Network(_) => "network",
            SingBoxError::Protocol(_) => "protocol",
            SingBoxError::Dns(_) => "dns",
            SingBoxError::Tls(_) => "tls",
            SingBoxError::Auth(_) => "authentication",
            SingBoxError::Route(_) => "routing",
            SingBoxError::Adapter(_) => "adapter",
            SingBoxError::Service(_) => "service",
            SingBoxError::Io(_) => "io",
            SingBoxError::Json(_) => "serialization",
            SingBoxError::Yaml(_) => "serialization",
            SingBoxError::Regex(_) => "regex",
            SingBoxError::Parse(_) => "parsing",
            SingBoxError::Timeout(_) => "timeout",
            SingBoxError::Internal(_) => "internal",
        }
    }
}

/// Error context builder
pub struct ErrorContextBuilder {
    error_id: Option<String>,
    component: Option<String>,
    operation: Option<String>,
    user_context: Option<String>,
    request_id: Option<String>,
    metadata: std::collections::HashMap<String, String>,
}

impl ErrorContextBuilder {
    /// Create a new error context builder
    pub fn new() -> Self {
        Self {
            error_id: None,
            component: None,
            operation: None,
            user_context: None,
            request_id: None,
            metadata: std::collections::HashMap::new(),
        }
    }
    
    /// Set error ID
    pub fn error_id(mut self, id: String) -> Self {
        self.error_id = Some(id);
        self
    }
    
    /// Set component
    pub fn component(mut self, component: String) -> Self {
        self.component = Some(component);
        self
    }
    
    /// Set operation
    pub fn operation(mut self, operation: String) -> Self {
        self.operation = Some(operation);
        self
    }
    
    /// Set user context
    pub fn user_context(mut self, user_context: String) -> Self {
        self.user_context = Some(user_context);
        self
    }
    
    /// Set request ID
    pub fn request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }
    
    /// Add metadata
    pub fn metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }
    
    /// Build error context
    pub fn build(self) -> ErrorContext {
        ErrorContext {
            error_id: self.error_id.unwrap_or_else(|| uuid::Uuid::new_v4().to_string()),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            component: self.component.unwrap_or_else(|| "unknown".to_string()),
            operation: self.operation.unwrap_or_else(|| "unknown".to_string()),
            user_context: self.user_context,
            request_id: self.request_id,
            metadata: self.metadata,
            stack_trace: None,
            related_errors: Vec::new(),
        }
    }
}

impl Default for ErrorContextBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Convenience macros for error handling
#[macro_export]
macro_rules! config_error {
    ($msg:expr) => {
        $crate::error::SingBoxError::Config($msg.to_string())
    };
    ($fmt:expr, $($arg:tt)*) => {
        $crate::error::SingBoxError::Config(format!($fmt, $($arg)*))
    };
}

#[macro_export]
macro_rules! network_error {
    ($msg:expr) => {
        $crate::error::SingBoxError::Network($msg.to_string())
    };
    ($fmt:expr, $($arg:tt)*) => {
        $crate::error::SingBoxError::Network(format!($fmt, $($arg)*))
    };
}

#[macro_export]
macro_rules! protocol_error {
    ($msg:expr) => {
        $crate::error::SingBoxError::Protocol($msg.to_string())
    };
    ($fmt:expr, $($arg:tt)*) => {
        $crate::error::SingBoxError::Protocol(format!($fmt, $($arg)*))
    };
}

#[macro_export]
macro_rules! auth_error {
    ($msg:expr) => {
        $crate::error::SingBoxError::Auth($msg.to_string())
    };
    ($fmt:expr, $($arg:tt)*) => {
        $crate::error::SingBoxError::Auth(format!($fmt, $($arg)*))
    };
}

#[macro_export]
macro_rules! internal_error {
    ($msg:expr) => {
        $crate::error::SingBoxError::Internal($msg.to_string())
    };
    ($fmt:expr, $($arg:tt)*) => {
        $crate::error::SingBoxError::Internal(format!($fmt, $($arg)*))
    };
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_singbox_error_creation() {
        let error = SingBoxError::Config("Invalid configuration".to_string());
        assert_eq!(error.category(), "configuration");
        assert!(!error.is_recoverable());
    }
    
    #[test]
    fn test_error_conversion() {
        let error = SingBoxError::Network("Connection failed".to_string());
        let production_error = error.to_production_error("proxy", "connect");
        
        match production_error {
            ProductionError::Network { message, .. } => {
                assert_eq!(message, "Connection failed");
            },
            _ => panic!("Expected network error"),
        }
    }
    
    #[test]
    fn test_error_context_builder() {
        let context = ErrorContextBuilder::new()
            .component("test".to_string())
            .operation("test_op".to_string())
            .metadata("key".to_string(), "value".to_string())
            .build();
        
        assert_eq!(context.component, "test");
        assert_eq!(context.operation, "test_op");
        assert!(context.metadata.contains_key("key"));
    }
    
    #[test]
    fn test_error_macros() {
        let error = config_error!("Test config error");
        assert!(matches!(error, SingBoxError::Config(_)));
        
        let error = network_error!("Connection to {} failed", "example.com");
        assert!(matches!(error, SingBoxError::Network(_)));
        
        let error = protocol_error!("Invalid protocol version");
        assert!(matches!(error, SingBoxError::Protocol(_)));
    }
    
    #[test]
    fn test_error_recoverability() {
        assert!(SingBoxError::Network("test".to_string()).is_recoverable());
        assert!(SingBoxError::Timeout("test".to_string()).is_recoverable());
        assert!(!SingBoxError::Config("test".to_string()).is_recoverable());
        assert!(!SingBoxError::Auth("test".to_string()).is_recoverable());
    }
}
