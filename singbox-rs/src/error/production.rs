//! Production-grade error handling system
//!
//! This module provides comprehensive error handling including error classification,
//! recovery strategies, monitoring integration, and detailed error reporting.

use std::collections::HashMap;
use std::fmt;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use thiserror::Error;

/// Production error types with detailed classification
#[derive(Debug, Error, Clone, Serialize, Deserialize)]
pub enum ProductionError {
    #[error("Network error: {message} (code: {code})")]
    Network {
        message: String,
        code: NetworkErrorCode,
        recoverable: bool,
        retry_after: Option<Duration>,
    },
    
    #[error("Protocol error: {message} (protocol: {protocol})")]
    Protocol {
        message: String,
        protocol: String,
        error_type: ProtocolErrorType,
        recoverable: bool,
    },
    
    #[error("Configuration error: {message} (component: {component})")]
    Configuration {
        message: String,
        component: String,
        severity: ErrorSeverity,
        suggestion: Option<String>,
    },
    
    #[error("Authentication error: {message}")]
    Authentication {
        message: String,
        auth_type: String,
        retry_allowed: bool,
    },
    
    #[error("Resource error: {message} (resource: {resource})")]
    Resource {
        message: String,
        resource: String,
        resource_type: ResourceType,
        current_usage: Option<u64>,
        limit: Option<u64>,
    },
    
    #[error("Service error: {message} (service: {service})")]
    Service {
        message: String,
        service: String,
        error_code: String,
        upstream_error: Option<Box<ProductionError>>,
    },
    
    #[error("Security error: {message}")]
    Security {
        message: String,
        threat_level: ThreatLevel,
        action_taken: SecurityAction,
    },
    
    #[error("Performance error: {message}")]
    Performance {
        message: String,
        metric: String,
        current_value: f64,
        threshold: f64,
        impact: PerformanceImpact,
    },
}

/// Network error codes
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum NetworkErrorCode {
    ConnectionTimeout,
    ConnectionRefused,
    ConnectionReset,
    HostUnreachable,
    NetworkUnreachable,
    DnsResolutionFailed,
    TlsHandshakeFailed,
    CertificateError,
    ProxyError,
    BandwidthExceeded,
}

impl std::fmt::Display for NetworkErrorCode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            NetworkErrorCode::ConnectionTimeout => write!(f, "CONNECTION_TIMEOUT"),
            NetworkErrorCode::ConnectionRefused => write!(f, "CONNECTION_REFUSED"),
            NetworkErrorCode::ConnectionReset => write!(f, "CONNECTION_RESET"),
            NetworkErrorCode::HostUnreachable => write!(f, "HOST_UNREACHABLE"),
            NetworkErrorCode::NetworkUnreachable => write!(f, "NETWORK_UNREACHABLE"),
            NetworkErrorCode::DnsResolutionFailed => write!(f, "DNS_RESOLUTION_FAILED"),
            NetworkErrorCode::TlsHandshakeFailed => write!(f, "TLS_HANDSHAKE_FAILED"),
            NetworkErrorCode::CertificateError => write!(f, "CERTIFICATE_ERROR"),
            NetworkErrorCode::ProxyError => write!(f, "PROXY_ERROR"),
            NetworkErrorCode::BandwidthExceeded => write!(f, "BANDWIDTH_EXCEEDED"),
        }
    }
}

/// Protocol error types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ProtocolErrorType {
    InvalidMessage,
    UnsupportedVersion,
    AuthenticationFailed,
    HandshakeFailed,
    EncryptionError,
    CompressionError,
    FlowControlError,
    StreamError,
}

/// Error severity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Resource types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ResourceType {
    Memory,
    Cpu,
    Network,
    Disk,
    FileDescriptor,
    Connection,
    Thread,
}

/// Threat levels for security errors
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum ThreatLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// Security actions taken
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SecurityAction {
    Logged,
    Blocked,
    Quarantined,
    AlertSent,
    ServiceStopped,
}

/// Performance impact levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum PerformanceImpact {
    Negligible,
    Minor,
    Moderate,
    Severe,
    Critical,
}

/// Error context information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    /// Error ID for tracking
    pub error_id: String,
    
    /// Timestamp when error occurred
    pub timestamp: u64,
    
    /// Component where error occurred
    pub component: String,
    
    /// Operation being performed
    pub operation: String,
    
    /// User/session context
    pub user_context: Option<String>,
    
    /// Request/connection ID
    pub request_id: Option<String>,
    
    /// Additional metadata
    pub metadata: HashMap<String, String>,
    
    /// Stack trace (if available)
    pub stack_trace: Option<String>,
    
    /// Related error IDs
    pub related_errors: Vec<String>,
}

/// Error statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorStats {
    /// Total error count
    pub total_errors: u64,
    
    /// Errors by type
    pub errors_by_type: HashMap<String, u64>,
    
    /// Errors by severity
    pub errors_by_severity: HashMap<ErrorSeverity, u64>,
    
    /// Errors by component
    pub errors_by_component: HashMap<String, u64>,
    
    /// Error rate (errors per second)
    pub error_rate: f64,
    
    /// Recovery success rate
    pub recovery_rate: f64,
    
    /// Average recovery time
    pub avg_recovery_time: Duration,
}

/// Recovery strategy
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecoveryStrategy {
    /// Strategy name
    pub name: String,
    
    /// Maximum retry attempts
    pub max_retries: u32,
    
    /// Retry delay strategy
    pub retry_delay: RetryDelay,
    
    /// Circuit breaker configuration
    pub circuit_breaker: Option<CircuitBreakerConfig>,
    
    /// Fallback action
    pub fallback: Option<FallbackAction>,
    
    /// Recovery timeout
    pub timeout: Duration,
}

/// Retry delay strategies
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RetryDelay {
    /// Fixed delay
    Fixed(Duration),
    
    /// Exponential backoff
    Exponential {
        initial: Duration,
        multiplier: f64,
        max_delay: Duration,
    },
    
    /// Linear backoff
    Linear {
        initial: Duration,
        increment: Duration,
        max_delay: Duration,
    },
    
    /// Jittered exponential backoff
    JitteredExponential {
        initial: Duration,
        multiplier: f64,
        max_delay: Duration,
        jitter_factor: f64,
    },
}

/// Circuit breaker configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerConfig {
    /// Failure threshold to open circuit
    pub failure_threshold: u32,
    
    /// Success threshold to close circuit
    pub success_threshold: u32,
    
    /// Timeout before trying to close circuit
    pub timeout: Duration,
    
    /// Window size for failure counting
    pub window_size: Duration,
}

/// Fallback actions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FallbackAction {
    /// Use alternative service
    AlternativeService(String),
    
    /// Return cached response
    CachedResponse,
    
    /// Return default response
    DefaultResponse(String),
    
    /// Graceful degradation
    GracefulDegradation,
    
    /// Fail fast
    FailFast,
}

/// Production error handler
pub struct ProductionErrorHandler {
    /// Error statistics
    stats: Arc<RwLock<ErrorStats>>,
    
    /// Error history
    error_history: Arc<RwLock<Vec<(ErrorContext, ProductionError)>>>,
    
    /// Recovery strategies by error type
    recovery_strategies: HashMap<String, RecoveryStrategy>,
    
    /// Circuit breaker states
    circuit_breakers: Arc<RwLock<HashMap<String, CircuitBreakerState>>>,
    
    /// Error reporting enabled
    reporting_enabled: bool,
    
    /// Maximum history size
    max_history_size: usize,
}

/// Circuit breaker state
#[derive(Debug, Clone)]
pub struct CircuitBreakerState {
    /// Current state
    pub state: CircuitState,
    
    /// Failure count in current window
    pub failure_count: u32,
    
    /// Success count in current window
    pub success_count: u32,
    
    /// Last state change time
    pub last_state_change: Instant,
    
    /// Window start time
    pub window_start: Instant,
}

/// Circuit breaker states
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CircuitState {
    Closed,
    Open,
    HalfOpen,
}

impl ProductionErrorHandler {
    /// Create a new production error handler
    pub fn new() -> Self {
        Self {
            stats: Arc::new(RwLock::new(ErrorStats {
                total_errors: 0,
                errors_by_type: HashMap::new(),
                errors_by_severity: HashMap::new(),
                errors_by_component: HashMap::new(),
                error_rate: 0.0,
                recovery_rate: 0.0,
                avg_recovery_time: Duration::ZERO,
            })),
            error_history: Arc::new(RwLock::new(Vec::new())),
            recovery_strategies: HashMap::new(),
            circuit_breakers: Arc::new(RwLock::new(HashMap::new())),
            reporting_enabled: true,
            max_history_size: 10000,
        }
    }
    
    /// Handle an error with full context
    pub async fn handle_error(&self, error: ProductionError, context: ErrorContext) -> Result<(), String> {
        // Update statistics
        self.update_stats(&error, &context).await;
        
        // Add to history
        self.add_to_history(context.clone(), error.clone()).await;
        
        // Check circuit breaker
        if self.should_circuit_break(&context.component, &error).await {
            return Err("Circuit breaker is open".to_string());
        }
        
        // Apply recovery strategy
        if let Some(strategy) = self.get_recovery_strategy(&error) {
            self.apply_recovery_strategy(strategy, &error, &context).await?;
        }
        
        // Report error if enabled
        if self.reporting_enabled {
            self.report_error(&error, &context).await;
        }
        
        Ok(())
    }
    
    /// Update error statistics
    async fn update_stats(&self, error: &ProductionError, context: &ErrorContext) {
        let mut stats = self.stats.write().await;
        
        stats.total_errors += 1;
        
        // Update by type
        let error_type = self.get_error_type_name(error);
        *stats.errors_by_type.entry(error_type).or_insert(0) += 1;
        
        // Update by severity
        let severity = self.get_error_severity(error);
        *stats.errors_by_severity.entry(severity).or_insert(0) += 1;
        
        // Update by component
        *stats.errors_by_component.entry(context.component.clone()).or_insert(0) += 1;
        
        // Calculate error rate (simplified)
        stats.error_rate = stats.total_errors as f64 / 60.0; // errors per minute
    }
    
    /// Add error to history
    async fn add_to_history(&self, context: ErrorContext, error: ProductionError) {
        let mut history = self.error_history.write().await;
        
        history.push((context, error));
        
        // Maintain history size limit
        if history.len() > self.max_history_size {
            history.remove(0);
        }
    }
    
    /// Check if circuit breaker should open
    async fn should_circuit_break(&self, component: &str, error: &ProductionError) -> bool {
        let circuit_breakers = self.circuit_breakers.read().await;
        
        if let Some(cb_state) = circuit_breakers.get(component) {
            match cb_state.state {
                CircuitState::Open => {
                    // Check if timeout has passed
                    cb_state.last_state_change.elapsed() < Duration::from_secs(30)
                },
                CircuitState::HalfOpen => {
                    // Allow limited requests
                    false
                },
                CircuitState::Closed => false,
            }
        } else {
            false
        }
    }
    
    /// Get recovery strategy for error
    fn get_recovery_strategy(&self, error: &ProductionError) -> Option<&RecoveryStrategy> {
        let error_type = self.get_error_type_name(error);
        self.recovery_strategies.get(&error_type)
    }
    
    /// Apply recovery strategy
    async fn apply_recovery_strategy(
        &self,
        strategy: &RecoveryStrategy,
        error: &ProductionError,
        context: &ErrorContext,
    ) -> Result<(), String> {
        println!("Applying recovery strategy '{}' for error in component '{}'", 
                 strategy.name, context.component);
        
        // In a real implementation, this would execute the actual recovery logic
        // For now, we'll just log the strategy
        
        match &strategy.fallback {
            Some(FallbackAction::AlternativeService(service)) => {
                println!("Falling back to alternative service: {}", service);
            },
            Some(FallbackAction::CachedResponse) => {
                println!("Using cached response");
            },
            Some(FallbackAction::DefaultResponse(response)) => {
                println!("Using default response: {}", response);
            },
            Some(FallbackAction::GracefulDegradation) => {
                println!("Applying graceful degradation");
            },
            Some(FallbackAction::FailFast) => {
                return Err("Failing fast as per strategy".to_string());
            },
            None => {
                println!("No fallback action configured");
            }
        }
        
        Ok(())
    }
    
    /// Report error to monitoring systems
    async fn report_error(&self, error: &ProductionError, context: &ErrorContext) {
        // In a real implementation, this would send to monitoring systems
        // like Prometheus, Grafana, Sentry, etc.
        
        println!("ERROR REPORT:");
        println!("  ID: {}", context.error_id);
        println!("  Component: {}", context.component);
        println!("  Operation: {}", context.operation);
        println!("  Error: {}", error);
        println!("  Severity: {:?}", self.get_error_severity(error));
        
        if let Some(ref stack_trace) = context.stack_trace {
            println!("  Stack Trace: {}", stack_trace);
        }
    }
    
    /// Get error type name
    fn get_error_type_name(&self, error: &ProductionError) -> String {
        match error {
            ProductionError::Network { .. } => "network".to_string(),
            ProductionError::Protocol { .. } => "protocol".to_string(),
            ProductionError::Configuration { .. } => "configuration".to_string(),
            ProductionError::Authentication { .. } => "authentication".to_string(),
            ProductionError::Resource { .. } => "resource".to_string(),
            ProductionError::Service { .. } => "service".to_string(),
            ProductionError::Security { .. } => "security".to_string(),
            ProductionError::Performance { .. } => "performance".to_string(),
        }
    }
    
    /// Get error severity
    fn get_error_severity(&self, error: &ProductionError) -> ErrorSeverity {
        match error {
            ProductionError::Network { .. } => ErrorSeverity::Medium,
            ProductionError::Protocol { .. } => ErrorSeverity::Medium,
            ProductionError::Configuration { severity, .. } => *severity,
            ProductionError::Authentication { .. } => ErrorSeverity::High,
            ProductionError::Resource { .. } => ErrorSeverity::High,
            ProductionError::Service { .. } => ErrorSeverity::Medium,
            ProductionError::Security { threat_level, .. } => {
                match threat_level {
                    ThreatLevel::Low => ErrorSeverity::Low,
                    ThreatLevel::Medium => ErrorSeverity::Medium,
                    ThreatLevel::High => ErrorSeverity::High,
                    ThreatLevel::Critical => ErrorSeverity::Critical,
                }
            },
            ProductionError::Performance { impact, .. } => {
                match impact {
                    PerformanceImpact::Negligible => ErrorSeverity::Low,
                    PerformanceImpact::Minor => ErrorSeverity::Low,
                    PerformanceImpact::Moderate => ErrorSeverity::Medium,
                    PerformanceImpact::Severe => ErrorSeverity::High,
                    PerformanceImpact::Critical => ErrorSeverity::Critical,
                }
            },
        }
    }
    
    /// Get error statistics
    pub async fn get_stats(&self) -> ErrorStats {
        self.stats.read().await.clone()
    }
    
    /// Get error history
    pub async fn get_error_history(&self, limit: Option<usize>) -> Vec<(ErrorContext, ProductionError)> {
        let history = self.error_history.read().await;
        if let Some(limit) = limit {
            history.iter().rev().take(limit).cloned().collect()
        } else {
            history.clone()
        }
    }
    
    /// Add recovery strategy
    pub fn add_recovery_strategy(&mut self, error_type: String, strategy: RecoveryStrategy) {
        self.recovery_strategies.insert(error_type, strategy);
    }
    
    /// Enable/disable error reporting
    pub fn set_reporting_enabled(&mut self, enabled: bool) {
        self.reporting_enabled = enabled;
    }
}

impl Default for ProductionErrorHandler {
    fn default() -> Self {
        let mut handler = Self::new();
        
        // Add default recovery strategies
        handler.add_recovery_strategy("network".to_string(), RecoveryStrategy {
            name: "network_recovery".to_string(),
            max_retries: 3,
            retry_delay: RetryDelay::Exponential {
                initial: Duration::from_millis(100),
                multiplier: 2.0,
                max_delay: Duration::from_secs(10),
            },
            circuit_breaker: Some(CircuitBreakerConfig {
                failure_threshold: 5,
                success_threshold: 3,
                timeout: Duration::from_secs(30),
                window_size: Duration::from_secs(60),
            }),
            fallback: Some(FallbackAction::AlternativeService("backup".to_string())),
            timeout: Duration::from_secs(30),
        });
        
        handler.add_recovery_strategy("service".to_string(), RecoveryStrategy {
            name: "service_recovery".to_string(),
            max_retries: 2,
            retry_delay: RetryDelay::Fixed(Duration::from_secs(1)),
            circuit_breaker: None,
            fallback: Some(FallbackAction::CachedResponse),
            timeout: Duration::from_secs(15),
        });
        
        handler
    }
}

/// Get current timestamp
fn current_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_error_handler_creation() {
        let handler = ProductionErrorHandler::new();
        let stats = handler.get_stats().await;
        
        assert_eq!(stats.total_errors, 0);
        assert_eq!(stats.error_rate, 0.0);
    }
    
    #[tokio::test]
    async fn test_error_handling() {
        let handler = ProductionErrorHandler::new();
        
        let error = ProductionError::Network {
            message: "Connection timeout".to_string(),
            code: NetworkErrorCode::ConnectionTimeout,
            recoverable: true,
            retry_after: Some(Duration::from_secs(5)),
        };
        
        let context = ErrorContext {
            error_id: "test-error-1".to_string(),
            timestamp: current_timestamp(),
            component: "proxy".to_string(),
            operation: "connect".to_string(),
            user_context: None,
            request_id: Some("req-123".to_string()),
            metadata: HashMap::new(),
            stack_trace: None,
            related_errors: Vec::new(),
        };
        
        let result = handler.handle_error(error, context).await;
        assert!(result.is_ok());
        
        let stats = handler.get_stats().await;
        assert_eq!(stats.total_errors, 1);
        assert!(stats.errors_by_type.contains_key("network"));
    }
    
    #[test]
    fn test_error_severity_mapping() {
        let handler = ProductionErrorHandler::new();
        
        let config_error = ProductionError::Configuration {
            message: "Invalid port".to_string(),
            component: "listener".to_string(),
            severity: ErrorSeverity::High,
            suggestion: Some("Use port 1024-65535".to_string()),
        };
        
        assert_eq!(handler.get_error_severity(&config_error), ErrorSeverity::High);
        
        let security_error = ProductionError::Security {
            message: "Suspicious activity detected".to_string(),
            threat_level: ThreatLevel::Critical,
            action_taken: SecurityAction::Blocked,
        };
        
        assert_eq!(handler.get_error_severity(&security_error), ErrorSeverity::Critical);
    }
    
    #[test]
    fn test_recovery_strategy() {
        let strategy = RecoveryStrategy {
            name: "test_strategy".to_string(),
            max_retries: 3,
            retry_delay: RetryDelay::Exponential {
                initial: Duration::from_millis(100),
                multiplier: 2.0,
                max_delay: Duration::from_secs(5),
            },
            circuit_breaker: None,
            fallback: Some(FallbackAction::DefaultResponse("fallback".to_string())),
            timeout: Duration::from_secs(10),
        };
        
        assert_eq!(strategy.max_retries, 3);
        assert!(matches!(strategy.fallback, Some(FallbackAction::DefaultResponse(_))));
    }
}
