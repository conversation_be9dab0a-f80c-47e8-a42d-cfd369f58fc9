//! SingBox-rs: A Rust implementation of sing-box
//!
//! This crate provides a high-performance, memory-safe implementation of the
//! sing-box universal proxy platform with 100% compatibility.

// Core modules
pub mod r#box;              // Box core implementation
pub mod box_service;        // Box service implementation
pub mod cli;                // CLI command layer

// Adapter layer
pub mod adapter;            // Adapter traits and interfaces

// Implementation layers
pub mod protocol;           // Protocol implementations
pub mod transport;          // Transport layer
pub mod route;              // Routing layer
pub mod dns;                // DNS layer
pub mod security;           // Security layer
pub mod stats;              // Statistics layer
pub mod service;            // Service layer
pub mod experimental;       // Experimental features
pub mod performance;        // Performance monitoring and optimization

// Foundation layer
pub mod network;            // Network layer
pub mod common;             // Common utilities
pub mod log;                // Logging system
pub mod config;             // Configuration system
pub mod constant;           // Constants and definitions
pub mod error;              // Error handling system
pub mod option;             // Configuration options

// Re-export commonly used types
pub use adapter::{Adapter, Inbound, Outbound, InboundContext, Lifecycle, StartStage};
pub use dns::{Client as DNSClient, ClientOptions, DNSQuery, DNSError};
pub use route::{Router, BasicRule, Rule, RouteError};
pub use protocol::{InboundRegistry, OutboundRegistry, ProtocolError, register_builtin_protocols};
pub use network::{NetworkManager, DefaultDialer, DefaultListener, Dialer, Listener, NetworkError};
pub use log::{Logger, Level, ConsoleLogger};
pub use common::interrupt::Context;
pub use r#box::{SingBox, BoxOptions, BoxError};
pub use cli::{Cli, Commands};
pub use config::Config;
pub use service::Service;
pub use experimental::ExperimentalConfig;
