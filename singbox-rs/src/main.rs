mod constant;
mod common;
mod log;
mod option;
mod adapter;
mod dns;
mod route;
mod protocol;
mod network;
mod config;
mod transport;
mod security;
mod stats;
mod service;
mod experimental;
mod performance;
mod error;
mod box_service;
mod cli;

use clap::Parser;
use cli::Cli;

#[tokio::main]
async fn main() {
    let cli = Cli::parse();

    if let Err(e) = cli.execute().await {
        eprintln!("Error: {}", e);
        std::process::exit(1);
    }
}
