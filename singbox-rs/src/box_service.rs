//! Box service implementation - core service management
//! 
//! This module provides the main Box service that coordinates all components
//! and provides the same interface as the Go version.


use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::signal;
use tokio::sync::oneshot;
use crate::config::Config;
use futures::TryFutureExt;

use crate::adapter::{Lifecycle, StartStage};
use crate::dns::{Client as DNSClient, ClientOptions};
use crate::route::Router;
use crate::protocol::{InboundRegistry, OutboundRegistry, register_builtin_protocols};
use crate::network::NetworkManager;
use crate::log::{Logger, Level, ConsoleLogger};
use crate::option::{Options, validate_options};
use crate::common::interrupt::Context;

/// Box service errors
#[derive(Debug, Clone)]
pub enum BoxError {
    ConfigError(String),
    ConfigurationError(String),
    StartupError(String),
    ShutdownError(String),
    ServiceError(String),
}

impl std::fmt::Display for BoxError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            BoxError::ConfigError(msg) => write!(f, "config error: {}", msg),
            BoxError::ConfigurationError(msg) => write!(f, "configuration error: {}", msg),
            BoxError::StartupError(msg) => write!(f, "startup error: {}", msg),
            BoxError::ShutdownError(msg) => write!(f, "shutdown error: {}", msg),
            BoxError::ServiceError(msg) => write!(f, "service error: {}", msg),
        }
    }
}

impl std::error::Error for BoxError {}

/// Box options for service creation
#[derive(Debug)]
pub struct BoxOptions {
    pub context: Context,
    pub options: Options,
    pub config: Config,
    pub working_directory: Option<String>,
    pub disable_color: bool,
}

impl Default for BoxOptions {
    fn default() -> Self {
        Self {
            context: Context::new(),
            options: Options::default(),
            config: Config::default(),
            working_directory: None,
            disable_color: false,
        }
    }
}

/// Main Box service - equivalent to Go version's Box struct
pub struct Box {
    created_at: Instant,
    logger: Arc<dyn Logger>,
    network_manager: Arc<Mutex<NetworkManager>>,
    dns_client: Arc<Mutex<DNSClient>>,
    router: Arc<Mutex<Router>>,
    inbound_registry: Arc<InboundRegistry>,
    outbound_registry: Arc<OutboundRegistry>,
    options: Options,
    context: Context,
    shutdown_tx: Option<oneshot::Sender<()>>,
    started: bool,
}

impl Box {
    /// Create a new Box instance - equivalent to Go's box.New()
    pub fn new(options: BoxOptions) -> Result<Self, BoxError> {
        let created_at = Instant::now();
        
        // Validate configuration
        validate_options(&options.options)
            .map_err(|e| BoxError::ConfigError(e))?;
        
        // Create logger
        let log_level = options.options.log.as_ref()
            .map(|l| match l.level.as_str() {
                "trace" => Level::Trace,
                "debug" => Level::Debug,
                "info" => Level::Info,
                "warn" => Level::Warn,
                "error" => Level::Error,
                _ => Level::Info,
            })
            .unwrap_or(Level::Info);
        
        let logger = Arc::new(ConsoleLogger::new(log_level));
        
        // Create core components
        let network_manager = Arc::new(Mutex::new(NetworkManager::new()));
        let dns_options = options.options.dns.as_ref()
            .map(|_| ClientOptions::default())
            .unwrap_or_default();
        let dns_client = Arc::new(Mutex::new(DNSClient::new(dns_options)));
        
        let default_outbound = options.options.route.as_ref()
            .and_then(|r| r.final_.as_ref())
            .cloned()
            .unwrap_or_else(|| "direct".to_string());
        let router = Arc::new(Mutex::new(Router::new(&default_outbound)));
        
        // Create registries
        let inbound_registry = Arc::new(InboundRegistry::new());
        let outbound_registry = Arc::new(OutboundRegistry::new());
        
        // Register built-in protocols
        register_builtin_protocols(&inbound_registry, &outbound_registry);
        
        logger.info("sing-box created");
        
        Ok(Self {
            created_at,
            logger,
            network_manager,
            dns_client,
            router,
            inbound_registry,
            outbound_registry,
            options: options.options,
            context: options.context,
            shutdown_tx: None,
            started: false,
        })
    }
    
    /// Pre-start the service - equivalent to Go's PreStart()
    pub async fn pre_start(&mut self) -> Result<(), BoxError> {
        self.logger.info("sing-box pre-starting...");
        
        // Initialize components in order
        {
            let mut network_manager = self.network_manager.lock().unwrap();
            network_manager.start(StartStage::Initialize)
                .map_err(|e| BoxError::StartupError(format!("network manager init: {}", e))).await?;
        }
        
        {
            let mut dns_client = self.dns_client.lock().unwrap();
            dns_client.start(StartStage::Initialize)
                .map_err(|e| BoxError::StartupError(format!("dns client init: {}", e))).await?;
        }
        
        {
            let mut router = self.router.lock().unwrap();
            router.start(StartStage::Initialize)
                .map_err(|e| BoxError::StartupError(format!("router init: {}", e))).await?;
        }
        
        let elapsed = self.created_at.elapsed();
        self.logger.info(&format!("sing-box pre-started ({:.3}s)", elapsed.as_secs_f64()));
        
        Ok(())
    }
    
    /// Start the service - equivalent to Go's Start()
    pub async fn start(&mut self) -> Result<(), BoxError> {
        self.pre_start().await?;
        
        self.logger.info("sing-box starting...");
        
        // Start components in order
        {
            let mut network_manager = self.network_manager.lock().unwrap();
            network_manager.start(StartStage::Start)
                .map_err(|e| BoxError::StartupError(format!("network manager start: {}", e))).await?;
        }
        
        {
            let mut dns_client = self.dns_client.lock().unwrap();
            dns_client.start(StartStage::Start)
                .map_err(|e| BoxError::StartupError(format!("dns client start: {}", e))).await?;
        }
        
        {
            let mut router = self.router.lock().unwrap();
            router.start(StartStage::Start)
                .map_err(|e| BoxError::StartupError(format!("router start: {}", e))).await?;
        }
        
        // Post-start phase
        {
            let mut network_manager = self.network_manager.lock().unwrap();
            network_manager.start(StartStage::PostStart)
                .map_err(|e| BoxError::StartupError(format!("network manager post-start: {}", e))).await?;
        }
        
        {
            let mut dns_client = self.dns_client.lock().unwrap();
            dns_client.start(StartStage::PostStart)
                .map_err(|e| BoxError::StartupError(format!("dns client post-start: {}", e))).await?;
        }
        
        {
            let mut router = self.router.lock().unwrap();
            router.start(StartStage::PostStart)
                .map_err(|e| BoxError::StartupError(format!("router post-start: {}", e))).await?;
        }
        
        // Started phase
        {
            let mut network_manager = self.network_manager.lock().unwrap();
            network_manager.start(StartStage::Started)
                .map_err(|e| BoxError::StartupError(format!("network manager started: {}", e))).await?;
        }
        
        {
            let mut dns_client = self.dns_client.lock().unwrap();
            dns_client.start(StartStage::Started)
                .map_err(|e| BoxError::StartupError(format!("dns client started: {}", e))).await?;
        }
        
        {
            let mut router = self.router.lock().unwrap();
            router.start(StartStage::Started)
                .map_err(|e| BoxError::StartupError(format!("router started: {}", e))).await?;
        }
        
        self.started = true;
        let elapsed = self.created_at.elapsed();
        self.logger.info(&format!("sing-box started ({:.3}s)", elapsed.as_secs_f64()));
        
        Ok(())
    }
    
    /// Close the service - equivalent to Go's Close()
    pub async fn close(&mut self) -> Result<(), BoxError> {
        if !self.started {
            return Ok(());
        }
        
        self.logger.info("sing-box closing...");
        
        // Send shutdown signal
        if let Some(tx) = self.shutdown_tx.take() {
            let _ = tx.send(());
        }
        
        // Close components in reverse order
        {
            let mut router = self.router.lock().unwrap();
            router.close()
                .map_err(|e| BoxError::ShutdownError(format!("router close: {}", e))).await?;
        }
        
        {
            let mut dns_client = self.dns_client.lock().unwrap();
            dns_client.close()
                .map_err(|e| BoxError::ShutdownError(format!("dns client close: {}", e))).await?;
        }
        
        {
            let mut network_manager = self.network_manager.lock().unwrap();
            network_manager.close()
                .map_err(|e| BoxError::ShutdownError(format!("network manager close: {}", e))).await?;
        }
        
        self.started = false;
        self.logger.info("sing-box closed");
        
        Ok(())
    }
    
    /// Get network manager reference
    pub fn network(&self) -> Arc<Mutex<NetworkManager>> {
        Arc::clone(&self.network_manager)
    }
    
    /// Get router reference
    pub fn router(&self) -> Arc<Mutex<Router>> {
        Arc::clone(&self.router)
    }
    
    /// Get DNS client reference
    pub fn dns(&self) -> Arc<Mutex<DNSClient>> {
        Arc::clone(&self.dns_client)
    }
    
    /// Check if service is started
    pub fn is_started(&self) -> bool {
        self.started
    }
    
    /// Get service uptime
    pub fn uptime(&self) -> Duration {
        self.created_at.elapsed()
    }
    
    /// Wait for shutdown signal
    pub async fn wait_for_shutdown(&mut self) -> Result<(), BoxError> {
        let (tx, rx) = oneshot::channel();
        self.shutdown_tx = Some(tx);
        
        tokio::select! {
            _ = rx => {
                self.logger.info("received shutdown signal");
                Ok(())
            }
            _ = signal::ctrl_c() => {
                self.logger.info("received interrupt signal");
                Ok(())
            }
        }
    }
}

impl Drop for Box {
    fn drop(&mut self) {
        if self.started {
            let _ = futures::executor::block_on(self.close());
        }
    }
}
