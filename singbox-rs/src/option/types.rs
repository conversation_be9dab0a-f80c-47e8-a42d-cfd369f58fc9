// Basic types for sing-box options
//
// This module defines basic types used in configuration options,
// including duration types, address types, and other fundamental types.

use std::time::Duration;
use std::net::{IpAddr, SocketAddr};
use std::str::FromStr;
use std::fmt;
use serde::{Deserialize, Serialize};

/// Duration type that can be parsed from various string formats
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct DurationOption(pub Duration);

impl DurationOption {
    pub fn new(duration: Duration) -> Self {
        Self(duration)
    }

    pub fn as_duration(&self) -> Duration {
        self.0
    }

    pub fn as_secs(&self) -> u64 {
        self.0.as_secs()
    }

    pub fn as_millis(&self) -> u128 {
        self.0.as_millis()
    }
}

impl From<Duration> for DurationOption {
    fn from(duration: Duration) -> Self {
        Self(duration)
    }
}

impl From<DurationOption> for Duration {
    fn from(duration_option: DurationOption) -> Self {
        duration_option.0
    }
}

impl FromStr for DurationOption {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        // Parse duration strings like "5s", "10m", "1h", etc.
        if s.is_empty() {
            return Err("empty duration string".to_string());
        }

        let s = s.trim();
        if s.ends_with("ns") {
            let num_str = &s[..s.len()-2];
            let nanos: u64 = num_str.parse().map_err(|_| format!("invalid nanoseconds: {}", num_str))?;
            Ok(Self(Duration::from_nanos(nanos)))
        } else if s.ends_with("us") || s.ends_with("μs") {
            let num_str = &s[..s.len()-2];
            let micros: u64 = num_str.parse().map_err(|_| format!("invalid microseconds: {}", num_str))?;
            Ok(Self(Duration::from_micros(micros)))
        } else if s.ends_with("ms") {
            let num_str = &s[..s.len()-2];
            let millis: u64 = num_str.parse().map_err(|_| format!("invalid milliseconds: {}", num_str))?;
            Ok(Self(Duration::from_millis(millis)))
        } else if s.ends_with('s') {
            let num_str = &s[..s.len()-1];
            let secs: u64 = num_str.parse().map_err(|_| format!("invalid seconds: {}", num_str))?;
            Ok(Self(Duration::from_secs(secs)))
        } else if s.ends_with('m') {
            let num_str = &s[..s.len()-1];
            let mins: u64 = num_str.parse().map_err(|_| format!("invalid minutes: {}", num_str))?;
            Ok(Self(Duration::from_secs(mins * 60)))
        } else if s.ends_with('h') {
            let num_str = &s[..s.len()-1];
            let hours: u64 = num_str.parse().map_err(|_| format!("invalid hours: {}", num_str))?;
            Ok(Self(Duration::from_secs(hours * 3600)))
        } else {
            // Try parsing as plain seconds
            let secs: u64 = s.parse().map_err(|_| format!("invalid duration format: {}", s))?;
            Ok(Self(Duration::from_secs(secs)))
        }
    }
}

impl fmt::Display for DurationOption {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let secs = self.0.as_secs();
        if secs >= 3600 && secs % 3600 == 0 {
            write!(f, "{}h", secs / 3600)
        } else if secs >= 60 && secs % 60 == 0 {
            write!(f, "{}m", secs / 60)
        } else if secs > 0 {
            write!(f, "{}s", secs)
        } else {
            let millis = self.0.as_millis();
            if millis > 0 {
                write!(f, "{}ms", millis)
            } else {
                write!(f, "{}ns", self.0.as_nanos())
            }
        }
    }
}

/// Address type that can represent various address formats
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum AddressOption {
    IP(IpAddr),
    Socket(SocketAddr),
    Domain(String),
    Empty,
}

impl AddressOption {
    pub fn is_empty(&self) -> bool {
        matches!(self, AddressOption::Empty)
    }

    pub fn is_ip(&self) -> bool {
        matches!(self, AddressOption::IP(_))
    }

    pub fn is_socket(&self) -> bool {
        matches!(self, AddressOption::Socket(_))
    }

    pub fn is_domain(&self) -> bool {
        matches!(self, AddressOption::Domain(_))
    }
}

impl FromStr for AddressOption {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        if s.is_empty() {
            return Ok(AddressOption::Empty);
        }

        // Try parsing as socket address first
        if let Ok(socket_addr) = s.parse::<SocketAddr>() {
            return Ok(AddressOption::Socket(socket_addr));
        }

        // Try parsing as IP address
        if let Ok(ip_addr) = s.parse::<IpAddr>() {
            return Ok(AddressOption::IP(ip_addr));
        }

        // Otherwise treat as domain name
        Ok(AddressOption::Domain(s.to_string()))
    }
}

impl fmt::Display for AddressOption {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AddressOption::IP(ip) => write!(f, "{}", ip),
            AddressOption::Socket(socket) => write!(f, "{}", socket),
            AddressOption::Domain(domain) => write!(f, "{}", domain),
            AddressOption::Empty => write!(f, ""),
        }
    }
}

/// Listable address type (can be a single address or a list)
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ListableAddress {
    Single(AddressOption),
    Multiple(Vec<AddressOption>),
}

impl ListableAddress {
    pub fn is_empty(&self) -> bool {
        match self {
            ListableAddress::Single(addr) => addr.is_empty(),
            ListableAddress::Multiple(addrs) => addrs.is_empty(),
        }
    }

    pub fn len(&self) -> usize {
        match self {
            ListableAddress::Single(_) => 1,
            ListableAddress::Multiple(addrs) => addrs.len(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{Ipv4Addr, Ipv6Addr};

    #[test]
    fn test_duration_option_parsing() {
        assert_eq!("5s".parse::<DurationOption>().unwrap().as_secs(), 5);
        assert_eq!("10m".parse::<DurationOption>().unwrap().as_secs(), 600);
        assert_eq!("1h".parse::<DurationOption>().unwrap().as_secs(), 3600);
        assert_eq!("500ms".parse::<DurationOption>().unwrap().as_millis(), 500);
        assert_eq!("30".parse::<DurationOption>().unwrap().as_secs(), 30);
        
        assert!("invalid".parse::<DurationOption>().is_err());
        assert!("".parse::<DurationOption>().is_err());
    }

    #[test]
    fn test_duration_option_display() {
        let dur_5s = DurationOption::new(Duration::from_secs(5));
        assert_eq!(format!("{}", dur_5s), "5s");
        
        let dur_10m = DurationOption::new(Duration::from_secs(600));
        assert_eq!(format!("{}", dur_10m), "10m");
        
        let dur_1h = DurationOption::new(Duration::from_secs(3600));
        assert_eq!(format!("{}", dur_1h), "1h");
        
        let dur_500ms = DurationOption::new(Duration::from_millis(500));
        assert_eq!(format!("{}", dur_500ms), "500ms");
    }

    #[test]
    fn test_address_option_parsing() {
        // Test IP address parsing
        let ip_addr = "***********".parse::<AddressOption>().unwrap();
        assert!(ip_addr.is_ip());
        
        // Test socket address parsing
        let socket_addr = "***********:8080".parse::<AddressOption>().unwrap();
        assert!(socket_addr.is_socket());
        
        // Test domain parsing
        let domain_addr = "example.com".parse::<AddressOption>().unwrap();
        assert!(domain_addr.is_domain());
        
        // Test empty address
        let empty_addr = "".parse::<AddressOption>().unwrap();
        assert!(empty_addr.is_empty());
    }

    #[test]
    fn test_address_option_display() {
        let ip = AddressOption::IP(IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1)));
        assert_eq!(format!("{}", ip), "***********");
        
        let domain = AddressOption::Domain("example.com".to_string());
        assert_eq!(format!("{}", domain), "example.com");
        
        let empty = AddressOption::Empty;
        assert_eq!(format!("{}", empty), "");
    }

    #[test]
    fn test_listable_address() {
        let single = ListableAddress::Single(AddressOption::Domain("example.com".to_string()));
        assert_eq!(single.len(), 1);
        assert!(!single.is_empty());
        
        let multiple = ListableAddress::Multiple(vec![
            AddressOption::Domain("example.com".to_string()),
            AddressOption::Domain("test.com".to_string()),
        ]);
        assert_eq!(multiple.len(), 2);
        assert!(!multiple.is_empty());
        
        let empty_multiple = ListableAddress::Multiple(vec![]);
        assert_eq!(empty_multiple.len(), 0);
        assert!(empty_multiple.is_empty());
    }
}
