// Main configuration options for sing-box
//
// This module defines the main configuration structures used by sing-box,
// including the root Options struct and its various sub-configurations.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::constant::dns::DomainStrategy;
use crate::option::types::{DurationOption, AddressOption};

/// Main configuration options structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Options {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub schema: Option<String>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub log: Option<LogOptions>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub dns: Option<DNSOptions>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ntp: Option<NTPOptions>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub certificate: Option<CertificateOptions>,
    
    #[serde(default, skip_serializing_if = "Vec::is_empty")]
    pub endpoints: Vec<Endpoint>,
    
    #[serde(default, skip_serializing_if = "Vec::is_empty")]
    pub inbounds: Vec<Inbound>,
    
    #[serde(default, skip_serializing_if = "Vec::is_empty")]
    pub outbounds: Vec<Outbound>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub route: Option<RouteOptions>,
    
    #[serde(default, skip_serializing_if = "Vec::is_empty")]
    pub services: Vec<Service>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub experimental: Option<ExperimentalOptions>,
}

impl Default for Options {
    fn default() -> Self {
        Self {
            schema: None,
            log: None,
            dns: None,
            ntp: None,
            certificate: None,
            endpoints: Vec::new(),
            inbounds: Vec::new(),
            outbounds: Vec::new(),
            route: None,
            services: Vec::new(),
            experimental: None,
        }
    }
}

/// Log configuration options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogOptions {
    #[serde(default, skip_serializing_if = "is_false")]
    pub disabled: bool,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub level: Option<String>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub output: Option<String>,
    
    #[serde(default, skip_serializing_if = "is_false")]
    pub timestamp: bool,
    
    #[serde(skip)]
    pub disable_color: bool,
}

impl Default for LogOptions {
    fn default() -> Self {
        Self {
            disabled: false,
            level: None,
            output: None,
            timestamp: false,
            disable_color: false,
        }
    }
}

/// DNS configuration options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DNSOptions {
    #[serde(default, skip_serializing_if = "Vec::is_empty")]
    pub servers: Vec<DNSServerOptions>,
    
    #[serde(default, skip_serializing_if = "Vec::is_empty")]
    pub rules: Vec<DNSRule>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub final_server: Option<String>,
    
    #[serde(default, skip_serializing_if = "is_false")]
    pub reverse_mapping: bool,
    
    pub strategy: DomainStrategy,
    
    #[serde(default, skip_serializing_if = "is_false")]
    pub disable_cache: bool,
    
    #[serde(default, skip_serializing_if = "is_false")]
    pub disable_expire: bool,
    
    #[serde(default, skip_serializing_if = "is_false")]
    pub independent_cache: bool,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cache_capacity: Option<u32>,
}

impl Default for DNSOptions {
    fn default() -> Self {
        Self {
            servers: Vec::new(),
            rules: Vec::new(),
            final_server: None,
            reverse_mapping: false,
            strategy: 0, // DOMAIN_STRATEGY_AS_IS
            disable_cache: false,
            disable_expire: false,
            independent_cache: false,
            cache_capacity: None,
        }
    }
}

/// DNS server configuration options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DNSServerOptions {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server_type: Option<String>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tag: Option<String>,
    
    pub address: String,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub address_resolver: Option<String>,
    
    pub address_strategy: DomainStrategy,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub address_fallback_delay: Option<DurationOption>,
    
    pub strategy: DomainStrategy,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub detour: Option<String>,
}

/// DNS rule placeholder
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DNSRule {
    // Placeholder for DNS rule implementation
    pub rule_type: String,
}

/// NTP configuration options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NTPOptions {
    #[serde(default, skip_serializing_if = "is_false")]
    pub enabled: bool,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server: Option<String>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server_port: Option<u16>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub interval: Option<DurationOption>,
}

/// Certificate configuration options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CertificateOptions {
    // Placeholder for certificate options
    pub auto_generate: bool,
}

/// Endpoint placeholder
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Endpoint {
    pub tag: String,
    pub endpoint_type: String,
}

/// Inbound placeholder
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Inbound {
    pub inbound_type: String,
    pub tag: String,
}

impl Default for Inbound {
    fn default() -> Self {
        Self {
            inbound_type: String::new(),
            tag: String::new(),
        }
    }
}

/// Outbound placeholder
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Outbound {
    pub outbound_type: String,
    pub tag: String,
}

impl Default for Outbound {
    fn default() -> Self {
        Self {
            outbound_type: String::new(),
            tag: String::new(),
        }
    }
}

/// Route configuration options
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct RouteOptions {
    // Placeholder for route options
    #[serde(default)]
    pub auto_detect_interface: bool,

    #[serde(skip_serializing_if = "Option::is_none")]
    pub final_outbound: Option<String>,
}

/// Service placeholder
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Service {
    pub service_type: String,
}

/// Experimental configuration options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExperimentalOptions {
    // Placeholder for experimental options
    pub enabled: bool,
}

/// Helper function for serde skip_serializing_if
fn is_false(b: &bool) -> bool {
    !b
}

/// Validate configuration options
pub fn validate_options(options: &Options) -> Result<(), String> {
    validate_inbounds(&options.inbounds)?;
    validate_outbounds(&options.outbounds, &options.endpoints)?;
    Ok(())
}

/// Validate inbound configurations
pub fn validate_inbounds(inbounds: &[Inbound]) -> Result<(), String> {
    let mut seen_tags = HashMap::new();
    
    for inbound in inbounds {
        if !inbound.tag.is_empty() {
            if seen_tags.contains_key(&inbound.tag) {
                return Err(format!("duplicate inbound tag: {}", inbound.tag));
            }
            seen_tags.insert(inbound.tag.clone(), true);
        }
    }
    
    Ok(())
}

/// Validate outbound configurations
pub fn validate_outbounds(outbounds: &[Outbound], endpoints: &[Endpoint]) -> Result<(), String> {
    let mut seen_tags = HashMap::new();
    
    for outbound in outbounds {
        if !outbound.tag.is_empty() {
            if seen_tags.contains_key(&outbound.tag) {
                return Err(format!("duplicate outbound/endpoint tag: {}", outbound.tag));
            }
            seen_tags.insert(outbound.tag.clone(), true);
        }
    }
    
    for endpoint in endpoints {
        if !endpoint.tag.is_empty() {
            if seen_tags.contains_key(&endpoint.tag) {
                return Err(format!("duplicate outbound/endpoint tag: {}", endpoint.tag));
            }
            seen_tags.insert(endpoint.tag.clone(), true);
        }
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_options_default() {
        let options = Options::default();
        assert!(options.inbounds.is_empty());
        assert!(options.outbounds.is_empty());
        assert!(options.endpoints.is_empty());
        assert!(options.log.is_none());
        assert!(options.dns.is_none());
    }

    #[test]
    fn test_log_options_default() {
        let log_opts = LogOptions::default();
        assert!(!log_opts.disabled);
        assert!(!log_opts.timestamp);
        assert!(!log_opts.disable_color);
        assert!(log_opts.level.is_none());
        assert!(log_opts.output.is_none());
    }

    #[test]
    fn test_dns_options_default() {
        let dns_opts = DNSOptions::default();
        assert!(dns_opts.servers.is_empty());
        assert!(dns_opts.rules.is_empty());
        assert!(!dns_opts.reverse_mapping);
        assert_eq!(dns_opts.strategy, 0); // DOMAIN_STRATEGY_AS_IS
        assert!(!dns_opts.disable_cache);
    }

    #[test]
    fn test_validate_inbounds_success() {
        let inbounds = vec![
            Inbound { inbound_type: "http".to_string(), tag: "http-in".to_string() },
            Inbound { inbound_type: "socks".to_string(), tag: "socks-in".to_string() },
        ];
        
        assert!(validate_inbounds(&inbounds).is_ok());
    }

    #[test]
    fn test_validate_inbounds_duplicate_tag() {
        let inbounds = vec![
            Inbound { inbound_type: "http".to_string(), tag: "same-tag".to_string() },
            Inbound { inbound_type: "socks".to_string(), tag: "same-tag".to_string() },
        ];
        
        let result = validate_inbounds(&inbounds);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("duplicate inbound tag"));
    }

    #[test]
    fn test_validate_outbounds_success() {
        let outbounds = vec![
            Outbound { outbound_type: "direct".to_string(), tag: "direct-out".to_string() },
        ];
        let endpoints = vec![
            Endpoint { endpoint_type: "shadowsocks".to_string(), tag: "ss-endpoint".to_string() },
        ];
        
        assert!(validate_outbounds(&outbounds, &endpoints).is_ok());
    }

    #[test]
    fn test_validate_outbounds_duplicate_tag() {
        let outbounds = vec![
            Outbound { outbound_type: "direct".to_string(), tag: "same-tag".to_string() },
        ];
        let endpoints = vec![
            Endpoint { endpoint_type: "shadowsocks".to_string(), tag: "same-tag".to_string() },
        ];
        
        let result = validate_outbounds(&outbounds, &endpoints);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("duplicate outbound/endpoint tag"));
    }

    #[test]
    fn test_validate_options_success() {
        let options = Options::default();
        assert!(validate_options(&options).is_ok());
    }
}
