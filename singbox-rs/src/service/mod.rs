//! Service layer implementation
//!
//! This module provides service implementations for sing-box,
//! including DERP, Resolved, and SSM API services.

use async_trait::async_trait;
use crate::adapter::{Lifecycle, StartStage};

pub mod derp;
pub mod resolved;
pub mod ssm_api;

/// Service trait for all sing-box services
pub trait Service: Lifecycle + Send + Sync {
    /// Get the service type
    fn service_type(&self) -> &str;
    
    /// Get the service name
    fn service_name(&self) -> &str;
    
    /// Check if the service is enabled
    fn is_enabled(&self) -> bool {
        true
    }
    
    /// Get service configuration
    fn config(&self) -> Option<&dyn std::any::Any> {
        None
    }
}

/// Service error types
#[derive(Debug, Clone)]
pub enum ServiceError {
    ConfigurationError(String),
    InitializationError(String),
    RuntimeError(String),
    NetworkError(String),
}

impl std::fmt::Display for ServiceError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ServiceError::ConfigurationError(msg) => write!(f, "Configuration error: {}", msg),
            ServiceError::InitializationError(msg) => write!(f, "Initialization error: {}", msg),
            ServiceError::RuntimeError(msg) => write!(f, "Runtime error: {}", msg),
            ServiceError::NetworkError(msg) => write!(f, "Network error: {}", msg),
        }
    }
}

impl std::error::Error for ServiceError {}

/// Base service implementation
pub struct BaseService {
    service_type: String,
    service_name: String,
    enabled: bool,
}

impl BaseService {
    pub fn new(service_type: &str, service_name: &str) -> Self {
        Self {
            service_type: service_type.to_string(),
            service_name: service_name.to_string(),
            enabled: true,
        }
    }
    
    pub fn with_enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }
}

impl Service for BaseService {
    fn service_type(&self) -> &str {
        &self.service_type
    }
    
    fn service_name(&self) -> &str {
        &self.service_name
    }
    
    fn is_enabled(&self) -> bool {
        self.enabled
    }
}

#[async_trait]
impl Lifecycle for BaseService {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        Ok(())
    }
    
    async fn close(&self) -> Result<(), String> {
        Ok(())
    }
}
