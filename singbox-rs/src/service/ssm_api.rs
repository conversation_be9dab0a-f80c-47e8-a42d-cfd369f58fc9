//! SSM API service implementation
//!
//! This service provides SSM (Sing-box Service Manager) API functionality.

use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use async_trait::async_trait;

use crate::adapter::{Lifecycle, StartStage};
use crate::service::{Service, ServiceError};

/// SSM API service configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SsmApiConfig {
    /// Whether to enable the service
    pub enabled: bool,
    
    /// API listen address
    pub listen: String,
    
    /// API authentication token
    pub token: Option<String>,
    
    /// CORS settings
    pub cors: Option<CorsConfig>,
}

/// CORS configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorsConfig {
    /// Allowed origins
    pub allowed_origins: Vec<String>,
    
    /// Allowed methods
    pub allowed_methods: Vec<String>,
    
    /// Allowed headers
    pub allowed_headers: Vec<String>,
}

impl Default for SsmApiConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            listen: "127.0.0.1:8080".to_string(),
            token: None,
            cors: None,
        }
    }
}

/// SSM API service implementation
pub struct SsmApiService {
    config: SsmApiConfig,
    started: Arc<RwLock<bool>>,
}

impl SsmApiService {
    /// Create a new SSM API service
    pub fn new(config: SsmApiConfig) -> Self {
        Self {
            config,
            started: Arc::new(RwLock::new(false)),
        }
    }
    
    /// Check if the service is enabled
    pub fn is_enabled(&self) -> bool {
        self.config.enabled
    }
    
    /// Get the listen address
    pub fn listen_addr(&self) -> &str {
        &self.config.listen
    }
    
    /// Get the authentication token
    pub fn token(&self) -> Option<&str> {
        self.config.token.as_deref()
    }
    
    /// Get CORS configuration
    pub fn cors_config(&self) -> Option<&CorsConfig> {
        self.config.cors.as_ref()
    }
}

impl Service for SsmApiService {
    fn service_type(&self) -> &str {
        "ssm_api"
    }
    
    fn service_name(&self) -> &str {
        "SSM API Service"
    }
    
    fn is_enabled(&self) -> bool {
        self.config.enabled
    }
    
    fn config(&self) -> Option<&dyn std::any::Any> {
        Some(&self.config)
    }
}

#[async_trait]
impl Lifecycle for SsmApiService {
    async fn start(&self, stage: StartStage) -> Result<(), String> {
        if !self.config.enabled {
            return Ok(());
        }
        
        match stage {
            StartStage::Initialize => {
                // Initialize API server
                Ok(())
            }
            StartStage::Start => {
                let mut started = self.started.write().await;
                if *started {
                    return Err("SSM API service is already started".to_string());
                }
                
                // Start HTTP API server
                // This would involve setting up an HTTP server with the configured routes
                
                *started = true;
                Ok(())
            }
            StartStage::PostStart => {
                // Post-start initialization
                Ok(())
            }
            StartStage::Started => {
                // Service is already started
                Ok(())
            }
        }
    }
    
    async fn close(&self) -> Result<(), String> {
        if !self.config.enabled {
            return Ok(());
        }
        
        let mut started = self.started.write().await;
        if !*started {
            return Ok(());
        }
        
        // Stop HTTP API server
        *started = false;
        Ok(())
    }
}
