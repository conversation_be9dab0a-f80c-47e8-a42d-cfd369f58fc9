//! Resolved service implementation
//!
//! This service provides systemd-resolved integration for DNS resolution.

use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use async_trait::async_trait;

use crate::adapter::{Lifecycle, StartStage};
use crate::service::{Service, ServiceError};

/// Resolved service configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResolvedConfig {
    /// Whether to enable the service
    pub enabled: bool,
    
    /// Detour tag for DNS queries
    pub detour: Option<String>,
}

impl Default for ResolvedConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            detour: None,
        }
    }
}

/// Resolved service implementation
pub struct ResolvedService {
    config: ResolvedConfig,
    started: Arc<RwLock<bool>>,
}

impl ResolvedService {
    /// Create a new Resolved service
    pub fn new(config: ResolvedConfig) -> Self {
        Self {
            config,
            started: Arc::new(RwLock::new(false)),
        }
    }
    
    /// Check if the service is enabled
    pub fn is_enabled(&self) -> bool {
        self.config.enabled
    }
    
    /// Get the detour tag
    pub fn detour(&self) -> Option<&str> {
        self.config.detour.as_deref()
    }
}

impl Service for ResolvedService {
    fn service_type(&self) -> &str {
        "resolved"
    }
    
    fn service_name(&self) -> &str {
        "Resolved Service"
    }
    
    fn is_enabled(&self) -> bool {
        self.config.enabled
    }
    
    fn config(&self) -> Option<&dyn std::any::Any> {
        Some(&self.config)
    }
}

#[async_trait]
impl Lifecycle for ResolvedService {
    async fn start(&self, stage: StartStage) -> Result<(), String> {
        if !self.config.enabled {
            return Ok(());
        }
        
        match stage {
            StartStage::Initialize => {
                // Initialize resolved integration
                Ok(())
            }
            StartStage::Start => {
                let mut started = self.started.write().await;
                if *started {
                    return Err("Resolved service is already started".to_string());
                }
                
                // Start systemd-resolved integration
                // This would involve setting up D-Bus connections and DNS interception
                
                *started = true;
                Ok(())
            }
            StartStage::PostStart => {
                // Post-start initialization
                Ok(())
            }
            StartStage::Started => {
                // Service is already started
                Ok(())
            }
        }
    }
    
    async fn close(&self) -> Result<(), String> {
        if !self.config.enabled {
            return Ok(());
        }
        
        let mut started = self.started.write().await;
        if !*started {
            return Ok(());
        }
        
        // Stop systemd-resolved integration
        *started = false;
        Ok(())
    }
}
