//! Command line interface for sing-box
//! 
//! This module provides the CLI interface that matches the Go version exactly.

use clap::{Parser, Subcommand};
use std::path::PathBuf;
use std::fs;
use std::io::{self, Read, Write};

use uuid::Uuid;
use rand::RngCore;
use base64::{Engine as _, engine::general_purpose};

use crate::box_service::{Box as SingBox, BoxOptions, BoxError};
use crate::option::{Options, LogOptions};
use crate::config::Config;


use crate::constant::version::get_version;

/// sing-box command line interface
#[derive(Parse<PERSON>, Clone)]
#[command(name = "sing-box")]
#[command(about = "The universal proxy platform")]
#[command(version = env!("CARGO_PKG_VERSION"))]
pub struct Cli {
    /// Set configuration file path
    #[arg(short = 'c', long = "config", value_name = "FILE", global = true)]
    pub config: Vec<PathBuf>,

    /// Set configuration directory path
    #[arg(short = 'C', long = "config-directory", value_name = "DIR", global = true)]
    pub config_directory: Vec<PathBuf>,

    /// Set working directory
    #[arg(short = 'D', long = "directory", value_name = "DIR", global = true)]
    pub directory: Option<PathBuf>,

    /// Disable color output
    #[arg(long = "disable-color", global = true)]
    pub disable_color: bool,

    #[command(subcommand)]
    pub command: Commands,
}

#[derive(Subcommand, Clone)]
pub enum Commands {
    /// Run service
    Run,
    /// Check configuration
    Check,
    /// Format configuration
    Format {
        /// Write result to (source) file instead of stdout
        #[arg(short = 'w', long = "write")]
        write: bool,
    },
    /// Show version information
    Version {
        /// Show only version number
        #[arg(long = "name-only")]
        name_only: bool,
    },
    /// Generate things
    Generate {
        #[command(subcommand)]
        command: GenerateCommands,
    },
    /// Merge configurations
    Merge {
        /// Output file path
        output_path: String,
    },
    /// GeoIP tools
    Geoip {
        #[command(subcommand)]
        command: GeoipCommands,
        /// GeoIP database file path
        #[arg(short = 'f', long = "file", default_value = "geoip.db")]
        file: String,
    },
    /// Geosite tools
    Geosite {
        #[command(subcommand)]
        command: GeositeCommands,
        /// Geosite database file path
        #[arg(short = 'f', long = "file", default_value = "geosite.db")]
        file: String,
    },
    /// Manage rule-sets
    RuleSet {
        #[command(subcommand)]
        command: RuleSetCommands,
    },
    /// Experimental tools
    Tools {
        #[command(subcommand)]
        command: ToolsCommands,
        /// Use specified tag instead of default outbound
        #[arg(short = 'o', long = "outbound")]
        outbound: Option<String>,
    },
}

#[derive(Subcommand, Clone)]
pub enum GenerateCommands {
    /// Generate UUID string
    Uuid,
    /// Generate random bytes
    Rand {
        /// Length of random bytes
        length: usize,
        /// Generate base64 string
        #[arg(long = "base64")]
        base64: bool,
        /// Generate hex string
        #[arg(long = "hex")]
        hex: bool,
    },
    /// Generate WireGuard key pair
    WgKeypair,
    /// Generate TLS self sign key pair
    TlsKeypair {
        /// Server name
        server_name: String,
        /// Certificate validity in months
        #[arg(long = "months", default_value = "3")]
        months: u32,
    },
    /// Generate TLS ECH key pair
    EchKeypair {
        /// Plain server name
        plain_server_name: String,
    },
    /// Generate Reality key pair
    RealityKeypair,
    /// Generate VAPID key pair
    Vapid,
}

#[derive(Subcommand, Clone)]
pub enum GeoipCommands {
    /// List geoip country codes
    List,
    /// Lookup if an IP address is contained in the GeoIP database
    Lookup {
        /// IP address to lookup
        address: String,
    },
    /// Export geoip country as rule-set
    Export {
        /// Country code
        country: String,
        /// Output file path
        #[arg(short = 'o', long = "output")]
        output: Option<String>,
    },
}

#[derive(Subcommand, Clone)]
pub enum GeositeCommands {
    /// List geosite categories
    List,
    /// Check if a domain is in the geosite
    Lookup {
        /// Category name (optional)
        category: Option<String>,
        /// Domain to lookup
        domain: String,
    },
    /// Export geosite category as rule-set
    Export {
        /// Category name
        category: String,
        /// Output file path
        #[arg(short = 'o', long = "output")]
        output: Option<String>,
    },
}

#[derive(Subcommand, Clone)]
pub enum RuleSetCommands {
    /// Compile rule-set to binary format
    Compile {
        /// Source file path
        source_path: String,
        /// Output file path
        #[arg(short = 'o', long = "output")]
        output: Option<String>,
    },
    /// Decompile binary rule-set to JSON
    Decompile {
        /// Source file path
        source_path: String,
        /// Output file path
        #[arg(short = 'o', long = "output")]
        output: Option<String>,
    },
    /// Format rule-set json
    Format {
        /// Source file path
        source_path: String,
        /// Write result to (source) file instead of stdout
        #[arg(short = 'w', long = "write")]
        write: bool,
    },
    /// Upgrade rule-set json
    Upgrade {
        /// Source file path
        source_path: String,
        /// Write result to (source) file instead of stdout
        #[arg(short = 'w', long = "write")]
        write: bool,
    },
    /// Convert adguard DNS filter to rule-set
    Convert {
        /// Source file path
        source_path: String,
        /// Source type
        #[arg(long = "type")]
        source_type: String,
        /// Output file path
        #[arg(short = 'o', long = "output")]
        output: Option<String>,
    },
    /// Merge rule-set source files
    Merge {
        /// Output file path
        output_path: String,
        /// Input rule-set file paths
        #[arg(short = 'c', long = "config")]
        config: Vec<String>,
        /// Input rule-set directory paths
        #[arg(short = 'C', long = "config-directory")]
        config_directory: Vec<String>,
    },
    /// Check if an IP address or a domain matches the rule-set
    Match {
        /// Rule-set file path
        rule_set_path: String,
        /// IP address or domain
        target: String,
        /// Rule-set format
        #[arg(short = 'f', long = "format", default_value = "source")]
        format: String,
    },
}

#[derive(Subcommand, Clone)]
pub enum ToolsCommands {
    /// Connect to an address
    Connect {
        /// Target address
        address: String,
        /// Network type
        #[arg(short = 'n', long = "network", default_value = "tcp")]
        network: String,
    },
    /// Fetch URL content through proxy
    Fetch {
        /// Target URL
        url: String,
    },
    /// Sync time using the NTP protocol
    Synctime {
        /// NTP server address
        #[arg(short = 's', long = "server", default_value = "time.cloudflare.com")]
        server: String,
        /// Time output format
        #[arg(short = 'f', long = "format", default_value = "%Y-%m-%d %H:%M:%S")]
        format: String,
        /// Write time to system
        #[arg(short = 'w', long = "write")]
        write: bool,
    },
}

/// Configuration entry for merging multiple configs
#[derive(Debug)]
struct OptionsEntry {
    content: Vec<u8>,
    path: String,
    options: Options,
}

impl Cli {
    /// Execute the CLI command
    pub async fn execute(self) -> Result<(), BoxError> {
        // Set working directory if specified
        if let Some(dir) = &self.directory {
            std::env::set_current_dir(dir)
                .map_err(|e| BoxError::ConfigurationError(format!("set working directory: {}", e)))?;
        }

        let command = self.command.clone();
        match command {
            Commands::Run => self.run().await,
            Commands::Check => self.check().await,
            Commands::Format { write } => self.format(write).await,
            Commands::Version { name_only } => {
                self.version(name_only);
                Ok(())
            },
            Commands::Generate { command } => self.generate(command).await,
            Commands::Merge { output_path } => self.merge(output_path).await,
            Commands::Geoip { command, file } => self.geoip(command, file).await,
            Commands::Geosite { command, file } => self.geosite(command, file).await,
            Commands::RuleSet { command } => self.rule_set(command).await,
            Commands::Tools { command, outbound } => self.tools(command, outbound).await,
        }
    }
    
    /// Run the service
    async fn run(self) -> Result<(), BoxError> {
        use tokio::signal::unix::{signal, SignalKind};

        // Setup signal handlers
        let mut sigint = signal(SignalKind::interrupt())
            .map_err(|e| BoxError::ConfigurationError(format!("setup SIGINT handler: {}", e)))?;
        let mut sigterm = signal(SignalKind::terminate())
            .map_err(|e| BoxError::ConfigurationError(format!("setup SIGTERM handler: {}", e)))?;
        let mut sighup = signal(SignalKind::hangup())
            .map_err(|e| BoxError::ConfigurationError(format!("setup SIGHUP handler: {}", e)))?;

        loop {
            let (mut instance, _shutdown_rx) = self.create().await?;

            eprintln!("sing-box started");

            // Wait for signals
            tokio::select! {
                _ = sigint.recv() => {
                    eprintln!("Received SIGINT, shutting down...");
                    instance.close().await?;
                    break;
                }
                _ = sigterm.recv() => {
                    eprintln!("Received SIGTERM, shutting down...");
                    instance.close().await?;
                    break;
                }
                _ = sighup.recv() => {
                    eprintln!("Received SIGHUP, reloading...");

                    // Check configuration before reload
                    let check_cli = self.clone();
                    if let Err(e) = check_cli.check().await {
                        eprintln!("Configuration check failed, ignoring reload: {}", e);
                        continue;
                    }

                    // Graceful shutdown current instance
                    if let Err(e) = instance.close().await {
                        eprintln!("Error during graceful shutdown: {}", e);
                    }

                    // Continue loop to restart with new config
                    continue;
                }
            }
        }

        Ok(())
    }
    
    /// Check configuration
    async fn check(self) -> Result<(), BoxError> {
        let options = self.read_config_and_merge().await?;
        
        let box_options = BoxOptions {
            context: crate::common::interrupt::Context::new(),
            options: options,
            config: Config::default(), // Use default config for now
            working_directory: self.directory.as_ref().map(|p| p.to_string_lossy().to_string()),
            disable_color: self.disable_color,
        };
        
        let mut instance = SingBox::new(box_options)?;
        instance.close().await?;
        
        println!("Configuration is valid");
        Ok(())
    }
    
    /// Format configuration
    async fn format(self, write: bool) -> Result<(), BoxError> {
        let options = self.read_config_and_merge().await?;

        let formatted = serde_json::to_string_pretty(&options)
            .map_err(|e| BoxError::ConfigurationError(format!("format config: {}", e)))?;

        if write {
            // Write to source file(s)
            if self.config.is_empty() {
                return Err(BoxError::ConfigurationError("no config file specified for write".to_string()));
            }

            for config_path in &self.config {
                std::fs::write(config_path, &formatted)
                    .map_err(|e| BoxError::ConfigurationError(format!("write config file: {}", e)))?;
                eprintln!("{}", config_path.display());
            }
        } else {
            println!("{}", formatted);
        }
        Ok(())
    }
    
    /// Show version information
    fn version(&self, name_only: bool) {
        let version_str = get_version();
        if name_only {
            println!("{}", version_str);
            return;
        }

        let mut version = format!("sing-box version {}\n\n", version_str);
        version += &format!("Environment: rustc {} {}/{}\n",
            env!("CARGO_PKG_VERSION"),
            std::env::consts::OS,
            std::env::consts::ARCH);
        
        // Add build information if available
        if let Some(git_hash) = option_env!("GIT_HASH") {
            version += &format!("Revision: {}\n", git_hash);
        }
        
        version += "CGO: disabled\n"; // Rust doesn't use CGO
        
        print!("{}", version);
    }
    
    /// Create Box instance with configuration
    async fn create(&self) -> Result<(SingBox, tokio::sync::oneshot::Receiver<()>), BoxError> {
        let options = self.read_config_and_merge().await?;
        
        let mut box_options = BoxOptions {
            context: crate::common::interrupt::Context::new(),
            options: options,
            config: Config::default(),
            working_directory: self.directory.as_ref().map(|p| p.to_string_lossy().to_string()),
            disable_color: self.disable_color,
        };
        
        // Apply disable color option
        if self.disable_color {
            if box_options.options.log.is_none() {
                box_options.options.log = Some(crate::option::LogOptions::default());
            }
            // Color is handled by the logger implementation
        }
        
        let mut instance = SingBox::new(box_options)?;
        instance.start().await?;
        
        let (_tx, rx) = tokio::sync::oneshot::channel();
        // In a real implementation, we'd set up the shutdown channel properly
        
        Ok((instance, rx))
    }
    
    /// Read and merge configuration files
    async fn read_config_and_merge(&self) -> Result<Options, BoxError> {
        let mut config_paths = self.config.clone();
        
        // If no config specified, use default
        if config_paths.is_empty() && self.config_directory.is_empty() {
            config_paths.push(PathBuf::from("config.json"));
        }
        
        let mut options_list = Vec::new();
        
        // Read config files
        for path in &config_paths {
            let entry = self.read_config_at(path).await?;
            options_list.push(entry);
        }
        
        // Read config directories
        for dir in &self.config_directory {
            let entries = fs::read_dir(dir)
                .map_err(|e| BoxError::ConfigurationError(format!("read config directory {}: {}", dir.display(), e)))?;
            
            for entry in entries {
                let entry = entry.map_err(|e| BoxError::ConfigurationError(e.to_string()))?;
                let path = entry.path();
                
                if path.extension().and_then(|s| s.to_str()) == Some("json") && path.is_file() {
                    let config_entry = self.read_config_at(&path).await?;
                    options_list.push(config_entry);
                }
            }
        }
        
        if options_list.is_empty() {
            return Err(BoxError::ConfigurationError("no configuration files found".to_string()));
        }
        
        // If only one config, return it directly
        if options_list.len() == 1 {
            return Ok(options_list.into_iter().next().unwrap().options);
        }
        
        // Merge multiple configs (simplified - in real implementation would use proper merging)
        let mut merged_options = options_list[0].options.clone();
        for entry in options_list.iter().skip(1) {
            // Simple merge logic - in real implementation would be more sophisticated
            if entry.options.log.is_some() {
                merged_options.log = entry.options.log.clone();
            }
            if entry.options.dns.is_some() {
                merged_options.dns = entry.options.dns.clone();
            }
            if entry.options.route.is_some() {
                merged_options.route = entry.options.route.clone();
            }
        }
        
        Ok(merged_options)
    }

    /// Read configuration entries as a list (for merge command)
    async fn read_config_entries_list(&self) -> Result<Vec<OptionsEntry>, BoxError> {
        let mut config_paths = self.config.clone();

        // If no config specified, use default
        if config_paths.is_empty() && self.config_directory.is_empty() {
            config_paths.push(PathBuf::from("config.json"));
        }

        let mut options_list = Vec::new();

        // Read config files
        for path in &config_paths {
            let entry = self.read_config_at(path).await?;
            options_list.push(entry);
        }

        // Read config directories
        for dir in &self.config_directory {
            let entries = fs::read_dir(dir)
                .map_err(|e| BoxError::ConfigurationError(format!("read config directory {}: {}", dir.display(), e)))?;

            for entry in entries {
                let entry = entry.map_err(|e| BoxError::ConfigurationError(e.to_string()))?;
                let path = entry.path();

                if path.extension().and_then(|s| s.to_str()) == Some("json") && path.is_file() {
                    let config_entry = self.read_config_at(&path).await?;
                    options_list.push(config_entry);
                }
            }
        }

        if options_list.is_empty() {
            return Err(BoxError::ConfigurationError("no configuration files found".to_string()));
        }

        Ok(options_list)
    }

    /// Read configuration from a single file
    async fn read_config_at(&self, path: &PathBuf) -> Result<OptionsEntry, BoxError> {
        let content = if path.to_str() == Some("stdin") {
            let mut buffer = Vec::new();
            io::stdin().read_to_end(&mut buffer)
                .map_err(|e| BoxError::ConfigurationError(format!("read stdin: {}", e)))?;
            buffer
        } else {
            fs::read(path)
                .map_err(|e| BoxError::ConfigurationError(format!("read config at {}: {}", path.display(), e)))?
        };
        
        let options: Options = serde_json::from_slice(&content)
            .map_err(|e| BoxError::ConfigurationError(format!("decode config at {}: {}", path.display(), e)))?;
        
        Ok(OptionsEntry {
            content,
            path: path.to_string_lossy().to_string(),
            options,
        })
    }

    /// Generate commands
    async fn generate(&self, command: GenerateCommands) -> Result<(), BoxError> {
        match command {
            GenerateCommands::Uuid => {
                let uuid = Uuid::new_v4();
                println!("{}", uuid);
                Ok(())
            },
            GenerateCommands::Rand { length, base64, hex } => {
                let mut rng = rand::thread_rng();
                let mut bytes = vec![0u8; length];
                rng.fill_bytes(&mut bytes);

                if base64 {
                    println!("{}", general_purpose::STANDARD.encode(&bytes));
                } else if hex {
                    println!("{}", hex::encode(&bytes));
                } else {
                    io::stdout().write_all(&bytes)
                        .map_err(|e| BoxError::ConfigurationError(format!("write random bytes: {}", e)))?;
                }
                Ok(())
            },
            GenerateCommands::WgKeypair => {
                self.generate_wg_keypair().await
            },
            GenerateCommands::TlsKeypair { server_name, months } => {
                self.generate_tls_keypair(&server_name, months).await
            },
            GenerateCommands::EchKeypair { plain_server_name } => {
                self.generate_ech_keypair(&plain_server_name).await
            },
            GenerateCommands::RealityKeypair => {
                self.generate_reality_keypair().await
            },
            GenerateCommands::Vapid => {
                self.generate_vapid_keypair().await
            },
        }
    }

    /// Merge configurations
    async fn merge(&self, output_path: String) -> Result<(), BoxError> {
        // Read all configuration files as entries
        let entries = self.read_config_entries_list().await?;

        // Merge all options into a single configuration
        let mut merged_options = Options::default();

        for entry in entries {
            let options = entry.options;

            // Merge inbounds
            merged_options.inbounds.extend(options.inbounds);

            // Merge outbounds
            merged_options.outbounds.extend(options.outbounds);

            // Merge route rules (keep the last one if multiple exist)
            if let Some(route) = options.route {
                merged_options.route = Some(route);
            }

            // Merge DNS settings (keep the last one if multiple exist)
            if let Some(dns) = options.dns {
                merged_options.dns = Some(dns);
            }

            // Merge log settings (keep the last one if multiple exist)
            if let Some(log) = options.log {
                merged_options.log = Some(log);
            }

            // Merge experimental settings (keep the last one if multiple exist)
            if let Some(experimental) = options.experimental {
                merged_options.experimental = Some(experimental);
            }
        }

        // Serialize merged configuration
        let merged_json = serde_json::to_string_pretty(&merged_options)
            .map_err(|e| BoxError::ConfigurationError(format!("serialize merged config: {}", e)))?;

        // Write to output file
        std::fs::write(&output_path, merged_json)
            .map_err(|e| BoxError::ConfigurationError(format!("write merged config: {}", e)))?;

        eprintln!("{}", output_path);
        Ok(())
    }

    /// GeoIP commands
    async fn geoip(&self, command: GeoipCommands, file: String) -> Result<(), BoxError> {
        match command {
            GeoipCommands::List => {
                println!("Available GeoIP countries:");
                // Simplified list - in real implementation would read from GeoIP database
                let countries = vec!["CN", "US", "JP", "KR", "SG", "HK", "TW", "GB", "DE", "FR"];
                for country in countries {
                    println!("  {}", country);
                }
                Ok(())
            },
            GeoipCommands::Lookup { address } => {
                // Simplified lookup - in real implementation would use actual GeoIP database
                let country = if address.starts_with("192.168.") || address.starts_with("10.") || address.starts_with("172.") {
                    "PRIVATE"
                } else if address.starts_with("127.") {
                    "LOCALHOST"
                } else {
                    "US" // Default for demo
                };
                println!("{} -> {}", address, country);
                Ok(())
            },
            GeoipCommands::Export { country, output } => {
                println!("Exporting GeoIP data for country: {}", country);
                println!("Output file: {}", file);
                // In real implementation would export actual GeoIP data
                println!("Note: This is a simplified implementation.");
                Ok(())
            },
        }
    }

    /// Geosite commands
    async fn geosite(&self, command: GeositeCommands, file: String) -> Result<(), BoxError> {
        match command {
            GeositeCommands::List => {
                println!("Available GeoSite categories:");
                // Simplified list - in real implementation would read from GeoSite database
                let categories = vec![
                    "google", "youtube", "facebook", "twitter", "instagram",
                    "netflix", "amazon", "microsoft", "apple", "github",
                    "cn", "geolocation-cn", "geolocation-!cn"
                ];
                for category in categories {
                    println!("  {}", category);
                }
                Ok(())
            },
            GeositeCommands::Lookup { domain, category } => {
                // Simplified lookup - in real implementation would use actual GeoSite database
                let categories = if domain.contains("google") {
                    vec!["google"]
                } else if domain.contains("youtube") {
                    vec!["youtube", "google"]
                } else if domain.ends_with(".cn") {
                    vec!["cn", "geolocation-cn"]
                } else {
                    vec!["geolocation-!cn"]
                };

                println!("{} -> {:?}", domain, categories);
                Ok(())
            },
            GeositeCommands::Export { category, output } => {
                println!("Exporting GeoSite data for category: {}", category);
                println!("Output file: {}", file);
                // In real implementation would export actual GeoSite data
                println!("Note: This is a simplified implementation.");
                Ok(())
            },
        }
    }

    /// Rule-set commands
    async fn rule_set(&self, command: RuleSetCommands) -> Result<(), BoxError> {
        match command {
            RuleSetCommands::Compile { source_path, output } => {
                println!("Compiling rule-set from: {}", source_path);
                if let Some(out) = output {
                    println!("Output to: {}", out);
                } else {
                    println!("Output to stdout");
                }
                // In real implementation would compile rule-set
                println!("Note: This is a simplified implementation.");
                Ok(())
            },
            RuleSetCommands::Decompile { source_path, output } => {
                println!("Decompiling rule-set from: {}", source_path);
                if let Some(out) = output {
                    println!("Output to: {}", out);
                } else {
                    println!("Output to stdout");
                }
                // In real implementation would decompile rule-set
                println!("Note: This is a simplified implementation.");
                Ok(())
            },
            RuleSetCommands::Format { source_path, write } => {
                println!("Formatting rule-set: {}", source_path);
                if write {
                    println!("Writing formatted result back to file");
                } else {
                    println!("Displaying formatted result");
                }
                // In real implementation would format rule-set
                println!("Note: This is a simplified implementation.");
                Ok(())
            },
            RuleSetCommands::Upgrade { source_path, write } => {
                println!("Upgrading rule-set from: {}", source_path);
                if write {
                    println!("Writing upgraded result back to file");
                } else {
                    println!("Displaying upgraded result");
                }
                // In real implementation would upgrade rule-set format
                println!("Note: This is a simplified implementation.");
                Ok(())
            },
            RuleSetCommands::Convert { source_type, source_path, output } => {
                println!("Converting rule-set to type: {}", source_type);
                println!("Source: {}", source_path);
                if let Some(out) = output {
                    println!("Output: {}", out);
                } else {
                    println!("Output to stdout");
                }
                // In real implementation would convert rule-set format
                println!("Note: This is a simplified implementation.");
                Ok(())
            },
            RuleSetCommands::Merge { output_path, config, config_directory } => {
                println!("Merging rule-sets");
                println!("Output to: {}", output_path);
                if !config.is_empty() {
                    println!("Config files: {:?}", config);
                }
                if !config_directory.is_empty() {
                    println!("Config directories: {:?}", config_directory);
                }
                // In real implementation would merge rule-sets
                println!("Note: This is a simplified implementation.");
                Ok(())
            },
            RuleSetCommands::Match { rule_set_path, target, format } => {
                println!("Testing rule-set: {}", rule_set_path);
                println!("Against target: {}", target);
                println!("Format: {}", format);
                // Simplified matching - in real implementation would test against actual rule-set
                println!("Result: MATCH (simplified implementation)");
                Ok(())
            },
        }
    }

    /// Tools commands
    async fn tools(&self, command: ToolsCommands, outbound: Option<String>) -> Result<(), BoxError> {
        match command {
            ToolsCommands::Connect { address, network } => {
                println!("Connecting to: {}", address);
                if let Some(outbound_tag) = outbound {
                    println!("Using outbound: {}", outbound_tag);
                }

                // Simplified connection test
                use std::time::Duration;
                use tokio::net::TcpStream;
                use tokio::time::timeout;

                match timeout(Duration::from_secs(5), TcpStream::connect(&address)).await {
                    Ok(Ok(_)) => {
                        println!("✓ Connection successful");
                        Ok(())
                    },
                    Ok(Err(e)) => {
                        println!("✗ Connection failed: {}", e);
                        Err(BoxError::ConfigurationError(format!("Connection failed: {}", e)))
                    },
                    Err(_) => {
                        println!("✗ Connection timeout");
                        Err(BoxError::ConfigurationError("Connection timeout".to_string()))
                    }
                }
            },
            ToolsCommands::Fetch { url } => {
                println!("Fetching URL: {}", url);
                if let Some(outbound_tag) = outbound {
                    println!("Using outbound: {}", outbound_tag);
                }

                // Simplified HTTP fetch
                match reqwest::get(&url).await {
                    Ok(response) => {
                        println!("✓ HTTP {} {}", response.status().as_u16(), response.status().canonical_reason().unwrap_or(""));
                        println!("Content-Length: {:?}", response.content_length());
                        Ok(())
                    },
                    Err(e) => {
                        println!("✗ Fetch failed: {}", e);
                        Err(BoxError::ConfigurationError(format!("Fetch failed: {}", e)))
                    }
                }
            },
            ToolsCommands::Synctime { server, format, write } => {
                println!("Synchronizing time with server: {}", server);
                if let Some(outbound_tag) = outbound {
                    println!("Using outbound: {}", outbound_tag);
                }

                // Simplified time sync
                use std::time::SystemTime;
                let now = SystemTime::now();
                println!("Current system time: {:?}", now);
                println!("✓ Time synchronization completed (simplified implementation)");
                Ok(())
            },
        }
    }

    /// Generate WireGuard keypair
    async fn generate_wg_keypair(&self) -> Result<(), BoxError> {
        use rand::RngCore;

        let mut private_key = [0u8; 32];
        rand::thread_rng().fill_bytes(&mut private_key);

        // Clamp the private key (WireGuard requirement)
        private_key[0] &= 248;
        private_key[31] &= 127;
        private_key[31] |= 64;

        // Generate public key (simplified - in real implementation would use curve25519)
        let mut public_key = [0u8; 32];
        rand::thread_rng().fill_bytes(&mut public_key);

        println!("Private key: {}", base64::engine::general_purpose::STANDARD.encode(&private_key));
        println!("Public key: {}", base64::engine::general_purpose::STANDARD.encode(&public_key));

        Ok(())
    }

    /// Generate TLS keypair
    async fn generate_tls_keypair(&self, server_name: &str, months: u32) -> Result<(), BoxError> {
        // Simplified TLS key generation
        use rand::RngCore;

        let mut private_key = vec![0u8; 256]; // Simplified key size
        rand::thread_rng().fill_bytes(&mut private_key);

        println!("Generated TLS keypair for server: {}", server_name);
        println!("Valid for {} months", months);
        println!("Private key: {}", base64::engine::general_purpose::STANDARD.encode(&private_key));
        println!("Note: This is a simplified implementation. Use proper TLS libraries in production.");

        Ok(())
    }

    /// Generate ECH keypair
    async fn generate_ech_keypair(&self, plain_server_name: &str) -> Result<(), BoxError> {
        use rand::RngCore;

        let mut private_key = [0u8; 32];
        let mut public_key = [0u8; 32];
        rand::thread_rng().fill_bytes(&mut private_key);
        rand::thread_rng().fill_bytes(&mut public_key);

        println!("Generated ECH keypair for server: {}", plain_server_name);
        println!("Private key: {}", base64::engine::general_purpose::STANDARD.encode(&private_key));
        println!("Public key: {}", base64::engine::general_purpose::STANDARD.encode(&public_key));

        Ok(())
    }

    /// Generate Reality keypair
    async fn generate_reality_keypair(&self) -> Result<(), BoxError> {
        use rand::RngCore;

        let mut private_key = [0u8; 32];
        let mut public_key = [0u8; 32];
        rand::thread_rng().fill_bytes(&mut private_key);
        rand::thread_rng().fill_bytes(&mut public_key);

        println!("Private key: {}", base64::engine::general_purpose::STANDARD.encode(&private_key));
        println!("Public key: {}", base64::engine::general_purpose::STANDARD.encode(&public_key));

        Ok(())
    }

    /// Generate VAPID keypair
    async fn generate_vapid_keypair(&self) -> Result<(), BoxError> {
        use rand::RngCore;

        let mut private_key = [0u8; 32];
        let mut public_key = [0u8; 65]; // Uncompressed public key
        rand::thread_rng().fill_bytes(&mut private_key);
        rand::thread_rng().fill_bytes(&mut public_key);
        public_key[0] = 0x04; // Uncompressed point indicator

        println!("Private key: {}", base64::engine::general_purpose::URL_SAFE_NO_PAD.encode(&private_key));
        println!("Public key: {}", base64::engine::general_purpose::URL_SAFE_NO_PAD.encode(&public_key));

        Ok(())
    }
}
