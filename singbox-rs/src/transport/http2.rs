/// HTTP/2 transport implementation
/// 
/// Provides HTTP/2 transport for protocols that need multiplexing

use std::io;
use std::net::SocketAddr;
use std::pin::Pin;
use std::task::{Context, Poll};
use tokio::io::{AsyncRead, AsyncWrite, ReadBuf};
use h2::{client, server, SendStream, RecvStream};
use http::{Request, Response, Method, Uri};
use bytes::Bytes;

use super::{TransportConnection, TransportDialer, TransportListener, TransportError, ConnectionStats};
use async_trait::async_trait;
use crate::adapter::{Lifecycle, StartStage};

/// HTTP/2 configuration
#[derive(Debug, Clone)]
pub struct Http2Config {
    /// HTTP/2 path for requests
    pub path: String,
    /// Custom headers
    pub headers: std::collections::HashMap<String, String>,
    /// Maximum frame size
    pub max_frame_size: u32,
    /// Initial window size
    pub initial_window_size: u32,
    /// Maximum concurrent streams
    pub max_concurrent_streams: u32,
}

impl Default for Http2Config {
    fn default() -> Self {
        Self {
            path: "/".to_string(),
            headers: std::collections::HashMap::new(),
            max_frame_size: 16384, // 16KB
            initial_window_size: 65535, // 64KB - 1
            max_concurrent_streams: 100,
        }
    }
}

/// HTTP/2 client connection
pub struct Http2ClientConnection {
    send_stream: SendStream<Bytes>,
    recv_stream: RecvStream,
    stats: ConnectionStats,
    read_buffer: Vec<u8>,
}

impl Http2ClientConnection {
    pub fn new(send_stream: SendStream<Bytes>, recv_stream: RecvStream) -> Self {
        Self {
            send_stream,
            recv_stream,
            stats: ConnectionStats::new(),
            read_buffer: Vec::new(),
        }
    }
}

impl AsyncRead for Http2ClientConnection {
    fn poll_read(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &mut ReadBuf<'_>,
    ) -> Poll<io::Result<()>> {
        // If we have data in the read buffer, return it first
        if !self.read_buffer.is_empty() {
            let to_copy = std::cmp::min(buf.remaining(), self.read_buffer.len());
            buf.put_slice(&self.read_buffer[..to_copy]);
            self.read_buffer.drain(..to_copy);
            self.stats.bytes_received += to_copy as u64;
            return Poll::Ready(Ok(()));
        }

        // Try to read data from the HTTP/2 stream
        match Pin::new(&mut self.recv_stream).poll_data(cx) {
            Poll::Ready(Some(Ok(data))) => {
                let to_copy = std::cmp::min(buf.remaining(), data.len());
                buf.put_slice(&data[..to_copy]);
                
                // Store remaining data in buffer
                if data.len() > to_copy {
                    self.read_buffer.extend_from_slice(&data[to_copy..]);
                }
                
                self.stats.bytes_received += data.len() as u64;
                self.stats.packets_received += 1;
                
                // Release flow control window
                let _ = self.recv_stream.flow_control().release_capacity(data.len());
                
                Poll::Ready(Ok(()))
            }
            Poll::Ready(Some(Err(e))) => {
                Poll::Ready(Err(io::Error::new(io::ErrorKind::Other, e)))
            }
            Poll::Ready(None) => {
                Poll::Ready(Ok(()))
            }
            Poll::Pending => Poll::Pending,
        }
    }
}

impl AsyncWrite for Http2ClientConnection {
    fn poll_write(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &[u8],
    ) -> Poll<Result<usize, io::Error>> {
        match self.send_stream.poll_capacity(cx) {
            Poll::Ready(Some(Ok(capacity))) => {
                let to_send = std::cmp::min(buf.len(), capacity);
                let data = Bytes::copy_from_slice(&buf[..to_send]);
                
                match self.send_stream.send_data(data, false) {
                    Ok(()) => {
                        self.stats.bytes_sent += to_send as u64;
                        self.stats.packets_sent += 1;
                        Poll::Ready(Ok(to_send))
                    }
                    Err(e) => Poll::Ready(Err(io::Error::new(io::ErrorKind::Other, e))),
                }
            }
            Poll::Ready(Some(Err(e))) => Poll::Ready(Err(io::Error::new(io::ErrorKind::Other, e))),
            Poll::Ready(None) => Poll::Ready(Err(io::Error::new(io::ErrorKind::UnexpectedEof, "Stream closed"))),
            Poll::Pending => Poll::Pending,
        }
    }

    fn poll_flush(mut self: Pin<&mut Self>, _cx: &mut Context<'_>) -> Poll<Result<(), io::Error>> {
        // HTTP/2 streams are automatically flushed
        Poll::Ready(Ok(()))
    }

    fn poll_shutdown(mut self: Pin<&mut Self>, _cx: &mut Context<'_>) -> Poll<Result<(), io::Error>> {
        match self.send_stream.send_data(Bytes::new(), true) {
            Ok(()) => Poll::Ready(Ok(())),
            Err(e) => Poll::Ready(Err(io::Error::new(io::ErrorKind::Other, e))),
        }
    }
}

impl TransportConnection for Http2ClientConnection {
    fn local_addr(&self) -> Result<SocketAddr, TransportError> {
        Err(TransportError::ProtocolError("HTTP/2 doesn't expose socket addresses".to_string()))
    }

    fn remote_addr(&self) -> Result<SocketAddr, TransportError> {
        Err(TransportError::ProtocolError("HTTP/2 doesn't expose socket addresses".to_string()))
    }

    fn is_secure(&self) -> bool {
        true // HTTP/2 is typically used over TLS
    }

    fn protocol(&self) -> &str {
        "http2"
    }

    fn stats(&self) -> ConnectionStats {
        self.stats.clone()
    }
}

/// HTTP/2 dialer for creating client connections
pub struct Http2Dialer {
    config: Http2Config,
}

impl Http2Dialer {
    pub fn new(config: Http2Config) -> Self {
        Self { config }
    }
}

#[async_trait]
impl Lifecycle for Http2Dialer {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        Ok(())
    }
}

impl TransportDialer for Http2Dialer {
    type Connection = Http2ClientConnection;

    async fn dial(&self, addr: &str) -> Result<Self::Connection, TransportError> {
        // Connect to the server
        let tcp_stream = tokio::net::TcpStream::connect(addr).await
            .map_err(|e| TransportError::ConnectionFailed(e.to_string()))?;

        // Perform HTTP/2 handshake
        let (mut client, connection) = client::handshake(tcp_stream).await
            .map_err(|e| TransportError::ProtocolError(format!("HTTP/2 handshake failed: {}", e)))?;

        // Spawn connection task
        tokio::spawn(async move {
            if let Err(e) = connection.await {
                eprintln!("HTTP/2 connection error: {}", e);
            }
        });

        // Wait for the client to be ready and get the ready client
        let mut ready_client = client.ready().await
            .map_err(|e| TransportError::ProtocolError(format!("HTTP/2 client not ready: {}", e)))?;

        // Create HTTP request
        let mut request = Request::builder()
            .method(Method::POST)
            .uri(&self.config.path);

        // Add custom headers
        for (key, value) in &self.config.headers {
            request = request.header(key, value);
        }

        let request = request.body(())
            .map_err(|e| TransportError::ConfigurationError(format!("Failed to build HTTP request: {}", e)))?;

        // Send request and get response
        let (response, send_stream) = ready_client.send_request(request, false)
            .map_err(|e| TransportError::ProtocolError(format!("Failed to send HTTP/2 request: {}", e)))?;

        // Wait for response headers
        let response = response.await
            .map_err(|e| TransportError::ProtocolError(format!("Failed to receive HTTP/2 response: {}", e)))?;

        let recv_stream = response.into_body();

        Ok(Http2ClientConnection::new(send_stream, recv_stream))
    }

    async fn dial_timeout(&self, addr: &str, timeout: std::time::Duration) -> Result<Self::Connection, TransportError> {
        tokio::time::timeout(timeout, self.dial(addr)).await
            .map_err(|_| TransportError::Timeout)?
    }

    fn name(&self) -> &str {
        "http2"
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_http2_config_default() {
        let config = Http2Config::default();
        assert_eq!(config.path, "/");
        assert!(config.headers.is_empty());
        assert_eq!(config.max_frame_size, 16384);
        assert_eq!(config.initial_window_size, 65535);
        assert_eq!(config.max_concurrent_streams, 100);
    }

    #[test]
    fn test_http2_dialer_creation() {
        let config = Http2Config::default();
        let dialer = Http2Dialer::new(config);
        assert_eq!(dialer.name(), "http2");
    }

    #[test]
    fn test_connection_stats() {
        let stats = ConnectionStats::new();
        assert_eq!(stats.bytes_sent, 0);
        assert_eq!(stats.bytes_received, 0);
        assert!(stats.duration().as_millis() >= 0);
    }
}
