/// Transport layer implementations for SingBox
/// 
/// This module provides various transport layer implementations including:
/// - TLS/SSL encryption
/// - WebSocket transport
/// - HTTP/2 transport
/// - QUIC transport

pub mod tls;
pub mod websocket;
pub mod http2;
pub mod quic;

use std::io;
use std::net::SocketAddr;
use tokio::io::{AsyncRead, AsyncWrite};
use crate::adapter::Lifecycle;

/// Transport error types
#[derive(Debug, Clone)]
pub enum TransportError {
    /// Connection failed
    ConnectionFailed(String),
    /// TLS handshake failed
    TlsHandshakeFailed(String),
    /// Certificate verification failed
    CertificateVerificationFailed(String),
    /// Protocol error
    ProtocolError(String),
    /// IO error
    IoError(String),
    /// Timeout error
    Timeout,
    /// Configuration error
    ConfigurationError(String),
}

impl std::fmt::Display for TransportError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TransportError::ConnectionFailed(msg) => write!(f, "Connection failed: {}", msg),
            TransportError::TlsHandshakeFailed(msg) => write!(f, "TLS handshake failed: {}", msg),
            TransportError::CertificateVerificationFailed(msg) => write!(f, "Certificate verification failed: {}", msg),
            TransportError::ProtocolError(msg) => write!(f, "Protocol error: {}", msg),
            TransportError::IoError(msg) => write!(f, "IO error: {}", msg),
            TransportError::Timeout => write!(f, "Timeout"),
            TransportError::ConfigurationError(msg) => write!(f, "Configuration error: {}", msg),
        }
    }
}

impl std::error::Error for TransportError {}

impl From<io::Error> for TransportError {
    fn from(err: io::Error) -> Self {
        TransportError::IoError(err.to_string())
    }
}

/// Transport connection trait
pub trait TransportConnection: AsyncRead + AsyncWrite + Send + Sync + Unpin {
    /// Get the local address
    fn local_addr(&self) -> Result<SocketAddr, TransportError>;
    
    /// Get the remote address
    fn remote_addr(&self) -> Result<SocketAddr, TransportError>;
    
    /// Check if the connection is secure (encrypted)
    fn is_secure(&self) -> bool;
    
    /// Get transport protocol name
    fn protocol(&self) -> &str;
    
    /// Get connection statistics
    fn stats(&self) -> ConnectionStats;
}

/// Connection statistics
#[derive(Debug, Clone)]
pub struct ConnectionStats {
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
    pub established_at: std::time::Instant,
}

impl Default for ConnectionStats {
    fn default() -> Self {
        Self::new()
    }
}

impl ConnectionStats {
    pub fn new() -> Self {
        Self {
            bytes_sent: 0,
            bytes_received: 0,
            packets_sent: 0,
            packets_received: 0,
            established_at: std::time::Instant::now(),
        }
    }

    pub fn duration(&self) -> std::time::Duration {
        self.established_at.elapsed()
    }
}

/// Transport dialer trait
pub trait TransportDialer: Lifecycle + Send + Sync {
    type Connection: TransportConnection;
    
    /// Dial to a remote address
    async fn dial(&self, addr: &str) -> Result<Self::Connection, TransportError>;
    
    /// Dial with timeout
    async fn dial_timeout(&self, addr: &str, timeout: std::time::Duration) -> Result<Self::Connection, TransportError>;
    
    /// Get dialer name
    fn name(&self) -> &str;
}

/// Transport listener trait
pub trait TransportListener: Lifecycle + Send + Sync {
    type Connection: TransportConnection;
    
    /// Accept incoming connections
    async fn accept(&mut self) -> Result<Self::Connection, TransportError>;
    
    /// Get local address
    fn local_addr(&self) -> Result<SocketAddr, TransportError>;
    
    /// Get listener name
    fn name(&self) -> &str;
}

/// Transport configuration
#[derive(Debug, Clone)]
pub struct TransportConfig {
    pub name: String,
    pub transport_type: String,
    pub options: serde_json::Value,
}

impl Default for TransportConfig {
    fn default() -> Self {
        Self {
            name: "default".to_string(),
            transport_type: "tcp".to_string(),
            options: serde_json::Value::Object(serde_json::Map::new()),
        }
    }
}

/// Transport types enum for concrete implementations
#[derive(Debug, Clone)]
pub enum TransportType {
    Tcp,
    Tls,
    WebSocket,
    Http2,
    Quic,
}

impl std::str::FromStr for TransportType {
    type Err = TransportError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "tcp" => Ok(TransportType::Tcp),
            "tls" => Ok(TransportType::Tls),
            "websocket" | "ws" => Ok(TransportType::WebSocket),
            "http2" | "h2" => Ok(TransportType::Http2),
            "quic" => Ok(TransportType::Quic),
            _ => Err(TransportError::ConfigurationError(format!("Unknown transport type: {}", s))),
        }
    }
}

/// Transport factory for creating dialers and listeners
pub struct TransportFactory {
    supported_types: Vec<TransportType>,
}

impl TransportFactory {
    pub fn new() -> Self {
        Self {
            supported_types: vec![
                TransportType::Tcp,
                TransportType::Tls,
                TransportType::WebSocket,
                TransportType::Http2,
                TransportType::Quic,
            ],
        }
    }

    pub fn supports_type(&self, transport_type: &str) -> bool {
        transport_type.parse::<TransportType>().is_ok()
    }

    pub fn create_tls_dialer(&self, config: &tls::TlsClientConfig) -> Result<tls::TlsDialer, TransportError> {
        tls::TlsDialer::new(config.clone())
    }

    pub fn create_websocket_dialer(&self, config: &websocket::WebSocketConfig) -> Result<websocket::WebSocketDialer, TransportError> {
        Ok(websocket::WebSocketDialer::new(config.clone()))
    }

    pub fn create_http2_dialer(&self, config: &http2::Http2Config) -> Result<http2::Http2Dialer, TransportError> {
        Ok(http2::Http2Dialer::new(config.clone()))
    }

    pub fn create_quic_dialer(&self, config: &quic::QuicConfig) -> Result<quic::QuicDialer, TransportError> {
        quic::QuicDialer::new(config.clone())
    }

    pub fn create_tls_listener(&self, config: &tls::TlsServerConfig, bind_addr: std::net::SocketAddr) -> Result<tls::TlsListener, TransportError> {
        tls::TlsListener::new(config.clone(), bind_addr)
    }

    pub fn list_supported_types(&self) -> Vec<String> {
        self.supported_types.iter()
            .map(|t| format!("{:?}", t).to_lowercase())
            .collect()
    }

    pub fn list_dialers(&self) -> Vec<String> {
        self.list_supported_types()
    }

    pub fn list_listeners(&self) -> Vec<String> {
        vec!["tls".to_string()] // Only TLS listener is implemented
    }
}

impl Default for TransportFactory {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_transport_error_display() {
        let err = TransportError::ConnectionFailed("test error".to_string());
        assert_eq!(err.to_string(), "Connection failed: test error");
        
        let err = TransportError::Timeout;
        assert_eq!(err.to_string(), "Timeout");
    }
    
    #[test]
    fn test_connection_stats() {
        let stats = ConnectionStats::new();
        assert_eq!(stats.bytes_sent, 0);
        assert_eq!(stats.bytes_received, 0);
        assert!(stats.duration().as_millis() >= 0);
    }
    
    #[test]
    fn test_transport_config_default() {
        let config = TransportConfig::default();
        assert_eq!(config.name, "default");
        assert_eq!(config.transport_type, "tcp");
    }
    
    #[test]
    fn test_transport_factory() {
        let factory = TransportFactory::new();
        assert_eq!(factory.list_dialers().len(), 5); // 修复：应该有5个支持的传输类型
        assert_eq!(factory.list_listeners().len(), 1); // 修复：应该有1个监听器类型
    }
}
