/// WebSocket transport implementation
/// 
/// Provides WebSocket transport for protocols that need to tunnel through HTTP

use std::io;
use std::net::SocketAddr;
use std::pin::Pin;
use std::task::{Context, Poll};
use tokio::io::{AsyncRead, AsyncWrite, ReadBuf};
use tokio_tungstenite::{WebSocketStream, MaybeTlsStream, connect_async};
use tokio_tungstenite::tungstenite::Message;
use futures_util::{SinkExt, StreamExt, Stream, Sink};
use url::Url;

use super::{TransportConnection, TransportDialer, TransportListener, TransportError, ConnectionStats};
use async_trait::async_trait;
use crate::adapter::{Lifecycle, StartStage};

/// WebSocket configuration
#[derive(Debug, Clone)]
pub struct WebSocketConfig {
    /// WebSocket URL path
    pub path: String,
    /// Custom headers
    pub headers: std::collections::HashMap<String, String>,
    /// Maximum message size
    pub max_message_size: usize,
    /// Enable compression
    pub compression: bool,
}

impl Default for WebSocketConfig {
    fn default() -> Self {
        Self {
            path: "/".to_string(),
            headers: std::collections::HashMap::new(),
            max_message_size: 64 * 1024 * 1024, // 64MB
            compression: false,
        }
    }
}

/// WebSocket connection wrapper
pub struct WebSocketConnection {
    inner: WebSocketStream<MaybeTlsStream<tokio::net::TcpStream>>,
    stats: ConnectionStats,
    read_buffer: Vec<u8>,
    write_buffer: Vec<u8>,
}

impl WebSocketConnection {
    pub fn new(stream: WebSocketStream<MaybeTlsStream<tokio::net::TcpStream>>) -> Self {
        Self {
            inner: stream,
            stats: ConnectionStats::new(),
            read_buffer: Vec::new(),
            write_buffer: Vec::new(),
        }
    }
}

impl AsyncRead for WebSocketConnection {
    fn poll_read(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &mut ReadBuf<'_>,
    ) -> Poll<io::Result<()>> {
        // If we have data in the read buffer, return it first
        if !self.read_buffer.is_empty() {
            let to_copy = std::cmp::min(buf.remaining(), self.read_buffer.len());
            buf.put_slice(&self.read_buffer[..to_copy]);
            self.read_buffer.drain(..to_copy);
            self.stats.bytes_received += to_copy as u64;
            return Poll::Ready(Ok(()));
        }

        // Try to read a message from the WebSocket
        match Pin::new(&mut self.inner).poll_next(cx) {
            Poll::Ready(Some(Ok(Message::Binary(data)))) => {
                let to_copy = std::cmp::min(buf.remaining(), data.len());
                buf.put_slice(&data[..to_copy]);
                
                // Store remaining data in buffer
                if data.len() > to_copy {
                    self.read_buffer.extend_from_slice(&data[to_copy..]);
                }
                
                self.stats.bytes_received += data.len() as u64;
                self.stats.packets_received += 1;
                Poll::Ready(Ok(()))
            }
            Poll::Ready(Some(Ok(Message::Close(_)))) => {
                Poll::Ready(Ok(()))
            }
            Poll::Ready(Some(Ok(_))) => {
                // Ignore non-binary messages
                self.poll_read(cx, buf)
            }
            Poll::Ready(Some(Err(e))) => {
                Poll::Ready(Err(io::Error::new(io::ErrorKind::Other, e)))
            }
            Poll::Ready(None) => {
                Poll::Ready(Ok(()))
            }
            Poll::Pending => Poll::Pending,
        }
    }
}

impl AsyncWrite for WebSocketConnection {
    fn poll_write(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &[u8],
    ) -> Poll<Result<usize, io::Error>> {
        // Add data to write buffer
        self.write_buffer.extend_from_slice(buf);
        
        // Try to send the buffered data as a WebSocket message
        let message = Message::Binary(self.write_buffer.clone());
        match Pin::new(&mut self.inner).poll_ready(cx) {
            Poll::Ready(Ok(())) => {
                match Pin::new(&mut self.inner).start_send(message) {
                    Ok(()) => {
                        let bytes_written = self.write_buffer.len();
                        self.write_buffer.clear();
                        self.stats.bytes_sent += bytes_written as u64;
                        self.stats.packets_sent += 1;
                        Poll::Ready(Ok(buf.len()))
                    }
                    Err(e) => Poll::Ready(Err(io::Error::new(io::ErrorKind::Other, e))),
                }
            }
            Poll::Ready(Err(e)) => Poll::Ready(Err(io::Error::new(io::ErrorKind::Other, e))),
            Poll::Pending => Poll::Pending,
        }
    }

    fn poll_flush(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), io::Error>> {
        Pin::new(&mut self.inner).poll_flush(cx)
            .map_err(|e| io::Error::new(io::ErrorKind::Other, e))
    }

    fn poll_shutdown(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), io::Error>> {
        Pin::new(&mut self.inner).poll_close(cx)
            .map_err(|e| io::Error::new(io::ErrorKind::Other, e))
    }
}

impl TransportConnection for WebSocketConnection {
    fn local_addr(&self) -> Result<SocketAddr, TransportError> {
        // WebSocket doesn't directly expose socket addresses
        // This is a limitation of the WebSocket abstraction
        Err(TransportError::ProtocolError("WebSocket doesn't expose socket addresses".to_string()))
    }

    fn remote_addr(&self) -> Result<SocketAddr, TransportError> {
        // WebSocket doesn't directly expose socket addresses
        Err(TransportError::ProtocolError("WebSocket doesn't expose socket addresses".to_string()))
    }

    fn is_secure(&self) -> bool {
        // Check if the underlying stream is TLS
        // Note: MaybeTlsStream doesn't expose TLS status directly in this version
        false // Simplified for now
    }

    fn protocol(&self) -> &str {
        "websocket"
    }

    fn stats(&self) -> ConnectionStats {
        self.stats.clone()
    }
}

/// WebSocket dialer for creating client connections
pub struct WebSocketDialer {
    config: WebSocketConfig,
}

impl WebSocketDialer {
    pub fn new(config: WebSocketConfig) -> Self {
        Self { config }
    }
}

#[async_trait]
impl Lifecycle for WebSocketDialer {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        Ok(())
    }
}

impl TransportDialer for WebSocketDialer {
    type Connection = WebSocketConnection;

    async fn dial(&self, addr: &str) -> Result<Self::Connection, TransportError> {
        // Parse the address and construct WebSocket URL
        let url = if addr.starts_with("ws://") || addr.starts_with("wss://") {
            addr.to_string()
        } else {
            format!("ws://{}{}", addr, self.config.path)
        };

        let url = Url::parse(&url)
            .map_err(|e| TransportError::ConfigurationError(format!("Invalid WebSocket URL: {}", e)))?;

        // Create request with custom headers
        let mut request = tokio_tungstenite::tungstenite::handshake::client::Request::builder()
            .uri(url.as_str());

        for (key, value) in &self.config.headers {
            request = request.header(key, value);
        }

        let request = request.body(())
            .map_err(|e| TransportError::ConfigurationError(format!("Failed to build WebSocket request: {}", e)))?;

        let (ws_stream, _response) = connect_async(request).await
            .map_err(|e| TransportError::ConnectionFailed(e.to_string()))?;

        Ok(WebSocketConnection::new(ws_stream))
    }

    async fn dial_timeout(&self, addr: &str, timeout: std::time::Duration) -> Result<Self::Connection, TransportError> {
        tokio::time::timeout(timeout, self.dial(addr)).await
            .map_err(|_| TransportError::Timeout)?
    }

    fn name(&self) -> &str {
        "websocket"
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_websocket_config_default() {
        let config = WebSocketConfig::default();
        assert_eq!(config.path, "/");
        assert!(config.headers.is_empty());
        assert_eq!(config.max_message_size, 64 * 1024 * 1024);
        assert!(!config.compression);
    }

    #[test]
    fn test_websocket_dialer_creation() {
        let config = WebSocketConfig::default();
        let dialer = WebSocketDialer::new(config);
        assert_eq!(dialer.name(), "websocket");
    }

    #[test]
    fn test_connection_stats() {
        let stats = ConnectionStats::new();
        assert_eq!(stats.bytes_sent, 0);
        assert_eq!(stats.bytes_received, 0);
        assert!(stats.duration().as_millis() >= 0);
    }
}
