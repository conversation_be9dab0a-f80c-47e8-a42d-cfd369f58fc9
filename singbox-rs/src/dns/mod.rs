//! DNS module for sing-box
//! 
//! This module provides DNS client functionality including caching,
//! transport management, and query routing.

use std::collections::HashMap;
use std::net::{IpAddr, SocketAddr};
use std::time::{Duration, Instant};
use std::sync::Mutex;
use async_trait::async_trait;
use crate::adapter::{Lifecycle, StartStage};
use crate::constant::dns::{DomainStrategy, DEFAULT_DNS_TTL};

pub mod resolver;

// Re-export modern DNS types for compatibility
// Note: modern module doesn't exist yet, commenting out for now
// pub use resolver::modern::{
//     ModernDnsResolver, ModernDnsConfig, DnsServerConfig, DnsProtocol,
//     LoadBalanceStrategy, DnsCacheConfig,
// };

// Re-export resolver types
pub use resolver::{DNSResolver, DNSUpstream, DNSProtocol};



/// DNS client errors
#[derive(Debug, Clone)]
pub enum DNSError {
    NoRawSupport,
    NotCached,
    ResponseRejected,
    ResponseRejectedCached,
    TransportError(String),
    InvalidQuery(String),
    Timeout,
    ParseError(String),
    ServerError(String),
    ResolutionFailed(String),
}

impl std::fmt::Display for DNSError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DNSError::NoRawSupport => write!(f, "no raw query support by current transport"),
            DNSError::NotCached => write!(f, "not cached"),
            DNSError::ResponseRejected => write!(f, "response rejected"),
            DNSError::ResponseRejectedCached => write!(f, "response rejected (cached)"),
            DNSError::TransportError(msg) => write!(f, "transport error: {}", msg),
            DNSError::InvalidQuery(msg) => write!(f, "invalid query: {}", msg),
            DNSError::Timeout => write!(f, "DNS query timeout"),
            DNSError::ParseError(msg) => write!(f, "DNS parse error: {}", msg),
            DNSError::ServerError(msg) => write!(f, "DNS server error: {}", msg),
            DNSError::ResolutionFailed(msg) => write!(f, "DNS resolution failed: {}", msg),
        }
    }
}

impl std::error::Error for DNSError {}

/// DNS query structure
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct DNSQuery {
    pub name: String,
    pub query_type: u16,
    pub query_class: u16,
}

impl DNSQuery {
    pub fn new(name: &str, query_type: u16) -> Self {
        Self {
            name: name.to_lowercase(),
            query_type,
            query_class: 1, // IN class
        }
    }

    pub fn a(name: &str) -> Self {
        Self::new(name, 1) // A record
    }

    pub fn aaaa(name: &str) -> Self {
        Self::new(name, 28) // AAAA record
    }
}

/// DNS response structure
#[derive(Debug, Clone)]
pub struct DNSResponse {
    pub query: DNSQuery,
    pub answers: Vec<DNSRecord>,
    pub authorities: Vec<DNSRecord>,
    pub additionals: Vec<DNSRecord>,
    pub response_code: u16,
    pub truncated: bool,
    pub authoritative: bool,
    pub recursion_available: bool,
    pub ttl: u32,
    pub cached_at: Option<Instant>,
}

impl DNSResponse {
    pub fn new(query: DNSQuery) -> Self {
        Self {
            query,
            answers: Vec::new(),
            authorities: Vec::new(),
            additionals: Vec::new(),
            response_code: 0,
            truncated: false,
            authoritative: false,
            recursion_available: false,
            ttl: DEFAULT_DNS_TTL,
            cached_at: None,
        }
    }

    pub fn is_expired(&self) -> bool {
        if let Some(cached_at) = self.cached_at {
            cached_at.elapsed().as_secs() > self.ttl as u64
        } else {
            false
        }
    }

    pub fn extract_ips(&self) -> Vec<IpAddr> {
        self.answers.iter()
            .filter_map(|record| match &record.data {
                DNSRecordData::A(ip) => Some(IpAddr::V4(*ip)),
                DNSRecordData::AAAA(ip) => Some(IpAddr::V6(*ip)),
                _ => None,
            })
            .collect()
    }
}

/// DNS record structure
#[derive(Debug, Clone)]
pub struct DNSRecord {
    pub name: String,
    pub record_type: u16,
    pub record_class: u16,
    pub ttl: u32,
    pub data: DNSRecordData,
}

/// DNS record data enumeration
#[derive(Debug, Clone)]
pub enum DNSRecordData {
    A(std::net::Ipv4Addr),
    AAAA(std::net::Ipv6Addr),
    CNAME(String),
    MX { priority: u16, exchange: String },
    TXT(Vec<String>),
    NS(String),
    PTR(String),
    SOA {
        mname: String,
        rname: String,
        serial: u32,
        refresh: u32,
        retry: u32,
        expire: u32,
        minimum: u32,
    },
    Unknown(Vec<u8>),
}

/// DNS client configuration
#[derive(Debug, Clone)]
pub struct ClientOptions {
    pub timeout: Duration,
    pub disable_cache: bool,
    pub disable_expire: bool,
    pub independent_cache: bool,
    pub cache_capacity: u32,
    pub client_subnet: Option<String>,
    pub strategy: DomainStrategy,
}

impl Default for ClientOptions {
    fn default() -> Self {
        Self {
            timeout: Duration::from_secs(10),
            disable_cache: false,
            disable_expire: false,
            independent_cache: false,
            cache_capacity: 1024,
            client_subnet: None,
            strategy: 0, // DOMAIN_STRATEGY_AS_IS
        }
    }
}

/// DNS client implementation
pub struct Client {
    options: ClientOptions,
    cache: Mutex<HashMap<DNSQuery, DNSResponse>>,
    transports: Mutex<Vec<Box<dyn DNSTransport>>>,
}

impl Client {
    pub fn new(options: ClientOptions) -> Self {
        Self {
            options,
            cache: Mutex::new(HashMap::new()),
            transports: Mutex::new(Vec::new()),
        }
    }

    pub fn add_transport(&mut self, transport: Box<dyn DNSTransport>) {
        self.transports.lock().unwrap().push(transport);
    }

    pub fn query(&mut self, query: DNSQuery) -> Result<DNSResponse, DNSError> {
        // Check cache first
        if !self.options.disable_cache {
            if let Some(cached) = self.cache.lock().unwrap().get(&query) {
                if !self.options.disable_expire && !cached.is_expired() {
                    return Ok(cached.clone());
                }
            }
        }

        // Query from transports
        for transport in self.transports.lock().unwrap().iter_mut() {
            match transport.query(&query) {
                Ok(mut response) => {
                    // Cache the response
                    if !self.options.disable_cache {
                        response.cached_at = Some(Instant::now());
                        self.cache.lock().unwrap().insert(query.clone(), response.clone());
                    }
                    return Ok(response);
                }
                Err(_) => continue,
            }
        }

        Err(DNSError::TransportError("all transports failed".to_string()))
    }

    pub fn clear_cache(&mut self) {
        self.cache.lock().unwrap().clear();
    }
}

#[async_trait]
impl Lifecycle for Client {
    async fn start(&self, stage: StartStage) -> Result<(), String> {
        // We can't hold the lock across await points, so we'll need to restructure this
        // For now, just return Ok as DNS client doesn't need complex startup
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        // We can't hold the lock across await points, so we'll need to restructure this
        // For now, just clear the cache
        self.cache.lock().unwrap().clear();
        Ok(())
    }
}

/// DNS transport trait
pub trait DNSTransport: Lifecycle + Send + Sync {
    fn query(&mut self, query: &DNSQuery) -> Result<DNSResponse, DNSError>;
    fn transport_type(&self) -> &str;
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{Ipv4Addr, Ipv6Addr};

    struct MockTransport {
        transport_type: String,
        should_fail: bool,
    }

    impl MockTransport {
        fn new(transport_type: &str) -> Self {
            Self {
                transport_type: transport_type.to_string(),
                should_fail: false,
            }
        }

        fn with_failure(mut self) -> Self {
            self.should_fail = true;
            self
        }
    }

    impl DNSTransport for MockTransport {
        fn query(&mut self, query: &DNSQuery) -> Result<DNSResponse, DNSError> {
            if self.should_fail {
                return Err(DNSError::TransportError("mock failure".to_string()));
            }

            let mut response = DNSResponse::new(query.clone());
            
            // Mock response based on query type
            match query.query_type {
                1 => { // A record
                    response.answers.push(DNSRecord {
                        name: query.name.clone(),
                        record_type: 1,
                        record_class: 1,
                        ttl: 300,
                        data: DNSRecordData::A(Ipv4Addr::new(192, 168, 1, 1)),
                    });
                }
                28 => { // AAAA record
                    response.answers.push(DNSRecord {
                        name: query.name.clone(),
                        record_type: 28,
                        record_class: 1,
                        ttl: 300,
                        data: DNSRecordData::AAAA(Ipv6Addr::new(0x2001, 0xdb8, 0, 0, 0, 0, 0, 1)),
                    });
                }
                _ => {}
            }

            Ok(response)
        }

        fn transport_type(&self) -> &str {
            &self.transport_type
        }
    }

    #[async_trait]
    impl Lifecycle for MockTransport {
        async fn start(&self, _stage: StartStage) -> Result<(), String> {
            Ok(())
        }

        async fn close(&self) -> Result<(), String> {
            Ok(())
        }
    }

    #[test]
    fn test_dns_query_creation() {
        let query = DNSQuery::new("example.com", 1);
        assert_eq!(query.name, "example.com");
        assert_eq!(query.query_type, 1);
        assert_eq!(query.query_class, 1);
    }

    #[test]
    fn test_dns_query_helpers() {
        let a_query = DNSQuery::a("example.com");
        assert_eq!(a_query.query_type, 1);
        
        let aaaa_query = DNSQuery::aaaa("example.com");
        assert_eq!(aaaa_query.query_type, 28);
    }

    #[test]
    fn test_dns_response_creation() {
        let query = DNSQuery::a("example.com");
        let response = DNSResponse::new(query.clone());
        
        assert_eq!(response.query, query);
        assert!(response.answers.is_empty());
        assert_eq!(response.response_code, 0);
        assert_eq!(response.ttl, DEFAULT_DNS_TTL);
    }

    #[test]
    fn test_dns_response_extract_ips() {
        let query = DNSQuery::a("example.com");
        let mut response = DNSResponse::new(query);
        
        response.answers.push(DNSRecord {
            name: "example.com".to_string(),
            record_type: 1,
            record_class: 1,
            ttl: 300,
            data: DNSRecordData::A(Ipv4Addr::new(192, 168, 1, 1)),
        });

        let ips = response.extract_ips();
        assert_eq!(ips.len(), 1);
        assert_eq!(ips[0], IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1)));
    }

    #[test]
    fn test_client_options_default() {
        let options = ClientOptions::default();
        assert_eq!(options.timeout, Duration::from_secs(10));
        assert!(!options.disable_cache);
        assert!(!options.disable_expire);
        assert_eq!(options.cache_capacity, 1024);
        assert_eq!(options.strategy, 0);
    }

    #[test]
    fn test_client_creation() {
        let options = ClientOptions::default();
        let client = Client::new(options);
        assert!(client.cache.lock().unwrap().is_empty());
        assert!(client.transports.lock().unwrap().is_empty());
    }

    #[test]
    fn test_client_query_with_transport() {
        let options = ClientOptions::default();
        let mut client = Client::new(options);
        
        let transport = Box::new(MockTransport::new("udp"));
        client.add_transport(transport);
        
        let query = DNSQuery::a("example.com");
        let result = client.query(query);
        
        assert!(result.is_ok());
        let response = result.unwrap();
        assert_eq!(response.answers.len(), 1);
    }

    #[test]
    fn test_client_query_cache() {
        let mut options = ClientOptions::default();
        options.disable_cache = false;
        let mut client = Client::new(options);
        
        let transport = Box::new(MockTransport::new("udp"));
        client.add_transport(transport);
        
        let query = DNSQuery::a("example.com");
        
        // First query should hit transport
        let result1 = client.query(query.clone());
        assert!(result1.is_ok());
        
        // Second query should hit cache
        let result2 = client.query(query);
        assert!(result2.is_ok());
        
        // Cache should contain the entry
        assert!(!client.cache.lock().unwrap().is_empty());
    }

    #[test]
    fn test_client_query_transport_failure() {
        let options = ClientOptions::default();
        let mut client = Client::new(options);
        
        let transport = Box::new(MockTransport::new("udp").with_failure());
        client.add_transport(transport);
        
        let query = DNSQuery::a("example.com");
        let result = client.query(query);
        
        assert!(result.is_err());
        match result.unwrap_err() {
            DNSError::TransportError(msg) => assert!(msg.contains("all transports failed")),
            _ => panic!("Expected TransportError"),
        }
    }

    #[tokio::test]
    async fn test_client_lifecycle() {
        let options = ClientOptions::default();
        let mut client = Client::new(options);

        let transport = Box::new(MockTransport::new("udp"));
        client.add_transport(transport);

        assert!(client.start(StartStage::Initialize).await.is_ok());
        assert!(client.start(StartStage::Start).await.is_ok());
        assert!(client.close().await.is_ok());
    }

    #[test]
    fn test_dns_error_display() {
        assert_eq!(format!("{}", DNSError::NoRawSupport), "no raw query support by current transport");
        assert_eq!(format!("{}", DNSError::NotCached), "not cached");
        assert_eq!(format!("{}", DNSError::Timeout), "DNS query timeout");
        
        let transport_err = DNSError::TransportError("test error".to_string());
        assert_eq!(format!("{}", transport_err), "transport error: test error");
    }
}


