use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddr};
use std::time::{Duration, Instant};
use tokio::net::UdpSocket;
use tokio::time::timeout;

use crate::dns::{DNSQuery, DNSResponse, DNSRecord, DNSRecordData, DNSError};

/// DNS resolver that supports multiple upstream servers
pub struct DNSResolver {
    upstreams: Vec<DNSUpstream>,
    cache: HashMap<DNSQuery, CachedResponse>,
    timeout: Duration,
}

/// Cached DNS response
#[derive(Clone)]
struct CachedResponse {
    response: DNSResponse,
    cached_at: Instant,
    ttl: Duration,
}

impl CachedResponse {
    fn is_expired(&self) -> bool {
        self.cached_at.elapsed() > self.ttl
    }
}

/// DNS upstream server configuration
#[derive(Clone)]
pub struct DNSUpstream {
    pub server: SocketAddr,
    pub protocol: DNSProtocol,
    pub timeout: Duration,
}

/// DNS transport protocols
#[derive(Clone, Debug, PartialEq)]
pub enum DNSProtocol {
    UDP,
    TCP,
    DoT,  // DNS over TLS
    DoH,  // DNS over HTTPS
}

impl DNSResolver {
    pub fn new(upstreams: Vec<DNSUpstream>, timeout: Duration) -> Self {
        Self {
            upstreams,
            cache: HashMap::new(),
            timeout,
        }
    }

    /// Resolve domain name to IP addresses
    pub async fn resolve(&mut self, domain: &str, record_type: u16) -> Result<Vec<IpAddr>, DNSError> {
        let query = DNSQuery {
            name: domain.to_string(),
            query_type: record_type as u16,
            query_class: 1, // IN class
        };

        // Check cache first
        if let Some(cached) = self.cache.get(&query) {
            if !cached.is_expired() {
                return Ok(cached.response.answers.iter()
                    .filter_map(|record| match &record.data {
                        DNSRecordData::A(address) => Some(IpAddr::V4(*address)),
                        DNSRecordData::AAAA(address) => Some(IpAddr::V6(*address)),
                        _ => None,
                    })
                    .collect());
            }
        }

        // Query from upstreams
        for upstream in &self.upstreams {
            match self.query_upstream(upstream, &query).await {
                Ok(response) => {
                    // Cache the response
                    let ttl = response.answers.iter()
                        .map(|record| Duration::from_secs(record.ttl as u64))
                        .min()
                        .unwrap_or(Duration::from_secs(300)); // Default 5 minutes

                    let cached = CachedResponse {
                        response: response.clone(),
                        cached_at: Instant::now(),
                        ttl,
                    };
                    self.cache.insert(query, cached);

                    // Extract IP addresses
                    let addresses = response.answers.iter()
                        .filter_map(|record| match &record.data {
                            DNSRecordData::A(address) => Some(IpAddr::V4(*address)),
                            DNSRecordData::AAAA(address) => Some(IpAddr::V6(*address)),
                            _ => None,
                        })
                        .collect();

                    return Ok(addresses);
                }
                Err(_) => continue, // Try next upstream
            }
        }

        Err(DNSError::ResolutionFailed("all upstreams failed".to_string()))
    }

    /// Query a specific upstream server
    async fn query_upstream(&self, upstream: &DNSUpstream, query: &DNSQuery) -> Result<DNSResponse, DNSError> {
        match upstream.protocol {
            DNSProtocol::UDP => self.query_udp(upstream, query).await,
            DNSProtocol::TCP => self.query_tcp(upstream, query).await,
            DNSProtocol::DoT => {
                // TODO: Implement DNS over TLS
                Err(DNSError::TransportError("DoT not implemented".to_string()))
            }
            DNSProtocol::DoH => {
                // TODO: Implement DNS over HTTPS
                Err(DNSError::TransportError("DoH not implemented".to_string()))
            }
        }
    }

    /// Query using UDP
    async fn query_udp(&self, upstream: &DNSUpstream, query: &DNSQuery) -> Result<DNSResponse, DNSError> {
        let socket = UdpSocket::bind("0.0.0.0:0").await
            .map_err(|e| DNSError::TransportError(format!("bind UDP socket: {}", e)))?;

        // Build DNS query packet
        let packet = self.build_query_packet(query)?;

        // Send query
        socket.send_to(&packet, &upstream.server).await
            .map_err(|e| DNSError::TransportError(format!("send UDP query: {}", e)))?;

        // Receive response with timeout
        let mut response_buf = vec![0u8; 512]; // Standard DNS packet size
        let (len, _) = timeout(upstream.timeout, socket.recv_from(&mut response_buf)).await
            .map_err(|_| DNSError::Timeout)?
            .map_err(|e| DNSError::TransportError(format!("receive UDP response: {}", e)))?;

        response_buf.truncate(len);
        self.parse_response_packet(&response_buf)
    }

    /// Query using TCP
    async fn query_tcp(&self, upstream: &DNSUpstream, query: &DNSQuery) -> Result<DNSResponse, DNSError> {
        use tokio::net::TcpStream;
        use tokio::io::{AsyncReadExt, AsyncWriteExt};

        let mut stream = TcpStream::connect(&upstream.server).await
            .map_err(|e| DNSError::TransportError(format!("connect TCP: {}", e)))?;

        // Build DNS query packet
        let packet = self.build_query_packet(query)?;

        // Send length-prefixed packet (TCP DNS format)
        let length = (packet.len() as u16).to_be_bytes();
        stream.write_all(&length).await
            .map_err(|e| DNSError::TransportError(format!("write TCP length: {}", e)))?;
        stream.write_all(&packet).await
            .map_err(|e| DNSError::TransportError(format!("write TCP query: {}", e)))?;

        // Read response length
        let mut length_buf = [0u8; 2];
        stream.read_exact(&mut length_buf).await
            .map_err(|e| DNSError::TransportError(format!("read TCP length: {}", e)))?;
        let response_len = u16::from_be_bytes(length_buf) as usize;

        // Read response data
        let mut response_buf = vec![0u8; response_len];
        stream.read_exact(&mut response_buf).await
            .map_err(|e| DNSError::TransportError(format!("read TCP response: {}", e)))?;

        self.parse_response_packet(&response_buf)
    }

    /// Build DNS query packet (simplified implementation)
    fn build_query_packet(&self, query: &DNSQuery) -> Result<Vec<u8>, DNSError> {
        let mut packet = Vec::new();

        // DNS Header (12 bytes)
        packet.extend_from_slice(&[
            0x12, 0x34, // Transaction ID
            0x01, 0x00, // Flags: standard query
            0x00, 0x01, // Questions: 1
            0x00, 0x00, // Answer RRs: 0
            0x00, 0x00, // Authority RRs: 0
            0x00, 0x00, // Additional RRs: 0
        ]);

        // Question section
        // Encode domain name
        for label in query.name.split('.') {
            if label.is_empty() {
                continue;
            }
            packet.push(label.len() as u8);
            packet.extend_from_slice(label.as_bytes());
        }
        packet.push(0); // End of domain name

        // Query type (use the query_type field directly)
        packet.extend_from_slice(&query.query_type.to_be_bytes());

        // Query class (IN = 1)
        packet.extend_from_slice(&1u16.to_be_bytes());

        Ok(packet)
    }

    /// Parse DNS response packet (simplified implementation)
    fn parse_response_packet(&self, packet: &[u8]) -> Result<DNSResponse, DNSError> {
        if packet.len() < 12 {
            return Err(DNSError::ParseError("packet too short".to_string()));
        }

        // Parse header
        let transaction_id = u16::from_be_bytes([packet[0], packet[1]]);
        let flags = u16::from_be_bytes([packet[2], packet[3]]);
        let questions = u16::from_be_bytes([packet[4], packet[5]]);
        let answers = u16::from_be_bytes([packet[6], packet[7]]);

        // Check if response
        if (flags & 0x8000) == 0 {
            return Err(DNSError::ParseError("not a response".to_string()));
        }

        // Check response code
        let rcode = flags & 0x000F;
        if rcode != 0 {
            return Err(DNSError::ServerError(format!("DNS error code: {}", rcode)));
        }

        // Skip question section (simplified)
        let mut offset = 12;
        for _ in 0..questions {
            // Skip domain name
            while offset < packet.len() && packet[offset] != 0 {
                let label_len = packet[offset] as usize;
                if label_len > 63 {
                    // Handle compression (simplified)
                    offset += 2;
                    break;
                }
                offset += 1 + label_len;
            }
            if offset < packet.len() && packet[offset] == 0 {
                offset += 1; // Skip null terminator
            }
            offset += 4; // Skip QTYPE and QCLASS
        }

        // Parse answer section (simplified)
        let mut dns_answers = Vec::new();
        for _ in 0..answers {
            if offset + 10 > packet.len() {
                break;
            }

            // Skip name (simplified - assume compression)
            if packet[offset] & 0xC0 == 0xC0 {
                offset += 2;
            } else {
                // Skip uncompressed name
                while offset < packet.len() && packet[offset] != 0 {
                    let label_len = packet[offset] as usize;
                    offset += 1 + label_len;
                }
                offset += 1; // Skip null terminator
            }

            if offset + 10 > packet.len() {
                break;
            }

            let record_type = u16::from_be_bytes([packet[offset], packet[offset + 1]]);
            let _class = u16::from_be_bytes([packet[offset + 2], packet[offset + 3]]);
            let ttl = u32::from_be_bytes([packet[offset + 4], packet[offset + 5], packet[offset + 6], packet[offset + 7]]);
            let data_len = u16::from_be_bytes([packet[offset + 8], packet[offset + 9]]) as usize;
            offset += 10;

            if offset + data_len > packet.len() {
                break;
            }

            match record_type {
                1 => {
                    // A record
                    if data_len == 4 {
                        let addr = Ipv4Addr::from([
                            packet[offset],
                            packet[offset + 1],
                            packet[offset + 2],
                            packet[offset + 3],
                        ]);
                        dns_answers.push(DNSRecord {
                            name: "".to_string(), // Simplified
                            record_type: 1,
                            record_class: 1,
                            ttl,
                            data: DNSRecordData::A(addr),
                        });
                    }
                }
                28 => {
                    // AAAA record
                    if data_len == 16 {
                        let mut addr_bytes = [0u8; 16];
                        addr_bytes.copy_from_slice(&packet[offset..offset + 16]);
                        let addr = Ipv6Addr::from(addr_bytes);
                        dns_answers.push(DNSRecord {
                            name: "".to_string(), // Simplified
                            record_type: 28,
                            record_class: 1,
                            ttl,
                            data: DNSRecordData::AAAA(addr),
                        });
                    }
                }
                _ => {
                    // Skip other record types for now
                }
            }

            offset += data_len;
        }

        Ok(DNSResponse {
            query: DNSQuery {
                name: "".to_string(), // Simplified
                query_type: 1,
                query_class: 1,
            },
            answers: dns_answers,
            authorities: Vec::new(),
            additionals: Vec::new(),
            response_code: rcode,
            truncated: (flags & 0x0200) != 0,
            authoritative: (flags & 0x0400) != 0,
            recursion_available: (flags & 0x0080) != 0,
            ttl: 300,
            cached_at: Some(std::time::Instant::now()),
        })
    }

    /// Clear DNS cache
    pub fn clear_cache(&mut self) {
        self.cache.clear();
    }

    /// Get cache statistics
    pub fn cache_stats(&self) -> (usize, usize) {
        let total = self.cache.len();
        let expired = self.cache.values()
            .filter(|cached| cached.is_expired())
            .count();
        (total, expired)
    }

    /// Clean expired entries from cache
    pub fn cleanup_cache(&mut self) {
        self.cache.retain(|_, cached| !cached.is_expired());
    }
}

/// Default DNS resolver with common public DNS servers
impl Default for DNSResolver {
    fn default() -> Self {
        let upstreams = vec![
            DNSUpstream {
                server: "*******:53".parse().unwrap(),
                protocol: DNSProtocol::UDP,
                timeout: Duration::from_secs(5),
            },
            DNSUpstream {
                server: "8.8.4.4:53".parse().unwrap(),
                protocol: DNSProtocol::UDP,
                timeout: Duration::from_secs(5),
            },
            DNSUpstream {
                server: "*******:53".parse().unwrap(),
                protocol: DNSProtocol::UDP,
                timeout: Duration::from_secs(5),
            },
            DNSUpstream {
                server: "1.0.0.1:53".parse().unwrap(),
                protocol: DNSProtocol::UDP,
                timeout: Duration::from_secs(5),
            },
        ];

        Self::new(upstreams, Duration::from_secs(5))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dns_resolver_creation() {
        let upstreams = vec![
            DNSUpstream {
                server: "*******:53".parse().unwrap(),
                protocol: DNSProtocol::UDP,
                timeout: Duration::from_secs(5),
            },
        ];

        let resolver = DNSResolver::new(upstreams, Duration::from_secs(10));
        let (total, expired) = resolver.cache_stats();
        assert_eq!(total, 0);
        assert_eq!(expired, 0);
    }

    #[test]
    fn test_dns_upstream_creation() {
        let upstream = DNSUpstream {
            server: "*******:53".parse().unwrap(),
            protocol: DNSProtocol::UDP,
            timeout: Duration::from_secs(5),
        };

        assert_eq!(upstream.server.ip().to_string(), "*******");
        assert_eq!(upstream.server.port(), 53);
        assert_eq!(upstream.protocol, DNSProtocol::UDP);
        assert_eq!(upstream.timeout, Duration::from_secs(5));
    }

    #[test]
    fn test_default_resolver() {
        let resolver = DNSResolver::default();
        assert_eq!(resolver.upstreams.len(), 4);
        assert_eq!(resolver.timeout, Duration::from_secs(5));
        
        // Check that we have Google and Cloudflare DNS
        let servers: Vec<String> = resolver.upstreams.iter()
            .map(|u| u.server.ip().to_string())
            .collect();
        assert!(servers.contains(&"*******".to_string()));
        assert!(servers.contains(&"*******".to_string()));
    }

    #[test]
    fn test_dns_protocol_enum() {
        // Test that enum variants exist and can be used
        let _udp = DNSProtocol::UDP;
        let _tcp = DNSProtocol::TCP;
        let _doh = DNSProtocol::DoH;
        let _dot = DNSProtocol::DoT;

        // Test that they're different
        assert_ne!(DNSProtocol::UDP, DNSProtocol::TCP);
        assert_ne!(DNSProtocol::DoH, DNSProtocol::DoT);
    }
}
