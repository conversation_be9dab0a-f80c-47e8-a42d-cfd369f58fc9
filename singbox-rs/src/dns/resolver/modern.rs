//! Modern DNS resolver implementation
//!
//! This module implements modern DNS protocols including DNS-over-HTTPS (DoH),
//! DNS-over-TLS (DoT), and DNS-over-QUIC (DoQ) with advanced features like
//! caching, load balancing, and failover.

use std::collections::HashMap;
use std::net::{IpAddr, SocketAddr};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use trust_dns_resolver::config::{ResolverConfig, ResolverOpts};
use trust_dns_resolver::TokioAsyncResolver;

use crate::dns::{DnsResolver, DnsQuery, DnsResponse, DnsError};

/// Modern DNS resolver configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModernDnsConfig {
    /// DNS servers configuration
    pub servers: Vec<DnsServerConfig>,
    
    /// DNS cache configuration
    pub cache: Option<DnsCacheConfig>,
    
    /// Load balancing strategy
    pub strategy: LoadBalanceStrategy,
    
    /// Query timeout
    pub timeout: Duration,
    
    /// Maximum concurrent queries
    pub max_concurrent_queries: usize,
    
    /// Enable DNS-over-HTTPS
    pub enable_doh: bool,
    
    /// Enable DNS-over-TLS
    pub enable_dot: bool,
    
    /// Enable DNS-over-QUIC
    pub enable_doq: bool,
    
    /// Fallback to traditional DNS
    pub fallback_to_udp: bool,
}

/// DNS server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DnsServerConfig {
    /// Server address
    pub address: String,
    
    /// Server port
    pub port: u16,
    
    /// Protocol type
    pub protocol: DnsProtocol,
    
    /// Server weight for load balancing
    pub weight: u32,
    
    /// Server priority (lower = higher priority)
    pub priority: u32,
    
    /// TLS server name (for DoT/DoH)
    pub tls_name: Option<String>,
    
    /// HTTP path (for DoH)
    pub path: Option<String>,
    
    /// Bootstrap resolver (for DoH/DoT)
    pub bootstrap: Option<Vec<IpAddr>>,
}

/// DNS protocol types
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum DnsProtocol {
    /// Traditional UDP DNS
    Udp,
    /// Traditional TCP DNS
    Tcp,
    /// DNS-over-TLS
    Tls,
    /// DNS-over-HTTPS
    Https,
    /// DNS-over-QUIC
    Quic,
}

/// Load balancing strategies
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum LoadBalanceStrategy {
    /// Round robin
    RoundRobin,
    /// Weighted round robin
    WeightedRoundRobin,
    /// Least connections
    LeastConnections,
    /// Fastest response
    FastestResponse,
    /// Priority-based
    Priority,
}

/// DNS cache configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DnsCacheConfig {
    /// Enable caching
    pub enabled: bool,
    
    /// Maximum cache size (number of entries)
    pub max_size: usize,
    
    /// Default TTL for cached entries
    pub default_ttl: Duration,
    
    /// Minimum TTL
    pub min_ttl: Duration,
    
    /// Maximum TTL
    pub max_ttl: Duration,
    
    /// Enable negative caching
    pub negative_cache: bool,

    /// Cache eviction policy
    pub eviction_policy: CacheEvictionPolicy,

    /// Enable cache compression
    pub compression: bool,

    /// Cache persistence
    pub persistence: Option<CachePersistenceConfig>,
}

/// Cache eviction policies
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CacheEvictionPolicy {
    /// Least Recently Used
    Lru,

    /// Least Frequently Used
    Lfu,

    /// Time-based expiration
    Ttl,

    /// Random eviction
    Random,
}

/// Cache persistence configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachePersistenceConfig {
    /// Enable persistence
    pub enabled: bool,

    /// Cache file path
    pub file_path: String,

    /// Save interval
    pub save_interval: Duration,

    /// Compression level (0-9)
    pub compression_level: u8,
}

/// DNS cache entry
#[derive(Debug, Clone)]
struct DnsCacheEntry {
    /// DNS response
    response: DnsResponse,
    
    /// Cache timestamp
    cached_at: Instant,
    
    /// Time-to-live
    ttl: Duration,
    
    /// Access count
    access_count: u64,
}

impl DnsCacheEntry {
    /// Check if cache entry is expired
    fn is_expired(&self) -> bool {
        self.cached_at.elapsed() > self.ttl
    }
}

/// Server statistics
#[derive(Debug, Clone, Default)]
struct ServerStats {
    /// Total queries sent
    total_queries: u64,
    
    /// Successful responses
    successful_responses: u64,
    
    /// Failed queries
    failed_queries: u64,
    
    /// Average response time
    avg_response_time: Duration,
    
    /// Current connections
    current_connections: u32,
    
    /// Last response time
    last_response_time: Option<Instant>,
}

/// Modern DNS resolver
pub struct ModernDnsResolver {
    /// Configuration
    config: ModernDnsConfig,
    
    /// DNS servers
    servers: Vec<Arc<DnsServer>>,
    
    /// DNS cache
    cache: Arc<RwLock<HashMap<String, DnsCacheEntry>>>,
    
    /// Server statistics
    stats: Arc<RwLock<HashMap<String, ServerStats>>>,
    
    /// Load balancer state
    lb_state: Arc<RwLock<LoadBalancerState>>,
    
    /// Traditional resolver (fallback)
    fallback_resolver: Option<TokioAsyncResolver>,
}

/// DNS server wrapper
struct DnsServer {
    /// Server configuration
    config: DnsServerConfig,
    
    /// HTTP client (for DoH)
    http_client: Option<reqwest::Client>,
    
    /// Server identifier
    id: String,
}

/// Load balancer state
#[derive(Debug, Default)]
struct LoadBalancerState {
    /// Round robin counter
    round_robin_counter: usize,
    
    /// Server weights
    server_weights: HashMap<String, u32>,
}

impl ModernDnsResolver {
    /// Create a new modern DNS resolver
    pub async fn new(config: ModernDnsConfig) -> Result<Self, DnsError> {
        let mut servers = Vec::new();
        let mut stats = HashMap::new();
        
        // Initialize DNS servers
        for (index, server_config) in config.servers.iter().enumerate() {
            let server_id = format!("server_{}", index);
            
            let mut http_client = None;
            if server_config.protocol == DnsProtocol::Https {
                http_client = Some(
                    reqwest::Client::builder()
                        .timeout(config.timeout)
                        .build()
                        .map_err(|e| DnsError::ConfigError(format!("Failed to create HTTP client: {}", e)))?
                );
            }
            
            let server = Arc::new(DnsServer {
                config: server_config.clone(),
                http_client,
                id: server_id.clone(),
            });
            
            servers.push(server);
            stats.insert(server_id, ServerStats::default());
        }
        
        // Initialize fallback resolver
        let fallback_resolver = if config.fallback_to_udp {
            Some(TokioAsyncResolver::tokio(
                ResolverConfig::default(),
                ResolverOpts::default(),
            )?)
        } else {
            None
        };
        
        Ok(Self {
            config,
            servers,
            cache: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(stats)),
            lb_state: Arc::new(RwLock::new(LoadBalancerState::default())),
            fallback_resolver,
        })
    }
    
    /// Select server based on load balancing strategy
    async fn select_server(&self) -> Option<Arc<DnsServer>> {
        if self.servers.is_empty() {
            return None;
        }
        
        match self.config.strategy {
            LoadBalanceStrategy::RoundRobin => {
                let mut state = self.lb_state.write().await;
                let index = state.round_robin_counter % self.servers.len();
                state.round_robin_counter = (state.round_robin_counter + 1) % self.servers.len();
                Some(self.servers[index].clone())
            },
            LoadBalanceStrategy::WeightedRoundRobin => {
                // Simplified weighted round robin
                let total_weight: u32 = self.servers.iter().map(|s| s.config.weight).sum();
                if total_weight == 0 {
                    return Some(self.servers[0].clone());
                }
                
                let mut state = self.lb_state.write().await;
                let target = (state.round_robin_counter as u32) % total_weight;
                state.round_robin_counter += 1;
                
                let mut current_weight = 0;
                for server in &self.servers {
                    current_weight += server.config.weight;
                    if target < current_weight {
                        return Some(server.clone());
                    }
                }
                
                Some(self.servers[0].clone())
            },
            LoadBalanceStrategy::Priority => {
                // Select server with highest priority (lowest priority number)
                self.servers.iter()
                    .min_by_key(|s| s.config.priority)
                    .cloned()
            },
            LoadBalanceStrategy::FastestResponse => {
                // Select server with fastest average response time
                let stats = self.stats.read().await;
                self.servers.iter()
                    .min_by_key(|s| {
                        stats.get(&s.id)
                            .map(|stat| stat.avg_response_time)
                            .unwrap_or(Duration::from_secs(999))
                    })
                    .cloned()
            },
            LoadBalanceStrategy::LeastConnections => {
                // Select server with least current connections
                let stats = self.stats.read().await;
                self.servers.iter()
                    .min_by_key(|s| {
                        stats.get(&s.id)
                            .map(|stat| stat.current_connections)
                            .unwrap_or(0)
                    })
                    .cloned()
            },
        }
    }
    
    /// Query DNS using DoH (DNS-over-HTTPS)
    async fn query_doh(&self, server: &DnsServer, query: &DnsQuery) -> Result<DnsResponse, DnsError> {
        let client = server.http_client.as_ref()
            .ok_or_else(|| DnsError::ConfigError("HTTP client not initialized".to_string()))?;
        
        let url = format!("https://{}:{}{}", 
            server.config.address, 
            server.config.port,
            server.config.path.as_deref().unwrap_or("/dns-query")
        );
        
        // Encode DNS query as base64url for GET request
        let query_bytes = self.encode_dns_query(query)?;
        let query_b64 = base64::engine::general_purpose::URL_SAFE_NO_PAD.encode(&query_bytes);
        
        let response = client
            .get(&url)
            .query(&[("dns", query_b64)])
            .header("Accept", "application/dns-message")
            .send()
            .await
            .map_err(|e| DnsError::NetworkError(format!("DoH request failed: {}", e)))?;
        
        if !response.status().is_success() {
            return Err(DnsError::ServerError(format!("DoH server returned status: {}", response.status())));
        }
        
        let response_bytes = response.bytes().await
            .map_err(|e| DnsError::NetworkError(format!("Failed to read DoH response: {}", e)))?;
        
        self.decode_dns_response(&response_bytes)
    }
    
    /// Query DNS using DoT (DNS-over-TLS)
    async fn query_dot(&self, server: &DnsServer, query: &DnsQuery) -> Result<DnsResponse, DnsError> {
        use tokio_rustls::{TlsConnector, rustls::ClientConfig};
        use std::sync::Arc;

        // Create TLS configuration
        let mut config = ClientConfig::builder()
            .with_safe_defaults()
            .with_root_certificates(rustls_native_certs::load_native_certs()
                .map_err(|e| DnsError::ConfigError(format!("Failed to load root certificates: {}", e)))?)
            .with_no_client_auth();

        let connector = TlsConnector::from(Arc::new(config));

        // Connect to DNS server
        let addr = format!("{}:{}", server.config.address, server.config.port);
        let stream = tokio::net::TcpStream::connect(&addr).await
            .map_err(|e| DnsError::NetworkError(format!("Failed to connect to DoT server: {}", e)))?;

        let domain = server.config.tls_name.as_deref()
            .unwrap_or(&server.config.address);

        let tls_stream = connector.connect(domain.try_into()
            .map_err(|e| DnsError::ConfigError(format!("Invalid server name: {}", e)))?, stream).await
            .map_err(|e| DnsError::NetworkError(format!("TLS handshake failed: {}", e)))?;

        // Encode and send DNS query
        let query_bytes = self.encode_dns_query_wire_format(query)?;
        let query_with_length = [&(query_bytes.len() as u16).to_be_bytes()[..], &query_bytes].concat();

        use tokio::io::{AsyncWriteExt, AsyncReadExt};
        let (mut reader, mut writer) = tokio::io::split(tls_stream);

        writer.write_all(&query_with_length).await
            .map_err(|e| DnsError::NetworkError(format!("Failed to send DoT query: {}", e)))?;

        // Read response
        let mut length_buf = [0u8; 2];
        reader.read_exact(&mut length_buf).await
            .map_err(|e| DnsError::NetworkError(format!("Failed to read DoT response length: {}", e)))?;

        let response_length = u16::from_be_bytes(length_buf) as usize;
        let mut response_buf = vec![0u8; response_length];
        reader.read_exact(&mut response_buf).await
            .map_err(|e| DnsError::NetworkError(format!("Failed to read DoT response: {}", e)))?;

        self.decode_dns_response_wire_format(&response_buf)
    }
    
    /// Query DNS using DoQ (DNS-over-QUIC)
    async fn query_doq(&self, server: &DnsServer, query: &DnsQuery) -> Result<DnsResponse, DnsError> {
        use quinn::{Endpoint, ClientConfig, Connection};
        use std::sync::Arc;

        // Create QUIC client configuration
        let mut client_config = ClientConfig::new(Arc::new(
            rustls::ClientConfig::builder()
                .with_safe_defaults()
                .with_root_certificates(rustls_native_certs::load_native_certs()
                    .map_err(|e| DnsError::ConfigError(format!("Failed to load root certificates: {}", e)))?)
                .with_no_client_auth()
        ));

        // Set ALPN for DNS-over-QUIC
        client_config.alpn_protocols = vec![b"doq".to_vec()];

        // Create endpoint
        let mut endpoint = Endpoint::client("0.0.0.0:0".parse().unwrap())
            .map_err(|e| DnsError::NetworkError(format!("Failed to create QUIC endpoint: {}", e)))?;
        endpoint.set_default_client_config(client_config);

        // Connect to DNS server
        let server_addr = format!("{}:{}", server.config.address, server.config.port);
        let connection = endpoint.connect(server_addr.parse()
            .map_err(|e| DnsError::ConfigError(format!("Invalid server address: {}", e)))?,
            server.config.tls_name.as_deref().unwrap_or(&server.config.address))?
            .await
            .map_err(|e| DnsError::NetworkError(format!("QUIC connection failed: {}", e)))?;

        // Open bidirectional stream
        let (mut send, mut recv) = connection.open_bi().await
            .map_err(|e| DnsError::NetworkError(format!("Failed to open QUIC stream: {}", e)))?;

        // Send DNS query
        let query_bytes = self.encode_dns_query_wire_format(query)?;
        let query_with_length = [&(query_bytes.len() as u16).to_be_bytes()[..], &query_bytes].concat();

        use quinn::AsyncWrite;
        send.write_all(&query_with_length).await
            .map_err(|e| DnsError::NetworkError(format!("Failed to send DoQ query: {}", e)))?;
        send.finish().await
            .map_err(|e| DnsError::NetworkError(format!("Failed to finish DoQ query: {}", e)))?;

        // Read response
        use quinn::AsyncRead;
        let mut length_buf = [0u8; 2];
        recv.read_exact(&mut length_buf).await
            .map_err(|e| DnsError::NetworkError(format!("Failed to read DoQ response length: {}", e)))?;

        let response_length = u16::from_be_bytes(length_buf) as usize;
        let mut response_buf = vec![0u8; response_length];
        recv.read_exact(&mut response_buf).await
            .map_err(|e| DnsError::NetworkError(format!("Failed to read DoQ response: {}", e)))?;

        self.decode_dns_response_wire_format(&response_buf)
    }
    
    /// Query DNS using traditional UDP/TCP
    async fn query_traditional(&self, server: &DnsServer, query: &DnsQuery) -> Result<DnsResponse, DnsError> {
        if let Some(ref resolver) = self.fallback_resolver {
            // Use trust-dns-resolver for traditional DNS
            let lookup_result = match query.record_type.as_str() {
                "A" => {
                    let lookup = resolver.lookup_ip(&query.name).await?;
                    let addresses: Vec<IpAddr> = lookup.iter().collect();
                    DnsResponse {
                        name: query.name.clone(),
                        record_type: query.record_type.clone(),
                        addresses,
                        ttl: Duration::from_secs(300), // Default TTL
                        authoritative: false,
                    }
                },
                _ => {
                    return Err(DnsError::NotImplemented(format!("Record type {} not implemented", query.record_type)));
                }
            };
            
            Ok(lookup_result)
        } else {
            Err(DnsError::ConfigError("Fallback resolver not configured".to_string()))
        }
    }
    
    /// Encode DNS query to bytes
    fn encode_dns_query(&self, query: &DnsQuery) -> Result<Vec<u8>, DnsError> {
        // Simplified DNS query encoding
        // In a real implementation, would use proper DNS message format
        let query_str = format!("{}:{}", query.name, query.record_type);
        Ok(query_str.into_bytes())
    }

    /// Encode DNS query in wire format
    fn encode_dns_query_wire_format(&self, query: &DnsQuery) -> Result<Vec<u8>, DnsError> {
        use trust_dns_proto::op::{Message, Query};
        use trust_dns_proto::rr::{Name, RecordType};

        // Parse domain name
        let name = Name::from_utf8(&query.name)
            .map_err(|e| DnsError::InvalidQuery(format!("Invalid domain name: {}", e)))?;

        // Parse record type
        let record_type = match query.record_type.to_uppercase().as_str() {
            "A" => RecordType::A,
            "AAAA" => RecordType::AAAA,
            "CNAME" => RecordType::CNAME,
            "MX" => RecordType::MX,
            "TXT" => RecordType::TXT,
            "NS" => RecordType::NS,
            "PTR" => RecordType::PTR,
            "SOA" => RecordType::SOA,
            _ => return Err(DnsError::InvalidQuery(format!("Unsupported record type: {}", query.record_type))),
        };

        // Create DNS message
        let mut message = Message::new();
        message.set_id(rand::random());
        message.set_recursion_desired(true);
        message.add_query(Query::query(name, record_type));

        // Serialize to bytes
        message.to_vec()
            .map_err(|e| DnsError::EncodingError(format!("Failed to encode DNS query: {}", e)))
    }

    /// Decode DNS response from bytes
    fn decode_dns_response(&self, bytes: &[u8]) -> Result<DnsResponse, DnsError> {
        // Simplified DNS response decoding
        // In a real implementation, would parse proper DNS message format
        let response_str = String::from_utf8_lossy(bytes);

        // Mock response for demonstration
        Ok(DnsResponse {
            name: "example.com".to_string(),
            record_type: "A".to_string(),
            addresses: vec!["*************".parse().unwrap()],
            ttl: Duration::from_secs(300),
            authoritative: false,
        })
    }

    /// Decode DNS response from wire format
    fn decode_dns_response_wire_format(&self, bytes: &[u8]) -> Result<DnsResponse, DnsError> {
        use trust_dns_proto::op::Message;
        use trust_dns_proto::rr::RecordType;

        // Parse DNS message
        let message = Message::from_vec(bytes)
            .map_err(|e| DnsError::DecodingError(format!("Failed to decode DNS response: {}", e)))?;

        // Extract answers
        let answers = message.answers();
        if answers.is_empty() {
            return Err(DnsError::NoRecords("No DNS records found".to_string()));
        }

        let first_answer = &answers[0];
        let name = first_answer.name().to_utf8();
        let record_type = match first_answer.record_type() {
            RecordType::A => "A",
            RecordType::AAAA => "AAAA",
            RecordType::CNAME => "CNAME",
            RecordType::MX => "MX",
            RecordType::TXT => "TXT",
            RecordType::NS => "NS",
            RecordType::PTR => "PTR",
            RecordType::SOA => "SOA",
            _ => "UNKNOWN",
        }.to_string();

        // Extract IP addresses
        let mut addresses = Vec::new();
        for answer in answers {
            if let Some(rdata) = answer.data() {
                match answer.record_type() {
                    RecordType::A => {
                        if let Some(a_record) = rdata.as_a() {
                            addresses.push(IpAddr::V4(*a_record));
                        }
                    },
                    RecordType::AAAA => {
                        if let Some(aaaa_record) = rdata.as_aaaa() {
                            addresses.push(IpAddr::V6(*aaaa_record));
                        }
                    },
                    _ => {
                        // Handle other record types as needed
                    }
                }
            }
        }

        Ok(DnsResponse {
            name,
            record_type,
            addresses,
            ttl: Duration::from_secs(first_answer.ttl() as u64),
            authoritative: message.authoritative(),
        })
    }
    
    /// Get cache key for query
    fn get_cache_key(&self, query: &DnsQuery) -> String {
        format!("{}:{}", query.name.to_lowercase(), query.record_type.to_uppercase())
    }
    
    /// Check cache for query
    async fn check_cache(&self, query: &DnsQuery) -> Option<DnsResponse> {
        if let Some(ref cache_config) = self.config.cache {
            if !cache_config.enabled {
                return None;
            }
            
            let cache_key = self.get_cache_key(query);
            let cache = self.cache.read().await;
            
            if let Some(entry) = cache.get(&cache_key) {
                if !entry.is_expired() {
                    return Some(entry.response.clone());
                }
            }
        }
        
        None
    }
    
    /// Store response in cache
    async fn store_cache(&self, query: &DnsQuery, response: &DnsResponse) {
        if let Some(ref cache_config) = self.config.cache {
            if !cache_config.enabled {
                return;
            }
            
            let cache_key = self.get_cache_key(query);
            let ttl = response.ttl.min(cache_config.max_ttl).max(cache_config.min_ttl);
            
            let entry = DnsCacheEntry {
                response: response.clone(),
                cached_at: Instant::now(),
                ttl,
                access_count: 1,
            };
            
            let mut cache = self.cache.write().await;
            
            // Check cache size limit
            if cache.len() >= cache_config.max_size {
                // Remove oldest entry (simplified LRU)
                if let Some(oldest_key) = cache.keys().next().cloned() {
                    cache.remove(&oldest_key);
                }
            }
            
            cache.insert(cache_key, entry);
        }
    }
    
    /// Update server statistics
    async fn update_stats(&self, server_id: &str, success: bool, response_time: Duration) {
        let mut stats = self.stats.write().await;
        if let Some(server_stats) = stats.get_mut(server_id) {
            server_stats.total_queries += 1;
            
            if success {
                server_stats.successful_responses += 1;
            } else {
                server_stats.failed_queries += 1;
            }
            
            // Update average response time (simple moving average)
            let total_responses = server_stats.successful_responses + server_stats.failed_queries;
            if total_responses > 0 {
                let current_avg = server_stats.avg_response_time.as_millis() as f64;
                let new_time = response_time.as_millis() as f64;
                let new_avg = (current_avg * (total_responses - 1) as f64 + new_time) / total_responses as f64;
                server_stats.avg_response_time = Duration::from_millis(new_avg as u64);
            }
            
            server_stats.last_response_time = Some(Instant::now());
        }
    }
}

impl DnsResolver for ModernDnsResolver {
    async fn resolve(&self, query: DnsQuery) -> Result<DnsResponse, DnsError> {
        // Check cache first
        if let Some(cached_response) = self.check_cache(&query).await {
            return Ok(cached_response);
        }
        
        // Select server
        let server = self.select_server().await
            .ok_or_else(|| DnsError::ConfigError("No DNS servers available".to_string()))?;
        
        let start_time = Instant::now();
        
        // Query based on protocol
        let result = match server.config.protocol {
            DnsProtocol::Https => self.query_doh(&server, &query).await,
            DnsProtocol::Tls => self.query_dot(&server, &query).await,
            DnsProtocol::Quic => self.query_doq(&server, &query).await,
            DnsProtocol::Udp | DnsProtocol::Tcp => self.query_traditional(&server, &query).await,
        };
        
        let response_time = start_time.elapsed();
        
        match result {
            Ok(response) => {
                // Update statistics
                self.update_stats(&server.id, true, response_time).await;
                
                // Store in cache
                self.store_cache(&query, &response).await;
                
                Ok(response)
            },
            Err(e) => {
                // Update statistics
                self.update_stats(&server.id, false, response_time).await;
                
                Err(e)
            }
        }
    }
}

impl Default for ModernDnsConfig {
    fn default() -> Self {
        Self {
            servers: vec![
                DnsServerConfig {
                    address: "*******".to_string(),
                    port: 53,
                    protocol: DnsProtocol::Udp,
                    weight: 1,
                    priority: 1,
                    tls_name: None,
                    path: None,
                    bootstrap: None,
                },
            ],
            cache: Some(DnsCacheConfig {
                enabled: true,
                max_size: 1000,
                default_ttl: Duration::from_secs(300),
                min_ttl: Duration::from_secs(60),
                max_ttl: Duration::from_secs(3600),
                negative_cache: true,
            }),
            strategy: LoadBalanceStrategy::RoundRobin,
            timeout: Duration::from_secs(5),
            max_concurrent_queries: 100,
            enable_doh: true,
            enable_dot: true,
            enable_doq: false,
            fallback_to_udp: true,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_modern_dns_resolver_creation() {
        let config = ModernDnsConfig::default();
        let resolver = ModernDnsResolver::new(config).await;
        assert!(resolver.is_ok());
    }
    
    #[tokio::test]
    async fn test_cache_key_generation() {
        let config = ModernDnsConfig::default();
        let resolver = ModernDnsResolver::new(config).await.unwrap();
        
        let query = DnsQuery {
            name: "Example.Com".to_string(),
            record_type: "a".to_string(),
        };
        
        let key = resolver.get_cache_key(&query);
        assert_eq!(key, "example.com:A");
    }
    
    #[tokio::test]
    async fn test_server_selection() {
        let mut config = ModernDnsConfig::default();
        config.servers.push(DnsServerConfig {
            address: "*******".to_string(),
            port: 53,
            protocol: DnsProtocol::Udp,
            weight: 2,
            priority: 2,
            tls_name: None,
            path: None,
            bootstrap: None,
        });
        
        let resolver = ModernDnsResolver::new(config).await.unwrap();
        let server = resolver.select_server().await;
        assert!(server.is_some());
    }
}
