//! TUIC protocol implementation
//!
//! This module implements the TUIC (TCP/UDP in QUIC) protocol, which provides
//! high-performance proxy services over QUIC with support for both TCP and UDP
//! traffic multiplexing.

use std::collections::HashMap;
use std::net::{<PERSON><PERSON><PERSON>dd<PERSON>, SocketAddr};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::{RwLock, Mutex};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use async_trait::async_trait;
use crate::adapter::{Adapter, Inbound, Outbound, Lifecycle, StartStage};
use crate::network::Connection;

/// TUIC protocol version
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TuicVersion {
    /// TUIC v4
    V4,
    /// TUIC v5
    V5,
}

impl Default for TuicVersion {
    fn default() -> Self {
        TuicVersion::V5
    }
}

/// TUIC configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TuicConfig {
    /// Protocol version
    pub version: TuicVersion,
    
    /// Server address (for outbound) or listen address (for inbound)
    pub server: Option<String>,
    
    /// Server port
    pub server_port: Option<u16>,
    
    /// Listen address (for inbound)
    pub listen: Option<String>,
    
    /// Listen port (for inbound)
    pub listen_port: Option<u16>,
    
    /// User UUID
    pub uuid: String,
    
    /// Password
    pub password: String,
    
    /// Congestion control algorithm
    pub congestion_control: Option<String>,
    
    /// UDP relay mode
    pub udp_relay_mode: Option<String>,
    
    /// Zero RTT handshake
    pub zero_rtt_handshake: Option<bool>,
    
    /// Heartbeat interval
    pub heartbeat: Option<Duration>,
    
    /// TLS configuration
    pub tls: Option<TuicTlsConfig>,
    
    /// QUIC configuration
    pub quic: Option<TuicQuicConfig>,
}

/// TUIC TLS configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuicTlsConfig {
    /// Server name
    pub server_name: Option<String>,
    
    /// ALPN protocols
    pub alpn: Option<Vec<String>>,
    
    /// Certificate file (for server)
    pub certificate: Option<String>,
    
    /// Private key file (for server)
    pub private_key: Option<String>,
    
    /// Certificate chain
    pub certificate_chain: Option<Vec<String>>,
    
    /// Insecure (skip certificate verification)
    pub insecure: Option<bool>,
}

/// TUIC QUIC configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuicQuicConfig {
    /// Initial stream receive window
    pub initial_stream_receive_window: Option<u64>,
    
    /// Initial connection receive window
    pub initial_connection_receive_window: Option<u64>,
    
    /// Maximum stream receive window
    pub max_stream_receive_window: Option<u64>,
    
    /// Maximum connection receive window
    pub max_connection_receive_window: Option<u64>,
    
    /// Keep alive period
    pub keep_alive_period: Option<Duration>,
    
    /// Maximum idle timeout
    pub max_idle_timeout: Option<Duration>,
    
    /// Disable path MTU discovery
    pub disable_path_mtu_discovery: Option<bool>,
}

/// TUIC command types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TuicCommand {
    /// Connect command (TCP)
    Connect = 0x01,
    /// UDP Associate command
    UdpAssociate = 0x02,
    /// Heartbeat command
    Heartbeat = 0x03,
    /// Authenticate command
    Authenticate = 0x04,
}

impl TuicCommand {
    /// Convert from byte
    pub fn from_byte(byte: u8) -> Option<Self> {
        match byte {
            0x01 => Some(TuicCommand::Connect),
            0x02 => Some(TuicCommand::UdpAssociate),
            0x03 => Some(TuicCommand::Heartbeat),
            0x04 => Some(TuicCommand::Authenticate),
            _ => None,
        }
    }
    
    /// Convert to byte
    pub fn to_byte(self) -> u8 {
        self as u8
    }
}

/// TUIC packet header
#[derive(Debug, Clone)]
pub struct TuicHeader {
    /// Command type
    pub command: TuicCommand,
    
    /// Stream ID
    pub stream_id: Option<u64>,
    
    /// Fragment ID (for UDP)
    pub fragment_id: Option<u16>,
    
    /// Fragment total (for UDP)
    pub fragment_total: Option<u16>,
    
    /// Destination address
    pub address: Option<SocketAddr>,
    
    /// Payload length
    pub payload_length: u16,
}

/// TUIC connection state
#[derive(Debug, Clone)]
pub struct TuicConnectionState {
    /// Connection ID
    pub id: String,
    
    /// Remote address
    pub remote_addr: SocketAddr,
    
    /// User UUID
    pub user_uuid: Uuid,
    
    /// Authentication status
    pub authenticated: bool,
    
    /// Active streams
    pub active_streams: HashMap<u64, TuicStreamState>,
    
    /// UDP associations
    pub udp_associations: HashMap<u16, TuicUdpAssociation>,
    
    /// Last heartbeat
    pub last_heartbeat: Option<Instant>,
    
    /// Connection start time
    pub start_time: SystemTime,
    
    /// Bytes sent
    pub bytes_sent: u64,
    
    /// Bytes received
    pub bytes_received: u64,
}

/// TUIC stream state
#[derive(Debug, Clone)]
pub struct TuicStreamState {
    /// Stream ID
    pub stream_id: u64,
    
    /// Stream type (TCP/UDP)
    pub stream_type: TuicStreamType,
    
    /// Destination address
    pub destination: SocketAddr,
    
    /// Stream start time
    pub start_time: Instant,
    
    /// Bytes transferred
    pub bytes_transferred: u64,
}

/// TUIC stream types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TuicStreamType {
    /// TCP stream
    Tcp,
    /// UDP stream
    Udp,
}

/// TUIC UDP association
#[derive(Debug, Clone)]
pub struct TuicUdpAssociation {
    /// Association ID
    pub id: u16,
    
    /// Client address
    pub client_addr: SocketAddr,
    
    /// Target address
    pub target_addr: Option<SocketAddr>,
    
    /// Last activity
    pub last_activity: Instant,
    
    /// Packet count
    pub packet_count: u64,
}

/// TUIC server/client implementation
pub struct TuicAdapter {
    /// Configuration
    config: TuicConfig,
    
    /// Adapter tag
    tag: String,
    
    /// Connection states
    connections: Arc<RwLock<HashMap<String, TuicConnectionState>>>,
    
    /// Stream counter
    stream_counter: Arc<Mutex<u64>>,
    
    /// Started flag
    started: std::sync::atomic::AtomicBool,
    
    /// Is server mode
    is_server: bool,
}

impl TuicAdapter {
    /// Create a new TUIC adapter
    pub fn new(tag: String, config: TuicConfig, is_server: bool) -> Result<Self, String> {
        // Validate configuration
        if config.uuid.is_empty() {
            return Err("UUID is required".to_string());
        }
        
        if config.password.is_empty() {
            return Err("Password is required".to_string());
        }
        
        if is_server {
            if config.listen.is_none() || config.listen_port.is_none() {
                return Err("Listen address and port are required for server mode".to_string());
            }
        } else {
            if config.server.is_none() || config.server_port.is_none() {
                return Err("Server address and port are required for client mode".to_string());
            }
        }
        
        Ok(Self {
            config,
            tag,
            connections: Arc::new(RwLock::new(HashMap::new())),
            stream_counter: Arc::new(Mutex::new(0)),
            started: std::sync::atomic::AtomicBool::new(false),
            is_server,
        })
    }
    
    /// Get next stream ID
    async fn next_stream_id(&self) -> u64 {
        let mut counter = self.stream_counter.lock().await;
        *counter += 1;
        *counter
    }
    
    /// Authenticate connection
    async fn authenticate(&self, uuid: &Uuid, password: &str) -> bool {
        // Parse configured UUID
        if let Ok(config_uuid) = Uuid::parse_str(&self.config.uuid) {
            config_uuid == *uuid && self.config.password == password
        } else {
            false
        }
    }
    
    /// Handle TUIC command
    async fn handle_command(
        &self,
        connection_id: &str,
        header: &TuicHeader,
        payload: &[u8],
    ) -> Result<Vec<u8>, String> {
        match header.command {
            TuicCommand::Authenticate => {
                self.handle_authenticate(connection_id, payload).await
            },
            TuicCommand::Connect => {
                self.handle_connect(connection_id, header, payload).await
            },
            TuicCommand::UdpAssociate => {
                self.handle_udp_associate(connection_id, header, payload).await
            },
            TuicCommand::Heartbeat => {
                self.handle_heartbeat(connection_id).await
            },
        }
    }
    
    /// Handle authentication command
    async fn handle_authenticate(&self, connection_id: &str, payload: &[u8]) -> Result<Vec<u8>, String> {
        if payload.len() < 16 + 4 { // UUID (16 bytes) + password length (4 bytes)
            return Err("Invalid authentication payload".to_string());
        }
        
        // Parse UUID
        let uuid_bytes = &payload[0..16];
        let uuid = Uuid::from_bytes_le(uuid_bytes.try_into().unwrap());
        
        // Parse password length
        let password_len = u32::from_le_bytes([payload[16], payload[17], payload[18], payload[19]]) as usize;
        
        if payload.len() < 20 + password_len {
            return Err("Invalid authentication payload length".to_string());
        }
        
        // Parse password
        let password = String::from_utf8_lossy(&payload[20..20 + password_len]);
        
        // Authenticate
        let authenticated = self.authenticate(&uuid, &password).await;
        
        // Update connection state
        let mut connections = self.connections.write().await;
        if let Some(conn_state) = connections.get_mut(connection_id) {
            conn_state.authenticated = authenticated;
            conn_state.user_uuid = uuid;
        }
        
        // Return authentication response
        if authenticated {
            Ok(vec![0x00]) // Success
        } else {
            Ok(vec![0x01]) // Failure
        }
    }
    
    /// Handle connect command
    async fn handle_connect(
        &self,
        connection_id: &str,
        header: &TuicHeader,
        _payload: &[u8],
    ) -> Result<Vec<u8>, String> {
        // Check authentication
        let connections = self.connections.read().await;
        let conn_state = connections.get(connection_id)
            .ok_or("Connection not found")?;
        
        if !conn_state.authenticated {
            return Err("Connection not authenticated".to_string());
        }
        
        let destination = header.address
            .ok_or("Destination address required for connect command")?;
        
        let stream_id = header.stream_id
            .ok_or("Stream ID required for connect command")?;
        
        drop(connections);
        
        // Create stream state
        let stream_state = TuicStreamState {
            stream_id,
            stream_type: TuicStreamType::Tcp,
            destination,
            start_time: Instant::now(),
            bytes_transferred: 0,
        };
        
        // Update connection state
        let mut connections = self.connections.write().await;
        if let Some(conn_state) = connections.get_mut(connection_id) {
            conn_state.active_streams.insert(stream_id, stream_state);
        }
        
        println!("TUIC TCP connection established: {} -> {}", connection_id, destination);
        
        // Return success response
        Ok(vec![0x00])
    }
    
    /// Handle UDP associate command
    async fn handle_udp_associate(
        &self,
        connection_id: &str,
        header: &TuicHeader,
        _payload: &[u8],
    ) -> Result<Vec<u8>, String> {
        // Check authentication
        let connections = self.connections.read().await;
        let conn_state = connections.get(connection_id)
            .ok_or("Connection not found")?;
        
        if !conn_state.authenticated {
            return Err("Connection not authenticated".to_string());
        }
        
        let client_addr = conn_state.remote_addr;
        drop(connections);
        
        // Generate association ID
        let association_id = rand::random::<u16>();
        
        // Create UDP association
        let udp_association = TuicUdpAssociation {
            id: association_id,
            client_addr,
            target_addr: header.address,
            last_activity: Instant::now(),
            packet_count: 0,
        };
        
        // Update connection state
        let mut connections = self.connections.write().await;
        if let Some(conn_state) = connections.get_mut(connection_id) {
            conn_state.udp_associations.insert(association_id, udp_association);
        }
        
        println!("TUIC UDP association created: {} (ID: {})", connection_id, association_id);
        
        // Return association ID
        Ok(association_id.to_le_bytes().to_vec())
    }
    
    /// Handle heartbeat command
    async fn handle_heartbeat(&self, connection_id: &str) -> Result<Vec<u8>, String> {
        // Update last heartbeat time
        let mut connections = self.connections.write().await;
        if let Some(conn_state) = connections.get_mut(connection_id) {
            conn_state.last_heartbeat = Some(Instant::now());
        }
        
        // Return heartbeat response
        Ok(vec![0x00])
    }
    
    /// Parse TUIC header
    fn parse_header(&self, data: &[u8]) -> Result<(TuicHeader, usize), String> {
        if data.len() < 3 {
            return Err("Data too short for TUIC header".to_string());
        }
        
        let command = TuicCommand::from_byte(data[0])
            .ok_or("Invalid TUIC command")?;
        
        let mut offset = 1;
        let mut header = TuicHeader {
            command,
            stream_id: None,
            fragment_id: None,
            fragment_total: None,
            address: None,
            payload_length: 0,
        };
        
        // Parse based on command type
        match command {
            TuicCommand::Connect => {
                if data.len() < offset + 8 {
                    return Err("Invalid connect command header".to_string());
                }
                
                // Stream ID (8 bytes)
                let stream_id = u64::from_le_bytes([
                    data[offset], data[offset + 1], data[offset + 2], data[offset + 3],
                    data[offset + 4], data[offset + 5], data[offset + 6], data[offset + 7],
                ]);
                header.stream_id = Some(stream_id);
                offset += 8;
                
                // Parse address
                let (addr, addr_len) = self.parse_address(&data[offset..])?;
                header.address = Some(addr);
                offset += addr_len;
            },
            TuicCommand::UdpAssociate => {
                // Similar parsing for UDP associate
                if data.len() >= offset + 4 {
                    let fragment_id = u16::from_le_bytes([data[offset], data[offset + 1]]);
                    let fragment_total = u16::from_le_bytes([data[offset + 2], data[offset + 3]]);
                    header.fragment_id = Some(fragment_id);
                    header.fragment_total = Some(fragment_total);
                    offset += 4;
                }
            },
            _ => {
                // Other commands may have different header formats
            }
        }
        
        // Payload length (2 bytes)
        if data.len() >= offset + 2 {
            header.payload_length = u16::from_le_bytes([data[offset], data[offset + 1]]);
            offset += 2;
        }
        
        Ok((header, offset))
    }
    
    /// Parse address from bytes
    fn parse_address(&self, data: &[u8]) -> Result<(SocketAddr, usize), String> {
        if data.is_empty() {
            return Err("Empty address data".to_string());
        }
        
        let addr_type = data[0];
        let mut offset = 1;
        
        match addr_type {
            0x01 => {
                // IPv4
                if data.len() < offset + 6 {
                    return Err("Invalid IPv4 address".to_string());
                }
                
                let ip = IpAddr::V4(std::net::Ipv4Addr::new(
                    data[offset], data[offset + 1], data[offset + 2], data[offset + 3]
                ));
                let port = u16::from_be_bytes([data[offset + 4], data[offset + 5]]);
                offset += 6;
                
                Ok((SocketAddr::new(ip, port), offset))
            },
            0x04 => {
                // IPv6
                if data.len() < offset + 18 {
                    return Err("Invalid IPv6 address".to_string());
                }
                
                let mut ipv6_bytes = [0u8; 16];
                ipv6_bytes.copy_from_slice(&data[offset..offset + 16]);
                let ip = IpAddr::V6(std::net::Ipv6Addr::from(ipv6_bytes));
                let port = u16::from_be_bytes([data[offset + 16], data[offset + 17]]);
                offset += 18;
                
                Ok((SocketAddr::new(ip, port), offset))
            },
            0x03 => {
                // Domain name
                if data.len() < offset + 1 {
                    return Err("Invalid domain address".to_string());
                }
                
                let domain_len = data[offset] as usize;
                offset += 1;
                
                if data.len() < offset + domain_len + 2 {
                    return Err("Invalid domain address length".to_string());
                }
                
                let domain = String::from_utf8_lossy(&data[offset..offset + domain_len]);
                let port = u16::from_be_bytes([data[offset + domain_len], data[offset + domain_len + 1]]);
                offset += domain_len + 2;
                
                // Resolve domain (simplified)
                let ip = "127.0.0.1".parse::<IpAddr>().unwrap(); // Placeholder
                Ok((SocketAddr::new(ip, port), offset))
            },
            _ => Err(format!("Unsupported address type: {}", addr_type)),
        }
    }
    
    /// Get connection statistics
    pub async fn get_connection_stats(&self) -> HashMap<String, TuicConnectionState> {
        self.connections.read().await.clone()
    }
    
    /// Start heartbeat task
    async fn start_heartbeat_task(&self) {
        let connections = Arc::clone(&self.connections);
        let heartbeat_interval = self.config.heartbeat.unwrap_or(Duration::from_secs(30));
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(heartbeat_interval);
            
            loop {
                interval.tick().await;
                
                let connection_ids: Vec<String> = {
                    let connections_guard = connections.read().await;
                    connections_guard.keys().cloned().collect()
                };
                
                for connection_id in connection_ids {
                    // Check if heartbeat is needed
                    let needs_heartbeat = {
                        let connections_guard = connections.read().await;
                        if let Some(conn_state) = connections_guard.get(&connection_id) {
                            conn_state.last_heartbeat
                                .map(|last| last.elapsed() > heartbeat_interval)
                                .unwrap_or(true)
                        } else {
                            false
                        }
                    };
                    
                    if needs_heartbeat {
                        // Send heartbeat (in real implementation)
                        println!("Sending heartbeat to connection: {}", connection_id);
                    }
                }
            }
        });
    }
}

impl Adapter for TuicAdapter {
    fn adapter_type(&self) -> &str {
        "tuic"
    }
    
    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Inbound for TuicAdapter {
    // TUIC can act as an inbound
}

impl Outbound for TuicAdapter {
    fn network(&self) -> Vec<String> {
        vec!["tcp".to_string(), "udp".to_string()]
    }

    fn dependencies(&self) -> Vec<String> {
        Vec::new()
    }

    fn dial(&self, _network: &str, _destination: &str) -> Result<(), String> {
        Ok(())
    }
}

#[async_trait]
impl Lifecycle for TuicAdapter {
    async fn start(&self, stage: StartStage) -> Result<(), String> {
        if self.started.load(std::sync::atomic::Ordering::Relaxed) {
            return Ok(());
        }
        
        match stage {
            StartStage::Initialize => {
                println!("Initializing TUIC adapter '{}'", self.tag);
            },
            StartStage::Start => {
                if self.is_server {
                    println!("Starting TUIC server '{}' on {}:{}", 
                             self.tag, 
                             self.config.listen.as_ref().unwrap(),
                             self.config.listen_port.unwrap());
                } else {
                    println!("Starting TUIC client '{}' connecting to {}:{}", 
                             self.tag,
                             self.config.server.as_ref().unwrap(),
                             self.config.server_port.unwrap());
                }
                
                // Start heartbeat task
                let rt = tokio::runtime::Handle::current();
                rt.spawn(async move {
                    // Heartbeat task would run here
                });
                
                self.started.store(true, std::sync::atomic::Ordering::Relaxed);
            },
            StartStage::PostStart => {
                println!("TUIC adapter '{}' post-start", self.tag);
            },
            StartStage::Started => {
                println!("TUIC adapter '{}' fully started", self.tag);
            },
        }
        
        Ok(())
    }
    
    async fn close(&self) -> Result<(), String> {
        if !self.started.load(std::sync::atomic::Ordering::Relaxed) {
            return Ok(());
        }

        self.started.store(false, std::sync::atomic::Ordering::Relaxed);
        println!("TUIC adapter '{}' stopped", self.tag);
        
        Ok(())
    }
}

impl Default for TuicConfig {
    fn default() -> Self {
        Self {
            version: TuicVersion::V5,
            server: None,
            server_port: None,
            listen: Some("0.0.0.0".to_string()),
            listen_port: Some(8443),
            uuid: Uuid::new_v4().to_string(),
            password: "password".to_string(),
            congestion_control: Some("bbr".to_string()),
            udp_relay_mode: Some("native".to_string()),
            zero_rtt_handshake: Some(false),
            heartbeat: Some(Duration::from_secs(30)),
            tls: None,
            quic: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_tuic_command_conversion() {
        assert_eq!(TuicCommand::Connect.to_byte(), 0x01);
        assert_eq!(TuicCommand::from_byte(0x01), Some(TuicCommand::Connect));
        
        assert_eq!(TuicCommand::UdpAssociate.to_byte(), 0x02);
        assert_eq!(TuicCommand::from_byte(0x02), Some(TuicCommand::UdpAssociate));
        
        assert_eq!(TuicCommand::from_byte(0xFF), None);
    }
    
    #[test]
    fn test_tuic_config_default() {
        let config = TuicConfig::default();
        
        assert_eq!(config.version, TuicVersion::V5);
        assert_eq!(config.listen_port, Some(8443));
        assert!(!config.uuid.is_empty());
        assert_eq!(config.password, "password");
    }
    
    #[tokio::test]
    async fn test_tuic_adapter_creation() {
        let config = TuicConfig::default();
        let adapter = TuicAdapter::new("test-tuic".to_string(), config, true);
        
        assert!(adapter.is_ok());
        
        let adapter = adapter.unwrap();
        assert_eq!(adapter.adapter_type(), "tuic");
        assert_eq!(adapter.tag(), "test-tuic");
    }
    
    #[tokio::test]
    async fn test_tuic_authentication() {
        let mut config = TuicConfig::default();
        config.uuid = "550e8400-e29b-41d4-a716-************".to_string();
        config.password = "test123".to_string();
        
        let adapter = TuicAdapter::new("test".to_string(), config, true).unwrap();
        
        let uuid = Uuid::parse_str("550e8400-e29b-41d4-a716-************").unwrap();
        assert!(adapter.authenticate(&uuid, "test123").await);
        assert!(!adapter.authenticate(&uuid, "wrong").await);
    }
    
    #[tokio::test]
    async fn test_stream_id_generation() {
        let config = TuicConfig::default();
        let adapter = TuicAdapter::new("test".to_string(), config, true).unwrap();
        
        let id1 = adapter.next_stream_id().await;
        let id2 = adapter.next_stream_id().await;
        
        assert_eq!(id1, 1);
        assert_eq!(id2, 2);
    }
}

/// TUIC performance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuicPerformanceConfig {
    /// Number of worker threads
    pub worker_threads: Option<usize>,

    /// Connection pool size
    pub connection_pool_size: Option<usize>,

    /// Stream buffer size
    pub stream_buffer_size: Option<usize>,

    /// Packet buffer size
    pub packet_buffer_size: Option<usize>,

    /// Enable batch processing
    pub batch_processing: Option<bool>,

    /// Batch size
    pub batch_size: Option<usize>,

    /// Enable connection multiplexing
    pub multiplexing: Option<bool>,

    /// Maximum concurrent streams per connection
    pub max_concurrent_streams: Option<u64>,

    /// Enable zero copy optimization
    pub zero_copy: Option<bool>,

    /// Memory pool size
    pub memory_pool_size: Option<usize>,
}

/// TUIC security configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuicSecurityConfig {
    /// Enable strict mode
    pub strict_mode: Option<bool>,

    /// Key rotation interval
    pub key_rotation_interval: Option<Duration>,

    /// Maximum connection time
    pub max_connection_time: Option<Duration>,

    /// Rate limiting
    pub rate_limiting: Option<TuicRateLimiting>,

    /// Access control
    pub access_control: Option<TuicAccessControl>,

    /// Enable connection fingerprinting
    pub fingerprinting: Option<bool>,

    /// Anti-replay protection
    pub anti_replay: Option<bool>,
}

/// TUIC rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuicRateLimiting {
    /// Connections per second limit
    pub connections_per_second: Option<u32>,

    /// Bytes per second limit
    pub bytes_per_second: Option<u64>,

    /// Packets per second limit
    pub packets_per_second: Option<u32>,

    /// Burst allowance
    pub burst_allowance: Option<u32>,

    /// Rate limiting window
    pub window: Option<Duration>,
}

/// TUIC access control configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuicAccessControl {
    /// Allowed source IPs
    pub allowed_ips: Option<Vec<String>>,

    /// Blocked source IPs
    pub blocked_ips: Option<Vec<String>>,

    /// Allowed countries (GeoIP)
    pub allowed_countries: Option<Vec<String>>,

    /// Blocked countries (GeoIP)
    pub blocked_countries: Option<Vec<String>>,

    /// User whitelist
    pub user_whitelist: Option<Vec<String>>,

    /// User blacklist
    pub user_blacklist: Option<Vec<String>>,
}

/// TUIC monitoring configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuicMonitoringConfig {
    /// Enable metrics collection
    pub enable_metrics: Option<bool>,

    /// Metrics export interval
    pub metrics_interval: Option<Duration>,

    /// Enable connection logging
    pub connection_logging: Option<bool>,

    /// Enable performance logging
    pub performance_logging: Option<bool>,

    /// Log level
    pub log_level: Option<String>,

    /// Metrics export format
    pub metrics_format: Option<String>,

    /// Health check endpoint
    pub health_check: Option<bool>,
}

/// TUIC experimental configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuicExperimentalConfig {
    /// Enable HTTP/3 support
    pub http3_support: Option<bool>,

    /// Enable WebTransport
    pub webtransport: Option<bool>,

    /// Enable post-quantum cryptography
    pub post_quantum: Option<bool>,

    /// Custom QUIC extensions
    pub quic_extensions: Option<Vec<String>>,

    /// Enable connection migration
    pub connection_migration: Option<bool>,

    /// Enable multipath support
    pub multipath: Option<bool>,

    /// Custom transport parameters
    pub transport_params: Option<HashMap<String, serde_json::Value>>,
}
