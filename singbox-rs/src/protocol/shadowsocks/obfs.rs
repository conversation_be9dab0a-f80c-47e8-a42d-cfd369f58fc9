//! Shadowsocks obfuscation methods
//!
//! This module provides traffic obfuscation capabilities
//! to help bypass deep packet inspection and censorship.

use std::collections::HashMap;
use std::sync::Arc;
use serde::{Deserialize, Serialize};
use rand::{Rng, Rng<PERSON>ore};

/// Obfuscation methods
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ObfsMethod {
    /// No obfuscation
    None,
    
    /// HTTP obfuscation
    Http,
    
    /// TLS obfuscation
    Tls,
    
    /// WebSocket obfuscation
    WebSocket,
    
    /// Random padding
    RandomPadding,
    
    /// Custom obfuscation
    Custom(String),
}

/// Obfuscation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ObfsConfig {
    /// Obfuscation method
    pub method: ObfsMethod,
    
    /// Obfuscation parameters
    pub params: HashMap<String, String>,
    
    /// Enable obfuscation
    pub enabled: bool,
}

/// HTTP obfuscation configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HttpObfsConfig {
    /// Host header
    pub host: String,
    
    /// User agent
    pub user_agent: Option<String>,
    
    /// Request path
    pub path: Option<String>,
    
    /// Additional headers
    pub headers: HashMap<String, String>,
}

/// TLS obfuscation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TlsObfsConfig {
    /// Server name indication
    pub sni: String,
    
    /// TLS version
    pub version: Option<String>,
    
    /// Cipher suites
    pub cipher_suites: Option<Vec<String>>,
    
    /// ALPN protocols
    pub alpn: Option<Vec<String>>,
}

/// WebSocket obfuscation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketObfsConfig {
    /// WebSocket path
    pub path: String,
    
    /// Host header
    pub host: String,
    
    /// Additional headers
    pub headers: HashMap<String, String>,
    
    /// Compression
    pub compression: bool,
}

/// Obfuscation trait
pub trait Obfuscator: Send + Sync {
    /// Get obfuscator name
    fn name(&self) -> &str;
    
    /// Obfuscate outgoing data
    fn obfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String>;
    
    /// Deobfuscate incoming data
    fn deobfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String>;
    
    /// Generate handshake data
    fn generate_handshake(&self) -> Result<Vec<u8>, String>;
    
    /// Process handshake response
    fn process_handshake_response(&self, data: &[u8]) -> Result<bool, String>;
}

/// HTTP obfuscator
pub struct HttpObfuscator {
    config: HttpObfsConfig,
    handshake_sent: bool,
}

impl HttpObfuscator {
    /// Create a new HTTP obfuscator
    pub fn new(config: HttpObfsConfig) -> Self {
        Self {
            config,
            handshake_sent: false,
        }
    }
}

impl Obfuscator for HttpObfuscator {
    fn name(&self) -> &str {
        "http"
    }
    
    fn obfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        if !self.handshake_sent {
            // First packet: wrap in HTTP request
            let user_agent = self.config.user_agent.as_deref()
                .unwrap_or("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            let path = self.config.path.as_deref().unwrap_or("/");
            
            let mut http_request = format!(
                "GET {} HTTP/1.1\r\n\
                 Host: {}\r\n\
                 User-Agent: {}\r\n\
                 Connection: Upgrade\r\n\
                 Upgrade: websocket\r\n\
                 Sec-WebSocket-Key: {}\r\n\
                 Sec-WebSocket-Version: 13\r\n",
                path,
                self.config.host,
                user_agent,
                base64::encode(&rand::thread_rng().gen::<[u8; 16]>())
            );
            
            // Add custom headers
            for (key, value) in &self.config.headers {
                http_request.push_str(&format!("{}: {}\r\n", key, value));
            }
            
            http_request.push_str("\r\n");
            
            // Append actual data
            let mut result = http_request.into_bytes();
            result.extend_from_slice(data);
            
            Ok(result)
        } else {
            // Subsequent packets: just pass through
            Ok(data.to_vec())
        }
    }
    
    fn deobfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        // For HTTP obfuscation, we need to strip HTTP headers from the first response
        if let Some(header_end) = data.windows(4).position(|window| window == b"\r\n\r\n") {
            Ok(data[header_end + 4..].to_vec())
        } else {
            Ok(data.to_vec())
        }
    }
    
    fn generate_handshake(&self) -> Result<Vec<u8>, String> {
        // HTTP handshake is generated in obfuscate method
        Ok(Vec::new())
    }
    
    fn process_handshake_response(&self, _data: &[u8]) -> Result<bool, String> {
        // For HTTP obfuscation, any response is considered successful
        Ok(true)
    }
}

/// TLS obfuscator
pub struct TlsObfuscator {
    config: TlsObfsConfig,
    handshake_complete: bool,
}

impl TlsObfuscator {
    /// Create a new TLS obfuscator
    pub fn new(config: TlsObfsConfig) -> Self {
        Self {
            config,
            handshake_complete: false,
        }
    }
    
    /// Generate TLS Client Hello
    fn generate_client_hello(&self) -> Vec<u8> {
        let mut hello = Vec::new();
        
        // TLS Record Header
        hello.push(0x16); // Content Type: Handshake
        hello.extend_from_slice(&[0x03, 0x03]); // Version: TLS 1.2
        
        // Record Length (placeholder, will be updated)
        let length_pos = hello.len();
        hello.extend_from_slice(&[0x00, 0x00]);
        
        // Handshake Header
        hello.push(0x01); // Handshake Type: Client Hello
        
        // Handshake Length (placeholder, will be updated)
        let handshake_length_pos = hello.len();
        hello.extend_from_slice(&[0x00, 0x00, 0x00]);
        
        // Client Hello
        hello.extend_from_slice(&[0x03, 0x03]); // Version: TLS 1.2
        
        // Random (32 bytes)
        let mut random = [0u8; 32];
        rand::thread_rng().fill_bytes(&mut random);
        hello.extend_from_slice(&random);
        
        // Session ID Length
        hello.push(0x00);
        
        // Cipher Suites
        let cipher_suites = vec![
            0x13, 0x01, // TLS_AES_128_GCM_SHA256
            0x13, 0x02, // TLS_AES_256_GCM_SHA384
            0x13, 0x03, // TLS_CHACHA20_POLY1305_SHA256
        ];
        hello.extend_from_slice(&[(cipher_suites.len() as u16).to_be_bytes()[0], (cipher_suites.len() as u16).to_be_bytes()[1]]);
        hello.extend_from_slice(&cipher_suites);
        
        // Compression Methods
        hello.push(0x01); // Length
        hello.push(0x00); // No compression
        
        // Extensions
        let extensions = self.generate_tls_extensions();
        hello.extend_from_slice(&[(extensions.len() as u16).to_be_bytes()[0], (extensions.len() as u16).to_be_bytes()[1]]);
        hello.extend_from_slice(&extensions);
        
        // Update lengths
        let handshake_length = hello.len() - handshake_length_pos - 3;
        hello[handshake_length_pos..handshake_length_pos + 3].copy_from_slice(&[
            ((handshake_length >> 16) & 0xFF) as u8,
            ((handshake_length >> 8) & 0xFF) as u8,
            (handshake_length & 0xFF) as u8,
        ]);
        
        let record_length = hello.len() - length_pos - 2;
        hello[length_pos..length_pos + 2].copy_from_slice(&(record_length as u16).to_be_bytes());
        
        hello
    }
    
    /// Generate TLS extensions
    fn generate_tls_extensions(&self) -> Vec<u8> {
        let mut extensions = Vec::new();
        
        // Server Name Indication (SNI)
        extensions.extend_from_slice(&[0x00, 0x00]); // Extension Type: SNI
        let sni_data = self.config.sni.as_bytes();
        let sni_length = sni_data.len() + 5;
        extensions.extend_from_slice(&(sni_length as u16).to_be_bytes());
        extensions.extend_from_slice(&((sni_data.len() + 3) as u16).to_be_bytes());
        extensions.push(0x00); // Name Type: hostname
        extensions.extend_from_slice(&(sni_data.len() as u16).to_be_bytes());
        extensions.extend_from_slice(sni_data);
        
        // ALPN (if configured)
        if let Some(ref alpn_protocols) = self.config.alpn {
            extensions.extend_from_slice(&[0x00, 0x10]); // Extension Type: ALPN
            
            let mut alpn_data = Vec::new();
            for protocol in alpn_protocols {
                alpn_data.push(protocol.len() as u8);
                alpn_data.extend_from_slice(protocol.as_bytes());
            }
            
            extensions.extend_from_slice(&((alpn_data.len() + 2) as u16).to_be_bytes());
            extensions.extend_from_slice(&(alpn_data.len() as u16).to_be_bytes());
            extensions.extend_from_slice(&alpn_data);
        }
        
        extensions
    }
}

impl Obfuscator for TlsObfuscator {
    fn name(&self) -> &str {
        "tls"
    }
    
    fn obfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        if !self.handshake_complete {
            // First packet: wrap in TLS Client Hello
            let mut result = self.generate_client_hello();
            result.extend_from_slice(data);
            Ok(result)
        } else {
            // Subsequent packets: wrap in TLS Application Data
            let mut result = Vec::new();
            result.push(0x17); // Content Type: Application Data
            result.extend_from_slice(&[0x03, 0x03]); // Version: TLS 1.2
            result.extend_from_slice(&(data.len() as u16).to_be_bytes());
            result.extend_from_slice(data);
            Ok(result)
        }
    }
    
    fn deobfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        if data.len() < 5 {
            return Ok(data.to_vec());
        }
        
        // Check if this is a TLS record
        if data[0] == 0x16 || data[0] == 0x17 {
            // Skip TLS record header (5 bytes)
            Ok(data[5..].to_vec())
        } else {
            Ok(data.to_vec())
        }
    }
    
    fn generate_handshake(&self) -> Result<Vec<u8>, String> {
        Ok(self.generate_client_hello())
    }
    
    fn process_handshake_response(&self, data: &[u8]) -> Result<bool, String> {
        // Check if response looks like TLS Server Hello
        if data.len() >= 5 && data[0] == 0x16 && data[1] == 0x03 {
            Ok(true)
        } else {
            Ok(false)
        }
    }
}

/// Random padding obfuscator
pub struct RandomPaddingObfuscator {
    /// Minimum padding size
    min_padding: usize,
    
    /// Maximum padding size
    max_padding: usize,
}

impl RandomPaddingObfuscator {
    /// Create a new random padding obfuscator
    pub fn new(min_padding: usize, max_padding: usize) -> Self {
        Self {
            min_padding,
            max_padding,
        }
    }
}

impl Obfuscator for RandomPaddingObfuscator {
    fn name(&self) -> &str {
        "random-padding"
    }
    
    fn obfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        let padding_size = rand::thread_rng().gen_range(self.min_padding..=self.max_padding);
        let mut padding = vec![0u8; padding_size];
        rand::thread_rng().fill_bytes(&mut padding);
        
        let mut result = Vec::new();
        result.extend_from_slice(&(data.len() as u16).to_be_bytes()); // Original data length
        result.extend_from_slice(&(padding_size as u16).to_be_bytes()); // Padding length
        result.extend_from_slice(data); // Original data
        result.extend_from_slice(&padding); // Random padding
        
        Ok(result)
    }
    
    fn deobfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        if data.len() < 4 {
            return Err("Data too short for random padding format".to_string());
        }
        
        let data_length = u16::from_be_bytes([data[0], data[1]]) as usize;
        let padding_length = u16::from_be_bytes([data[2], data[3]]) as usize;
        
        if data.len() < 4 + data_length + padding_length {
            return Err("Invalid random padding format".to_string());
        }
        
        Ok(data[4..4 + data_length].to_vec())
    }
    
    fn generate_handshake(&self) -> Result<Vec<u8>, String> {
        // No special handshake for random padding
        Ok(Vec::new())
    }
    
    fn process_handshake_response(&self, _data: &[u8]) -> Result<bool, String> {
        Ok(true)
    }
}

/// Obfuscation factory
pub struct ObfuscationFactory;

impl ObfuscationFactory {
    /// Create obfuscator from configuration
    pub fn create_obfuscator(config: &ObfsConfig) -> Result<Arc<dyn Obfuscator>, String> {
        if !config.enabled {
            return Ok(Arc::new(NoObfuscator));
        }
        
        match &config.method {
            ObfsMethod::None => Ok(Arc::new(NoObfuscator)),
            ObfsMethod::Http => {
                let host = config.params.get("host")
                    .ok_or_else(|| "HTTP obfuscation requires 'host' parameter".to_string())?;
                
                let http_config = HttpObfsConfig {
                    host: host.clone(),
                    user_agent: config.params.get("user_agent").cloned(),
                    path: config.params.get("path").cloned(),
                    headers: config.params.iter()
                        .filter(|(k, _)| k.starts_with("header_"))
                        .map(|(k, v)| (k.strip_prefix("header_").unwrap().to_string(), v.clone()))
                        .collect(),
                };
                
                Ok(Arc::new(HttpObfuscator::new(http_config)))
            },
            ObfsMethod::Tls => {
                let sni = config.params.get("sni")
                    .ok_or_else(|| "TLS obfuscation requires 'sni' parameter".to_string())?;
                
                let tls_config = TlsObfsConfig {
                    sni: sni.clone(),
                    version: config.params.get("version").cloned(),
                    cipher_suites: config.params.get("cipher_suites")
                        .map(|s| s.split(',').map(|s| s.trim().to_string()).collect()),
                    alpn: config.params.get("alpn")
                        .map(|s| s.split(',').map(|s| s.trim().to_string()).collect()),
                };
                
                Ok(Arc::new(TlsObfuscator::new(tls_config)))
            },
            ObfsMethod::WebSocket => {
                let host = config.params.get("host")
                    .ok_or_else(|| "WebSocket obfuscation requires 'host' parameter".to_string())?;
                let path = config.params.get("path").unwrap_or(&"/".to_string());
                
                let ws_config = WebSocketObfsConfig {
                    path: path.clone(),
                    host: host.clone(),
                    headers: config.params.iter()
                        .filter(|(k, _)| k.starts_with("header_"))
                        .map(|(k, v)| (k.strip_prefix("header_").unwrap().to_string(), v.clone()))
                        .collect(),
                    compression: config.params.get("compression")
                        .map(|s| s.parse().unwrap_or(false))
                        .unwrap_or(false),
                };
                
                Ok(Arc::new(WebSocketObfuscator::new(ws_config)))
            },
            ObfsMethod::RandomPadding => {
                let min_padding = config.params.get("min_padding")
                    .and_then(|s| s.parse().ok())
                    .unwrap_or(16);
                let max_padding = config.params.get("max_padding")
                    .and_then(|s| s.parse().ok())
                    .unwrap_or(64);
                
                Ok(Arc::new(RandomPaddingObfuscator::new(min_padding, max_padding)))
            },
            ObfsMethod::Custom(name) => {
                Err(format!("Custom obfuscation method '{}' not implemented", name))
            },
        }
    }
}

/// No-op obfuscator
pub struct NoObfuscator;

impl Obfuscator for NoObfuscator {
    fn name(&self) -> &str {
        "none"
    }
    
    fn obfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        Ok(data.to_vec())
    }
    
    fn deobfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        Ok(data.to_vec())
    }
    
    fn generate_handshake(&self) -> Result<Vec<u8>, String> {
        Ok(Vec::new())
    }
    
    fn process_handshake_response(&self, _data: &[u8]) -> Result<bool, String> {
        Ok(true)
    }
}

/// WebSocket obfuscator (placeholder)
pub struct WebSocketObfuscator {
    config: WebSocketObfsConfig,
}

impl WebSocketObfuscator {
    pub fn new(config: WebSocketObfsConfig) -> Self {
        Self { config }
    }
}

impl Obfuscator for WebSocketObfuscator {
    fn name(&self) -> &str {
        "websocket"
    }
    
    fn obfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        // WebSocket frame format implementation would go here
        Ok(data.to_vec())
    }
    
    fn deobfuscate(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        // WebSocket frame parsing would go here
        Ok(data.to_vec())
    }
    
    fn generate_handshake(&self) -> Result<Vec<u8>, String> {
        // WebSocket handshake generation would go here
        Ok(Vec::new())
    }
    
    fn process_handshake_response(&self, _data: &[u8]) -> Result<bool, String> {
        Ok(true)
    }
}

impl Default for ObfsConfig {
    fn default() -> Self {
        Self {
            method: ObfsMethod::None,
            params: HashMap::new(),
            enabled: false,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_obfs_config_creation() {
        let config = ObfsConfig::default();
        assert!(matches!(config.method, ObfsMethod::None));
        assert!(!config.enabled);
    }
    
    #[test]
    fn test_http_obfuscator() {
        let config = HttpObfsConfig {
            host: "example.com".to_string(),
            user_agent: Some("Test-Agent".to_string()),
            path: Some("/api".to_string()),
            headers: HashMap::new(),
        };
        
        let obfuscator = HttpObfuscator::new(config);
        assert_eq!(obfuscator.name(), "http");
        
        let test_data = b"test data";
        let obfuscated = obfuscator.obfuscate(test_data).unwrap();
        
        // Should contain HTTP headers
        let obfuscated_str = String::from_utf8_lossy(&obfuscated);
        assert!(obfuscated_str.contains("GET /api HTTP/1.1"));
        assert!(obfuscated_str.contains("Host: example.com"));
        assert!(obfuscated_str.contains("User-Agent: Test-Agent"));
    }
    
    #[test]
    fn test_random_padding_obfuscator() {
        let obfuscator = RandomPaddingObfuscator::new(16, 32);
        assert_eq!(obfuscator.name(), "random-padding");
        
        let test_data = b"test data";
        let obfuscated = obfuscator.obfuscate(test_data).unwrap();
        
        // Should be longer than original data
        assert!(obfuscated.len() > test_data.len());
        
        // Should be able to deobfuscate
        let deobfuscated = obfuscator.deobfuscate(&obfuscated).unwrap();
        assert_eq!(deobfuscated, test_data);
    }
    
    #[test]
    fn test_tls_obfuscator() {
        let config = TlsObfsConfig {
            sni: "example.com".to_string(),
            version: Some("1.3".to_string()),
            cipher_suites: None,
            alpn: Some(vec!["h2".to_string(), "http/1.1".to_string()]),
        };
        
        let obfuscator = TlsObfuscator::new(config);
        assert_eq!(obfuscator.name(), "tls");
        
        let client_hello = obfuscator.generate_client_hello();
        
        // Should start with TLS record header
        assert_eq!(client_hello[0], 0x16); // Handshake
        assert_eq!(client_hello[1], 0x03); // TLS version major
        assert_eq!(client_hello[2], 0x03); // TLS version minor
    }
    
    #[test]
    fn test_obfuscation_factory() {
        // Test HTTP obfuscation
        let mut params = HashMap::new();
        params.insert("host".to_string(), "example.com".to_string());
        
        let config = ObfsConfig {
            method: ObfsMethod::Http,
            params,
            enabled: true,
        };
        
        let obfuscator = ObfuscationFactory::create_obfuscator(&config).unwrap();
        assert_eq!(obfuscator.name(), "http");
        
        // Test disabled obfuscation
        let config = ObfsConfig {
            method: ObfsMethod::Http,
            params: HashMap::new(),
            enabled: false,
        };
        
        let obfuscator = ObfuscationFactory::create_obfuscator(&config).unwrap();
        assert_eq!(obfuscator.name(), "none");
    }
}
