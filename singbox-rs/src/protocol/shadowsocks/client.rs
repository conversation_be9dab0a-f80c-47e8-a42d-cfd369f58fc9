//! Shadowsocks client implementation
//!
//! This module provides the client-side implementation of the
//! Shadowsocks protocol for outbound connections.

use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::net::{TcpStream, UdpSocket};
use tokio::sync::{RwLock, Mutex};
use tokio::io::{AsyncRead, AsyncWrite, AsyncReadExt, AsyncWriteExt};

use super::{ShadowsocksConfig, ShadowsocksProtocol, SocksAddr, ShadowsocksRequest, ShadowsocksResponse};
use crate::adapter::{Outbound, OutboundContext};

/// Shadowsocks client
pub struct ShadowsocksClient {
    /// Client configuration
    config: ShadowsocksConfig,
    
    /// Protocol handler
    protocol: Arc<Mutex<ShadowsocksProtocol>>,
    
    /// Connection pool
    connection_pool: Arc<RwLock<HashMap<String, Vec<PooledTcpConnection>>>>,
    
    /// UDP socket
    udp_socket: Arc<RwLock<Option<UdpSocket>>>,
    
    /// Client statistics
    stats: Arc<RwLock<ClientStats>>,
    
    /// Active connections
    active_connections: Arc<RwLock<HashMap<String, ActiveConnection>>>,
}

/// Pooled TCP connection
#[derive(Debug)]
pub struct PooledTcpConnection {
    /// Connection stream
    pub stream: TcpStream,
    
    /// Connection ID
    pub id: String,
    
    /// Creation time
    pub created_at: Instant,
    
    /// Last used time
    pub last_used: Instant,
    
    /// Use count
    pub use_count: u32,
    
    /// Connection healthy
    pub healthy: bool,
}

/// Active connection information
#[derive(Debug, Clone)]
pub struct ActiveConnection {
    /// Connection ID
    pub id: String,
    
    /// Target address
    pub target: SocksAddr,
    
    /// Start time
    pub start_time: Instant,
    
    /// Bytes sent
    pub bytes_sent: u64,
    
    /// Bytes received
    pub bytes_received: u64,
    
    /// Connection type
    pub connection_type: ConnectionType,
}

/// Connection types
#[derive(Debug, Clone)]
pub enum ConnectionType {
    /// TCP connection
    Tcp,
    
    /// UDP association
    Udp,
}

/// Client statistics
#[derive(Debug, Clone)]
pub struct ClientStats {
    /// Total connections
    pub total_connections: u64,
    
    /// Active connections
    pub active_connections: u64,
    
    /// Failed connections
    pub failed_connections: u64,
    
    /// Total bytes sent
    pub bytes_sent: u64,
    
    /// Total bytes received
    pub bytes_received: u64,
    
    /// Average connection time
    pub avg_connection_time: Duration,
    
    /// Connection errors
    pub connection_errors: HashMap<String, u64>,
    
    /// Pool statistics
    pub pool_hits: u64,
    
    /// Pool misses
    pub pool_misses: u64,
}

impl ShadowsocksClient {
    /// Create a new Shadowsocks client
    pub fn new(config: ShadowsocksConfig) -> Result<Self, String> {
        let protocol = ShadowsocksProtocol::new(&config.method, &config.password)?;
        
        Ok(Self {
            config,
            protocol: Arc::new(Mutex::new(protocol)),
            connection_pool: Arc::new(RwLock::new(HashMap::new())),
            udp_socket: Arc::new(RwLock::new(None)),
            stats: Arc::new(RwLock::new(ClientStats::default())),
            active_connections: Arc::new(RwLock::new(HashMap::new())),
        })
    }
    
    /// Start the client
    pub async fn start(&self) -> Result<(), String> {
        println!("🔐 Starting Shadowsocks client...");
        
        // Initialize UDP socket if UDP is enabled
        if self.config.udp.unwrap_or(false) {
            let udp_socket = UdpSocket::bind("0.0.0.0:0").await
                .map_err(|e| format!("Failed to bind UDP socket: {}", e))?;
            
            *self.udp_socket.write().await = Some(udp_socket);
        }
        
        // Start connection pool cleanup task
        self.start_cleanup_task().await;
        
        println!("🔐 Shadowsocks client started");
        Ok(())
    }
    
    /// Stop the client
    pub async fn stop(&self) {
        println!("🔐 Stopping Shadowsocks client...");
        
        // Close all pooled connections
        let mut pool = self.connection_pool.write().await;
        pool.clear();
        
        // Close UDP socket
        *self.udp_socket.write().await = None;
        
        println!("🔐 Shadowsocks client stopped");
    }
    
    /// Connect to target through Shadowsocks server
    pub async fn connect(&self, target: SocksAddr) -> Result<ShadowsocksConnection, String> {
        let start_time = Instant::now();
        
        // Try to get connection from pool
        let mut connection = if let Some(pooled) = self.get_pooled_connection(&target).await {
            pooled.stream
        } else {
            // Create new connection
            self.create_new_connection().await?
        };
        
        // Send connection request
        let request = ShadowsocksRequest {
            address: target.clone(),
            data: Vec::new(),
            timestamp: Instant::now(),
        };
        
        {
            let mut protocol = self.protocol.lock().await;
            protocol.encode_request(&mut connection, &request).await?;
        }
        
        // Update statistics
        let connection_time = start_time.elapsed();
        let mut stats = self.stats.write().await;
        stats.total_connections += 1;
        stats.active_connections += 1;
        stats.avg_connection_time = Duration::from_nanos(
            (stats.avg_connection_time.as_nanos() as u64 * (stats.total_connections - 1) + 
             connection_time.as_nanos() as u64) / stats.total_connections
        );
        
        // Record active connection
        let connection_id = uuid::Uuid::new_v4().to_string();
        let active_conn = ActiveConnection {
            id: connection_id.clone(),
            target: target.clone(),
            start_time,
            bytes_sent: 0,
            bytes_received: 0,
            connection_type: ConnectionType::Tcp,
        };
        
        self.active_connections.write().await.insert(connection_id.clone(), active_conn);
        
        Ok(ShadowsocksConnection {
            id: connection_id,
            stream: connection,
            target,
            client: Arc::downgrade(&Arc::new(self.clone())),
            protocol: Arc::clone(&self.protocol),
        })
    }
    
    /// Send UDP packet through Shadowsocks server
    pub async fn send_udp(&self, target: SocksAddr, data: &[u8]) -> Result<(), String> {
        let udp_socket = self.udp_socket.read().await;
        let socket = udp_socket.as_ref()
            .ok_or_else(|| "UDP not enabled".to_string())?;
        
        // Encode UDP packet
        let request = ShadowsocksRequest {
            address: target,
            data: data.to_vec(),
            timestamp: Instant::now(),
        };
        
        let mut encoded_data = Vec::new();
        {
            let mut protocol = self.protocol.lock().await;
            protocol.encode_request(&mut encoded_data, &request).await?;
        }
        
        // Send to Shadowsocks server
        let server_addr: SocketAddr = format!("{}:{}", self.config.server, self.config.server_port)
            .parse()
            .map_err(|e| format!("Invalid server address: {}", e))?;
        
        socket.send_to(&encoded_data, server_addr).await
            .map_err(|e| format!("Failed to send UDP packet: {}", e))?;
        
        // Update statistics
        let mut stats = self.stats.write().await;
        stats.bytes_sent += encoded_data.len() as u64;
        
        Ok(())
    }
    
    /// Get pooled connection
    async fn get_pooled_connection(&self, _target: &SocksAddr) -> Option<PooledTcpConnection> {
        let mut pool = self.connection_pool.write().await;
        let server_key = format!("{}:{}", self.config.server, self.config.server_port);
        
        if let Some(connections) = pool.get_mut(&server_key) {
            if let Some(mut conn) = connections.pop() {
                conn.last_used = Instant::now();
                conn.use_count += 1;
                
                // Update statistics
                let mut stats = self.stats.write().await;
                stats.pool_hits += 1;
                
                return Some(conn);
            }
        }
        
        // Update statistics
        let mut stats = self.stats.write().await;
        stats.pool_misses += 1;
        
        None
    }
    
    /// Create new connection to Shadowsocks server
    async fn create_new_connection(&self) -> Result<TcpStream, String> {
        let server_addr: SocketAddr = format!("{}:{}", self.config.server, self.config.server_port)
            .parse()
            .map_err(|e| format!("Invalid server address: {}", e))?;
        
        let timeout = self.config.connect_timeout.unwrap_or(Duration::from_secs(10));
        
        let stream = tokio::time::timeout(timeout, TcpStream::connect(server_addr)).await
            .map_err(|_| "Connection timeout".to_string())?
            .map_err(|e| format!("Failed to connect to server: {}", e))?;
        
        // Configure TCP options
        if let Some(no_delay) = self.config.no_delay {
            stream.set_nodelay(no_delay)
                .map_err(|e| format!("Failed to set TCP_NODELAY: {}", e))?;
        }
        
        Ok(stream)
    }
    
    /// Start cleanup task for connection pool
    async fn start_cleanup_task(&self) {
        let pool = Arc::clone(&self.connection_pool);
        let active_connections = Arc::clone(&self.active_connections);
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));
            
            loop {
                interval.tick().await;
                
                // Clean up expired pooled connections
                let mut pool_guard = pool.write().await;
                for connections in pool_guard.values_mut() {
                    connections.retain(|conn| {
                        conn.healthy && 
                        conn.last_used.elapsed() < Duration::from_secs(300) &&
                        conn.created_at.elapsed() < Duration::from_secs(3600)
                    });
                }
                
                // Remove empty pools
                pool_guard.retain(|_, connections| !connections.is_empty());
                
                // Clean up stale active connections
                let mut active_guard = active_connections.write().await;
                active_guard.retain(|_, conn| {
                    conn.start_time.elapsed() < Duration::from_secs(3600)
                });
            }
        });
    }
    
    /// Get client statistics
    pub async fn get_stats(&self) -> ClientStats {
        self.stats.read().await.clone()
    }
    
    /// Return connection to pool
    pub async fn return_connection(&self, connection: PooledTcpConnection) {
        let server_key = format!("{}:{}", self.config.server, self.config.server_port);
        let mut pool = self.connection_pool.write().await;
        
        // Add to pool if healthy and not expired
        if connection.healthy && 
           connection.last_used.elapsed() < Duration::from_secs(300) &&
           connection.created_at.elapsed() < Duration::from_secs(3600) {
            
            let connections = pool.entry(server_key).or_insert_with(Vec::new);
            
            // Limit pool size
            if connections.len() < 10 {
                connections.push(connection);
            }
        }
    }
}

/// Shadowsocks connection wrapper
pub struct ShadowsocksConnection {
    /// Connection ID
    pub id: String,
    
    /// TCP stream
    stream: TcpStream,
    
    /// Target address
    target: SocksAddr,
    
    /// Reference to client
    client: std::sync::Weak<ShadowsocksClient>,
    
    /// Protocol handler
    protocol: Arc<Mutex<ShadowsocksProtocol>>,
}

impl ShadowsocksConnection {
    /// Read data from connection
    pub async fn read(&mut self, buf: &mut [u8]) -> Result<usize, String> {
        let n = self.stream.read(buf).await
            .map_err(|e| format!("Failed to read from connection: {}", e))?;
        
        // Update statistics
        if let Some(client) = self.client.upgrade() {
            let mut stats = client.stats.write().await;
            stats.bytes_received += n as u64;
            
            let mut active_connections = client.active_connections.write().await;
            if let Some(conn) = active_connections.get_mut(&self.id) {
                conn.bytes_received += n as u64;
            }
        }
        
        Ok(n)
    }
    
    /// Write data to connection
    pub async fn write(&mut self, buf: &[u8]) -> Result<usize, String> {
        let n = self.stream.write(buf).await
            .map_err(|e| format!("Failed to write to connection: {}", e))?;
        
        // Update statistics
        if let Some(client) = self.client.upgrade() {
            let mut stats = client.stats.write().await;
            stats.bytes_sent += n as u64;
            
            let mut active_connections = client.active_connections.write().await;
            if let Some(conn) = active_connections.get_mut(&self.id) {
                conn.bytes_sent += n as u64;
            }
        }
        
        Ok(n)
    }
    
    /// Close connection
    pub async fn close(self) -> Result<(), String> {
        // Update statistics
        if let Some(client) = self.client.upgrade() {
            let mut stats = client.stats.write().await;
            stats.active_connections = stats.active_connections.saturating_sub(1);
            
            // Remove from active connections
            client.active_connections.write().await.remove(&self.id);
        }
        
        Ok(())
    }
    
    /// Get connection target
    pub fn target(&self) -> &SocksAddr {
        &self.target
    }
    
    /// Get connection ID
    pub fn id(&self) -> &str {
        &self.id
    }
}

impl Clone for ShadowsocksClient {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            protocol: Arc::clone(&self.protocol),
            connection_pool: Arc::clone(&self.connection_pool),
            udp_socket: Arc::clone(&self.udp_socket),
            stats: Arc::clone(&self.stats),
            active_connections: Arc::clone(&self.active_connections),
        }
    }
}

impl Default for ClientStats {
    fn default() -> Self {
        Self {
            total_connections: 0,
            active_connections: 0,
            failed_connections: 0,
            bytes_sent: 0,
            bytes_received: 0,
            avg_connection_time: Duration::ZERO,
            connection_errors: HashMap::new(),
            pool_hits: 0,
            pool_misses: 0,
        }
    }
}

#[async_trait::async_trait]
impl Outbound for ShadowsocksClient {
    async fn connect(&self, context: &OutboundContext) -> Result<Box<dyn AsyncRead + AsyncWrite + Unpin + Send>, String> {
        let target = SocksAddr::from_socket_addr(context.destination);
        let connection = self.connect(target).await?;
        
        // Return a boxed connection that implements the required traits
        Ok(Box::new(ConnectionWrapper::new(connection)))
    }
    
    async fn start(&self) -> Result<(), String> {
        self.start().await
    }
    
    async fn stop(&self) {
        self.stop().await;
    }
    
    fn tag(&self) -> &str {
        "shadowsocks-client"
    }
}

/// Wrapper to make ShadowsocksConnection implement required traits
pub struct ConnectionWrapper {
    connection: ShadowsocksConnection,
}

impl ConnectionWrapper {
    pub fn new(connection: ShadowsocksConnection) -> Self {
        Self { connection }
    }
}

impl AsyncRead for ConnectionWrapper {
    fn poll_read(
        mut self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
        buf: &mut tokio::io::ReadBuf<'_>,
    ) -> std::task::Poll<std::io::Result<()>> {
        std::pin::Pin::new(&mut self.connection.stream).poll_read(cx, buf)
    }
}

impl AsyncWrite for ConnectionWrapper {
    fn poll_write(
        mut self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
        buf: &[u8],
    ) -> std::task::Poll<Result<usize, std::io::Error>> {
        std::pin::Pin::new(&mut self.connection.stream).poll_write(cx, buf)
    }
    
    fn poll_flush(
        mut self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Result<(), std::io::Error>> {
        std::pin::Pin::new(&mut self.connection.stream).poll_flush(cx)
    }
    
    fn poll_shutdown(
        mut self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Result<(), std::io::Error>> {
        std::pin::Pin::new(&mut self.connection.stream).poll_shutdown(cx)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_shadowsocks_client_creation() {
        let config = ShadowsocksConfig {
            server: "127.0.0.1".to_string(),
            server_port: 8388,
            method: "aes-256-gcm".to_string(),
            password: "test-password".to_string(),
            plugin: None,
            udp: Some(true),
            fast_open: Some(true),
            reuse_port: Some(true),
            no_delay: Some(true),
            keep_alive: Some(Duration::from_secs(30)),
            connect_timeout: Some(Duration::from_secs(10)),
            udp_timeout: Some(Duration::from_secs(60)),
            users: None,
            interface: None,
            routing_mark: None,
            protect_path: None,
            multiplex: None,
        };
        
        let client = ShadowsocksClient::new(config);
        assert!(client.is_ok());
        
        let client = client.unwrap();
        assert_eq!(client.config.server, "127.0.0.1");
        assert_eq!(client.config.server_port, 8388);
    }
    
    #[tokio::test]
    async fn test_client_stats() {
        let config = ShadowsocksConfig {
            server: "127.0.0.1".to_string(),
            server_port: 8388,
            method: "aes-256-gcm".to_string(),
            password: "test-password".to_string(),
            plugin: None,
            udp: Some(true),
            fast_open: Some(true),
            reuse_port: Some(true),
            no_delay: Some(true),
            keep_alive: Some(Duration::from_secs(30)),
            connect_timeout: Some(Duration::from_secs(10)),
            udp_timeout: Some(Duration::from_secs(60)),
            users: None,
            interface: None,
            routing_mark: None,
            protect_path: None,
            multiplex: None,
        };
        
        let client = ShadowsocksClient::new(config).unwrap();
        let stats = client.get_stats().await;
        
        assert_eq!(stats.total_connections, 0);
        assert_eq!(stats.active_connections, 0);
        assert_eq!(stats.bytes_sent, 0);
        assert_eq!(stats.bytes_received, 0);
    }
    
    #[test]
    fn test_active_connection() {
        let target = SocksAddr::from_domain("example.com", 443);
        let connection = ActiveConnection {
            id: "test-id".to_string(),
            target: target.clone(),
            start_time: Instant::now(),
            bytes_sent: 1024,
            bytes_received: 2048,
            connection_type: ConnectionType::Tcp,
        };
        
        assert_eq!(connection.id, "test-id");
        assert_eq!(connection.target, target);
        assert_eq!(connection.bytes_sent, 1024);
        assert_eq!(connection.bytes_received, 2048);
        assert!(matches!(connection.connection_type, ConnectionType::Tcp));
    }
}
