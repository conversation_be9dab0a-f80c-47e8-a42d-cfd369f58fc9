//! Shadowsocks cryptography implementation
//!
//! This module provides encryption and decryption functionality
//! for the Shadowsocks protocol with support for various cipher methods.

use std::collections::HashMap;
use std::sync::Arc;
use serde::{Deserialize, Serialize};
use aes_gcm::{Aes128Gcm, Aes256Gcm, KeyInit, Nonce};
use chacha20poly1305::{ChaCha20Poly1305, XChaCha20Poly1305};
use ring::digest;

/// Supported encryption methods
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum CryptoMethod {
    /// AES-128-GCM
    Aes128Gcm,
    
    /// AES-256-GCM
    Aes256Gcm,
    
    /// ChaCha20-Poly1305
    ChaCha20Poly1305,
    
    /// XChaCha20-Poly1305
    XChaCha20Poly1305,
    
    /// AES-128-CFB (legacy)
    Aes128Cfb,
    
    /// AES-256-CFB (legacy)
    Aes256Cfb,
    
    /// Cha<PERSON>ha20 (legacy)
    Cha<PERSON>ha20,
    
    /// RC4-MD5 (deprecated)
    Rc4Md5,
}

/// Cipher interface for encryption/decryption
pub trait Cipher: Send + Sync {
    /// Get cipher name
    fn name(&self) -> &str;
    
    /// Get key size in bytes
    fn key_size(&self) -> usize;
    
    /// Get nonce/IV size in bytes
    fn nonce_size(&self) -> usize;
    
    /// Get tag size in bytes (for AEAD ciphers)
    fn tag_size(&self) -> usize;
    
    /// Encrypt data
    fn encrypt(&self, key: &[u8], nonce: &[u8], plaintext: &[u8], associated_data: &[u8]) -> Result<Vec<u8>, String>;
    
    /// Decrypt data
    fn decrypt(&self, key: &[u8], nonce: &[u8], ciphertext: &[u8], associated_data: &[u8]) -> Result<Vec<u8>, String>;
    
    /// Generate random nonce
    fn generate_nonce(&self) -> Vec<u8> {
        use rand::RngCore;
        let mut nonce = vec![0u8; self.nonce_size()];
        rand::thread_rng().fill_bytes(&mut nonce);
        nonce
    }
}

/// AES-128-GCM cipher implementation
pub struct Aes128GcmCipher;

impl Cipher for Aes128GcmCipher {
    fn name(&self) -> &str {
        "aes-128-gcm"
    }
    
    fn key_size(&self) -> usize {
        16
    }
    
    fn nonce_size(&self) -> usize {
        12
    }
    
    fn tag_size(&self) -> usize {
        16
    }
    
    fn encrypt(&self, key: &[u8], nonce: &[u8], plaintext: &[u8], associated_data: &[u8]) -> Result<Vec<u8>, String> {
        use aes_gcm::aead::{Aead, Payload};
        
        if key.len() != self.key_size() {
            return Err(format!("Invalid key size: expected {}, got {}", self.key_size(), key.len()));
        }
        
        if nonce.len() != self.nonce_size() {
            return Err(format!("Invalid nonce size: expected {}, got {}", self.nonce_size(), nonce.len()));
        }
        
        let cipher = Aes128Gcm::new_from_slice(key)
            .map_err(|e| format!("Failed to create AES-128-GCM cipher: {}", e))?;
        
        let nonce = Nonce::from_slice(nonce);
        let payload = Payload {
            msg: plaintext,
            aad: associated_data,
        };
        
        cipher.encrypt(nonce, payload)
            .map_err(|e| format!("AES-128-GCM encryption failed: {}", e))
    }
    
    fn decrypt(&self, key: &[u8], nonce: &[u8], ciphertext: &[u8], associated_data: &[u8]) -> Result<Vec<u8>, String> {
        use aes_gcm::aead::{Aead, Payload};
        
        if key.len() != self.key_size() {
            return Err(format!("Invalid key size: expected {}, got {}", self.key_size(), key.len()));
        }
        
        if nonce.len() != self.nonce_size() {
            return Err(format!("Invalid nonce size: expected {}, got {}", self.nonce_size(), nonce.len()));
        }
        
        let cipher = Aes128Gcm::new_from_slice(key)
            .map_err(|e| format!("Failed to create AES-128-GCM cipher: {}", e))?;
        
        let nonce = Nonce::from_slice(nonce);
        let payload = Payload {
            msg: ciphertext,
            aad: associated_data,
        };
        
        cipher.decrypt(nonce, payload)
            .map_err(|e| format!("AES-128-GCM decryption failed: {}", e))
    }
}

/// AES-256-GCM cipher implementation
pub struct Aes256GcmCipher;

impl Cipher for Aes256GcmCipher {
    fn name(&self) -> &str {
        "aes-256-gcm"
    }
    
    fn key_size(&self) -> usize {
        32
    }
    
    fn nonce_size(&self) -> usize {
        12
    }
    
    fn tag_size(&self) -> usize {
        16
    }
    
    fn encrypt(&self, key: &[u8], nonce: &[u8], plaintext: &[u8], associated_data: &[u8]) -> Result<Vec<u8>, String> {
        use aes_gcm::aead::{Aead, Payload};
        
        if key.len() != self.key_size() {
            return Err(format!("Invalid key size: expected {}, got {}", self.key_size(), key.len()));
        }
        
        let cipher = Aes256Gcm::new_from_slice(key)
            .map_err(|e| format!("Failed to create AES-256-GCM cipher: {}", e))?;
        
        let nonce = Nonce::from_slice(nonce);
        let payload = Payload {
            msg: plaintext,
            aad: associated_data,
        };
        
        cipher.encrypt(nonce, payload)
            .map_err(|e| format!("AES-256-GCM encryption failed: {}", e))
    }
    
    fn decrypt(&self, key: &[u8], nonce: &[u8], ciphertext: &[u8], associated_data: &[u8]) -> Result<Vec<u8>, String> {
        use aes_gcm::aead::{Aead, Payload};
        
        if key.len() != self.key_size() {
            return Err(format!("Invalid key size: expected {}, got {}", self.key_size(), key.len()));
        }
        
        let cipher = Aes256Gcm::new_from_slice(key)
            .map_err(|e| format!("Failed to create AES-256-GCM cipher: {}", e))?;
        
        let nonce = Nonce::from_slice(nonce);
        let payload = Payload {
            msg: ciphertext,
            aad: associated_data,
        };
        
        cipher.decrypt(nonce, payload)
            .map_err(|e| format!("AES-256-GCM decryption failed: {}", e))
    }
}

/// ChaCha20-Poly1305 cipher implementation
pub struct ChaCha20Poly1305Cipher;

impl Cipher for ChaCha20Poly1305Cipher {
    fn name(&self) -> &str {
        "chacha20-poly1305"
    }
    
    fn key_size(&self) -> usize {
        32
    }
    
    fn nonce_size(&self) -> usize {
        12
    }
    
    fn tag_size(&self) -> usize {
        16
    }
    
    fn encrypt(&self, key: &[u8], nonce: &[u8], plaintext: &[u8], associated_data: &[u8]) -> Result<Vec<u8>, String> {
        use chacha20poly1305::aead::{Aead, Payload};
        
        if key.len() != self.key_size() {
            return Err(format!("Invalid key size: expected {}, got {}", self.key_size(), key.len()));
        }
        
        let cipher = ChaCha20Poly1305::new_from_slice(key)
            .map_err(|e| format!("Failed to create ChaCha20-Poly1305 cipher: {}", e))?;
        
        let nonce = chacha20poly1305::Nonce::from_slice(nonce);
        let payload = Payload {
            msg: plaintext,
            aad: associated_data,
        };
        
        cipher.encrypt(nonce, payload)
            .map_err(|e| format!("ChaCha20-Poly1305 encryption failed: {}", e))
    }
    
    fn decrypt(&self, key: &[u8], nonce: &[u8], ciphertext: &[u8], associated_data: &[u8]) -> Result<Vec<u8>, String> {
        use chacha20poly1305::aead::{Aead, Payload};
        
        if key.len() != self.key_size() {
            return Err(format!("Invalid key size: expected {}, got {}", self.key_size(), key.len()));
        }
        
        let cipher = ChaCha20Poly1305::new_from_slice(key)
            .map_err(|e| format!("Failed to create ChaCha20-Poly1305 cipher: {}", e))?;
        
        let nonce = chacha20poly1305::Nonce::from_slice(nonce);
        let payload = Payload {
            msg: ciphertext,
            aad: associated_data,
        };
        
        cipher.decrypt(nonce, payload)
            .map_err(|e| format!("ChaCha20-Poly1305 decryption failed: {}", e))
    }
}

/// Cipher factory for creating cipher instances
pub struct CipherFactory {
    /// Available ciphers
    ciphers: HashMap<String, Arc<dyn Cipher>>,
}

impl CipherFactory {
    /// Create a new cipher factory
    pub fn new() -> Self {
        let mut factory = Self {
            ciphers: HashMap::new(),
        };
        
        // Register built-in ciphers
        factory.register_cipher("aes-128-gcm", Arc::new(Aes128GcmCipher));
        factory.register_cipher("aes-256-gcm", Arc::new(Aes256GcmCipher));
        factory.register_cipher("chacha20-poly1305", Arc::new(ChaCha20Poly1305Cipher));
        
        factory
    }
    
    /// Register a cipher
    pub fn register_cipher(&mut self, name: &str, cipher: Arc<dyn Cipher>) {
        self.ciphers.insert(name.to_string(), cipher);
    }
    
    /// Get cipher by name
    pub fn get_cipher(&self, name: &str) -> Option<Arc<dyn Cipher>> {
        self.ciphers.get(name).cloned()
    }
    
    /// Get all available cipher names
    pub fn get_available_ciphers(&self) -> Vec<String> {
        self.ciphers.keys().cloned().collect()
    }
}

/// Key derivation for Shadowsocks
pub struct KeyDerivation;

impl KeyDerivation {
    /// Derive key from password using EVP_BytesToKey
    pub fn derive_key(password: &str, key_size: usize) -> Vec<u8> {
        let mut key = Vec::with_capacity(key_size);
        let mut hash_input = password.as_bytes().to_vec();
        
        while key.len() < key_size {
            let hash = digest::digest(&digest::MD5, &hash_input);
            let hash_bytes = hash.as_ref();
            
            let remaining = key_size - key.len();
            if remaining >= hash_bytes.len() {
                key.extend_from_slice(hash_bytes);
            } else {
                key.extend_from_slice(&hash_bytes[..remaining]);
            }
            
            // Prepare for next iteration
            hash_input = [hash_bytes, password.as_bytes()].concat();
        }
        
        key
    }
    
    /// Derive key using HKDF (for modern methods)
    pub fn derive_key_hkdf(password: &str, salt: &[u8], info: &[u8], key_size: usize) -> Result<Vec<u8>, String> {
        use ring::hkdf;
        
        let salt = hkdf::Salt::new(hkdf::HKDF_SHA256, salt);
        let prk = salt.extract(password.as_bytes());
        
        let mut key = vec![0u8; key_size];
        prk.expand(&[info], hkdf::HKDF_SHA256)
            .map_err(|e| format!("HKDF expansion failed: {}", e))?
            .fill(&mut key)
            .map_err(|e| format!("HKDF fill failed: {}", e))?;
        
        Ok(key)
    }
    
    /// Generate salt for key derivation
    pub fn generate_salt(size: usize) -> Vec<u8> {
        use rand::RngCore;
        let mut salt = vec![0u8; size];
        rand::thread_rng().fill_bytes(&mut salt);
        salt
    }
}

/// Shadowsocks crypto context
pub struct CryptoContext {
    /// Cipher instance
    cipher: Arc<dyn Cipher>,
    
    /// Encryption key
    encrypt_key: Vec<u8>,
    
    /// Decryption key
    decrypt_key: Vec<u8>,
    
    /// Nonce counter for encryption
    encrypt_nonce_counter: u64,
    
    /// Nonce counter for decryption
    decrypt_nonce_counter: u64,
    
    /// Salt for key derivation
    salt: Vec<u8>,
}

impl CryptoContext {
    /// Create a new crypto context
    pub fn new(method: &str, password: &str) -> Result<Self, String> {
        let factory = CipherFactory::new();
        let cipher = factory.get_cipher(method)
            .ok_or_else(|| format!("Unsupported cipher method: {}", method))?;
        
        // Generate salt
        let salt = KeyDerivation::generate_salt(cipher.key_size());
        
        // Derive keys
        let master_key = KeyDerivation::derive_key(password, cipher.key_size());
        let encrypt_key = KeyDerivation::derive_key_hkdf(
            &hex::encode(&master_key),
            &salt,
            b"ss-subkey",
            cipher.key_size(),
        )?;
        let decrypt_key = encrypt_key.clone(); // Same key for symmetric encryption
        
        Ok(Self {
            cipher,
            encrypt_key,
            decrypt_key,
            encrypt_nonce_counter: 0,
            decrypt_nonce_counter: 0,
            salt,
        })
    }
    
    /// Encrypt data
    pub fn encrypt(&mut self, plaintext: &[u8], associated_data: &[u8]) -> Result<Vec<u8>, String> {
        let nonce = self.generate_encrypt_nonce();
        let ciphertext = self.cipher.encrypt(&self.encrypt_key, &nonce, plaintext, associated_data)?;
        
        // Prepend nonce to ciphertext
        let mut result = nonce;
        result.extend_from_slice(&ciphertext);
        
        Ok(result)
    }
    
    /// Decrypt data
    pub fn decrypt(&mut self, data: &[u8], associated_data: &[u8]) -> Result<Vec<u8>, String> {
        let nonce_size = self.cipher.nonce_size();
        
        if data.len() < nonce_size {
            return Err("Data too short to contain nonce".to_string());
        }
        
        let (nonce, ciphertext) = data.split_at(nonce_size);
        self.cipher.decrypt(&self.decrypt_key, nonce, ciphertext, associated_data)
    }
    
    /// Generate nonce for encryption
    fn generate_encrypt_nonce(&mut self) -> Vec<u8> {
        let mut nonce = vec![0u8; self.cipher.nonce_size()];
        
        // Use counter-based nonce generation
        let counter_bytes = self.encrypt_nonce_counter.to_le_bytes();
        let copy_len = std::cmp::min(nonce.len(), counter_bytes.len());
        nonce[..copy_len].copy_from_slice(&counter_bytes[..copy_len]);
        
        self.encrypt_nonce_counter += 1;
        nonce
    }
    
    /// Get salt
    pub fn get_salt(&self) -> &[u8] {
        &self.salt
    }
    
    /// Get cipher name
    pub fn get_cipher_name(&self) -> &str {
        self.cipher.name()
    }
}

impl CryptoMethod {
    /// Parse method from string
    pub fn from_str(s: &str) -> Result<Self, String> {
        match s.to_lowercase().as_str() {
            "aes-128-gcm" => Ok(CryptoMethod::Aes128Gcm),
            "aes-256-gcm" => Ok(CryptoMethod::Aes256Gcm),
            "chacha20-poly1305" => Ok(CryptoMethod::ChaCha20Poly1305),
            "xchacha20-poly1305" => Ok(CryptoMethod::XChaCha20Poly1305),
            "aes-128-cfb" => Ok(CryptoMethod::Aes128Cfb),
            "aes-256-cfb" => Ok(CryptoMethod::Aes256Cfb),
            "chacha20" => Ok(CryptoMethod::ChaCha20),
            "rc4-md5" => Ok(CryptoMethod::Rc4Md5),
            _ => Err(format!("Unknown encryption method: {}", s)),
        }
    }
    
    /// Convert to string
    pub fn to_string(&self) -> String {
        match self {
            CryptoMethod::Aes128Gcm => "aes-128-gcm".to_string(),
            CryptoMethod::Aes256Gcm => "aes-256-gcm".to_string(),
            CryptoMethod::ChaCha20Poly1305 => "chacha20-poly1305".to_string(),
            CryptoMethod::XChaCha20Poly1305 => "xchacha20-poly1305".to_string(),
            CryptoMethod::Aes128Cfb => "aes-128-cfb".to_string(),
            CryptoMethod::Aes256Cfb => "aes-256-cfb".to_string(),
            CryptoMethod::ChaCha20 => "chacha20".to_string(),
            CryptoMethod::Rc4Md5 => "rc4-md5".to_string(),
        }
    }
    
    /// Check if method is AEAD
    pub fn is_aead(&self) -> bool {
        matches!(self, 
            CryptoMethod::Aes128Gcm | 
            CryptoMethod::Aes256Gcm | 
            CryptoMethod::ChaCha20Poly1305 | 
            CryptoMethod::XChaCha20Poly1305
        )
    }
    
    /// Get key size for method
    pub fn key_size(&self) -> usize {
        match self {
            CryptoMethod::Aes128Gcm | CryptoMethod::Aes128Cfb => 16,
            CryptoMethod::Aes256Gcm | CryptoMethod::Aes256Cfb => 32,
            CryptoMethod::ChaCha20Poly1305 | CryptoMethod::XChaCha20Poly1305 | CryptoMethod::ChaCha20 => 32,
            CryptoMethod::Rc4Md5 => 16,
        }
    }
    
    /// Get nonce size for method
    pub fn nonce_size(&self) -> usize {
        match self {
            CryptoMethod::Aes128Gcm | CryptoMethod::Aes256Gcm => 12,
            CryptoMethod::ChaCha20Poly1305 => 12,
            CryptoMethod::XChaCha20Poly1305 => 24,
            CryptoMethod::Aes128Cfb | CryptoMethod::Aes256Cfb => 16,
            CryptoMethod::ChaCha20 => 8,
            CryptoMethod::Rc4Md5 => 16,
        }
    }
}

impl Default for CipherFactory {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_crypto_method_parsing() {
        assert_eq!(CryptoMethod::from_str("aes-128-gcm").unwrap(), CryptoMethod::Aes128Gcm);
        assert_eq!(CryptoMethod::from_str("aes-256-gcm").unwrap(), CryptoMethod::Aes256Gcm);
        assert_eq!(CryptoMethod::from_str("chacha20-poly1305").unwrap(), CryptoMethod::ChaCha20Poly1305);
        
        assert!(CryptoMethod::from_str("unknown-method").is_err());
    }
    
    #[test]
    fn test_crypto_method_properties() {
        assert!(CryptoMethod::Aes128Gcm.is_aead());
        assert!(CryptoMethod::Aes256Gcm.is_aead());
        assert!(CryptoMethod::ChaCha20Poly1305.is_aead());
        assert!(!CryptoMethod::Aes128Cfb.is_aead());
        
        assert_eq!(CryptoMethod::Aes128Gcm.key_size(), 16);
        assert_eq!(CryptoMethod::Aes256Gcm.key_size(), 32);
        assert_eq!(CryptoMethod::ChaCha20Poly1305.key_size(), 32);
    }
    
    #[test]
    fn test_key_derivation() {
        let password = "test-password";
        let key = KeyDerivation::derive_key(password, 32);
        
        assert_eq!(key.len(), 32);
        
        // Same password should produce same key
        let key2 = KeyDerivation::derive_key(password, 32);
        assert_eq!(key, key2);
        
        // Different password should produce different key
        let key3 = KeyDerivation::derive_key("different-password", 32);
        assert_ne!(key, key3);
    }
    
    #[test]
    fn test_cipher_factory() {
        let factory = CipherFactory::new();
        
        assert!(factory.get_cipher("aes-128-gcm").is_some());
        assert!(factory.get_cipher("aes-256-gcm").is_some());
        assert!(factory.get_cipher("chacha20-poly1305").is_some());
        assert!(factory.get_cipher("unknown-cipher").is_none());
        
        let available = factory.get_available_ciphers();
        assert!(available.contains(&"aes-128-gcm".to_string()));
        assert!(available.contains(&"aes-256-gcm".to_string()));
        assert!(available.contains(&"chacha20-poly1305".to_string()));
    }
    
    #[test]
    fn test_aes_128_gcm_cipher() {
        let cipher = Aes128GcmCipher;
        let key = vec![0u8; cipher.key_size()];
        let nonce = vec![0u8; cipher.nonce_size()];
        let plaintext = b"Hello, World!";
        let associated_data = b"";
        
        let ciphertext = cipher.encrypt(&key, &nonce, plaintext, associated_data).unwrap();
        let decrypted = cipher.decrypt(&key, &nonce, &ciphertext, associated_data).unwrap();
        
        assert_eq!(decrypted, plaintext);
    }
    
    #[test]
    fn test_crypto_context() {
        let mut context = CryptoContext::new("aes-256-gcm", "test-password").unwrap();
        
        let plaintext = b"Hello, Shadowsocks!";
        let associated_data = b"";
        
        let encrypted = context.encrypt(plaintext, associated_data).unwrap();
        let decrypted = context.decrypt(&encrypted, associated_data).unwrap();
        
        assert_eq!(decrypted, plaintext);
    }
}
