//! Shadowsocks server implementation
//!
//! This module provides the server-side implementation of the
//! Shadowsocks protocol for inbound connections.

use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::net::{TcpL<PERSON>ener, TcpStream, UdpSocket};
use tokio::sync::{RwLock, Mutex};
use tokio::io::{AsyncRead, AsyncWrite, AsyncReadExt, AsyncWriteExt};

use super::{ShadowsocksConfig, ShadowsocksProtocol, SocksAddr, ShadowsocksRequest, UserSession};
use crate::adapter::{Inbound, InboundContext};

/// Shadowsocks server
pub struct ShadowsocksServer {
    /// Server configuration
    config: ShadowsocksConfig,
    
    /// Protocol handler
    protocol: Arc<Mutex<ShadowsocksProtocol>>,
    
    /// TCP listener
    tcp_listener: Arc<RwLock<Option<TcpListener>>>,
    
    /// UDP socket
    udp_socket: Arc<RwLock<Option<UdpSocket>>>,
    
    /// Server statistics
    stats: Arc<RwLock<ServerStats>>,
    
    /// Active sessions
    active_sessions: Arc<RwLock<HashMap<String, UserSession>>>,
    
    /// User authentication
    user_auth: Arc<RwLock<HashMap<String, String>>>, // username -> password
    
    /// Connection handlers
    connection_handlers: Arc<RwLock<Vec<tokio::task::JoinHandle<()>>>>,
}

/// Server statistics
#[derive(Debug, Clone)]
pub struct ServerStats {
    /// Total connections
    pub total_connections: u64,
    
    /// Active connections
    pub active_connections: u64,
    
    /// Failed connections
    pub failed_connections: u64,
    
    /// Authentication failures
    pub auth_failures: u64,
    
    /// Total bytes sent
    pub bytes_sent: u64,
    
    /// Total bytes received
    pub bytes_received: u64,
    
    /// Average connection duration
    pub avg_connection_duration: Duration,
    
    /// Connections by user
    pub connections_by_user: HashMap<String, u64>,
    
    /// Traffic by user
    pub traffic_by_user: HashMap<String, (u64, u64)>, // (sent, received)
    
    /// Connection errors
    pub connection_errors: HashMap<String, u64>,
    
    /// UDP packets processed
    pub udp_packets: u64,
}

/// Client connection handler
pub struct ClientHandler {
    /// Client stream
    stream: TcpStream,
    
    /// Client address
    client_addr: SocketAddr,
    
    /// Protocol handler
    protocol: Arc<Mutex<ShadowsocksProtocol>>,
    
    /// Server statistics
    stats: Arc<RwLock<ServerStats>>,
    
    /// User session
    session: Option<UserSession>,
}

impl ShadowsocksServer {
    /// Create a new Shadowsocks server
    pub fn new(config: ShadowsocksConfig) -> Result<Self, String> {
        let protocol = ShadowsocksProtocol::new(&config.method, &config.password)?;
        
        // Initialize user authentication
        let mut user_auth = HashMap::new();
        if let Some(ref users) = config.users {
            for user in users {
                user_auth.insert(user.name.clone(), user.password.clone());
            }
        }
        
        Ok(Self {
            config,
            protocol: Arc::new(Mutex::new(protocol)),
            tcp_listener: Arc::new(RwLock::new(None)),
            udp_socket: Arc::new(RwLock::new(None)),
            stats: Arc::new(RwLock::new(ServerStats::default())),
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
            user_auth: Arc::new(RwLock::new(user_auth)),
            connection_handlers: Arc::new(RwLock::new(Vec::new())),
        })
    }
    
    /// Start the server
    pub async fn start(&self) -> Result<(), String> {
        println!("🔐 Starting Shadowsocks server on {}:{}...", self.config.server, self.config.server_port);
        
        // Start TCP listener
        let listen_addr: SocketAddr = format!("{}:{}", self.config.server, self.config.server_port)
            .parse()
            .map_err(|e| format!("Invalid listen address: {}", e))?;
        
        let listener = TcpListener::bind(listen_addr).await
            .map_err(|e| format!("Failed to bind TCP listener: {}", e))?;
        
        *self.tcp_listener.write().await = Some(listener);
        
        // Start UDP socket if enabled
        if self.config.udp.unwrap_or(false) {
            let udp_socket = UdpSocket::bind(listen_addr).await
                .map_err(|e| format!("Failed to bind UDP socket: {}", e))?;
            
            *self.udp_socket.write().await = Some(udp_socket);
            
            // Start UDP handler
            self.start_udp_handler().await;
        }
        
        // Start TCP accept loop
        self.start_tcp_accept_loop().await;
        
        // Start cleanup task
        self.start_cleanup_task().await;
        
        println!("🔐 Shadowsocks server started");
        Ok(())
    }
    
    /// Stop the server
    pub async fn stop(&self) {
        println!("🔐 Stopping Shadowsocks server...");
        
        // Close TCP listener
        *self.tcp_listener.write().await = None;
        
        // Close UDP socket
        *self.udp_socket.write().await = None;
        
        // Stop all connection handlers
        let mut handlers = self.connection_handlers.write().await;
        for handler in handlers.drain(..) {
            handler.abort();
        }
        
        println!("🔐 Shadowsocks server stopped");
    }
    
    /// Start TCP accept loop
    async fn start_tcp_accept_loop(&self) {
        let tcp_listener = Arc::clone(&self.tcp_listener);
        let protocol = Arc::clone(&self.protocol);
        let stats = Arc::clone(&self.stats);
        let active_sessions = Arc::clone(&self.active_sessions);
        let user_auth = Arc::clone(&self.user_auth);
        let connection_handlers = Arc::clone(&self.connection_handlers);
        
        let handler = tokio::spawn(async move {
            loop {
                let listener_guard = tcp_listener.read().await;
                if let Some(ref listener) = *listener_guard {
                    match listener.accept().await {
                        Ok((stream, client_addr)) => {
                            // Update statistics
                            {
                                let mut stats_guard = stats.write().await;
                                stats_guard.total_connections += 1;
                                stats_guard.active_connections += 1;
                            }
                            
                            // Handle client connection
                            let client_handler = ClientHandler::new(
                                stream,
                                client_addr,
                                Arc::clone(&protocol),
                                Arc::clone(&stats),
                            );
                            
                            let stats_clone = Arc::clone(&stats);
                            let sessions_clone = Arc::clone(&active_sessions);
                            let auth_clone = Arc::clone(&user_auth);
                            
                            let handle = tokio::spawn(async move {
                                if let Err(e) = client_handler.handle(sessions_clone, auth_clone).await {
                                    eprintln!("Client handler error: {}", e);
                                    
                                    // Update error statistics
                                    let mut stats_guard = stats_clone.write().await;
                                    stats_guard.failed_connections += 1;
                                    *stats_guard.connection_errors.entry(e).or_insert(0) += 1;
                                }
                                
                                // Update active connections count
                                let mut stats_guard = stats_clone.write().await;
                                stats_guard.active_connections = stats_guard.active_connections.saturating_sub(1);
                            });
                            
                            connection_handlers.write().await.push(handle);
                        },
                        Err(e) => {
                            eprintln!("Failed to accept TCP connection: {}", e);
                            tokio::time::sleep(Duration::from_millis(100)).await;
                        }
                    }
                } else {
                    break;
                }
            }
        });
        
        self.connection_handlers.write().await.push(handler);
    }
    
    /// Start UDP handler
    async fn start_udp_handler(&self) {
        let udp_socket = Arc::clone(&self.udp_socket);
        let protocol = Arc::clone(&self.protocol);
        let stats = Arc::clone(&self.stats);
        
        let handler = tokio::spawn(async move {
            let mut buf = vec![0u8; 65536];
            
            loop {
                let socket_guard = udp_socket.read().await;
                if let Some(ref socket) = *socket_guard {
                    match socket.recv_from(&mut buf).await {
                        Ok((len, client_addr)) => {
                            // Update statistics
                            {
                                let mut stats_guard = stats.write().await;
                                stats_guard.udp_packets += 1;
                                stats_guard.bytes_received += len as u64;
                            }
                            
                            // Process UDP packet
                            let packet_data = &buf[..len];
                            if let Err(e) = Self::handle_udp_packet(
                                &protocol,
                                socket,
                                packet_data,
                                client_addr,
                                &stats,
                            ).await {
                                eprintln!("UDP packet handling error: {}", e);
                            }
                        },
                        Err(e) => {
                            eprintln!("Failed to receive UDP packet: {}", e);
                            tokio::time::sleep(Duration::from_millis(100)).await;
                        }
                    }
                } else {
                    break;
                }
            }
        });
        
        self.connection_handlers.write().await.push(handler);
    }
    
    /// Handle UDP packet
    async fn handle_udp_packet(
        protocol: &Arc<Mutex<ShadowsocksProtocol>>,
        socket: &UdpSocket,
        data: &[u8],
        client_addr: SocketAddr,
        stats: &Arc<RwLock<ServerStats>>,
    ) -> Result<(), String> {
        // Decrypt and parse UDP packet
        let mut protocol_guard = protocol.lock().await;
        let request = protocol_guard.decode_request(&mut std::io::Cursor::new(data)).await?;
        
        // Forward UDP packet to target
        let target_socket = UdpSocket::bind("0.0.0.0:0").await
            .map_err(|e| format!("Failed to bind target UDP socket: {}", e))?;
        
        if let Ok(target_addr) = request.address.to_socket_addr() {
            target_socket.send_to(&request.data, target_addr).await
                .map_err(|e| format!("Failed to forward UDP packet: {}", e))?;
            
            // Read response
            let mut response_buf = vec![0u8; 65536];
            if let Ok((response_len, _)) = tokio::time::timeout(
                Duration::from_secs(5),
                target_socket.recv_from(&mut response_buf)
            ).await {
                let (response_len, _) = response_len
                    .map_err(|e| format!("Failed to receive UDP response: {}", e))?;
                
                // Encrypt and send response back to client
                let response_data = &response_buf[..response_len];
                let mut encrypted_response = Vec::new();
                
                let response = super::ShadowsocksResponse {
                    data: response_data.to_vec(),
                    timestamp: Instant::now(),
                };
                
                protocol_guard.encode_response(&mut encrypted_response, &response).await?;
                
                socket.send_to(&encrypted_response, client_addr).await
                    .map_err(|e| format!("Failed to send UDP response: {}", e))?;
                
                // Update statistics
                let mut stats_guard = stats.write().await;
                stats_guard.bytes_sent += encrypted_response.len() as u64;
            }
        }
        
        Ok(())
    }
    
    /// Start cleanup task
    async fn start_cleanup_task(&self) {
        let active_sessions = Arc::clone(&self.active_sessions);
        let connection_handlers = Arc::clone(&self.connection_handlers);
        
        let handler = tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(300)); // 5 minutes
            
            loop {
                interval.tick().await;
                
                // Clean up inactive sessions
                let mut sessions = active_sessions.write().await;
                let now = Instant::now();
                sessions.retain(|_, session| {
                    now.duration_since(session.last_activity) < Duration::from_secs(3600) // 1 hour
                });
                
                // Clean up finished connection handlers
                let mut handlers = connection_handlers.write().await;
                handlers.retain(|handle| !handle.is_finished());
            }
        });
        
        self.connection_handlers.write().await.push(handler);
    }
    
    /// Get server statistics
    pub async fn get_stats(&self) -> ServerStats {
        self.stats.read().await.clone()
    }
    
    /// Get active sessions
    pub async fn get_active_sessions(&self) -> Vec<UserSession> {
        self.active_sessions.read().await.values().cloned().collect()
    }
    
    /// Add user
    pub async fn add_user(&self, username: String, password: String) {
        self.user_auth.write().await.insert(username, password);
    }
    
    /// Remove user
    pub async fn remove_user(&self, username: &str) -> bool {
        self.user_auth.write().await.remove(username).is_some()
    }
}

impl ClientHandler {
    /// Create a new client handler
    pub fn new(
        stream: TcpStream,
        client_addr: SocketAddr,
        protocol: Arc<Mutex<ShadowsocksProtocol>>,
        stats: Arc<RwLock<ServerStats>>,
    ) -> Self {
        Self {
            stream,
            client_addr,
            protocol,
            stats,
            session: None,
        }
    }
    
    /// Handle client connection
    pub async fn handle(
        mut self,
        active_sessions: Arc<RwLock<HashMap<String, UserSession>>>,
        user_auth: Arc<RwLock<HashMap<String, String>>>,
    ) -> Result<(), String> {
        let start_time = Instant::now();
        
        // Read and decrypt initial request
        let request = {
            let mut protocol = self.protocol.lock().await;
            protocol.decode_request(&mut self.stream).await?
        };
        
        // Authenticate user (if multi-user mode)
        if !user_auth.read().await.is_empty() {
            // User authentication logic would go here
            // For now, assume authentication is successful
        }
        
        // Create user session
        let session_id = uuid::Uuid::new_v4().to_string();
        let session = UserSession {
            user_name: "default".to_string(),
            start_time,
            last_activity: Instant::now(),
            bytes_sent: 0,
            bytes_received: 0,
            connection_count: 1,
            client_addr: self.client_addr,
            metadata: HashMap::new(),
        };
        
        active_sessions.write().await.insert(session_id.clone(), session.clone());
        self.session = Some(session);
        
        // Connect to target
        let target_stream = self.connect_to_target(&request.address).await?;
        
        // Start data relay
        self.relay_data(target_stream, &session_id, active_sessions).await?;
        
        Ok(())
    }
    
    /// Connect to target server
    async fn connect_to_target(&self, target: &SocksAddr) -> Result<TcpStream, String> {
        let target_addr = if let Ok(addr) = target.to_socket_addr() {
            addr
        } else {
            // Resolve domain name
            let resolved = tokio::net::lookup_host(format!("{}:{}", target.addr, target.port)).await
                .map_err(|e| format!("Failed to resolve target address: {}", e))?;
            
            resolved.into_iter().next()
                .ok_or_else(|| "No addresses resolved for target".to_string())?
        };
        
        let stream = TcpStream::connect(target_addr).await
            .map_err(|e| format!("Failed to connect to target: {}", e))?;
        
        Ok(stream)
    }
    
    /// Relay data between client and target
    async fn relay_data(
        mut self,
        mut target_stream: TcpStream,
        session_id: &str,
        active_sessions: Arc<RwLock<HashMap<String, UserSession>>>,
    ) -> Result<(), String> {
        let (mut client_read, mut client_write) = self.stream.split();
        let (mut target_read, mut target_write) = target_stream.split();
        
        let stats = Arc::clone(&self.stats);
        let protocol = Arc::clone(&self.protocol);
        let session_id = session_id.to_string();
        
        // Client to target relay
        let client_to_target = {
            let stats = Arc::clone(&stats);
            let sessions = Arc::clone(&active_sessions);
            let session_id = session_id.clone();
            
            tokio::spawn(async move {
                let mut buf = vec![0u8; 8192];
                
                loop {
                    match client_read.read(&mut buf).await {
                        Ok(0) => break, // EOF
                        Ok(n) => {
                            if let Err(e) = target_write.write_all(&buf[..n]).await {
                                eprintln!("Failed to write to target: {}", e);
                                break;
                            }
                            
                            // Update statistics
                            {
                                let mut stats_guard = stats.write().await;
                                stats_guard.bytes_received += n as u64;
                            }
                            
                            // Update session
                            {
                                let mut sessions_guard = sessions.write().await;
                                if let Some(session) = sessions_guard.get_mut(&session_id) {
                                    session.bytes_received += n as u64;
                                    session.last_activity = Instant::now();
                                }
                            }
                        },
                        Err(e) => {
                            eprintln!("Failed to read from client: {}", e);
                            break;
                        }
                    }
                }
            })
        };
        
        // Target to client relay
        let target_to_client = {
            let stats = Arc::clone(&stats);
            let sessions = Arc::clone(&active_sessions);
            let session_id = session_id.clone();
            
            tokio::spawn(async move {
                let mut buf = vec![0u8; 8192];
                
                loop {
                    match target_read.read(&mut buf).await {
                        Ok(0) => break, // EOF
                        Ok(n) => {
                            if let Err(e) = client_write.write_all(&buf[..n]).await {
                                eprintln!("Failed to write to client: {}", e);
                                break;
                            }
                            
                            // Update statistics
                            {
                                let mut stats_guard = stats.write().await;
                                stats_guard.bytes_sent += n as u64;
                            }
                            
                            // Update session
                            {
                                let mut sessions_guard = sessions.write().await;
                                if let Some(session) = sessions_guard.get_mut(&session_id) {
                                    session.bytes_sent += n as u64;
                                    session.last_activity = Instant::now();
                                }
                            }
                        },
                        Err(e) => {
                            eprintln!("Failed to read from target: {}", e);
                            break;
                        }
                    }
                }
            })
        };
        
        // Wait for either direction to complete
        tokio::select! {
            _ = client_to_target => {},
            _ = target_to_client => {},
        }
        
        // Clean up session
        active_sessions.write().await.remove(&session_id);
        
        Ok(())
    }
    
    /// Start UDP handler
    async fn start_udp_handler(&self) {
        let udp_socket = Arc::clone(&self.udp_socket);
        let protocol = Arc::clone(&self.protocol);
        let stats = Arc::clone(&self.stats);
        
        let handler = tokio::spawn(async move {
            let mut buf = vec![0u8; 65536];
            
            loop {
                let socket_guard = udp_socket.read().await;
                if let Some(ref socket) = *socket_guard {
                    match socket.recv_from(&mut buf).await {
                        Ok((len, client_addr)) => {
                            // Update statistics
                            {
                                let mut stats_guard = stats.write().await;
                                stats_guard.udp_packets += 1;
                                stats_guard.bytes_received += len as u64;
                            }
                            
                            // Handle UDP packet
                            let packet_data = &buf[..len];
                            if let Err(e) = Self::handle_udp_packet(
                                &protocol,
                                socket,
                                packet_data,
                                client_addr,
                                &stats,
                            ).await {
                                eprintln!("UDP packet handling error: {}", e);
                            }
                        },
                        Err(e) => {
                            eprintln!("Failed to receive UDP packet: {}", e);
                            tokio::time::sleep(Duration::from_millis(100)).await;
                        }
                    }
                } else {
                    break;
                }
            }
        });
        
        self.connection_handlers.write().await.push(handler);
    }
    
    /// Handle UDP packet (same as in client, but for server side)
    async fn handle_udp_packet(
        protocol: &Arc<Mutex<ShadowsocksProtocol>>,
        socket: &UdpSocket,
        data: &[u8],
        client_addr: SocketAddr,
        stats: &Arc<RwLock<ServerStats>>,
    ) -> Result<(), String> {
        // Implementation similar to client UDP handling
        // but for server-side processing
        Ok(())
    }
}

impl Default for ServerStats {
    fn default() -> Self {
        Self {
            total_connections: 0,
            active_connections: 0,
            failed_connections: 0,
            auth_failures: 0,
            bytes_sent: 0,
            bytes_received: 0,
            avg_connection_duration: Duration::ZERO,
            connections_by_user: HashMap::new(),
            traffic_by_user: HashMap::new(),
            connection_errors: HashMap::new(),
            udp_packets: 0,
        }
    }
}

#[async_trait::async_trait]
impl Inbound for ShadowsocksServer {
    async fn start(&self) -> Result<(), String> {
        self.start().await
    }
    
    async fn stop(&self) {
        self.stop().await;
    }
    
    fn tag(&self) -> &str {
        "shadowsocks-server"
    }
    
    async fn handle_connection(&self, _context: InboundContext) -> Result<(), String> {
        // Connection handling is done in the accept loop
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_shadowsocks_server_creation() {
        let config = ShadowsocksConfig {
            server: "127.0.0.1".to_string(),
            server_port: 8388,
            method: "aes-256-gcm".to_string(),
            password: "test-password".to_string(),
            plugin: None,
            udp: Some(true),
            fast_open: Some(true),
            reuse_port: Some(true),
            no_delay: Some(true),
            keep_alive: Some(Duration::from_secs(30)),
            connect_timeout: Some(Duration::from_secs(10)),
            udp_timeout: Some(Duration::from_secs(60)),
            users: None,
            interface: None,
            routing_mark: None,
            protect_path: None,
            multiplex: None,
        };
        
        let server = ShadowsocksServer::new(config);
        assert!(server.is_ok());
    }
    
    #[tokio::test]
    async fn test_server_stats() {
        let config = ShadowsocksConfig {
            server: "127.0.0.1".to_string(),
            server_port: 8388,
            method: "aes-256-gcm".to_string(),
            password: "test-password".to_string(),
            plugin: None,
            udp: Some(true),
            fast_open: Some(true),
            reuse_port: Some(true),
            no_delay: Some(true),
            keep_alive: Some(Duration::from_secs(30)),
            connect_timeout: Some(Duration::from_secs(10)),
            udp_timeout: Some(Duration::from_secs(60)),
            users: None,
            interface: None,
            routing_mark: None,
            protect_path: None,
            multiplex: None,
        };
        
        let server = ShadowsocksServer::new(config).unwrap();
        let stats = server.get_stats().await;
        
        assert_eq!(stats.total_connections, 0);
        assert_eq!(stats.active_connections, 0);
        assert_eq!(stats.udp_packets, 0);
    }
}
