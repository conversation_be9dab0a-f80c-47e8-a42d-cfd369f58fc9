//! Shadowsocks protocol implementation
//!
//! This module handles the Shadowsocks protocol message format,
//! address encoding/decoding, and packet processing.

use std::net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddr};
use std::io::{<PERSON><PERSON><PERSON>, <PERSON>, Write};
use serde::{Deserialize, Serialize};
use tokio::io::{AsyncRead, AsyncWrite, AsyncReadExt, AsyncWriteExt};

use super::crypto::CryptoContext;

/// Shadowsocks protocol handler
pub struct ShadowsocksProtocol {
    /// Crypto context
    crypto: CryptoContext,
    
    /// Protocol version
    version: u8,
    
    /// Enable UDP relay
    udp_enabled: bool,
    
    /// One-time auth (deprecated)
    ota_enabled: bool,
}

/// Address types in Shadowsocks protocol
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum AddressType {
    /// IPv4 address
    Ipv4 = 0x01,
    
    /// Domain name
    Domain = 0x03,
    
    /// IPv6 address
    Ipv6 = 0x04,
}

/// Shadowsocks address representation
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq)]
pub struct SocksAddr {
    /// Address type
    pub addr_type: AddressType,
    
    /// Address data
    pub addr: String,
    
    /// Port number
    pub port: u16,
}

/// Shadowsocks request
#[derive(Debug, Clone)]
pub struct ShadowsocksRequest {
    /// Target address
    pub address: SocksAddr,
    
    /// Request data
    pub data: Vec<u8>,
    
    /// Request timestamp
    pub timestamp: std::time::Instant,
}

/// Shadowsocks response
#[derive(Debug, Clone)]
pub struct ShadowsocksResponse {
    /// Response data
    pub data: Vec<u8>,
    
    /// Response timestamp
    pub timestamp: std::time::Instant,
}

impl ShadowsocksProtocol {
    /// Create a new Shadowsocks protocol handler
    pub fn new(method: &str, password: &str) -> Result<Self, String> {
        let crypto = CryptoContext::new(method, password)?;
        
        Ok(Self {
            crypto,
            version: 1,
            udp_enabled: true,
            ota_enabled: false,
        })
    }
    
    /// Encode Shadowsocks request
    pub async fn encode_request<W>(&mut self, writer: &mut W, request: &ShadowsocksRequest) -> Result<(), String>
    where
        W: AsyncWrite + Unpin,
    {
        // Encode address
        let addr_bytes = self.encode_address(&request.address)?;
        
        // Combine address and data
        let mut payload = addr_bytes;
        payload.extend_from_slice(&request.data);
        
        // Encrypt payload
        let encrypted = self.crypto.encrypt(&payload, &[])?;
        
        // Write salt + encrypted data
        writer.write_all(self.crypto.get_salt()).await
            .map_err(|e| format!("Failed to write salt: {}", e))?;
        writer.write_all(&encrypted).await
            .map_err(|e| format!("Failed to write encrypted data: {}", e))?;
        
        Ok(())
    }
    
    /// Decode Shadowsocks request
    pub async fn decode_request<R>(&mut self, reader: &mut R) -> Result<ShadowsocksRequest, String>
    where
        R: AsyncRead + Unpin,
    {
        // Read salt
        let salt_size = self.crypto.get_salt().len();
        let mut salt = vec![0u8; salt_size];
        reader.read_exact(&mut salt).await
            .map_err(|e| format!("Failed to read salt: {}", e))?;
        
        // Read encrypted data (read available data)
        let mut encrypted_data = Vec::new();
        reader.read_to_end(&mut encrypted_data).await
            .map_err(|e| format!("Failed to read encrypted data: {}", e))?;
        
        // Decrypt data
        let decrypted = self.crypto.decrypt(&encrypted_data, &[])?;
        
        // Parse address and data
        let (address, data) = self.parse_request_data(&decrypted)?;
        
        Ok(ShadowsocksRequest {
            address,
            data,
            timestamp: std::time::Instant::now(),
        })
    }
    
    /// Encode Shadowsocks response
    pub async fn encode_response<W>(&mut self, writer: &mut W, response: &ShadowsocksResponse) -> Result<(), String>
    where
        W: AsyncWrite + Unpin,
    {
        // Encrypt response data
        let encrypted = self.crypto.encrypt(&response.data, &[])?;
        
        // Write encrypted data
        writer.write_all(&encrypted).await
            .map_err(|e| format!("Failed to write encrypted response: {}", e))?;
        
        Ok(())
    }
    
    /// Decode Shadowsocks response
    pub async fn decode_response<R>(&mut self, reader: &mut R) -> Result<ShadowsocksResponse, String>
    where
        R: AsyncRead + Unpin,
    {
        // Read encrypted data
        let mut encrypted_data = Vec::new();
        reader.read_to_end(&mut encrypted_data).await
            .map_err(|e| format!("Failed to read encrypted response: {}", e))?;
        
        // Decrypt data
        let decrypted = self.crypto.decrypt(&encrypted_data, &[])?;
        
        Ok(ShadowsocksResponse {
            data: decrypted,
            timestamp: std::time::Instant::now(),
        })
    }
    
    /// Encode address to bytes
    fn encode_address(&self, addr: &SocksAddr) -> Result<Vec<u8>, String> {
        let mut bytes = Vec::new();
        
        match addr.addr_type {
            AddressType::Ipv4 => {
                bytes.push(0x01);
                let ip: Ipv4Addr = addr.addr.parse()
                    .map_err(|e| format!("Invalid IPv4 address: {}", e))?;
                bytes.extend_from_slice(&ip.octets());
            },
            AddressType::Domain => {
                bytes.push(0x03);
                let domain_bytes = addr.addr.as_bytes();
                if domain_bytes.len() > 255 {
                    return Err("Domain name too long".to_string());
                }
                bytes.push(domain_bytes.len() as u8);
                bytes.extend_from_slice(domain_bytes);
            },
            AddressType::Ipv6 => {
                bytes.push(0x04);
                let ip: Ipv6Addr = addr.addr.parse()
                    .map_err(|e| format!("Invalid IPv6 address: {}", e))?;
                bytes.extend_from_slice(&ip.octets());
            },
        }
        
        // Add port
        bytes.extend_from_slice(&addr.port.to_be_bytes());
        
        Ok(bytes)
    }
    
    /// Parse request data to extract address and payload
    fn parse_request_data(&self, data: &[u8]) -> Result<(SocksAddr, Vec<u8>), String> {
        if data.is_empty() {
            return Err("Empty request data".to_string());
        }
        
        let mut cursor = Cursor::new(data);
        
        // Read address type
        let mut addr_type_byte = [0u8; 1];
        cursor.read_exact(&mut addr_type_byte)
            .map_err(|e| format!("Failed to read address type: {}", e))?;
        
        let addr_type = match addr_type_byte[0] {
            0x01 => AddressType::Ipv4,
            0x03 => AddressType::Domain,
            0x04 => AddressType::Ipv6,
            _ => return Err(format!("Unknown address type: {}", addr_type_byte[0])),
        };
        
        // Read address
        let addr = match addr_type {
            AddressType::Ipv4 => {
                let mut ip_bytes = [0u8; 4];
                cursor.read_exact(&mut ip_bytes)
                    .map_err(|e| format!("Failed to read IPv4 address: {}", e))?;
                let ip = Ipv4Addr::from(ip_bytes);
                ip.to_string()
            },
            AddressType::Domain => {
                let mut len_byte = [0u8; 1];
                cursor.read_exact(&mut len_byte)
                    .map_err(|e| format!("Failed to read domain length: {}", e))?;
                
                let domain_len = len_byte[0] as usize;
                let mut domain_bytes = vec![0u8; domain_len];
                cursor.read_exact(&mut domain_bytes)
                    .map_err(|e| format!("Failed to read domain: {}", e))?;
                
                String::from_utf8(domain_bytes)
                    .map_err(|e| format!("Invalid domain name: {}", e))?
            },
            AddressType::Ipv6 => {
                let mut ip_bytes = [0u8; 16];
                cursor.read_exact(&mut ip_bytes)
                    .map_err(|e| format!("Failed to read IPv6 address: {}", e))?;
                let ip = Ipv6Addr::from(ip_bytes);
                ip.to_string()
            },
        };
        
        // Read port
        let mut port_bytes = [0u8; 2];
        cursor.read_exact(&mut port_bytes)
            .map_err(|e| format!("Failed to read port: {}", e))?;
        let port = u16::from_be_bytes(port_bytes);
        
        // Read remaining data
        let mut remaining_data = Vec::new();
        cursor.read_to_end(&mut remaining_data)
            .map_err(|e| format!("Failed to read remaining data: {}", e))?;
        
        let socks_addr = SocksAddr {
            addr_type,
            addr,
            port,
        };
        
        Ok((socks_addr, remaining_data))
    }
}

impl SocksAddr {
    /// Create a new SOCKS address from socket address
    pub fn from_socket_addr(addr: SocketAddr) -> Self {
        match addr.ip() {
            IpAddr::V4(ip) => Self {
                addr_type: AddressType::Ipv4,
                addr: ip.to_string(),
                port: addr.port(),
            },
            IpAddr::V6(ip) => Self {
                addr_type: AddressType::Ipv6,
                addr: ip.to_string(),
                port: addr.port(),
            },
        }
    }
    
    /// Create a new SOCKS address from domain and port
    pub fn from_domain(domain: &str, port: u16) -> Self {
        Self {
            addr_type: AddressType::Domain,
            addr: domain.to_string(),
            port,
        }
    }
    
    /// Convert to socket address (if possible)
    pub fn to_socket_addr(&self) -> Result<SocketAddr, String> {
        match self.addr_type {
            AddressType::Ipv4 | AddressType::Ipv6 => {
                let ip: IpAddr = self.addr.parse()
                    .map_err(|e| format!("Invalid IP address: {}", e))?;
                Ok(SocketAddr::new(ip, self.port))
            },
            AddressType::Domain => {
                Err("Cannot convert domain to socket address without DNS resolution".to_string())
            },
        }
    }
    
    /// Get address string representation
    pub fn to_string(&self) -> String {
        match self.addr_type {
            AddressType::Ipv4 | AddressType::Ipv6 => {
                format!("{}:{}", self.addr, self.port)
            },
            AddressType::Domain => {
                format!("{}:{}", self.addr, self.port)
            },
        }
    }
    
    /// Check if address is IPv4
    pub fn is_ipv4(&self) -> bool {
        matches!(self.addr_type, AddressType::Ipv4)
    }
    
    /// Check if address is IPv6
    pub fn is_ipv6(&self) -> bool {
        matches!(self.addr_type, AddressType::Ipv6)
    }
    
    /// Check if address is domain
    pub fn is_domain(&self) -> bool {
        matches!(self.addr_type, AddressType::Domain)
    }
}

impl AddressType {
    /// Parse address type from byte
    pub fn from_byte(byte: u8) -> Result<Self, String> {
        match byte {
            0x01 => Ok(AddressType::Ipv4),
            0x03 => Ok(AddressType::Domain),
            0x04 => Ok(AddressType::Ipv6),
            _ => Err(format!("Unknown address type: {}", byte)),
        }
    }
    
    /// Convert address type to byte
    pub fn to_byte(&self) -> u8 {
        match self {
            AddressType::Ipv4 => 0x01,
            AddressType::Domain => 0x03,
            AddressType::Ipv6 => 0x04,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_address_type_conversion() {
        assert_eq!(AddressType::from_byte(0x01).unwrap(), AddressType::Ipv4);
        assert_eq!(AddressType::from_byte(0x03).unwrap(), AddressType::Domain);
        assert_eq!(AddressType::from_byte(0x04).unwrap(), AddressType::Ipv6);
        
        assert!(AddressType::from_byte(0x99).is_err());
        
        assert_eq!(AddressType::Ipv4.to_byte(), 0x01);
        assert_eq!(AddressType::Domain.to_byte(), 0x03);
        assert_eq!(AddressType::Ipv6.to_byte(), 0x04);
    }
    
    #[test]
    fn test_socks_addr_creation() {
        // Test IPv4 address
        let ipv4_addr = SocksAddr::from_socket_addr("127.0.0.1:8080".parse().unwrap());
        assert!(ipv4_addr.is_ipv4());
        assert_eq!(ipv4_addr.port, 8080);
        
        // Test IPv6 address
        let ipv6_addr = SocksAddr::from_socket_addr("[::1]:8080".parse().unwrap());
        assert!(ipv6_addr.is_ipv6());
        assert_eq!(ipv6_addr.port, 8080);
        
        // Test domain address
        let domain_addr = SocksAddr::from_domain("example.com", 443);
        assert!(domain_addr.is_domain());
        assert_eq!(domain_addr.addr, "example.com");
        assert_eq!(domain_addr.port, 443);
    }
    
    #[test]
    fn test_socks_addr_conversion() {
        // Test IPv4 conversion
        let ipv4_addr = SocksAddr::from_socket_addr("***********:80".parse().unwrap());
        let socket_addr = ipv4_addr.to_socket_addr().unwrap();
        assert_eq!(socket_addr.to_string(), "***********:80");
        
        // Test domain conversion (should fail)
        let domain_addr = SocksAddr::from_domain("example.com", 443);
        assert!(domain_addr.to_socket_addr().is_err());
    }
    
    #[test]
    fn test_shadowsocks_protocol_creation() {
        let protocol = ShadowsocksProtocol::new("aes-256-gcm", "test-password");
        assert!(protocol.is_ok());
        
        let protocol = protocol.unwrap();
        assert_eq!(protocol.version, 1);
        assert!(protocol.udp_enabled);
        assert!(!protocol.ota_enabled);
    }
    
    #[tokio::test]
    async fn test_address_encoding() {
        let mut protocol = ShadowsocksProtocol::new("aes-256-gcm", "test-password").unwrap();
        
        // Test IPv4 address encoding
        let ipv4_addr = SocksAddr {
            addr_type: AddressType::Ipv4,
            addr: "***********".to_string(),
            port: 80,
        };
        
        let encoded = protocol.encode_address(&ipv4_addr).unwrap();
        assert_eq!(encoded[0], 0x01); // IPv4 type
        assert_eq!(encoded.len(), 1 + 4 + 2); // type + IP + port
        
        // Test domain address encoding
        let domain_addr = SocksAddr {
            addr_type: AddressType::Domain,
            addr: "example.com".to_string(),
            port: 443,
        };
        
        let encoded = protocol.encode_address(&domain_addr).unwrap();
        assert_eq!(encoded[0], 0x03); // Domain type
        assert_eq!(encoded[1], 11); // Domain length
        assert_eq!(encoded.len(), 1 + 1 + 11 + 2); // type + length + domain + port
    }
    
    #[tokio::test]
    async fn test_request_parsing() {
        let mut protocol = ShadowsocksProtocol::new("aes-256-gcm", "test-password").unwrap();
        
        // Create test data with IPv4 address
        let mut test_data = Vec::new();
        test_data.push(0x01); // IPv4 type
        test_data.extend_from_slice(&[192, 168, 1, 1]); // IP address
        test_data.extend_from_slice(&80u16.to_be_bytes()); // Port
        test_data.extend_from_slice(b"GET / HTTP/1.1\r\n"); // HTTP request
        
        let (address, data) = protocol.parse_request_data(&test_data).unwrap();
        
        assert!(address.is_ipv4());
        assert_eq!(address.addr, "***********");
        assert_eq!(address.port, 80);
        assert_eq!(data, b"GET / HTTP/1.1\r\n");
    }
}
