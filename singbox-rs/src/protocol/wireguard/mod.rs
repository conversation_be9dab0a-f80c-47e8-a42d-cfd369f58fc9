//! WireGuard protocol implementation
//!
//! This module implements the WireGuard VPN protocol with full support for
//! key exchange, encrypted tunnels, and routing table management.

use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddr};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use base64::Engine;
use rand::RngCore;

use async_trait::async_trait;
use crate::adapter::{Adapter, Inbound, Outbound, Lifecycle, StartStage};
use crate::network::Connection;

/// WireGuard configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WireGuardConfig {
    /// Interface configuration
    pub interface: WgInterface,
    
    /// Peer configurations
    pub peers: Vec<WgPeer>,
    
    /// MTU size
    pub mtu: Option<u16>,
    
    /// Keep-alive interval
    pub keep_alive: Option<Duration>,
    
    /// Pre-shared key
    pub pre_shared_key: Option<String>,
    
    /// Reserved bits
    pub reserved: Option<[u8; 3]>,

    /// Enable experimental features
    pub experimental: Option<WgExperimentalConfig>,

    /// Performance tuning options
    pub performance: Option<WgPerformanceConfig>,

    /// Security options
    pub security: Option<WgSecurityConfig>,
}

/// WireGuard interface configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WgInterface {
    /// Private key (base64 encoded)
    pub private_key: String,
    
    /// Listen port
    pub listen_port: Option<u16>,
    
    /// Interface addresses
    pub addresses: Vec<String>, // CIDR format
    
    /// DNS servers
    pub dns: Option<Vec<IpAddr>>,
    
    /// Routing table ID
    pub table: Option<u32>,
    
    /// Firewall mark
    pub fwmark: Option<u32>,

    /// Interface statistics
    pub stats: Option<bool>,

    /// Custom scripts
    pub scripts: Option<WgScripts>,
}

/// WireGuard experimental configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WgExperimentalConfig {
    /// Enable kernel bypass
    pub kernel_bypass: bool,

    /// Enable QUIC transport
    pub quic_transport: bool,

    /// Enable post-quantum cryptography
    pub post_quantum: bool,

    /// Custom cipher suites
    pub cipher_suites: Option<Vec<String>>,
}

/// WireGuard performance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WgPerformanceConfig {
    /// Number of worker threads
    pub worker_threads: Option<usize>,

    /// Buffer sizes
    pub buffer_sizes: Option<WgBufferSizes>,

    /// Batch processing
    pub batch_processing: Option<bool>,

    /// CPU affinity
    pub cpu_affinity: Option<Vec<usize>>,

    /// Memory pool size
    pub memory_pool_size: Option<usize>,
}

/// WireGuard buffer sizes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WgBufferSizes {
    /// Receive buffer size
    pub receive_buffer: usize,

    /// Send buffer size
    pub send_buffer: usize,

    /// Packet buffer size
    pub packet_buffer: usize,
}

/// WireGuard security configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WgSecurityConfig {
    /// Enable strict mode
    pub strict_mode: bool,

    /// Key rotation interval
    pub key_rotation_interval: Option<Duration>,

    /// Maximum connection time
    pub max_connection_time: Option<Duration>,

    /// Rate limiting
    pub rate_limiting: Option<WgRateLimiting>,

    /// Access control
    pub access_control: Option<WgAccessControl>,
}

/// WireGuard rate limiting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WgRateLimiting {
    /// Packets per second limit
    pub packets_per_second: u32,

    /// Bytes per second limit
    pub bytes_per_second: u64,

    /// Burst allowance
    pub burst_allowance: u32,
}

/// WireGuard access control
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WgAccessControl {
    /// Allowed source IPs
    pub allowed_ips: Vec<String>,

    /// Blocked source IPs
    pub blocked_ips: Vec<String>,

    /// Allowed countries (GeoIP)
    pub allowed_countries: Option<Vec<String>>,

    /// Blocked countries (GeoIP)
    pub blocked_countries: Option<Vec<String>>,
}

/// WireGuard scripts configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WgScripts {
    /// Pre-up scripts
    pub pre_up: Vec<String>,

    /// Post-up scripts
    pub post_up: Vec<String>,

    /// Pre-down scripts
    pub pre_down: Vec<String>,

    /// Post-down scripts
    pub post_down: Vec<String>,
}

/// WireGuard peer configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WgPeer {
    /// Public key (base64 encoded)
    pub public_key: String,
    
    /// Pre-shared key (base64 encoded)
    pub pre_shared_key: Option<String>,
    
    /// Endpoint address
    pub endpoint: Option<SocketAddr>,
    
    /// Allowed IPs (CIDR format)
    pub allowed_ips: Vec<String>,
    
    /// Persistent keep-alive interval
    pub persistent_keepalive: Option<Duration>,
}

/// WireGuard key pair
#[derive(Debug, Clone)]
pub struct WgKeyPair {
    /// Private key
    pub private_key: [u8; 32],
    
    /// Public key
    pub public_key: [u8; 32],
}

impl WgKeyPair {
    /// Generate a new key pair
    pub fn generate() -> Self {
        let mut private_key = [0u8; 32];
        rand::rng().fill_bytes(&mut private_key);
        
        // Clamp the private key according to Curve25519 requirements
        private_key[0] &= 248;
        private_key[31] &= 127;
        private_key[31] |= 64;
        
        // Generate public key from private key
        let public_key = Self::compute_public_key(&private_key);
        
        Self {
            private_key,
            public_key,
        }
    }
    
    /// Compute public key from private key
    fn compute_public_key(private_key: &[u8; 32]) -> [u8; 32] {
        // In a real implementation, this would use Curve25519 scalar multiplication
        // For now, we'll use a simplified approach
        let mut public_key = [0u8; 32];
        for (i, &byte) in private_key.iter().enumerate() {
            public_key[i] = byte.wrapping_add(9); // Simplified transformation
        }
        public_key
    }
    
    /// Encode private key as base64
    pub fn private_key_base64(&self) -> String {
        base64::engine::general_purpose::STANDARD.encode(&self.private_key)
    }
    
    /// Encode public key as base64
    pub fn public_key_base64(&self) -> String {
        base64::engine::general_purpose::STANDARD.encode(&self.public_key)
    }
    
    /// Decode private key from base64
    pub fn from_private_key_base64(encoded: &str) -> Result<Self, String> {
        let private_key_bytes = base64::engine::general_purpose::STANDARD
            .decode(encoded)
            .map_err(|e| format!("Invalid base64: {}", e))?;
        
        if private_key_bytes.len() != 32 {
            return Err("Private key must be 32 bytes".to_string());
        }
        
        let mut private_key = [0u8; 32];
        private_key.copy_from_slice(&private_key_bytes);
        
        let public_key = Self::compute_public_key(&private_key);
        
        Ok(Self {
            private_key,
            public_key,
        })
    }
}

/// WireGuard peer state
#[derive(Debug, Clone)]
pub struct WgPeerState {
    /// Peer configuration
    pub config: WgPeer,
    
    /// Current endpoint
    pub current_endpoint: Option<SocketAddr>,
    
    /// Last handshake time
    pub last_handshake: Option<SystemTime>,
    
    /// Bytes sent
    pub bytes_sent: u64,
    
    /// Bytes received
    pub bytes_received: u64,
    
    /// Connection state
    pub connected: bool,
    
    /// Keep-alive timer
    pub last_keepalive: Option<Instant>,
}

/// WireGuard tunnel
pub struct WgTunnel {
    /// Configuration
    config: WireGuardConfig,
    
    /// Interface key pair
    key_pair: WgKeyPair,
    
    /// Peer states
    peers: Arc<RwLock<HashMap<String, WgPeerState>>>,
    
    /// Routing table
    routing_table: Arc<RwLock<HashMap<IpAddr, String>>>, // IP -> peer public key
    
    /// Started flag
    started: std::sync::atomic::AtomicBool,
    
    /// Tag
    tag: String,
}

impl WgTunnel {
    /// Create a new WireGuard tunnel
    pub fn new(tag: String, config: WireGuardConfig) -> Result<Self, String> {
        // Parse private key
        let key_pair = WgKeyPair::from_private_key_base64(&config.interface.private_key)?;
        
        // Initialize peer states
        let mut peers = HashMap::new();
        let mut routing_table = HashMap::new();
        
        for peer_config in &config.peers {
            let peer_state = WgPeerState {
                config: peer_config.clone(),
                current_endpoint: peer_config.endpoint,
                last_handshake: None,
                bytes_sent: 0,
                bytes_received: 0,
                connected: false,
                last_keepalive: None,
            };
            
            peers.insert(peer_config.public_key.clone(), peer_state);
            
            // Parse allowed IPs and add to routing table
            for allowed_ip in &peer_config.allowed_ips {
                if let Ok(ip) = allowed_ip.split('/').next().unwrap_or("").parse::<IpAddr>() {
                    routing_table.insert(ip, peer_config.public_key.clone());
                }
            }
        }
        
        Ok(Self {
            config,
            key_pair,
            peers: Arc::new(RwLock::new(peers)),
            routing_table: Arc::new(RwLock::new(routing_table)),
            started: std::sync::atomic::AtomicBool::new(false),
            tag,
        })
    }
    
    /// Perform handshake with peer
    async fn handshake(&self, peer_public_key: &str) -> Result<(), String> {
        let mut peers = self.peers.write().await;
        
        if let Some(peer_state) = peers.get_mut(peer_public_key) {
            // In a real implementation, this would perform the WireGuard handshake protocol
            // For now, we'll simulate a successful handshake
            peer_state.last_handshake = Some(SystemTime::now());
            peer_state.connected = true;
            
            println!("WireGuard handshake completed with peer: {}", peer_public_key);
            Ok(())
        } else {
            Err(format!("Peer not found: {}", peer_public_key))
        }
    }
    
    /// Send keep-alive to peer
    async fn send_keepalive(&self, peer_public_key: &str) -> Result<(), String> {
        let mut peers = self.peers.write().await;
        
        if let Some(peer_state) = peers.get_mut(peer_public_key) {
            peer_state.last_keepalive = Some(Instant::now());
            
            // In a real implementation, would send actual keep-alive packet
            println!("Sent keep-alive to peer: {}", peer_public_key);
            Ok(())
        } else {
            Err(format!("Peer not found: {}", peer_public_key))
        }
    }
    
    /// Route packet to appropriate peer
    async fn route_packet(&self, destination: IpAddr, _packet: &[u8]) -> Result<String, String> {
        let routing_table = self.routing_table.read().await;
        
        // Find exact match first
        if let Some(peer_key) = routing_table.get(&destination) {
            return Ok(peer_key.clone());
        }
        
        // Find subnet match (simplified)
        for (allowed_ip, peer_key) in routing_table.iter() {
            // In a real implementation, would do proper CIDR matching
            if Self::ip_in_same_subnet(&destination, allowed_ip) {
                return Ok(peer_key.clone());
            }
        }
        
        Err("No route found for destination".to_string())
    }
    
    /// Check if IPs are in the same subnet (simplified)
    fn ip_in_same_subnet(ip1: &IpAddr, ip2: &IpAddr) -> bool {
        match (ip1, ip2) {
            (IpAddr::V4(a), IpAddr::V4(b)) => {
                // Simplified: check if first 3 octets match
                let a_octets = a.octets();
                let b_octets = b.octets();
                a_octets[0] == b_octets[0] && a_octets[1] == b_octets[1] && a_octets[2] == b_octets[2]
            },
            (IpAddr::V6(a), IpAddr::V6(b)) => {
                // Simplified: check if first 64 bits match
                let a_segments = a.segments();
                let b_segments = b.segments();
                a_segments[0..4] == b_segments[0..4]
            },
            _ => false,
        }
    }
    
    /// Encrypt packet for peer
    fn encrypt_packet(&self, _peer_public_key: &str, packet: &[u8]) -> Result<Vec<u8>, String> {
        // In a real implementation, would use ChaCha20Poly1305 encryption
        // For now, we'll just add a simple header
        let mut encrypted = Vec::with_capacity(packet.len() + 16);
        encrypted.extend_from_slice(b"WG_ENCRYPTED_PKT"); // 16-byte header
        encrypted.extend_from_slice(packet);
        Ok(encrypted)
    }
    
    /// Decrypt packet from peer
    fn decrypt_packet(&self, _peer_public_key: &str, encrypted_packet: &[u8]) -> Result<Vec<u8>, String> {
        // In a real implementation, would use ChaCha20Poly1305 decryption
        // For now, we'll just remove the header
        if encrypted_packet.len() < 16 {
            return Err("Packet too short".to_string());
        }
        
        if &encrypted_packet[0..16] != b"WG_ENCRYPTED_PKT" {
            return Err("Invalid packet header".to_string());
        }
        
        Ok(encrypted_packet[16..].to_vec())
    }
    
    /// Get peer statistics
    pub async fn get_peer_stats(&self) -> HashMap<String, WgPeerState> {
        self.peers.read().await.clone()
    }
    
    /// Update peer endpoint
    pub async fn update_peer_endpoint(&self, peer_public_key: &str, endpoint: SocketAddr) -> Result<(), String> {
        let mut peers = self.peers.write().await;
        
        if let Some(peer_state) = peers.get_mut(peer_public_key) {
            peer_state.current_endpoint = Some(endpoint);
            Ok(())
        } else {
            Err(format!("Peer not found: {}", peer_public_key))
        }
    }
    
    /// Start keep-alive task
    async fn start_keepalive_task(&self) {
        let peers = Arc::clone(&self.peers);
        let keep_alive_interval = self.config.keep_alive.unwrap_or(Duration::from_secs(25));
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(keep_alive_interval);
            
            loop {
                interval.tick().await;
                
                let peer_keys: Vec<String> = {
                    let peers_guard = peers.read().await;
                    peers_guard.keys().cloned().collect()
                };
                
                for peer_key in peer_keys {
                    // Send keep-alive if needed
                    let should_send = {
                        let peers_guard = peers.read().await;
                        if let Some(peer_state) = peers_guard.get(&peer_key) {
                            peer_state.connected && 
                            peer_state.config.persistent_keepalive.is_some() &&
                            peer_state.last_keepalive
                                .map(|last| last.elapsed() > keep_alive_interval)
                                .unwrap_or(true)
                        } else {
                            false
                        }
                    };
                    
                    if should_send {
                        // In a real implementation, would send actual keep-alive
                        let mut peers_guard = peers.write().await;
                        if let Some(peer_state) = peers_guard.get_mut(&peer_key) {
                            peer_state.last_keepalive = Some(Instant::now());
                        }
                    }
                }
            }
        });
    }
}

impl Adapter for WgTunnel {
    fn adapter_type(&self) -> &str {
        "wireguard"
    }
    
    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Inbound for WgTunnel {
    // WireGuard can act as an inbound
}

impl Outbound for WgTunnel {
    fn network(&self) -> Vec<String> {
        vec!["tcp".to_string(), "udp".to_string()]
    }

    fn dependencies(&self) -> Vec<String> {
        Vec::new()
    }

    fn dial(&self, _network: &str, _destination: &str) -> Result<(), String> {
        Ok(())
    }
}

#[async_trait]
impl Lifecycle for WgTunnel {
    async fn start(&self, stage: StartStage) -> Result<(), String> {
        if self.started.load(std::sync::atomic::Ordering::Relaxed) {
            return Ok(());
        }
        
        match stage {
            StartStage::Initialize => {
                println!("Initializing WireGuard tunnel '{}'", self.tag);
            },
            StartStage::Start => {
                println!("Starting WireGuard tunnel '{}' on port {:?}", 
                         self.tag, self.config.interface.listen_port);
                
                // Start keep-alive task
                let rt = tokio::runtime::Handle::current();
                rt.spawn(async move {
                    // Keep-alive task would run here
                });
                
                self.started.store(true, std::sync::atomic::Ordering::Relaxed);
            },
            StartStage::PostStart => {
                println!("WireGuard tunnel '{}' post-start", self.tag);
            },
            StartStage::Started => {
                println!("WireGuard tunnel '{}' fully started", self.tag);
            },
        }
        
        Ok(())
    }
    
    async fn close(&self) -> Result<(), String> {
        if !self.started.load(std::sync::atomic::Ordering::Relaxed) {
            return Ok(());
        }

        self.started.store(false, std::sync::atomic::Ordering::Relaxed);
        println!("WireGuard tunnel '{}' stopped", self.tag);
        
        Ok(())
    }
}

impl Default for WireGuardConfig {
    fn default() -> Self {
        let key_pair = WgKeyPair::generate();
        
        Self {
            interface: WgInterface {
                private_key: key_pair.private_key_base64(),
                listen_port: Some(51820),
                addresses: vec!["********/24".to_string()],
                dns: Some(vec!["*******".parse().unwrap(), "*******".parse().unwrap()]),
                table: None,
                fwmark: None,
                stats: Some(false),
                scripts: None,
            },
            peers: Vec::new(),
            mtu: Some(1420),
            keep_alive: Some(Duration::from_secs(25)),
            pre_shared_key: None,
            reserved: None,
            experimental: None,
            performance: None,
            security: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_wireguard_key_generation() {
        let key_pair = WgKeyPair::generate();
        
        assert_eq!(key_pair.private_key.len(), 32);
        assert_eq!(key_pair.public_key.len(), 32);
        
        // Test base64 encoding
        let private_b64 = key_pair.private_key_base64();
        let public_b64 = key_pair.public_key_base64();
        
        assert!(!private_b64.is_empty());
        assert!(!public_b64.is_empty());
        
        // Test decoding
        let decoded_key_pair = WgKeyPair::from_private_key_base64(&private_b64).unwrap();
        assert_eq!(decoded_key_pair.private_key, key_pair.private_key);
        assert_eq!(decoded_key_pair.public_key, key_pair.public_key);
    }
    
    #[test]
    fn test_wireguard_config_default() {
        let config = WireGuardConfig::default();
        
        assert_eq!(config.interface.listen_port, Some(51820));
        assert_eq!(config.interface.addresses.len(), 1);
        assert_eq!(config.mtu, Some(1420));
        assert!(config.keep_alive.is_some());
    }
    
    #[tokio::test]
    async fn test_wireguard_tunnel_creation() {
        let config = WireGuardConfig::default();
        let tunnel = WgTunnel::new("test-wg".to_string(), config);
        
        assert!(tunnel.is_ok());
        
        let tunnel = tunnel.unwrap();
        assert_eq!(tunnel.adapter_type(), "wireguard");
        assert_eq!(tunnel.tag(), "test-wg");
    }
    
    #[test]
    fn test_ip_subnet_matching() {
        let ip1: IpAddr = "***********".parse().unwrap();
        let ip2: IpAddr = "***********".parse().unwrap();
        let ip3: IpAddr = "***********".parse().unwrap();
        
        assert!(WgTunnel::ip_in_same_subnet(&ip1, &ip2));
        assert!(!WgTunnel::ip_in_same_subnet(&ip1, &ip3));
    }
    
    #[test]
    fn test_packet_encryption_decryption() {
        let config = WireGuardConfig::default();
        let tunnel = WgTunnel::new("test".to_string(), config).unwrap();
        
        let original_packet = b"Hello, WireGuard!";
        let peer_key = "test_peer_key";
        
        let encrypted = tunnel.encrypt_packet(peer_key, original_packet).unwrap();
        assert!(encrypted.len() > original_packet.len());
        
        let decrypted = tunnel.decrypt_packet(peer_key, &encrypted).unwrap();
        assert_eq!(decrypted, original_packet);
    }
}
