//! Hysteria protocol implementation
//!
//! This module implements the Hysteria protocol, a high-performance proxy protocol
//! based on QUIC that provides excellent performance over lossy networks.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::sync::RwLock;

use async_trait::async_trait;
use crate::adapter::{Adapter, Inbound, Lifecycle, StartStage};

/// Hysteria protocol configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HysteriaConfig {
    /// Listen address
    pub listen: String,
    
    /// Listen port
    pub listen_port: u16,
    
    /// TLS configuration
    pub tls: Option<HysteriaTlsConfig>,
    
    /// Authentication configuration
    pub auth: Option<HysteriaAuthConfig>,
    
    /// QUIC configuration
    pub quic: Option<HysteriaQuicConfig>,
    
    /// Bandwidth configuration
    pub bandwidth: Option<HysteriaBandwidthConfig>,
    
    /// Obfuscation password
    pub obfs: Option<String>,
    
    /// Disable MTU discovery
    pub disable_mtu_discovery: Option<bool>,
}

/// Hysteria TLS configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HysteriaTlsConfig {
    /// Certificate file path
    pub cert: Option<String>,
    
    /// Private key file path
    pub key: Option<String>,
    
    /// Certificate chain
    pub cert_chain: Option<Vec<String>>,
    
    /// ALPN protocols
    pub alpn: Option<Vec<String>>,
}

/// Hysteria authentication configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HysteriaAuthConfig {
    /// Authentication type
    pub r#type: String,
    
    /// Password for password authentication
    pub password: Option<String>,
    
    /// User database for userpass authentication
    pub userpass: Option<HashMap<String, String>>,
    
    /// HTTP authentication URL
    pub http: Option<String>,
}

/// Hysteria QUIC configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HysteriaQuicConfig {
    /// Initial stream receive window
    pub init_stream_receive_window: Option<u64>,
    
    /// Maximum stream receive window
    pub max_stream_receive_window: Option<u64>,
    
    /// Initial connection receive window
    pub init_conn_receive_window: Option<u64>,
    
    /// Maximum connection receive window
    pub max_conn_receive_window: Option<u64>,
    
    /// Maximum idle timeout (seconds)
    pub max_idle_timeout: Option<u64>,
    
    /// Maximum incoming streams
    pub max_incoming_streams: Option<u64>,
    
    /// Disable path MTU discovery
    pub disable_path_mtu_discovery: Option<bool>,
}

/// Hysteria bandwidth configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HysteriaBandwidthConfig {
    /// Upload bandwidth (Mbps)
    pub up: Option<u64>,
    
    /// Download bandwidth (Mbps)
    pub down: Option<u64>,
}

/// Hysteria connection state
#[derive(Debug, Clone)]
pub struct HysteriaConnection {
    /// Connection ID
    pub id: String,
    
    /// Remote address
    pub remote_addr: SocketAddr,
    
    /// Authentication status
    pub authenticated: bool,
    
    /// Bandwidth limits
    pub bandwidth_up: Option<u64>,
    pub bandwidth_down: Option<u64>,
}

/// Hysteria inbound implementation
#[derive(Debug)]
pub struct HysteriaInbound {
    config: HysteriaConfig,
    tag: String,
    started: std::sync::atomic::AtomicBool,
    connections: Arc<RwLock<HashMap<String, HysteriaConnection>>>,
}

impl HysteriaInbound {
    /// Create a new Hysteria inbound
    pub fn new(tag: String, config: HysteriaConfig) -> Result<Self, String> {
        // Validate configuration
        if config.listen.is_empty() {
            return Err("Listen address cannot be empty".to_string());
        }
        
        if config.listen_port == 0 {
            return Err("Listen port cannot be zero".to_string());
        }
        
        // Validate TLS configuration if present
        if let Some(ref tls_config) = config.tls {
            if tls_config.cert.is_none() && tls_config.cert_chain.is_none() {
                return Err("TLS certificate is required".to_string());
            }
            
            if tls_config.key.is_none() {
                return Err("TLS private key is required".to_string());
            }
        }
        
        Ok(Self {
            config,
            tag,
            started: std::sync::atomic::AtomicBool::new(false),
            connections: Arc::new(RwLock::new(HashMap::new())),
        })
    }
    
    /// Get listen address
    pub fn listen_addr(&self) -> String {
        format!("{}:{}", self.config.listen, self.config.listen_port)
    }
    
    /// Authenticate connection
    async fn authenticate(&self, auth_data: &[u8]) -> Result<bool, String> {
        let auth_config = match &self.config.auth {
            Some(auth) => auth,
            None => return Ok(true), // No authentication required
        };
        
        match auth_config.r#type.as_str() {
            "password" => {
                if let Some(ref password) = auth_config.password {
                    let provided_password = std::str::from_utf8(auth_data)
                        .map_err(|_| "Invalid password format".to_string())?;
                    Ok(provided_password == password)
                } else {
                    Err("Password not configured".to_string())
                }
            },
            "userpass" => {
                if let Some(ref userpass) = auth_config.userpass {
                    let auth_str = std::str::from_utf8(auth_data)
                        .map_err(|_| "Invalid userpass format".to_string())?;
                    
                    if let Some((username, password)) = auth_str.split_once(':') {
                        Ok(userpass.get(username).map_or(false, |p| p == password))
                    } else {
                        Err("Invalid userpass format".to_string())
                    }
                } else {
                    Err("Userpass database not configured".to_string())
                }
            },
            "http" => {
                // HTTP authentication would require making HTTP requests
                // This is a simplified implementation
                println!("HTTP authentication not fully implemented");
                Ok(true)
            },
            _ => Err(format!("Unsupported authentication type: {}", auth_config.r#type)),
        }
    }
    
    /// Handle new QUIC connection
    async fn handle_connection(&self, connection_id: String, remote_addr: SocketAddr) -> Result<(), String> {
        println!("New Hysteria connection: {} from {}", connection_id, remote_addr);
        
        // Create connection state
        let conn = HysteriaConnection {
            id: connection_id.clone(),
            remote_addr,
            authenticated: false,
            bandwidth_up: self.config.bandwidth.as_ref().and_then(|b| b.up),
            bandwidth_down: self.config.bandwidth.as_ref().and_then(|b| b.down),
        };
        
        // Store connection
        self.connections.write().await.insert(connection_id.clone(), conn);
        
        // In a real implementation, this would:
        // 1. Perform QUIC handshake
        // 2. Handle authentication
        // 3. Process Hysteria protocol messages
        // 4. Forward traffic to destination
        
        println!("Hysteria connection {} established", connection_id);
        Ok(())
    }
    
    /// Get active connections count
    pub async fn connection_count(&self) -> usize {
        self.connections.read().await.len()
    }
    
    /// Close connection
    pub async fn close_connection(&self, connection_id: &str) -> Result<(), String> {
        if let Some(_conn) = self.connections.write().await.remove(connection_id) {
            println!("Closed Hysteria connection: {}", connection_id);
            Ok(())
        } else {
            Err(format!("Connection not found: {}", connection_id))
        }
    }
}

impl Adapter for HysteriaInbound {
    fn adapter_type(&self) -> &str {
        "hysteria"
    }
    
    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Inbound for HysteriaInbound {
    // Inherits from Adapter and Lifecycle traits
}

#[async_trait]
impl Lifecycle for HysteriaInbound {
    async fn start(&self, stage: StartStage) -> Result<(), String> {
        if self.started.load(std::sync::atomic::Ordering::Relaxed) {
            return Ok(());
        }
        
        match stage {
            StartStage::Initialize => {
                println!("Initializing Hysteria inbound '{}'", self.tag);
                // Initialize QUIC endpoint, load TLS certificates, etc.
            },
            StartStage::Start => {
                println!("Starting Hysteria inbound '{}' on {}", self.tag, self.listen_addr());
                // Start QUIC listener
                self.started.store(true, std::sync::atomic::Ordering::Relaxed);
            },
            StartStage::PostStart => {
                println!("Hysteria inbound '{}' post-start", self.tag);
                // Post-start initialization
            },
            StartStage::Started => {
                println!("Hysteria inbound '{}' fully started", self.tag);
            },
        }
        
        Ok(())
    }
    
    async fn close(&self) -> Result<(), String> {
        if !self.started.load(std::sync::atomic::Ordering::Relaxed) {
            return Ok(());
        }

        self.started.store(false, std::sync::atomic::Ordering::Relaxed);
        println!("Hysteria inbound '{}' stopped", self.tag);
        
        Ok(())
    }
}

impl Default for HysteriaConfig {
    fn default() -> Self {
        Self {
            listen: "0.0.0.0".to_string(),
            listen_port: 443,
            tls: None,
            auth: None,
            quic: None,
            bandwidth: None,
            obfs: None,
            disable_mtu_discovery: Some(false),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_hysteria_config_default() {
        let config = HysteriaConfig::default();
        assert_eq!(config.listen, "0.0.0.0");
        assert_eq!(config.listen_port, 443);
        assert!(config.tls.is_none());
        assert!(config.auth.is_none());
    }
    
    #[test]
    fn test_hysteria_inbound_creation() {
        let config = HysteriaConfig {
            listen: "127.0.0.1".to_string(),
            listen_port: 8443,
            ..Default::default()
        };
        
        let hysteria = HysteriaInbound::new("test-hysteria".to_string(), config);
        assert!(hysteria.is_ok());
        
        let hysteria = hysteria.unwrap();
        assert_eq!(hysteria.adapter_type(), "hysteria");
        assert_eq!(hysteria.tag(), "test-hysteria");
        assert_eq!(hysteria.listen_addr(), "127.0.0.1:8443");
    }
    
    #[test]
    fn test_hysteria_config_validation() {
        // Test empty listen address
        let config = HysteriaConfig {
            listen: "".to_string(),
            listen_port: 443,
            ..Default::default()
        };
        
        let result = HysteriaInbound::new("test".to_string(), config);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Listen address cannot be empty"));
        
        // Test zero port
        let config = HysteriaConfig {
            listen: "127.0.0.1".to_string(),
            listen_port: 0,
            ..Default::default()
        };
        
        let result = HysteriaInbound::new("test".to_string(), config);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Listen port cannot be zero"));
    }
    
    #[tokio::test]
    async fn test_hysteria_authentication() {
        let config = HysteriaConfig {
            listen: "127.0.0.1".to_string(),
            listen_port: 8443,
            auth: Some(HysteriaAuthConfig {
                r#type: "password".to_string(),
                password: Some("test123".to_string()),
                userpass: None,
                http: None,
            }),
            ..Default::default()
        };
        
        let hysteria = HysteriaInbound::new("test".to_string(), config).unwrap();
        
        // Test correct password
        let result = hysteria.authenticate(b"test123").await;
        assert!(result.is_ok());
        assert!(result.unwrap());
        
        // Test incorrect password
        let result = hysteria.authenticate(b"wrong").await;
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }
}
