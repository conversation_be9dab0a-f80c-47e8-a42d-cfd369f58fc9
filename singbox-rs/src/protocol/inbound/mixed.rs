//! Mixed protocol implementation (HTTP + SOCKS)
//!
//! This module implements a mixed inbound that can handle both HTTP and SOCKS protocols
//! on the same port, automatically detecting the protocol type.

use std::sync::atomic::{AtomicBool, Ordering};
use serde::{Deserialize, Serialize};
use async_trait::async_trait;
use crate::adapter::{Adapter, Inbound, Lifecycle, StartStage};

/// Mixed protocol configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MixedConfig {
    /// Listen address
    pub listen: String,
    
    /// Listen port
    pub listen_port: u16,
    
    /// Users for authentication
    pub users: Option<Vec<MixedUser>>,
    
    /// Set system proxy
    pub set_system_proxy: Option<bool>,
}

/// Mixed protocol user
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MixedUser {
    /// Username
    pub username: String,
    
    /// Password
    pub password: String,
}

/// Protocol detection result
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub enum DetectedProtocol {
    Http,
    Socks4,
    Socks5,
}

/// Mixed inbound implementation
pub struct MixedInbound {
    config: MixedConfig,
    tag: String,
    started: AtomicBool,
}

impl MixedInbound {
    /// Create a new Mixed inbound
    pub fn new(tag: String, config: MixedConfig) -> Result<Self, String> {
        Ok(Self {
            config,
            tag,
            started: AtomicBool::new(false),
        })
    }
    
    /// Detect protocol from the first few bytes (simplified implementation)
    fn detect_protocol(&self, buffer: &[u8]) -> DetectedProtocol {
        if buffer.is_empty() {
            return DetectedProtocol::Http;
        }

        // HTTP detection
        if self.is_http_request(buffer) {
            return DetectedProtocol::Http;
        }

        // SOCKS5 detection
        if buffer.len() >= 2 && buffer[0] == 0x05 {
            return DetectedProtocol::Socks5;
        }

        // SOCKS4 detection
        if buffer.len() >= 2 && buffer[0] == 0x04 {
            return DetectedProtocol::Socks4;
        }

        // Default to HTTP if uncertain
        DetectedProtocol::Http
    }
    
    /// Check if the buffer contains an HTTP request
    fn is_http_request(&self, buffer: &[u8]) -> bool {
        let text = match std::str::from_utf8(buffer) {
            Ok(text) => text,
            Err(_) => return false,
        };
        
        // Check for HTTP methods
        text.starts_with("GET ") ||
        text.starts_with("POST ") ||
        text.starts_with("PUT ") ||
        text.starts_with("DELETE ") ||
        text.starts_with("HEAD ") ||
        text.starts_with("OPTIONS ") ||
        text.starts_with("PATCH ") ||
        text.starts_with("CONNECT ") ||
        text.starts_with("TRACE ")
    }
    
    /// Handle connection with detected protocol (simplified implementation)
    fn handle_with_protocol(
        &self,
        protocol: DetectedProtocol,
    ) -> Result<(), String> {
        match protocol {
            DetectedProtocol::Http => {
                println!("Handling HTTP connection on mixed inbound '{}'", self.tag);
                // In a real implementation, this would delegate to HTTP handler
                Ok(())
            },
            DetectedProtocol::Socks4 | DetectedProtocol::Socks5 => {
                println!("Handling SOCKS connection on mixed inbound '{}'", self.tag);
                // In a real implementation, this would delegate to SOCKS handler
                Ok(())
            },
        }
    }
}

impl Adapter for MixedInbound {
    fn adapter_type(&self) -> &str {
        "mixed"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Inbound for MixedInbound {
    // Inherits from Adapter and Lifecycle traits
}

#[async_trait]
impl Lifecycle for MixedInbound {
    async fn start(&self, stage: StartStage) -> Result<(), String> {
        if self.started.load(Ordering::Relaxed) {
            return Ok(());
        }

        self.started.store(true, Ordering::Relaxed);
        println!("Mixed inbound '{}' started on {}:{} (stage: {})",
                 self.tag, self.config.listen, self.config.listen_port, stage);

        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        if !self.started.load(Ordering::Relaxed) {
            return Ok(());
        }

        self.started.store(false, Ordering::Relaxed);
        println!("Mixed inbound '{}' stopped", self.tag);

        Ok(())
    }
}

impl Default for MixedConfig {
    fn default() -> Self {
        Self {
            listen: "127.0.0.1".to_string(),
            listen_port: 8080,
            users: None,
            set_system_proxy: Some(false),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_http_detection() {
        let mixed = MixedInbound::new("test".to_string(), MixedConfig::default()).unwrap();
        
        assert!(mixed.is_http_request(b"GET / HTTP/1.1\r\n"));
        assert!(mixed.is_http_request(b"POST /api HTTP/1.1\r\n"));
        assert!(mixed.is_http_request(b"CONNECT example.com:443 HTTP/1.1\r\n"));
        
        assert!(!mixed.is_http_request(&[0x05, 0x01, 0x00])); // SOCKS5
        assert!(!mixed.is_http_request(&[0x04, 0x01])); // SOCKS4
    }
    
    #[tokio::test]
    async fn test_mixed_inbound_creation() {
        let config = MixedConfig {
            listen: "127.0.0.1".to_string(),
            listen_port: 8080,
            users: Some(vec![MixedUser {
                username: "test".to_string(),
                password: "pass".to_string(),
            }]),
            set_system_proxy: Some(false),
        };
        
        let mixed = MixedInbound::new("test-mixed".to_string(), config);
        assert!(mixed.is_ok());
        
        let mixed = mixed.unwrap();
        assert_eq!(mixed.adapter_type(), "mixed");
        assert_eq!(mixed.tag(), "test-mixed");
    }
}
