//! Hysteria2 protocol implementation
//!
//! This module implements Hysteria2, the second version of the Hysteria protocol
//! with improved performance, stability, and features over the original version.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::sync::RwLock;

use async_trait::async_trait;
use crate::adapter::{Adapter, Inbound, Lifecycle, StartStage};

/// Hysteria2 protocol configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Hysteria2Config {
    /// Listen address
    pub listen: String,
    
    /// Listen port
    pub listen_port: u16,
    
    /// TLS configuration
    pub tls: Option<Hysteria2TlsConfig>,
    
    /// Authentication configuration
    pub auth: Option<Hysteria2AuthConfig>,
    
    /// Masquerade configuration
    pub masquerade: Option<Hysteria2MasqueradeConfig>,
    
    /// Bandwidth configuration
    pub bandwidth: Option<Hysteria2BandwidthConfig>,
    
    /// Ignore client bandwidth
    pub ignore_client_bandwidth: Option<bool>,
    
    /// QUIC configuration
    pub quic: Option<Hysteria2QuicConfig>,
    
    /// Obfuscation configuration
    pub obfs: Option<Hysteria2ObfsConfig>,
}

/// Hysteria2 TLS configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Hysteria2TlsConfig {
    /// Certificate file path
    pub cert: Option<String>,
    
    /// Private key file path
    pub key: Option<String>,
    
    /// Certificate chain
    pub cert_chain: Option<Vec<String>>,
    
    /// ALPN protocols
    pub alpn: Option<Vec<String>>,
    
    /// Server name
    pub server_name: Option<String>,
    
    /// Insecure skip verify
    pub insecure: Option<bool>,
}

/// Hysteria2 authentication configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Hysteria2AuthConfig {
    /// Authentication type
    pub r#type: String,
    
    /// Password for password authentication
    pub password: Option<String>,
    
    /// User database for userpass authentication
    pub userpass: Option<HashMap<String, String>>,
    
    /// HTTP authentication URL
    pub http: Option<String>,
    
    /// Command for external authentication
    pub command: Option<String>,
}

/// Hysteria2 masquerade configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Hysteria2MasqueradeConfig {
    /// Masquerade type (http, file)
    pub r#type: String,
    
    /// HTTP masquerade configuration
    pub http: Option<Hysteria2HttpMasqueradeConfig>,
    
    /// File masquerade configuration
    pub file: Option<Hysteria2FileMasqueradeConfig>,
}

/// Hysteria2 HTTP masquerade configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Hysteria2HttpMasqueradeConfig {
    /// URL to masquerade as
    pub url: String,
    
    /// Force HTTPS
    pub force_https: Option<bool>,
}

/// Hysteria2 file masquerade configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Hysteria2FileMasqueradeConfig {
    /// Directory to serve files from
    pub dir: String,
}

/// Hysteria2 bandwidth configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Hysteria2BandwidthConfig {
    /// Upload bandwidth (Mbps)
    pub up: Option<u64>,
    
    /// Download bandwidth (Mbps)
    pub down: Option<u64>,
}

/// Hysteria2 QUIC configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Hysteria2QuicConfig {
    /// Initial stream receive window
    pub init_stream_receive_window: Option<u64>,
    
    /// Maximum stream receive window
    pub max_stream_receive_window: Option<u64>,
    
    /// Initial connection receive window
    pub init_conn_receive_window: Option<u64>,
    
    /// Maximum connection receive window
    pub max_conn_receive_window: Option<u64>,
    
    /// Maximum idle timeout (seconds)
    pub max_idle_timeout: Option<u64>,
    
    /// Maximum incoming streams
    pub max_incoming_streams: Option<u64>,
    
    /// Disable path MTU discovery
    pub disable_path_mtu_discovery: Option<bool>,
}

/// Hysteria2 obfuscation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Hysteria2ObfsConfig {
    /// Obfuscation type
    pub r#type: String,
    
    /// Salamander obfuscation password
    pub salamander: Option<Hysteria2SalamanderConfig>,
}

/// Hysteria2 Salamander obfuscation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Hysteria2SalamanderConfig {
    /// Obfuscation password
    pub password: String,
}

/// Hysteria2 connection state
#[derive(Debug, Clone)]
pub struct Hysteria2Connection {
    /// Connection ID
    pub id: String,
    
    /// Remote address
    pub remote_addr: SocketAddr,
    
    /// Authentication status
    pub authenticated: bool,
    
    /// Client bandwidth limits
    pub client_bandwidth_up: Option<u64>,
    pub client_bandwidth_down: Option<u64>,
    
    /// Server bandwidth limits
    pub server_bandwidth_up: Option<u64>,
    pub server_bandwidth_down: Option<u64>,
    
    /// Connection start time
    pub start_time: std::time::Instant,
}

/// Hysteria2 inbound implementation
#[derive(Debug)]
pub struct Hysteria2Inbound {
    config: Hysteria2Config,
    tag: String,
    started: std::sync::atomic::AtomicBool,
    connections: Arc<RwLock<HashMap<String, Hysteria2Connection>>>,
}

impl Hysteria2Inbound {
    /// Create a new Hysteria2 inbound
    pub fn new(tag: String, config: Hysteria2Config) -> Result<Self, String> {
        // Validate configuration
        if config.listen.is_empty() {
            return Err("Listen address cannot be empty".to_string());
        }
        
        if config.listen_port == 0 {
            return Err("Listen port cannot be zero".to_string());
        }
        
        // Validate TLS configuration if present
        if let Some(ref tls_config) = config.tls {
            if tls_config.cert.is_none() && tls_config.cert_chain.is_none() {
                return Err("TLS certificate is required".to_string());
            }
            
            if tls_config.key.is_none() {
                return Err("TLS private key is required".to_string());
            }
        }
        
        // Validate masquerade configuration if present
        if let Some(ref masq_config) = config.masquerade {
            match masq_config.r#type.as_str() {
                "http" => {
                    if masq_config.http.is_none() {
                        return Err("HTTP masquerade configuration is required".to_string());
                    }
                },
                "file" => {
                    if masq_config.file.is_none() {
                        return Err("File masquerade configuration is required".to_string());
                    }
                },
                _ => {
                    return Err(format!("Unsupported masquerade type: {}", masq_config.r#type));
                }
            }
        }
        
        Ok(Self {
            config,
            tag,
            started: std::sync::atomic::AtomicBool::new(false),
            connections: Arc::new(RwLock::new(HashMap::new())),
        })
    }
    
    /// Get listen address
    pub fn listen_addr(&self) -> String {
        format!("{}:{}", self.config.listen, self.config.listen_port)
    }
    
    /// Authenticate connection
    async fn authenticate(&self, auth_data: &[u8]) -> Result<bool, String> {
        let auth_config = match &self.config.auth {
            Some(auth) => auth,
            None => return Ok(true), // No authentication required
        };
        
        match auth_config.r#type.as_str() {
            "password" => {
                if let Some(ref password) = auth_config.password {
                    let provided_password = std::str::from_utf8(auth_data)
                        .map_err(|_| "Invalid password format".to_string())?;
                    Ok(provided_password == password)
                } else {
                    Err("Password not configured".to_string())
                }
            },
            "userpass" => {
                if let Some(ref userpass) = auth_config.userpass {
                    let auth_str = std::str::from_utf8(auth_data)
                        .map_err(|_| "Invalid userpass format".to_string())?;
                    
                    if let Some((username, password)) = auth_str.split_once(':') {
                        Ok(userpass.get(username).map_or(false, |p| p == password))
                    } else {
                        Err("Invalid userpass format".to_string())
                    }
                } else {
                    Err("Userpass database not configured".to_string())
                }
            },
            "http" => {
                // HTTP authentication would require making HTTP requests
                println!("HTTP authentication not fully implemented");
                Ok(true)
            },
            "command" => {
                // Command authentication would require executing external commands
                println!("Command authentication not fully implemented");
                Ok(true)
            },
            _ => Err(format!("Unsupported authentication type: {}", auth_config.r#type)),
        }
    }
    
    /// Handle masquerade request
    async fn handle_masquerade(&self, _request: &[u8]) -> Result<Vec<u8>, String> {
        let masq_config = match &self.config.masquerade {
            Some(masq) => masq,
            None => return Err("Masquerade not configured".to_string()),
        };
        
        match masq_config.r#type.as_str() {
            "http" => {
                if let Some(ref http_config) = masq_config.http {
                    // In a real implementation, this would fetch content from the URL
                    let response = format!(
                        "HTTP/1.1 200 OK\r\nContent-Type: text/html\r\n\r\n<html><body>Masquerading as {}</body></html>",
                        http_config.url
                    );
                    Ok(response.into_bytes())
                } else {
                    Err("HTTP masquerade configuration missing".to_string())
                }
            },
            "file" => {
                if let Some(ref file_config) = masq_config.file {
                    // In a real implementation, this would serve files from the directory
                    let response = format!(
                        "HTTP/1.1 200 OK\r\nContent-Type: text/html\r\n\r\n<html><body>File server: {}</body></html>",
                        file_config.dir
                    );
                    Ok(response.into_bytes())
                } else {
                    Err("File masquerade configuration missing".to_string())
                }
            },
            _ => Err(format!("Unsupported masquerade type: {}", masq_config.r#type)),
        }
    }
    
    /// Handle new QUIC connection
    async fn handle_connection(&self, connection_id: String, remote_addr: SocketAddr) -> Result<(), String> {
        println!("New Hysteria2 connection: {} from {}", connection_id, remote_addr);
        
        // Create connection state
        let conn = Hysteria2Connection {
            id: connection_id.clone(),
            remote_addr,
            authenticated: false,
            client_bandwidth_up: None,
            client_bandwidth_down: None,
            server_bandwidth_up: self.config.bandwidth.as_ref().and_then(|b| b.up),
            server_bandwidth_down: self.config.bandwidth.as_ref().and_then(|b| b.down),
            start_time: std::time::Instant::now(),
        };
        
        // Store connection
        self.connections.write().await.insert(connection_id.clone(), conn);
        
        // In a real implementation, this would:
        // 1. Perform QUIC handshake with Hysteria2 protocol
        // 2. Handle authentication
        // 3. Process masquerade requests if needed
        // 4. Handle bandwidth negotiation
        // 5. Forward traffic to destination
        
        println!("Hysteria2 connection {} established", connection_id);
        Ok(())
    }
    
    /// Get active connections count
    pub async fn connection_count(&self) -> usize {
        self.connections.read().await.len()
    }
    
    /// Close connection
    pub async fn close_connection(&self, connection_id: &str) -> Result<(), String> {
        if let Some(_conn) = self.connections.write().await.remove(connection_id) {
            println!("Closed Hysteria2 connection: {}", connection_id);
            Ok(())
        } else {
            Err(format!("Connection not found: {}", connection_id))
        }
    }
}

impl Adapter for Hysteria2Inbound {
    fn adapter_type(&self) -> &str {
        "hysteria2"
    }
    
    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Inbound for Hysteria2Inbound {
    // Inherits from Adapter and Lifecycle traits
}

#[async_trait]
impl Lifecycle for Hysteria2Inbound {
    async fn start(&self, stage: StartStage) -> Result<(), String> {
        if self.started.load(std::sync::atomic::Ordering::Relaxed) {
            return Ok(());
        }
        
        match stage {
            StartStage::Initialize => {
                println!("Initializing Hysteria2 inbound '{}'", self.tag);
                // Initialize QUIC endpoint, load TLS certificates, setup masquerade, etc.
            },
            StartStage::Start => {
                println!("Starting Hysteria2 inbound '{}' on {}", self.tag, self.listen_addr());
                // Start QUIC listener
                self.started.store(true, std::sync::atomic::Ordering::Relaxed);
            },
            StartStage::PostStart => {
                println!("Hysteria2 inbound '{}' post-start", self.tag);
                // Post-start initialization
            },
            StartStage::Started => {
                println!("Hysteria2 inbound '{}' fully started", self.tag);
            },
        }
        
        Ok(())
    }
    
    async fn close(&self) -> Result<(), String> {
        if !self.started.load(std::sync::atomic::Ordering::Relaxed) {
            return Ok(());
        }

        self.started.store(false, std::sync::atomic::Ordering::Relaxed);
        println!("Hysteria2 inbound '{}' stopped", self.tag);
        
        Ok(())
    }
}

impl Default for Hysteria2Config {
    fn default() -> Self {
        Self {
            listen: "0.0.0.0".to_string(),
            listen_port: 443,
            tls: None,
            auth: None,
            masquerade: None,
            bandwidth: None,
            ignore_client_bandwidth: Some(false),
            quic: None,
            obfs: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hysteria2_config_default() {
        let config = Hysteria2Config::default();
        assert_eq!(config.listen, "0.0.0.0");
        assert_eq!(config.listen_port, 443);
        assert!(config.tls.is_none());
        assert!(config.auth.is_none());
        assert!(config.masquerade.is_none());
    }

    #[test]
    fn test_hysteria2_inbound_creation() {
        let config = Hysteria2Config {
            listen: "127.0.0.1".to_string(),
            listen_port: 8443,
            ..Default::default()
        };

        let hysteria2 = Hysteria2Inbound::new("test-hysteria2".to_string(), config);
        assert!(hysteria2.is_ok());

        let hysteria2 = hysteria2.unwrap();
        assert_eq!(hysteria2.adapter_type(), "hysteria2");
        assert_eq!(hysteria2.tag(), "test-hysteria2");
        assert_eq!(hysteria2.listen_addr(), "127.0.0.1:8443");
    }

    #[test]
    fn test_hysteria2_config_validation() {
        // Test empty listen address
        let config = Hysteria2Config {
            listen: "".to_string(),
            listen_port: 443,
            ..Default::default()
        };

        let result = Hysteria2Inbound::new("test".to_string(), config);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Listen address cannot be empty"));

        // Test zero port
        let config = Hysteria2Config {
            listen: "127.0.0.1".to_string(),
            listen_port: 0,
            ..Default::default()
        };

        let result = Hysteria2Inbound::new("test".to_string(), config);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Listen port cannot be zero"));
    }

    #[test]
    fn test_hysteria2_masquerade_validation() {
        // Test HTTP masquerade without config
        let config = Hysteria2Config {
            listen: "127.0.0.1".to_string(),
            listen_port: 8443,
            masquerade: Some(Hysteria2MasqueradeConfig {
                r#type: "http".to_string(),
                http: None,
                file: None,
            }),
            ..Default::default()
        };

        let result = Hysteria2Inbound::new("test".to_string(), config);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("HTTP masquerade configuration is required"));

        // Test file masquerade without config
        let config = Hysteria2Config {
            listen: "127.0.0.1".to_string(),
            listen_port: 8443,
            masquerade: Some(Hysteria2MasqueradeConfig {
                r#type: "file".to_string(),
                http: None,
                file: None,
            }),
            ..Default::default()
        };

        let result = Hysteria2Inbound::new("test".to_string(), config);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("File masquerade configuration is required"));
    }

    #[tokio::test]
    async fn test_hysteria2_authentication() {
        let config = Hysteria2Config {
            listen: "127.0.0.1".to_string(),
            listen_port: 8443,
            auth: Some(Hysteria2AuthConfig {
                r#type: "password".to_string(),
                password: Some("test123".to_string()),
                userpass: None,
                http: None,
                command: None,
            }),
            ..Default::default()
        };

        let hysteria2 = Hysteria2Inbound::new("test".to_string(), config).unwrap();

        // Test correct password
        let result = hysteria2.authenticate(b"test123").await;
        assert!(result.is_ok());
        assert!(result.unwrap());

        // Test incorrect password
        let result = hysteria2.authenticate(b"wrong").await;
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }

    #[tokio::test]
    async fn test_hysteria2_masquerade() {
        let config = Hysteria2Config {
            listen: "127.0.0.1".to_string(),
            listen_port: 8443,
            masquerade: Some(Hysteria2MasqueradeConfig {
                r#type: "http".to_string(),
                http: Some(Hysteria2HttpMasqueradeConfig {
                    url: "https://example.com".to_string(),
                    force_https: Some(true),
                }),
                file: None,
            }),
            ..Default::default()
        };

        let hysteria2 = Hysteria2Inbound::new("test".to_string(), config).unwrap();

        let result = hysteria2.handle_masquerade(b"GET / HTTP/1.1\r\n\r\n").await;
        assert!(result.is_ok());

        let response = String::from_utf8(result.unwrap()).unwrap();
        assert!(response.contains("200 OK"));
        assert!(response.contains("example.com"));
    }
}
