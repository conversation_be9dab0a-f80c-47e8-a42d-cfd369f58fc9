use std::collections::HashMap;
use std::net::{SocketAddr, Ipv4Addr, Ipv6Addr};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use uuid::Uuid;
use rand::RngCore;
use async_trait::async_trait;

use crate::adapter::{Adapter, Inbound, Outbound, Lifecycle, StartStage};
use crate::protocol::ProtocolError;

/// VLESS protocol version
const VLESS_VERSION: u8 = 0;

/// VLESS commands
const VLESS_CMD_TCP: u8 = 1;
const VLESS_CMD_UDP: u8 = 2;
const VLESS_CMD_MUX: u8 = 3;

/// VLESS address types
#[derive(Debug, Clone, PartialEq)]
pub enum VLessAddressType {
    IPv4 = 1,
    Domain = 2,
    IPv6 = 3,
}

/// VLESS flow control types
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum VLessFlow {
    None,
    XtlsRprvVision,
    XtlsRprvVisionUdp443,
}

impl VLessFlow {
    pub fn from_str(flow: &str) -> Result<Self, ProtocolError> {
        match flow {
            "" => Ok(Self::None),
            "xtls-rprx-vision" => Ok(Self::XtlsRprvVision),
            "xtls-rprx-vision-udp443" => Ok(Self::XtlsRprvVisionUdp443),
            _ => Err(ProtocolError::UnsupportedProtocol(format!("unsupported VLESS flow: {}", flow))),
        }
    }

    pub fn as_str(&self) -> &str {
        match self {
            Self::None => "",
            Self::XtlsRprvVision => "xtls-rprx-vision",
            Self::XtlsRprvVisionUdp443 => "xtls-rprx-vision-udp443",
        }
    }
}

/// VLESS user configuration
#[derive(Debug, Clone)]
pub struct VLessUser {
    pub name: String,
    pub uuid: Uuid,
    pub flow: VLessFlow,
}

impl VLessUser {
    pub fn new(name: &str, uuid: &str, flow: &str) -> Result<Self, ProtocolError> {
        let uuid = Uuid::parse_str(uuid)
            .map_err(|_| ProtocolError::InvalidOptions("invalid UUID format".to_string()))?;
        let flow = VLessFlow::from_str(flow)?;
        
        Ok(Self {
            name: name.to_string(),
            uuid,
            flow,
        })
    }
}

/// VLESS request header
#[derive(Debug)]
pub struct VLessRequest {
    pub version: u8,
    pub uuid: Uuid,
    pub addons_length: u8,
    pub addons: Vec<u8>,
    pub command: u8,
    pub port: u16,
    pub address_type: VLessAddressType,
    pub address: String,
}

impl VLessRequest {
    /// Parse VLESS request from bytes
    pub fn parse(data: &[u8]) -> Result<Self, ProtocolError> {
        if data.len() < 18 {  // Minimum: 1 + 16 + 1 = 18 bytes
            return Err(ProtocolError::ProtocolViolation("request too short".to_string()));
        }

        let mut offset = 0;

        // Version
        let version = data[offset];
        if version != VLESS_VERSION {
            return Err(ProtocolError::ProtocolViolation(format!("unsupported version: {}", version)));
        }
        offset += 1;

        // UUID (16 bytes)
        let mut uuid_bytes = [0u8; 16];
        uuid_bytes.copy_from_slice(&data[offset..offset + 16]);
        let uuid = Uuid::from_bytes(uuid_bytes);
        offset += 16;

        // Addons length
        let addons_length = data[offset];
        offset += 1;

        // Addons
        if data.len() < offset + addons_length as usize {
            return Err(ProtocolError::ProtocolViolation("incomplete addons".to_string()));
        }
        let addons = data[offset..offset + addons_length as usize].to_vec();
        offset += addons_length as usize;

        // Command
        if data.len() < offset + 1 {
            return Err(ProtocolError::ProtocolViolation("missing command".to_string()));
        }
        let command = data[offset];
        offset += 1;

        // Port (2 bytes, big endian)
        if data.len() < offset + 2 {
            return Err(ProtocolError::ProtocolViolation("incomplete port".to_string()));
        }
        let port = u16::from_be_bytes([data[offset], data[offset + 1]]);
        offset += 2;

        // Address type
        if data.len() < offset + 1 {
            return Err(ProtocolError::ProtocolViolation("missing address type".to_string()));
        }
        let address_type = match data[offset] {
            1 => VLessAddressType::IPv4,
            2 => VLessAddressType::Domain,
            3 => VLessAddressType::IPv6,
            _ => return Err(ProtocolError::ProtocolViolation("invalid address type".to_string())),
        };
        offset += 1;

        // Address
        let address = match address_type {
            VLessAddressType::IPv4 => {
                if data.len() < offset + 4 {
                    return Err(ProtocolError::ProtocolViolation("incomplete IPv4 address".to_string()));
                }
                let addr = Ipv4Addr::from([data[offset], data[offset + 1], data[offset + 2], data[offset + 3]]);
                addr.to_string()
            }
            VLessAddressType::Domain => {
                if data.len() < offset + 1 {
                    return Err(ProtocolError::ProtocolViolation("incomplete domain length".to_string()));
                }
                let domain_len = data[offset] as usize;
                offset += 1;
                if data.len() < offset + domain_len {
                    return Err(ProtocolError::ProtocolViolation("incomplete domain".to_string()));
                }
                String::from_utf8(data[offset..offset + domain_len].to_vec())
                    .map_err(|_| ProtocolError::ProtocolViolation("invalid domain encoding".to_string()))?
            }
            VLessAddressType::IPv6 => {
                if data.len() < offset + 16 {
                    return Err(ProtocolError::ProtocolViolation("incomplete IPv6 address".to_string()));
                }
                let mut addr_bytes = [0u8; 16];
                addr_bytes.copy_from_slice(&data[offset..offset + 16]);
                let addr = Ipv6Addr::from(addr_bytes);
                addr.to_string()
            }
        };

        Ok(VLessRequest {
            version,
            uuid,
            addons_length,
            addons,
            command,
            port,
            address_type,
            address,
        })
    }

    /// Serialize request to bytes
    pub fn serialize(&self) -> Vec<u8> {
        let mut data = Vec::new();

        // Version
        data.push(self.version);

        // UUID
        data.extend_from_slice(self.uuid.as_bytes());

        // Addons length and addons
        data.push(self.addons_length);
        data.extend_from_slice(&self.addons);

        // Command
        data.push(self.command);

        // Port
        data.extend_from_slice(&self.port.to_be_bytes());

        // Address type
        data.push(self.address_type.clone() as u8);

        // Address
        match self.address_type {
            VLessAddressType::IPv4 => {
                let addr: Ipv4Addr = self.address.parse().unwrap();
                data.extend_from_slice(&addr.octets());
            }
            VLessAddressType::Domain => {
                data.push(self.address.len() as u8);
                data.extend_from_slice(self.address.as_bytes());
            }
            VLessAddressType::IPv6 => {
                let addr: Ipv6Addr = self.address.parse().unwrap();
                data.extend_from_slice(&addr.octets());
            }
        }

        data
    }
}

/// VLESS response header
#[derive(Debug)]
pub struct VLessResponse {
    pub version: u8,
    pub addons_length: u8,
    pub addons: Vec<u8>,
}

impl VLessResponse {
    pub fn new() -> Self {
        Self {
            version: VLESS_VERSION,
            addons_length: 0,
            addons: Vec::new(),
        }
    }

    pub fn serialize(&self) -> Vec<u8> {
        let mut data = Vec::new();
        data.push(self.version);
        data.push(self.addons_length);
        data.extend_from_slice(&self.addons);
        data
    }
}

/// VLESS inbound handler
pub struct VLessInbound {
    tag: String,
    listen_addr: SocketAddr,
    users: HashMap<Uuid, VLessUser>,
}

impl VLessInbound {
    pub fn new(tag: String, listen_addr: SocketAddr, users: Vec<VLessUser>) -> Self {
        let user_map = users.into_iter()
            .map(|user| (user.uuid, user))
            .collect();

        Self {
            tag,
            listen_addr,
            users: user_map,
        }
    }

    /// Handle incoming VLESS connection
    pub async fn handle_connection(&self, mut stream: TcpStream) -> Result<(), ProtocolError> {
        // Read request header
        let mut buffer = vec![0u8; 4096];
        let n = stream.read(&mut buffer).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read request: {}", e)))?;
        
        if n < 18 {
            return Err(ProtocolError::ProtocolViolation("request too short".to_string()));
        }

        buffer.truncate(n);

        // Parse request
        let request = VLessRequest::parse(&buffer)?;

        // Verify user
        let _user = self.users.get(&request.uuid)
            .ok_or(ProtocolError::AuthenticationFailed)?;

        // Send response
        let response = VLessResponse::new();
        stream.write_all(&response.serialize()).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("write response: {}", e)))?;

        // Handle command
        match request.command {
            VLESS_CMD_TCP => {
                self.handle_tcp(stream, &request).await
            }
            VLESS_CMD_UDP => {
                self.handle_udp(stream, &request).await
            }
            VLESS_CMD_MUX => {
                self.handle_mux(stream, &request).await
            }
            _ => Err(ProtocolError::ProtocolViolation(format!("unsupported command: {}", request.command))),
        }
    }

    async fn handle_tcp(&self, mut client_stream: TcpStream, request: &VLessRequest) -> Result<(), ProtocolError> {
        // Connect to target
        let target_addr = format!("{}:{}", request.address, request.port);
        let mut target_stream = TcpStream::connect(&target_addr).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to {}: {}", target_addr, e)))?;

        // Start bidirectional forwarding
        let (mut client_read, mut client_write) = client_stream.split();
        let (mut target_read, mut target_write) = target_stream.split();

        let forward1 = tokio::io::copy(&mut client_read, &mut target_write);
        let forward2 = tokio::io::copy(&mut target_read, &mut client_write);

        // Wait for either direction to close
        tokio::select! {
            result1 = forward1 => {
                if let Err(e) = result1 {
                    eprintln!("Forward client->target error: {}", e);
                }
            }
            result2 = forward2 => {
                if let Err(e) = result2 {
                    eprintln!("Forward target->client error: {}", e);
                }
            }
        }

        Ok(())
    }

    async fn handle_udp(&self, _stream: TcpStream, _request: &VLessRequest) -> Result<(), ProtocolError> {
        // UDP handling is more complex and requires packet forwarding
        Err(ProtocolError::UnsupportedProtocol("UDP not implemented".to_string()))
    }

    async fn handle_mux(&self, _stream: TcpStream, _request: &VLessRequest) -> Result<(), ProtocolError> {
        // Multiplex handling requires additional protocol implementation
        Err(ProtocolError::UnsupportedProtocol("MUX not implemented".to_string()))
    }
}

impl Adapter for VLessInbound {
    fn adapter_type(&self) -> &str {
        "vless"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

#[async_trait]
impl Lifecycle for VLessInbound {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        println!("VLESS inbound {} starting", self.tag);
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        println!("VLESS inbound {} closing", self.tag);
        Ok(())
    }
}

impl Inbound for VLessInbound {
    // Inherits from Adapter and Lifecycle
}

/// VLESS outbound handler
pub struct VLessOutbound {
    tag: String,
    server_addr: SocketAddr,
    uuid: Uuid,
    flow: VLessFlow,
}

impl VLessOutbound {
    pub fn new(tag: String, server_addr: SocketAddr, uuid: &str, flow: &str) -> Result<Self, ProtocolError> {
        let uuid = Uuid::parse_str(uuid)
            .map_err(|_| ProtocolError::InvalidOptions("invalid UUID format".to_string()))?;
        let flow = VLessFlow::from_str(flow)?;

        Ok(Self {
            tag,
            server_addr,
            uuid,
            flow,
        })
    }

    /// Dial through VLESS proxy
    pub async fn dial(&self, target_host: &str, target_port: u16, network: &str) -> Result<TcpStream, ProtocolError> {
        // Connect to VLESS server
        let mut stream = TcpStream::connect(&self.server_addr).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to server: {}", e)))?;

        // Determine command based on network
        let command = match network {
            "tcp" => VLESS_CMD_TCP,
            "udp" => VLESS_CMD_UDP,
            _ => return Err(ProtocolError::UnsupportedProtocol(format!("unsupported network: {}", network))),
        };

        // Determine address type
        let (address_type, address) = if let Ok(_) = target_host.parse::<Ipv4Addr>() {
            (VLessAddressType::IPv4, target_host.to_string())
        } else if let Ok(_) = target_host.parse::<Ipv6Addr>() {
            (VLessAddressType::IPv6, target_host.to_string())
        } else {
            (VLessAddressType::Domain, target_host.to_string())
        };

        // Create request
        let request = VLessRequest {
            version: VLESS_VERSION,
            uuid: self.uuid,
            addons_length: 0,
            addons: Vec::new(),
            command,
            port: target_port,
            address_type,
            address,
        };

        // Send request
        let request_data = request.serialize();
        stream.write_all(&request_data).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("write request: {}", e)))?;

        // Read response
        let mut response_buffer = [0u8; 2];
        stream.read_exact(&mut response_buffer).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read response: {}", e)))?;

        if response_buffer[0] != VLESS_VERSION {
            return Err(ProtocolError::ProtocolViolation("invalid response version".to_string()));
        }

        // Read addons if any
        let addons_length = response_buffer[1];
        if addons_length > 0 {
            let mut addons_buffer = vec![0u8; addons_length as usize];
            stream.read_exact(&mut addons_buffer).await
                .map_err(|e| ProtocolError::ConnectionFailed(format!("read addons: {}", e)))?;
        }

        Ok(stream)
    }
}

impl Adapter for VLessOutbound {
    fn adapter_type(&self) -> &str {
        "vless"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Outbound for VLessOutbound {
    fn network(&self) -> Vec<String> {
        vec!["tcp".to_string(), "udp".to_string()]
    }

    fn dependencies(&self) -> Vec<String> {
        vec![]
    }

    fn dial(&self, _network: &str, _destination: &str) -> Result<(), String> {
        // This is a simplified interface - in a real implementation,
        // this would return a connection or be async
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_vless_flow_from_str() {
        assert_eq!(VLessFlow::from_str("").unwrap(), VLessFlow::None);
        assert_eq!(VLessFlow::from_str("xtls-rprx-vision").unwrap(), VLessFlow::XtlsRprvVision);
        assert_eq!(VLessFlow::from_str("xtls-rprx-vision-udp443").unwrap(), VLessFlow::XtlsRprvVisionUdp443);
        assert!(VLessFlow::from_str("invalid").is_err());
    }

    #[test]
    fn test_vless_user_creation() {
        let user = VLessUser::new("test", "550e8400-e29b-41d4-a716-************", "").unwrap();
        assert_eq!(user.name, "test");
        assert_eq!(user.flow, VLessFlow::None);
        assert!(VLessUser::new("test", "invalid-uuid", "").is_err());
    }

    #[test]
    fn test_vless_request_serialization() {
        let uuid = Uuid::parse_str("550e8400-e29b-41d4-a716-************").unwrap();
        let request = VLessRequest {
            version: VLESS_VERSION,
            uuid,
            addons_length: 0,
            addons: Vec::new(),
            command: VLESS_CMD_TCP,
            port: 443,
            address_type: VLessAddressType::Domain,
            address: "example.com".to_string(),
        };

        let serialized = request.serialize();
        assert!(!serialized.is_empty());
        assert_eq!(serialized[0], VLESS_VERSION);
    }

    #[test]
    fn test_vless_response_serialization() {
        let response = VLessResponse::new();
        let serialized = response.serialize();
        assert_eq!(serialized.len(), 2);
        assert_eq!(serialized[0], VLESS_VERSION);
        assert_eq!(serialized[1], 0);
    }
}
