//! Protocol module for sing-box
//!
//! This module provides protocol implementations including HTTP, SOCKS,
//! direct connections, and other network protocols.

use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::{Arc, Mutex};
use async_trait::async_trait;
use crate::adapter::{Inbound, Outbound, Lifecycle, StartStage};
use crate::common::interrupt::Context;

pub mod http;
pub mod socks;
pub mod shadowsocks;
pub mod vmess;
pub mod trojan;
pub mod vless;
pub mod wireguard;
pub mod tuic;

// Inbound-only protocols
pub mod inbound {
    pub mod mixed;
    pub mod hysteria;
    pub mod hysteria2;
}

/// Protocol errors
#[derive(Debug, Clone)]
pub enum ProtocolError {
    UnsupportedProtocol(String),
    InvalidOptions(String),
    ConnectionFailed(String),
    AuthenticationFailed,
    ProtocolViolation(String),
    EncryptionFailed,
    DecryptionFailed,
}

impl std::fmt::Display for ProtocolError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ProtocolError::UnsupportedProtocol(proto) => write!(f, "unsupported protocol: {}", proto),
            ProtocolError::InvalidOptions(msg) => write!(f, "invalid options: {}", msg),
            ProtocolError::ConnectionFailed(msg) => write!(f, "connection failed: {}", msg),
            ProtocolError::AuthenticationFailed => write!(f, "authentication failed"),
            ProtocolError::ProtocolViolation(msg) => write!(f, "protocol violation: {}", msg),
            ProtocolError::EncryptionFailed => write!(f, "Encryption failed"),
            ProtocolError::DecryptionFailed => write!(f, "Decryption failed"),
        }
    }
}

impl std::error::Error for ProtocolError {}

/// Constructor function type for inbound protocols
pub type InboundConstructor = fn(&Context, &str, &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError>;

/// Constructor function type for outbound protocols
pub type OutboundConstructor = fn(&Context, &str, &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError>;

/// Registry for inbound protocols
#[derive(Debug)]
pub struct InboundRegistry {
    constructors: Arc<Mutex<HashMap<String, InboundConstructor>>>,
}

impl InboundRegistry {
    pub fn new() -> Self {
        Self {
            constructors: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub fn register(&self, protocol_type: &str, constructor: InboundConstructor) {
        let mut constructors = self.constructors.lock().unwrap();
        constructors.insert(protocol_type.to_string(), constructor);
    }

    pub fn create(&self, ctx: &Context, protocol_type: &str, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
        let constructors = self.constructors.lock().unwrap();
        if let Some(constructor) = constructors.get(protocol_type) {
            constructor(ctx, tag, options)
        } else {
            Err(ProtocolError::UnsupportedProtocol(protocol_type.to_string()))
        }
    }

    pub fn list_protocols(&self) -> Vec<String> {
        let constructors = self.constructors.lock().unwrap();
        constructors.keys().cloned().collect()
    }
}

impl Default for InboundRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// Registry for outbound protocols
#[derive(Debug)]
pub struct OutboundRegistry {
    constructors: Arc<Mutex<HashMap<String, OutboundConstructor>>>,
}

impl OutboundRegistry {
    pub fn new() -> Self {
        Self {
            constructors: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub fn register(&self, protocol_type: &str, constructor: OutboundConstructor) {
        let mut constructors = self.constructors.lock().unwrap();
        constructors.insert(protocol_type.to_string(), constructor);
    }

    pub fn create(&self, ctx: &Context, protocol_type: &str, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError> {
        let constructors = self.constructors.lock().unwrap();
        if let Some(constructor) = constructors.get(protocol_type) {
            constructor(ctx, tag, options)
        } else {
            Err(ProtocolError::UnsupportedProtocol(protocol_type.to_string()))
        }
    }

    pub fn list_protocols(&self) -> Vec<String> {
        let constructors = self.constructors.lock().unwrap();
        constructors.keys().cloned().collect()
    }
}

impl Default for OutboundRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// Direct protocol implementation (simplest case)
pub struct DirectInbound {
    tag: String,
    started: std::sync::atomic::AtomicBool,
}

impl DirectInbound {
    pub fn new(tag: &str) -> Self {
        Self {
            tag: tag.to_string(),
            started: std::sync::atomic::AtomicBool::new(false),
        }
    }
}

impl crate::adapter::Adapter for DirectInbound {
    fn adapter_type(&self) -> &str {
        "direct"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

#[async_trait]
impl Lifecycle for DirectInbound {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        self.started.store(true, std::sync::atomic::Ordering::Relaxed);
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        self.started.store(false, std::sync::atomic::Ordering::Relaxed);
        Ok(())
    }
}

impl Inbound for DirectInbound {}

/// Direct outbound implementation
pub struct DirectOutbound {
    tag: String,
    networks: Vec<String>,
}

impl DirectOutbound {
    pub fn new(tag: &str) -> Self {
        Self {
            tag: tag.to_string(),
            networks: vec!["tcp".to_string(), "udp".to_string()],
        }
    }
}

impl crate::adapter::Adapter for DirectOutbound {
    fn adapter_type(&self) -> &str {
        "direct"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Outbound for DirectOutbound {
    fn network(&self) -> Vec<String> {
        self.networks.clone()
    }

    fn dependencies(&self) -> Vec<String> {
        Vec::new()
    }

    fn dial(&self, _network: &str, _destination: &str) -> Result<(), String> {
        // Direct connection implementation would go here
        Ok(())
    }
}

/// Block outbound implementation (rejects all connections)
pub struct BlockOutbound {
    tag: String,
}

impl BlockOutbound {
    pub fn new(tag: &str) -> Self {
        Self {
            tag: tag.to_string(),
        }
    }
}

impl crate::adapter::Adapter for BlockOutbound {
    fn adapter_type(&self) -> &str {
        "block"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Outbound for BlockOutbound {
    fn network(&self) -> Vec<String> {
        vec!["tcp".to_string(), "udp".to_string()]
    }

    fn dependencies(&self) -> Vec<String> {
        Vec::new()
    }

    fn dial(&self, _network: &str, destination: &str) -> Result<(), String> {
        Err(format!("blocked connection to {}", destination))
    }
}

/// Constructor functions for built-in protocols
pub fn create_direct_inbound(_ctx: &Context, tag: &str, _options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    Ok(Box::new(DirectInbound::new(tag)))
}

pub fn create_direct_outbound(_ctx: &Context, tag: &str, _options: &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError> {
    Ok(Box::new(DirectOutbound::new(tag)))
}

pub fn create_block_outbound(_ctx: &Context, tag: &str, _options: &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError> {
    Ok(Box::new(BlockOutbound::new(tag)))
}

// HTTP protocol constructors
pub fn create_http_inbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    let listen_addr: SocketAddr = options.get("listen")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .unwrap_or_else(|| "127.0.0.1:8080".parse().unwrap());

    let users = options.get("users")
        .and_then(|v| v.as_object())
        .map(|obj| {
            obj.iter().map(|(k, v)| (k.clone(), v.as_str().unwrap_or("").to_string())).collect()
        })
        .unwrap_or_default();

    Ok(Box::new(http::HttpInbound::new(tag.to_string(), listen_addr, users)))
}

pub fn create_http_outbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError> {
    let server_addr: SocketAddr = options.get("server")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .ok_or_else(|| ProtocolError::InvalidOptions("missing server address".to_string()))?;

    let username = options.get("username").and_then(|v| v.as_str()).unwrap_or("");
    let password = options.get("password").and_then(|v| v.as_str()).unwrap_or("");

    Ok(Box::new(http::HttpOutbound::new(tag.to_string(), server_addr, Some(username.to_string()), Some(password.to_string()))))
}

// SOCKS protocol constructors
pub fn create_socks_inbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    let listen_addr: SocketAddr = options.get("listen")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .unwrap_or_else(|| "127.0.0.1:1080".parse().unwrap());

    let users = options.get("users")
        .and_then(|v| v.as_object())
        .map(|obj| {
            obj.iter().map(|(k, v)| (k.clone(), v.as_str().unwrap_or("").to_string())).collect()
        })
        .unwrap_or_else(HashMap::new);

    Ok(Box::new(socks::SocksInbound::new(tag.to_string(), listen_addr, socks::SocksVersion::V5, users)))
}

pub fn create_socks_outbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError> {
    let server_addr: SocketAddr = options.get("server")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .ok_or_else(|| ProtocolError::InvalidOptions("missing server address".to_string()))?;

    let username = options.get("username").and_then(|v| v.as_str()).unwrap_or("");
    let password = options.get("password").and_then(|v| v.as_str()).unwrap_or("");

    Ok(Box::new(socks::SocksOutbound::new(tag.to_string(), server_addr, socks::SocksVersion::V5, Some(username.to_string()), Some(password.to_string()))))
}

// Shadowsocks protocol constructors
pub fn create_shadowsocks_inbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    let listen_addr: SocketAddr = options.get("listen")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .unwrap_or_else(|| "127.0.0.1:8388".parse().unwrap());

    let method = options.get("method").and_then(|v| v.as_str()).unwrap_or("aes-128-gcm");
    let password = options.get("password").and_then(|v| v.as_str()).unwrap_or("");

    let users = HashMap::new(); // Shadowsocks typically uses single password, not user-based auth
    Ok(Box::new(shadowsocks::ShadowsocksInbound::new(tag.to_string(), listen_addr, method, password, users)?))
}

pub fn create_shadowsocks_outbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError> {
    let server_addr: SocketAddr = options.get("server")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .ok_or_else(|| ProtocolError::InvalidOptions("missing server address".to_string()))?;

    let method = options.get("method").and_then(|v| v.as_str()).unwrap_or("aes-128-gcm");
    let password = options.get("password").and_then(|v| v.as_str()).unwrap_or("");

    Ok(Box::new(shadowsocks::ShadowsocksOutbound::new(tag.to_string(), server_addr, method, password)?))
}

// VMess protocol constructors
pub fn create_vmess_inbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    let listen_addr: SocketAddr = options.get("listen")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .unwrap_or_else(|| "127.0.0.1:10086".parse().unwrap());

    let empty_vec = vec![];
    let users_array = options.get("users").and_then(|v| v.as_array()).unwrap_or(&empty_vec);
    let mut users = Vec::new();

    for user_obj in users_array {
        if let Some(user) = user_obj.as_object() {
            let name = user.get("name").and_then(|v| v.as_str()).unwrap_or("default");
            let uuid = user.get("uuid").and_then(|v| v.as_str()).unwrap_or("");
            let alter_id = user.get("alter_id").and_then(|v| v.as_u64()).unwrap_or(0) as u16;

            users.push(vmess::VMessUser::new(name, uuid, alter_id)?);
        }
    }

    Ok(Box::new(vmess::VMessInbound::new(tag.to_string(), listen_addr, users)))
}

pub fn create_vmess_outbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError> {
    let server_addr: SocketAddr = options.get("server")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .ok_or_else(|| ProtocolError::InvalidOptions("missing server address".to_string()))?;

    let uuid = options.get("uuid").and_then(|v| v.as_str()).unwrap_or("");
    let security = options.get("security").and_then(|v| v.as_str()).unwrap_or("auto");
    let alter_id = options.get("alter_id").and_then(|v| v.as_u64()).unwrap_or(0) as u16;
    let global_padding = options.get("global_padding").and_then(|v| v.as_bool()).unwrap_or(false);
    let authenticated_length = options.get("authenticated_length").and_then(|v| v.as_bool()).unwrap_or(true);
    let packet_encoding = options.get("packet_encoding").and_then(|v| v.as_str()).unwrap_or("");

    Ok(Box::new(vmess::VMessOutbound::new(
        tag.to_string(),
        server_addr,
        uuid,
        security,
        alter_id,
        global_padding,
        authenticated_length,
        packet_encoding,
    )?))
}

// Trojan protocol constructors
pub fn create_trojan_inbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    let listen_addr: SocketAddr = options.get("listen")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .unwrap_or_else(|| "127.0.0.1:443".parse().unwrap());

    let empty_vec = vec![];
    let users_array = options.get("users").and_then(|v| v.as_array()).unwrap_or(&empty_vec);
    let mut users = Vec::new();

    for user_obj in users_array {
        if let Some(user) = user_obj.as_object() {
            let name = user.get("name").and_then(|v| v.as_str()).unwrap_or("default");
            let password = user.get("password").and_then(|v| v.as_str()).unwrap_or("");

            users.push(trojan::TrojanUser::new(name, password));
        }
    }

    Ok(Box::new(trojan::TrojanInbound::new(tag.to_string(), listen_addr, users)))
}

pub fn create_trojan_outbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError> {
    let server_addr: SocketAddr = options.get("server")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .ok_or_else(|| ProtocolError::InvalidOptions("missing server address".to_string()))?;

    let password = options.get("password").and_then(|v| v.as_str()).unwrap_or("");

    Ok(Box::new(trojan::TrojanOutbound::new(tag.to_string(), server_addr, password)))
}

// VLESS protocol constructors
pub fn create_vless_inbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    let listen_addr: SocketAddr = options.get("listen")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .unwrap_or_else(|| "127.0.0.1:443".parse().unwrap());

    let empty_vec = vec![];
    let users_array = options.get("users").and_then(|v| v.as_array()).unwrap_or(&empty_vec);
    let mut users = Vec::new();

    for user_obj in users_array {
        if let Some(user) = user_obj.as_object() {
            let name = user.get("name").and_then(|v| v.as_str()).unwrap_or("default");
            let uuid = user.get("uuid").and_then(|v| v.as_str()).unwrap_or("");
            let flow = user.get("flow").and_then(|v| v.as_str()).unwrap_or("");

            users.push(vless::VLessUser::new(name, uuid, flow)?);
        }
    }

    Ok(Box::new(vless::VLessInbound::new(tag.to_string(), listen_addr, users)))
}

pub fn create_vless_outbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError> {
    let server_addr: SocketAddr = options.get("server")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .ok_or_else(|| ProtocolError::InvalidOptions("missing server address".to_string()))?;

    let uuid = options.get("uuid").and_then(|v| v.as_str()).unwrap_or("");
    let flow = options.get("flow").and_then(|v| v.as_str()).unwrap_or("");

    Ok(Box::new(vless::VLessOutbound::new(tag.to_string(), server_addr, uuid, flow)?))
}

// Mixed protocol constructor
pub fn create_mixed_inbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    let listen = options.get("listen").and_then(|v| v.as_str()).unwrap_or("127.0.0.1").to_string();
    let listen_port = options.get("listen_port").and_then(|v| v.as_u64()).unwrap_or(8080) as u16;

    let users = options.get("users").and_then(|v| v.as_array()).map(|users_array| {
        users_array.iter().filter_map(|user_obj| {
            user_obj.as_object().map(|user| {
                let username = user.get("username").and_then(|v| v.as_str()).unwrap_or("").to_string();
                let password = user.get("password").and_then(|v| v.as_str()).unwrap_or("").to_string();
                inbound::mixed::MixedUser { username, password }
            })
        }).collect()
    });

    let set_system_proxy = options.get("set_system_proxy").and_then(|v| v.as_bool());

    let config = inbound::mixed::MixedConfig {
        listen,
        listen_port,
        users,
        set_system_proxy,
    };

    let mixed_inbound = inbound::mixed::MixedInbound::new(tag.to_string(), config)
        .map_err(|e| ProtocolError::InvalidOptions(e))?;

    Ok(Box::new(mixed_inbound))
}

// Hysteria protocol constructor
pub fn create_hysteria_inbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    let listen = options.get("listen").and_then(|v| v.as_str()).unwrap_or("0.0.0.0").to_string();
    let listen_port = options.get("listen_port").and_then(|v| v.as_u64()).unwrap_or(443) as u16;

    // Parse TLS configuration
    let tls = options.get("tls").and_then(|v| v.as_object()).map(|tls_obj| {
        inbound::hysteria::HysteriaTlsConfig {
            cert: tls_obj.get("cert").and_then(|v| v.as_str()).map(|s| s.to_string()),
            key: tls_obj.get("key").and_then(|v| v.as_str()).map(|s| s.to_string()),
            cert_chain: tls_obj.get("cert_chain").and_then(|v| v.as_array()).map(|arr| {
                arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect()
            }),
            alpn: tls_obj.get("alpn").and_then(|v| v.as_array()).map(|arr| {
                arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect()
            }),
        }
    });

    // Parse authentication configuration
    let auth = options.get("auth").and_then(|v| v.as_object()).map(|auth_obj| {
        let auth_type = auth_obj.get("type").and_then(|v| v.as_str()).unwrap_or("none").to_string();
        let password = auth_obj.get("password").and_then(|v| v.as_str()).map(|s| s.to_string());
        let userpass = auth_obj.get("userpass").and_then(|v| v.as_object()).map(|obj| {
            obj.iter().filter_map(|(k, v)| {
                v.as_str().map(|password| (k.clone(), password.to_string()))
            }).collect()
        });
        let http = auth_obj.get("http").and_then(|v| v.as_str()).map(|s| s.to_string());

        inbound::hysteria::HysteriaAuthConfig {
            r#type: auth_type,
            password,
            userpass,
            http,
        }
    });

    // Parse bandwidth configuration
    let bandwidth = options.get("bandwidth").and_then(|v| v.as_object()).map(|bw_obj| {
        inbound::hysteria::HysteriaBandwidthConfig {
            up: bw_obj.get("up").and_then(|v| v.as_u64()),
            down: bw_obj.get("down").and_then(|v| v.as_u64()),
        }
    });

    let obfs = options.get("obfs").and_then(|v| v.as_str()).map(|s| s.to_string());
    let disable_mtu_discovery = options.get("disable_mtu_discovery").and_then(|v| v.as_bool());

    let config = inbound::hysteria::HysteriaConfig {
        listen,
        listen_port,
        tls,
        auth,
        quic: None, // QUIC config parsing can be added later
        bandwidth,
        obfs,
        disable_mtu_discovery,
    };

    let hysteria_inbound = inbound::hysteria::HysteriaInbound::new(tag.to_string(), config)
        .map_err(|e| ProtocolError::InvalidOptions(e))?;

    Ok(Box::new(hysteria_inbound))
}

// Hysteria2 protocol constructor
pub fn create_hysteria2_inbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    let listen = options.get("listen").and_then(|v| v.as_str()).unwrap_or("0.0.0.0").to_string();
    let listen_port = options.get("listen_port").and_then(|v| v.as_u64()).unwrap_or(443) as u16;

    // Parse TLS configuration
    let tls = options.get("tls").and_then(|v| v.as_object()).map(|tls_obj| {
        inbound::hysteria2::Hysteria2TlsConfig {
            cert: tls_obj.get("cert").and_then(|v| v.as_str()).map(|s| s.to_string()),
            key: tls_obj.get("key").and_then(|v| v.as_str()).map(|s| s.to_string()),
            cert_chain: tls_obj.get("cert_chain").and_then(|v| v.as_array()).map(|arr| {
                arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect()
            }),
            alpn: tls_obj.get("alpn").and_then(|v| v.as_array()).map(|arr| {
                arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect()
            }),
            server_name: tls_obj.get("server_name").and_then(|v| v.as_str()).map(|s| s.to_string()),
            insecure: tls_obj.get("insecure").and_then(|v| v.as_bool()),
        }
    });

    // Parse authentication configuration
    let auth = options.get("auth").and_then(|v| v.as_object()).map(|auth_obj| {
        let auth_type = auth_obj.get("type").and_then(|v| v.as_str()).unwrap_or("none").to_string();
        let password = auth_obj.get("password").and_then(|v| v.as_str()).map(|s| s.to_string());
        let userpass = auth_obj.get("userpass").and_then(|v| v.as_object()).map(|obj| {
            obj.iter().filter_map(|(k, v)| {
                v.as_str().map(|password| (k.clone(), password.to_string()))
            }).collect()
        });
        let http = auth_obj.get("http").and_then(|v| v.as_str()).map(|s| s.to_string());
        let command = auth_obj.get("command").and_then(|v| v.as_str()).map(|s| s.to_string());

        inbound::hysteria2::Hysteria2AuthConfig {
            r#type: auth_type,
            password,
            userpass,
            http,
            command,
        }
    });

    // Parse masquerade configuration
    let masquerade = options.get("masquerade").and_then(|v| v.as_object()).map(|masq_obj| {
        let masq_type = masq_obj.get("type").and_then(|v| v.as_str()).unwrap_or("http").to_string();
        let http = masq_obj.get("http").and_then(|v| v.as_object()).map(|http_obj| {
            inbound::hysteria2::Hysteria2HttpMasqueradeConfig {
                url: http_obj.get("url").and_then(|v| v.as_str()).unwrap_or("https://example.com").to_string(),
                force_https: http_obj.get("force_https").and_then(|v| v.as_bool()),
            }
        });
        let file = masq_obj.get("file").and_then(|v| v.as_object()).map(|file_obj| {
            inbound::hysteria2::Hysteria2FileMasqueradeConfig {
                dir: file_obj.get("dir").and_then(|v| v.as_str()).unwrap_or("/var/www").to_string(),
            }
        });

        inbound::hysteria2::Hysteria2MasqueradeConfig {
            r#type: masq_type,
            http,
            file,
        }
    });

    // Parse bandwidth configuration
    let bandwidth = options.get("bandwidth").and_then(|v| v.as_object()).map(|bw_obj| {
        inbound::hysteria2::Hysteria2BandwidthConfig {
            up: bw_obj.get("up").and_then(|v| v.as_u64()),
            down: bw_obj.get("down").and_then(|v| v.as_u64()),
        }
    });

    let ignore_client_bandwidth = options.get("ignore_client_bandwidth").and_then(|v| v.as_bool());

    let config = inbound::hysteria2::Hysteria2Config {
        listen,
        listen_port,
        tls,
        auth,
        masquerade,
        bandwidth,
        ignore_client_bandwidth,
        quic: None, // QUIC config parsing can be added later
        obfs: None, // Obfs config parsing can be added later
    };

    let hysteria2_inbound = inbound::hysteria2::Hysteria2Inbound::new(tag.to_string(), config)
        .map_err(|e| ProtocolError::InvalidOptions(e))?;

    Ok(Box::new(hysteria2_inbound))
}

// WireGuard protocol constructor
pub fn create_wireguard_inbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    let private_key = options.get("private_key")
        .and_then(|v| v.as_str())
        .ok_or_else(|| ProtocolError::InvalidOptions("WireGuard private_key is required".to_string()))?;

    let listen_port = options.get("listen_port").and_then(|v| v.as_u64()).map(|p| p as u16);

    let addresses = options.get("addresses")
        .and_then(|v| v.as_array())
        .map(|arr| {
            arr.iter()
                .filter_map(|v| v.as_str().map(|s| s.to_string()))
                .collect()
        })
        .unwrap_or_else(|| vec!["********/24".to_string()]);

    let dns = options.get("dns")
        .and_then(|v| v.as_array())
        .map(|arr| {
            arr.iter()
                .filter_map(|v| v.as_str().and_then(|s| s.parse().ok()))
                .collect()
        });

    let peers = options.get("peers")
        .and_then(|v| v.as_array())
        .map(|arr| {
            arr.iter()
                .filter_map(|peer_obj| {
                    peer_obj.as_object().map(|peer| {
                        let public_key = peer.get("public_key")?.as_str()?.to_string();
                        let endpoint = peer.get("endpoint")
                            .and_then(|v| v.as_str())
                            .and_then(|s| s.parse().ok());
                        let allowed_ips = peer.get("allowed_ips")
                            .and_then(|v| v.as_array())
                            .map(|arr| {
                                arr.iter()
                                    .filter_map(|v| v.as_str().map(|s| s.to_string()))
                                    .collect()
                            })
                            .unwrap_or_default();

                        Some(wireguard::WgPeer {
                            public_key,
                            pre_shared_key: peer.get("pre_shared_key")
                                .and_then(|v| v.as_str())
                                .map(|s| s.to_string()),
                            endpoint,
                            allowed_ips,
                            persistent_keepalive: peer.get("persistent_keepalive")
                                .and_then(|v| v.as_u64())
                                .map(|s| std::time::Duration::from_secs(s)),
                        })
                    })
                })
                .collect::<Option<Vec<_>>>()
        })
        .unwrap_or_default();

    let config = wireguard::WireGuardConfig {
        interface: wireguard::WgInterface {
            private_key: private_key.to_string(),
            listen_port,
            addresses,
            dns,
            table: options.get("table").and_then(|v| v.as_u64()).map(|t| t as u32),
            fwmark: options.get("fwmark").and_then(|v| v.as_u64()).map(|f| f as u32),
            stats: Some(false),
            scripts: None,
        },
        peers: peers.unwrap_or_default(),
        mtu: options.get("mtu").and_then(|v| v.as_u64()).map(|m| m as u16),
        keep_alive: options.get("keep_alive")
            .and_then(|v| v.as_u64())
            .map(|s| std::time::Duration::from_secs(s)),
        pre_shared_key: options.get("pre_shared_key")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string()),
        reserved: None,
        experimental: None,
        performance: None,
        security: None,
    };

    let wg_tunnel = wireguard::WgTunnel::new(tag.to_string(), config)
        .map_err(|e| ProtocolError::InvalidOptions(e))?;

    Ok(Box::new(wg_tunnel))
}

pub fn create_wireguard_outbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError> {
    // WireGuard can also act as an outbound
    let private_key = options.get("private_key")
        .and_then(|v| v.as_str())
        .ok_or_else(|| ProtocolError::InvalidOptions("WireGuard private_key is required".to_string()))?;

    let peer_public_key = options.get("peer_public_key")
        .and_then(|v| v.as_str())
        .ok_or_else(|| ProtocolError::InvalidOptions("WireGuard peer_public_key is required".to_string()))?;

    let endpoint = options.get("endpoint")
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse().ok())
        .ok_or_else(|| ProtocolError::InvalidOptions("WireGuard endpoint is required".to_string()))?;

    let allowed_ips = options.get("allowed_ips")
        .and_then(|v| v.as_array())
        .map(|arr| {
            arr.iter()
                .filter_map(|v| v.as_str().map(|s| s.to_string()))
                .collect()
        })
        .unwrap_or_else(|| vec!["0.0.0.0/0".to_string()]);

    let config = wireguard::WireGuardConfig {
        interface: wireguard::WgInterface {
            private_key: private_key.to_string(),
            listen_port: None,
            addresses: vec!["********/24".to_string()],
            dns: None,
            table: None,
            fwmark: None,
            stats: Some(false),
            scripts: None,
        },
        peers: vec![wireguard::WgPeer {
            public_key: peer_public_key.to_string(),
            pre_shared_key: options.get("pre_shared_key")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string()),
            endpoint: Some(endpoint),
            allowed_ips,
            persistent_keepalive: options.get("persistent_keepalive")
                .and_then(|v| v.as_u64())
                .map(|s| std::time::Duration::from_secs(s)),
        }],
        mtu: options.get("mtu").and_then(|v| v.as_u64()).map(|m| m as u16),
        keep_alive: Some(std::time::Duration::from_secs(25)),
        pre_shared_key: None,
        reserved: None,
        experimental: None,
        performance: None,
        security: None,
    };

    let wg_tunnel = wireguard::WgTunnel::new(tag.to_string(), config)
        .map_err(|e| ProtocolError::InvalidOptions(e))?;

    Ok(Box::new(wg_tunnel))
}

// TUIC protocol constructors
pub fn create_tuic_inbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Inbound>, ProtocolError> {
    let listen = options.get("listen").and_then(|v| v.as_str()).unwrap_or("0.0.0.0").to_string();
    let listen_port = options.get("listen_port").and_then(|v| v.as_u64()).unwrap_or(8443) as u16;

    let uuid = options.get("uuid")
        .and_then(|v| v.as_str())
        .ok_or_else(|| ProtocolError::InvalidOptions("TUIC uuid is required".to_string()))?;

    let password = options.get("password")
        .and_then(|v| v.as_str())
        .ok_or_else(|| ProtocolError::InvalidOptions("TUIC password is required".to_string()))?;

    let version = options.get("version")
        .and_then(|v| v.as_str())
        .map(|v| match v {
            "4" | "v4" => tuic::TuicVersion::V4,
            "5" | "v5" => tuic::TuicVersion::V5,
            _ => tuic::TuicVersion::V5,
        })
        .unwrap_or(tuic::TuicVersion::V5);

    let config = tuic::TuicConfig {
        version,
        server: None,
        server_port: None,
        listen: Some(listen),
        listen_port: Some(listen_port),
        uuid: uuid.to_string(),
        password: password.to_string(),
        congestion_control: options.get("congestion_control").and_then(|v| v.as_str()).map(|s| s.to_string()),
        udp_relay_mode: options.get("udp_relay_mode").and_then(|v| v.as_str()).map(|s| s.to_string()),
        zero_rtt_handshake: options.get("zero_rtt_handshake").and_then(|v| v.as_bool()),
        heartbeat: options.get("heartbeat").and_then(|v| v.as_u64()).map(|s| std::time::Duration::from_secs(s)),
        tls: None,
        quic: None,
    };

    let tuic_adapter = tuic::TuicAdapter::new(tag.to_string(), config, true)
        .map_err(|e| ProtocolError::InvalidOptions(e))?;

    Ok(Box::new(tuic_adapter))
}

pub fn create_tuic_outbound(_ctx: &Context, tag: &str, options: &serde_json::Value) -> Result<Box<dyn Outbound>, ProtocolError> {
    let server = options.get("server")
        .and_then(|v| v.as_str())
        .ok_or_else(|| ProtocolError::InvalidOptions("TUIC server is required".to_string()))?;

    let server_port = options.get("server_port")
        .and_then(|v| v.as_u64())
        .ok_or_else(|| ProtocolError::InvalidOptions("TUIC server_port is required".to_string()))? as u16;

    let uuid = options.get("uuid")
        .and_then(|v| v.as_str())
        .ok_or_else(|| ProtocolError::InvalidOptions("TUIC uuid is required".to_string()))?;

    let password = options.get("password")
        .and_then(|v| v.as_str())
        .ok_or_else(|| ProtocolError::InvalidOptions("TUIC password is required".to_string()))?;

    let version = options.get("version")
        .and_then(|v| v.as_str())
        .map(|v| match v {
            "4" | "v4" => tuic::TuicVersion::V4,
            "5" | "v5" => tuic::TuicVersion::V5,
            _ => tuic::TuicVersion::V5,
        })
        .unwrap_or(tuic::TuicVersion::V5);

    let config = tuic::TuicConfig {
        version,
        server: Some(server.to_string()),
        server_port: Some(server_port),
        listen: None,
        listen_port: None,
        uuid: uuid.to_string(),
        password: password.to_string(),
        congestion_control: options.get("congestion_control").and_then(|v| v.as_str()).map(|s| s.to_string()),
        udp_relay_mode: options.get("udp_relay_mode").and_then(|v| v.as_str()).map(|s| s.to_string()),
        zero_rtt_handshake: options.get("zero_rtt_handshake").and_then(|v| v.as_bool()),
        heartbeat: options.get("heartbeat").and_then(|v| v.as_u64()).map(|s| std::time::Duration::from_secs(s)),
        tls: None,
        quic: None,
    };

    let tuic_adapter = tuic::TuicAdapter::new(tag.to_string(), config, false)
        .map_err(|e| ProtocolError::InvalidOptions(e))?;

    Ok(Box::new(tuic_adapter))
}

/// Register built-in protocols
pub fn register_builtin_protocols(inbound_registry: &InboundRegistry, outbound_registry: &OutboundRegistry) {
    // Basic protocols
    inbound_registry.register("direct", create_direct_inbound);
    outbound_registry.register("direct", create_direct_outbound);
    outbound_registry.register("block", create_block_outbound);

    // HTTP proxy
    inbound_registry.register("http", create_http_inbound);
    outbound_registry.register("http", create_http_outbound);

    // SOCKS proxy
    inbound_registry.register("socks", create_socks_inbound);
    outbound_registry.register("socks", create_socks_outbound);

    // Shadowsocks
    inbound_registry.register("shadowsocks", create_shadowsocks_inbound);
    outbound_registry.register("shadowsocks", create_shadowsocks_outbound);

    // VMess
    inbound_registry.register("vmess", create_vmess_inbound);
    outbound_registry.register("vmess", create_vmess_outbound);

    // Trojan
    inbound_registry.register("trojan", create_trojan_inbound);
    outbound_registry.register("trojan", create_trojan_outbound);

    // VLESS
    inbound_registry.register("vless", create_vless_inbound);
    outbound_registry.register("vless", create_vless_outbound);

    // Mixed (inbound only)
    inbound_registry.register("mixed", create_mixed_inbound);

    // Hysteria (inbound only)
    inbound_registry.register("hysteria", create_hysteria_inbound);

    // Hysteria2 (inbound only)
    inbound_registry.register("hysteria2", create_hysteria2_inbound);

    // WireGuard (both inbound and outbound)
    inbound_registry.register("wireguard", create_wireguard_inbound);
    outbound_registry.register("wireguard", create_wireguard_outbound);

    // TUIC (both inbound and outbound)
    inbound_registry.register("tuic", create_tuic_inbound);
    outbound_registry.register("tuic", create_tuic_outbound);
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::adapter::Adapter;

    #[test]
    fn test_protocol_error_display() {
        let err1 = ProtocolError::UnsupportedProtocol("test".to_string());
        assert_eq!(format!("{}", err1), "unsupported protocol: test");
        
        let err2 = ProtocolError::InvalidOptions("bad config".to_string());
        assert_eq!(format!("{}", err2), "invalid options: bad config");
        
        let err3 = ProtocolError::AuthenticationFailed;
        assert_eq!(format!("{}", err3), "authentication failed");
    }

    #[test]
    fn test_inbound_registry() {
        let registry = InboundRegistry::new();
        
        // Test empty registry
        assert!(registry.list_protocols().is_empty());
        
        // Test registration
        registry.register("test", create_direct_inbound);
        let protocols = registry.list_protocols();
        assert_eq!(protocols.len(), 1);
        assert!(protocols.contains(&"test".to_string()));
    }

    #[test]
    fn test_outbound_registry() {
        let registry = OutboundRegistry::new();
        
        // Test empty registry
        assert!(registry.list_protocols().is_empty());
        
        // Test registration
        registry.register("test", create_direct_outbound);
        let protocols = registry.list_protocols();
        assert_eq!(protocols.len(), 1);
        assert!(protocols.contains(&"test".to_string()));
    }

    #[tokio::test]
    async fn test_direct_inbound() {
        let mut inbound = DirectInbound::new("test-direct");

        assert_eq!(inbound.adapter_type(), "direct");
        assert_eq!(inbound.tag(), "test-direct");
        assert!(!inbound.started.load(std::sync::atomic::Ordering::Relaxed));

        assert!(inbound.start(StartStage::Start).await.is_ok());
        assert!(inbound.started.load(std::sync::atomic::Ordering::Relaxed));

        assert!(inbound.close().await.is_ok());
        assert!(!inbound.started.load(std::sync::atomic::Ordering::Relaxed));
    }

    #[test]
    fn test_direct_outbound() {
        let outbound = DirectOutbound::new("test-direct");
        
        assert_eq!(outbound.adapter_type(), "direct");
        assert_eq!(outbound.tag(), "test-direct");
        assert_eq!(outbound.network(), vec!["tcp", "udp"]);
        assert!(outbound.dependencies().is_empty());
        assert!(outbound.dial("tcp", "example.com:80").is_ok());
    }

    #[test]
    fn test_block_outbound() {
        let outbound = BlockOutbound::new("test-block");
        
        assert_eq!(outbound.adapter_type(), "block");
        assert_eq!(outbound.tag(), "test-block");
        assert_eq!(outbound.network(), vec!["tcp", "udp"]);
        assert!(outbound.dependencies().is_empty());
        
        let result = outbound.dial("tcp", "example.com:80");
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("blocked connection"));
    }

    #[test]
    fn test_inbound_registry_create() {
        let registry = InboundRegistry::new();
        registry.register("direct", create_direct_inbound);
        
        let ctx = Context::new();
        let options = serde_json::json!({});
        
        let result = registry.create(&ctx, "direct", "test-tag", &options);
        assert!(result.is_ok());
        
        let inbound = result.unwrap();
        assert_eq!(inbound.tag(), "test-tag");
        assert_eq!(inbound.adapter_type(), "direct");
    }

    #[test]
    fn test_outbound_registry_create() {
        let registry = OutboundRegistry::new();
        registry.register("direct", create_direct_outbound);
        
        let ctx = Context::new();
        let options = serde_json::json!({});
        
        let result = registry.create(&ctx, "direct", "test-tag", &options);
        assert!(result.is_ok());
        
        let outbound = result.unwrap();
        assert_eq!(outbound.tag(), "test-tag");
        assert_eq!(outbound.adapter_type(), "direct");
    }

    #[test]
    fn test_registry_unsupported_protocol() {
        let registry = InboundRegistry::new();
        let ctx = Context::new();
        let options = serde_json::json!({});
        
        let result = registry.create(&ctx, "unknown", "test-tag", &options);
        assert!(result.is_err());

        if let Err(ProtocolError::UnsupportedProtocol(proto)) = result {
            assert_eq!(proto, "unknown");
        } else {
            panic!("Expected UnsupportedProtocol error");
        }
    }

    #[test]
    fn test_register_builtin_protocols() {
        let inbound_registry = InboundRegistry::new();
        let outbound_registry = OutboundRegistry::new();
        
        register_builtin_protocols(&inbound_registry, &outbound_registry);
        
        let inbound_protocols = inbound_registry.list_protocols();
        assert!(inbound_protocols.contains(&"direct".to_string()));
        
        let outbound_protocols = outbound_registry.list_protocols();
        assert!(outbound_protocols.contains(&"direct".to_string()));
        assert!(outbound_protocols.contains(&"block".to_string()));
    }
}
