use std::collections::HashMap;
use std::net::SocketAddr;
use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader as AsyncBufReader};
use tokio::net::TcpStream as AsyncTcpStream;
use base64::{Engine as _, engine::general_purpose};

use crate::adapter::{Adapter, Inbound, Outbound, Lifecycle, StartStage};
use async_trait::async_trait;
use crate::common::interrupt::Context;
use crate::protocol::ProtocolError;

/// HTTP proxy inbound handler
pub struct HttpInbound {
    tag: String,
    listen_addr: SocketAddr,
    users: HashMap<String, String>, // username -> password
}

impl HttpInbound {
    pub fn new(tag: String, listen_addr: SocketAddr, users: HashMap<String, String>) -> Self {
        Self {
            tag,
            listen_addr,
            users,
        }
    }

    /// Handle HTTP CONNECT method
    async fn handle_connect(
        &self,
        mut stream: AsyncTcpStream,
        target_host: String,
        target_port: u16,
    ) -> Result<(), ProtocolError> {
        // Connect to target
        let target_addr = format!("{}:{}", target_host, target_port);
        let mut target_stream = AsyncTcpStream::connect(&target_addr).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to {}: {}", target_addr, e)))?;

        // Send 200 Connection established
        stream.write_all(b"HTTP/1.1 200 Connection established\r\n\r\n").await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("write response: {}", e)))?;

        // Start bidirectional forwarding
        let (mut client_read, mut client_write) = stream.split();
        let (mut target_read, mut target_write) = target_stream.split();

        let forward1 = tokio::io::copy(&mut client_read, &mut target_write);
        let forward2 = tokio::io::copy(&mut target_read, &mut client_write);

        // Wait for either direction to close
        tokio::select! {
            result1 = forward1 => {
                if let Err(e) = result1 {
                    eprintln!("Forward client->target error: {}", e);
                }
            }
            result2 = forward2 => {
                if let Err(e) = result2 {
                    eprintln!("Forward target->client error: {}", e);
                }
            }
        }

        Ok(())
    }

    /// Parse HTTP request line
    fn parse_request_line(line: &str) -> Result<(String, String, u16), ProtocolError> {
        let parts: Vec<&str> = line.split_whitespace().collect();
        if parts.len() != 3 {
            return Err(ProtocolError::ProtocolViolation("invalid request line".to_string()));
        }

        let method = parts[0];
        let url = parts[1];
        let version = parts[2];

        if method != "CONNECT" {
            return Err(ProtocolError::UnsupportedProtocol(format!("unsupported method: {}", method)));
        }

        if !version.starts_with("HTTP/") {
            return Err(ProtocolError::ProtocolViolation("invalid HTTP version".to_string()));
        }

        // Parse host:port from URL
        let host_port: Vec<&str> = url.split(':').collect();
        if host_port.len() != 2 {
            return Err(ProtocolError::ProtocolViolation("invalid CONNECT target".to_string()));
        }

        let host = host_port[0].to_string();
        let port = host_port[1].parse::<u16>()
            .map_err(|_| ProtocolError::ProtocolViolation("invalid port number".to_string()))?;

        Ok((method.to_string(), host, port))
    }

    /// Parse HTTP headers
    fn parse_headers(lines: &[String]) -> HashMap<String, String> {
        let mut headers = HashMap::new();
        
        for line in lines {
            if let Some(colon_pos) = line.find(':') {
                let key = line[..colon_pos].trim().to_lowercase();
                let value = line[colon_pos + 1..].trim().to_string();
                headers.insert(key, value);
            }
        }
        
        headers
    }

    /// Check authentication
    fn check_auth(&self, headers: &HashMap<String, String>) -> bool {
        if self.users.is_empty() {
            return true; // No authentication required
        }

        if let Some(auth_header) = headers.get("proxy-authorization") {
            if let Some(basic_auth) = auth_header.strip_prefix("Basic ") {
                if let Ok(decoded) = general_purpose::STANDARD.decode(basic_auth) {
                    if let Ok(auth_str) = String::from_utf8(decoded) {
                        if let Some(colon_pos) = auth_str.find(':') {
                            let username = &auth_str[..colon_pos];
                            let password = &auth_str[colon_pos + 1..];
                            
                            return self.users.get(username)
                                .map(|stored_password| stored_password == password)
                                .unwrap_or(false);
                        }
                    }
                }
            }
        }

        false
    }

    /// Handle incoming connection
    pub async fn handle_connection(&self, mut stream: AsyncTcpStream) -> Result<(), ProtocolError> {
        let mut reader = AsyncBufReader::new(&mut stream);
        let mut lines = Vec::new();
        
        // Read HTTP request
        loop {
            let mut line = String::new();
            let bytes_read = reader.read_line(&mut line).await
                .map_err(|e| ProtocolError::ConnectionFailed(format!("read request: {}", e)))?;
            
            if bytes_read == 0 {
                return Err(ProtocolError::ConnectionFailed("connection closed".to_string()));
            }
            
            let line = line.trim_end_matches(&['\r', '\n'][..]).to_string();
            
            if line.is_empty() {
                break; // End of headers
            }
            
            lines.push(line);
        }

        if lines.is_empty() {
            return Err(ProtocolError::ProtocolViolation("empty request".to_string()));
        }

        // Parse request line
        let (method, host, port) = Self::parse_request_line(&lines[0])?;
        
        // Parse headers
        let headers = Self::parse_headers(&lines[1..]);
        
        // Check authentication
        if !self.check_auth(&headers) {
            stream.write_all(b"HTTP/1.1 407 Proxy Authentication Required\r\n\r\n").await
                .map_err(|e| ProtocolError::ProtocolViolation(format!("write auth error: {}", e)))?;
            return Err(ProtocolError::AuthenticationFailed);
        }

        // Handle CONNECT method
        if method == "CONNECT" {
            self.handle_connect(stream, host, port).await?;
        } else {
            return Err(ProtocolError::UnsupportedProtocol(format!("unsupported method: {}", method)));
        }

        Ok(())
    }
}

impl Adapter for HttpInbound {
    fn adapter_type(&self) -> &str {
        "http"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

#[async_trait]
impl Lifecycle for HttpInbound {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        println!("HTTP inbound {} starting", self.tag);
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        println!("HTTP inbound {} closing", self.tag);
        Ok(())
    }
}

impl Inbound for HttpInbound {
    // Inherits from Adapter and Lifecycle
}

/// HTTP proxy outbound handler
pub struct HttpOutbound {
    tag: String,
    server_addr: SocketAddr,
    username: Option<String>,
    password: Option<String>,
}

impl HttpOutbound {
    pub fn new(
        tag: String,
        server_addr: SocketAddr,
        username: Option<String>,
        password: Option<String>,
    ) -> Self {
        Self {
            tag,
            server_addr,
            username,
            password,
        }
    }

    /// Create HTTP CONNECT request
    fn create_connect_request(&self, host: &str, port: u16) -> String {
        let mut request = format!("CONNECT {}:{} HTTP/1.1\r\n", host, port);
        request.push_str(&format!("Host: {}:{}\r\n", host, port));
        
        // Add authentication if provided
        if let (Some(username), Some(password)) = (&self.username, &self.password) {
            let auth_str = format!("{}:{}", username, password);
            let auth_b64 = general_purpose::STANDARD.encode(auth_str.as_bytes());
            request.push_str(&format!("Proxy-Authorization: Basic {}\r\n", auth_b64));
        }
        
        request.push_str("\r\n");
        request
    }

    /// Dial through HTTP proxy
    pub async fn dial(&self, target_host: &str, target_port: u16) -> Result<AsyncTcpStream, ProtocolError> {
        // Connect to proxy server
        let mut proxy_stream = AsyncTcpStream::connect(&self.server_addr).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to proxy: {}", e)))?;

        // Send CONNECT request
        let connect_request = self.create_connect_request(target_host, target_port);
        proxy_stream.write_all(connect_request.as_bytes()).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("send CONNECT: {}", e)))?;

        // Read response
        let mut reader = AsyncBufReader::new(&mut proxy_stream);
        let mut response_line = String::new();
        reader.read_line(&mut response_line).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("read response: {}", e)))?;

        // Parse response
        let response_parts: Vec<&str> = response_line.trim().split_whitespace().collect();
        if response_parts.len() < 2 {
            return Err(ProtocolError::ProtocolViolation("invalid response".to_string()));
        }

        let status_code = response_parts[1].parse::<u16>()
            .map_err(|_| ProtocolError::ProtocolViolation("invalid status code".to_string()))?;

        if status_code != 200 {
            return Err(ProtocolError::ConnectionFailed(format!("proxy returned status {}", status_code)));
        }

        // Skip remaining headers
        loop {
            let mut line = String::new();
            reader.read_line(&mut line).await
                .map_err(|e| ProtocolError::ProtocolViolation(format!("read headers: {}", e)))?;
            
            if line.trim().is_empty() {
                break;
            }
        }

        Ok(proxy_stream)
    }
}

impl Adapter for HttpOutbound {
    fn adapter_type(&self) -> &str {
        "http"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Outbound for HttpOutbound {
    fn network(&self) -> Vec<String> {
        vec!["tcp".to_string()]
    }

    fn dependencies(&self) -> Vec<String> {
        vec![]
    }

    fn dial(&self, _network: &str, _destination: &str) -> Result<(), String> {
        // This is a simplified interface - in a real implementation,
        // this would return a connection or be async
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_request_line() {
        let line = "CONNECT example.com:443 HTTP/1.1";
        let result = HttpInbound::parse_request_line(line).unwrap();
        assert_eq!(result.0, "CONNECT");
        assert_eq!(result.1, "example.com");
        assert_eq!(result.2, 443);
    }

    #[test]
    fn test_parse_headers() {
        let lines = vec![
            "Host: example.com:443".to_string(),
            "Proxy-Authorization: Basic dGVzdDp0ZXN0".to_string(),
            "User-Agent: test-client".to_string(),
        ];
        
        let headers = HttpInbound::parse_headers(&lines);
        assert_eq!(headers.get("host"), Some(&"example.com:443".to_string()));
        assert_eq!(headers.get("proxy-authorization"), Some(&"Basic dGVzdDp0ZXN0".to_string()));
        assert_eq!(headers.get("user-agent"), Some(&"test-client".to_string()));
    }

    #[test]
    fn test_create_connect_request() {
        let outbound = HttpOutbound::new(
            "test".to_string(),
            "127.0.0.1:8080".parse().unwrap(),
            Some("user".to_string()),
            Some("pass".to_string()),
        );
        
        let request = outbound.create_connect_request("example.com", 443);
        assert!(request.contains("CONNECT example.com:443 HTTP/1.1"));
        assert!(request.contains("Host: example.com:443"));
        assert!(request.contains("Proxy-Authorization: Basic"));
    }
}
