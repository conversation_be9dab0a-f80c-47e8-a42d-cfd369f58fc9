use std::collections::HashMap;
use std::net::{SocketAddr, Ipv4Addr, Ipv6Addr};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use sha2::{Sha224, Digest};
use hex;

use async_trait::async_trait;
use crate::adapter::{Adapter, Inbound, Outbound, Lifecycle, StartStage};
use crate::protocol::ProtocolError;

/// Trojan protocol constants
const TROJAN_CONNECT: u8 = 0x01;
const TROJAN_UDP_ASSOCIATE: u8 = 0x03;
const CRLF: &[u8] = b"\r\n";

/// Trojan address types
#[derive(Debug, Clone, PartialEq)]
pub enum AddressType {
    IPv4 = 0x01,
    Domain = 0x03,
    IPv6 = 0x04,
}

/// Trojan user configuration
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct TrojanUser {
    pub name: String,
    pub password: String,
    pub password_hash: String,
}

impl TrojanUser {
    pub fn new(name: &str, password: &str) -> Self {
        let password_hash = Self::hash_password(password);
        Self {
            name: name.to_string(),
            password: password.to_string(),
            password_hash,
        }
    }

    /// Generate SHA224 hash of password
    fn hash_password(password: &str) -> String {
        let mut hasher = Sha224::new();
        hasher.update(password.as_bytes());
        hex::encode(hasher.finalize())
    }

    pub fn verify_password(&self, hash: &str) -> bool {
        self.password_hash == hash
    }
}

/// Trojan request structure
#[derive(Debug)]
pub struct TrojanRequest {
    pub command: u8,
    pub address_type: AddressType,
    pub address: String,
    pub port: u16,
    pub payload: Vec<u8>,
}

impl TrojanRequest {
    /// Parse Trojan request from bytes
    pub fn parse(data: &[u8]) -> Result<Self, ProtocolError> {
        if data.len() < 59 {  // 56 bytes hash + 2 bytes CRLF + 1 byte command
            return Err(ProtocolError::ProtocolViolation("request too short".to_string()));
        }

        let mut offset = 0;

        // Skip password hash (56 bytes) and CRLF (2 bytes)
        offset += 58;

        // Command
        let command = data[offset];
        offset += 1;

        // Address type
        let address_type = match data[offset] {
            0x01 => AddressType::IPv4,
            0x03 => AddressType::Domain,
            0x04 => AddressType::IPv6,
            _ => return Err(ProtocolError::ProtocolViolation("invalid address type".to_string())),
        };
        offset += 1;

        // Address
        let address = match address_type {
            AddressType::IPv4 => {
                if data.len() < offset + 4 {
                    return Err(ProtocolError::ProtocolViolation("incomplete IPv4 address".to_string()));
                }
                let addr = Ipv4Addr::from([data[offset], data[offset + 1], data[offset + 2], data[offset + 3]]);
                offset += 4;
                addr.to_string()
            }
            AddressType::Domain => {
                if data.len() < offset + 1 {
                    return Err(ProtocolError::ProtocolViolation("incomplete domain length".to_string()));
                }
                let domain_len = data[offset] as usize;
                offset += 1;
                if data.len() < offset + domain_len {
                    return Err(ProtocolError::ProtocolViolation("incomplete domain".to_string()));
                }
                let domain = String::from_utf8(data[offset..offset + domain_len].to_vec())
                    .map_err(|_| ProtocolError::ProtocolViolation("invalid domain encoding".to_string()))?;
                offset += domain_len;
                domain
            }
            AddressType::IPv6 => {
                if data.len() < offset + 16 {
                    return Err(ProtocolError::ProtocolViolation("incomplete IPv6 address".to_string()));
                }
                let mut addr_bytes = [0u8; 16];
                addr_bytes.copy_from_slice(&data[offset..offset + 16]);
                let addr = Ipv6Addr::from(addr_bytes);
                offset += 16;
                addr.to_string()
            }
        };

        // Port
        if data.len() < offset + 2 {
            return Err(ProtocolError::ProtocolViolation("incomplete port".to_string()));
        }
        let port = u16::from_be_bytes([data[offset], data[offset + 1]]);
        offset += 2;

        // Skip CRLF
        if data.len() < offset + 2 || &data[offset..offset + 2] != CRLF {
            return Err(ProtocolError::ProtocolViolation("missing CRLF after request".to_string()));
        }
        offset += 2;

        // Payload (remaining data)
        let payload = data[offset..].to_vec();

        Ok(TrojanRequest {
            command,
            address_type,
            address,
            port,
            payload,
        })
    }

    /// Serialize request to bytes
    pub fn serialize(&self, password_hash: &str) -> Vec<u8> {
        let mut data = Vec::new();

        // Password hash (56 bytes)
        data.extend_from_slice(password_hash.as_bytes());
        data.extend_from_slice(CRLF);

        // Command
        data.push(self.command);

        // Address type
        data.push(self.address_type.clone() as u8);

        // Address
        match self.address_type {
            AddressType::IPv4 => {
                let addr: Ipv4Addr = self.address.parse().unwrap();
                data.extend_from_slice(&addr.octets());
            }
            AddressType::Domain => {
                data.push(self.address.len() as u8);
                data.extend_from_slice(self.address.as_bytes());
            }
            AddressType::IPv6 => {
                let addr: Ipv6Addr = self.address.parse().unwrap();
                data.extend_from_slice(&addr.octets());
            }
        }

        // Port
        data.extend_from_slice(&self.port.to_be_bytes());
        data.extend_from_slice(CRLF);

        // Payload
        data.extend_from_slice(&self.payload);

        data
    }
}

/// Trojan inbound handler
pub struct TrojanInbound {
    tag: String,
    listen_addr: SocketAddr,
    users: HashMap<String, TrojanUser>,
}

impl TrojanInbound {
    pub fn new(tag: String, listen_addr: SocketAddr, users: Vec<TrojanUser>) -> Self {
        let user_map = users.into_iter()
            .map(|user| (user.password_hash.clone(), user))
            .collect();

        Self {
            tag,
            listen_addr,
            users: user_map,
        }
    }

    /// Handle incoming Trojan connection
    pub async fn handle_connection(&self, mut stream: TcpStream) -> Result<(), ProtocolError> {
        // Read initial data to parse request
        let mut buffer = vec![0u8; 4096];
        let n = stream.read(&mut buffer).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read request: {}", e)))?;
        
        if n < 59 {
            return Err(ProtocolError::ProtocolViolation("request too short".to_string()));
        }

        buffer.truncate(n);

        // Extract password hash
        let password_hash = String::from_utf8(buffer[..56].to_vec())
            .map_err(|_| ProtocolError::ProtocolViolation("invalid password hash encoding".to_string()))?;

        // Verify user
        let _user = self.users.get(&password_hash)
            .ok_or(ProtocolError::AuthenticationFailed)?;

        // Parse request
        let request = TrojanRequest::parse(&buffer)?;

        match request.command {
            TROJAN_CONNECT => {
                self.handle_connect(stream, &request).await
            }
            TROJAN_UDP_ASSOCIATE => {
                self.handle_udp_associate(stream, &request).await
            }
            _ => Err(ProtocolError::ProtocolViolation(format!("unsupported command: {}", request.command))),
        }
    }

    async fn handle_connect(&self, mut client_stream: TcpStream, request: &TrojanRequest) -> Result<(), ProtocolError> {
        // Connect to target
        let target_addr = format!("{}:{}", request.address, request.port);
        let mut target_stream = TcpStream::connect(&target_addr).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to {}: {}", target_addr, e)))?;

        // Forward initial payload if any
        if !request.payload.is_empty() {
            target_stream.write_all(&request.payload).await
                .map_err(|e| ProtocolError::ConnectionFailed(format!("write initial payload: {}", e)))?;
        }

        // Start bidirectional forwarding
        let (mut client_read, mut client_write) = client_stream.split();
        let (mut target_read, mut target_write) = target_stream.split();

        let forward1 = tokio::io::copy(&mut client_read, &mut target_write);
        let forward2 = tokio::io::copy(&mut target_read, &mut client_write);

        // Wait for either direction to close
        tokio::select! {
            result1 = forward1 => {
                if let Err(e) = result1 {
                    eprintln!("Forward client->target error: {}", e);
                }
            }
            result2 = forward2 => {
                if let Err(e) = result2 {
                    eprintln!("Forward target->client error: {}", e);
                }
            }
        }

        Ok(())
    }

    async fn handle_udp_associate(&self, _stream: TcpStream, _request: &TrojanRequest) -> Result<(), ProtocolError> {
        // UDP associate is more complex and requires packet forwarding
        // For now, return an error
        Err(ProtocolError::UnsupportedProtocol("UDP associate not implemented".to_string()))
    }
}

impl Adapter for TrojanInbound {
    fn adapter_type(&self) -> &str {
        "trojan"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

#[async_trait]
impl Lifecycle for TrojanInbound {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        println!("Trojan inbound {} starting", self.tag);
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        println!("Trojan inbound {} closing", self.tag);
        Ok(())
    }
}

impl Inbound for TrojanInbound {
    // Inherits from Adapter and Lifecycle
}

/// Trojan outbound handler
pub struct TrojanOutbound {
    tag: String,
    server_addr: SocketAddr,
    password: String,
    password_hash: String,
}

impl TrojanOutbound {
    pub fn new(tag: String, server_addr: SocketAddr, password: &str) -> Self {
        let password_hash = TrojanUser::hash_password(password);
        
        Self {
            tag,
            server_addr,
            password: password.to_string(),
            password_hash,
        }
    }

    /// Dial through Trojan proxy
    pub async fn dial(&self, target_host: &str, target_port: u16, network: &str) -> Result<TcpStream, ProtocolError> {
        // Connect to Trojan server
        let mut stream = TcpStream::connect(&self.server_addr).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to server: {}", e)))?;

        // Determine command based on network
        let command = match network {
            "tcp" => TROJAN_CONNECT,
            "udp" => TROJAN_UDP_ASSOCIATE,
            _ => return Err(ProtocolError::UnsupportedProtocol(format!("unsupported network: {}", network))),
        };

        // Determine address type and create request
        let (address_type, address) = if let Ok(_) = target_host.parse::<Ipv4Addr>() {
            (AddressType::IPv4, target_host.to_string())
        } else if let Ok(_) = target_host.parse::<Ipv6Addr>() {
            (AddressType::IPv6, target_host.to_string())
        } else {
            (AddressType::Domain, target_host.to_string())
        };

        let request = TrojanRequest {
            command,
            address_type,
            address,
            port: target_port,
            payload: Vec::new(),
        };

        // Send request
        let request_data = request.serialize(&self.password_hash);
        stream.write_all(&request_data).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("write request: {}", e)))?;

        Ok(stream)
    }
}

impl Adapter for TrojanOutbound {
    fn adapter_type(&self) -> &str {
        "trojan"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Outbound for TrojanOutbound {
    fn network(&self) -> Vec<String> {
        vec!["tcp".to_string(), "udp".to_string()]
    }

    fn dependencies(&self) -> Vec<String> {
        vec![]
    }

    fn dial(&self, _network: &str, _destination: &str) -> Result<(), String> {
        // This is a simplified interface - in a real implementation,
        // this would return a connection or be async
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_trojan_user_creation() {
        let user = TrojanUser::new("test", "password123");
        assert_eq!(user.name, "test");
        assert_eq!(user.password, "password123");
        assert!(!user.password_hash.is_empty());
        assert_eq!(user.password_hash.len(), 56); // SHA224 hex string length
    }

    #[test]
    fn test_password_verification() {
        let user = TrojanUser::new("test", "password123");
        assert!(user.verify_password(&user.password_hash));
        assert!(!user.verify_password("wrong_hash"));
    }

    #[test]
    fn test_trojan_request_serialization() {
        let request = TrojanRequest {
            command: TROJAN_CONNECT,
            address_type: AddressType::Domain,
            address: "example.com".to_string(),
            port: 443,
            payload: b"GET / HTTP/1.1\r\n".to_vec(),
        };

        let password_hash = "a".repeat(56); // Mock hash
        let serialized = request.serialize(&password_hash);
        
        assert!(!serialized.is_empty());
        assert!(serialized.starts_with(password_hash.as_bytes()));
    }

    #[test]
    fn test_address_type_values() {
        assert_eq!(AddressType::IPv4 as u8, 0x01);
        assert_eq!(AddressType::Domain as u8, 0x03);
        assert_eq!(AddressType::IPv6 as u8, 0x04);
    }
}
