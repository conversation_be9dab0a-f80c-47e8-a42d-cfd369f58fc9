use std::collections::HashMap;
use std::net::{SocketAddr, Ipv4Addr, Ipv6Addr};
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use aes_gcm::{Aes128Gcm, <PERSON>, <PERSON>ce, AeadInPlace};
use async_trait::async_trait;
use chacha20poly1305::ChaCha20Poly1305;
use uuid::Uuid;
use rand::RngCore;
use sha2::{Sha256, Digest};
use md5;

use crate::adapter::{Adapter, Inbound, Outbound, Lifecycle, StartStage};
use crate::protocol::ProtocolError;

/// VMess protocol version
const VMESS_VERSION: u8 = 1;

/// VMess security types
#[derive(Debug, Clone, PartialEq)]
pub enum VMessSecurity {
    Auto,
    None,
    Zero,
    Aes128Gcm,
    ChaCha20Poly1305,
    Aes128Ctr, // Legacy
}

impl VMessSecurity {
    pub fn from_str(security: &str) -> Result<Self, ProtocolError> {
        match security.to_lowercase().as_str() {
            "auto" => Ok(Self::Auto),
            "none" => Ok(Self::None),
            "zero" => Ok(Self::Zero),
            "aes-128-gcm" => Ok(Self::Aes128Gcm),
            "chacha20-poly1305" => Ok(Self::ChaCha20Poly1305),
            "aes-128-ctr" => Ok(Self::Aes128Ctr),
            _ => Err(ProtocolError::UnsupportedProtocol(format!("unsupported VMess security: {}", security))),
        }
    }

    pub fn is_aead(&self) -> bool {
        matches!(self, Self::Aes128Gcm | Self::ChaCha20Poly1305)
    }
}

/// VMess packet encoding
#[derive(Debug, Clone, PartialEq)]
pub enum PacketEncoding {
    None,
    PacketAddr,
    Xudp,
}

impl PacketEncoding {
    pub fn from_str(encoding: &str) -> Result<Self, ProtocolError> {
        match encoding {
            "" => Ok(Self::None),
            "packetaddr" => Ok(Self::PacketAddr),
            "xudp" => Ok(Self::Xudp),
            _ => Err(ProtocolError::UnsupportedProtocol(format!("unknown packet encoding: {}", encoding))),
        }
    }
}

/// VMess user configuration
#[derive(Debug, Clone)]
pub struct VMessUser {
    pub name: String,
    pub uuid: Uuid,
    pub alter_id: u16,
}

impl VMessUser {
    pub fn new(name: &str, uuid: &str, alter_id: u16) -> Result<Self, ProtocolError> {
        let uuid = Uuid::parse_str(uuid)
            .map_err(|_| ProtocolError::InvalidOptions("invalid UUID format".to_string()))?;
        
        Ok(Self {
            name: name.to_string(),
            uuid,
            alter_id,
        })
    }
}

/// VMess client for outbound connections
pub struct VMessClient {
    uuid: Uuid,
    security: VMessSecurity,
    alter_id: u16,
    global_padding: bool,
    authenticated_length: bool,
}

impl VMessClient {
    pub fn new(
        uuid: &str,
        security: &str,
        alter_id: u16,
        global_padding: bool,
        authenticated_length: bool,
    ) -> Result<Self, ProtocolError> {
        let uuid = Uuid::parse_str(uuid)
            .map_err(|_| ProtocolError::InvalidOptions("invalid UUID format".to_string()))?;
        let security = VMessSecurity::from_str(security)?;

        Ok(Self {
            uuid,
            security,
            alter_id,
            global_padding,
            authenticated_length,
        })
    }

    /// Create VMess request header
    pub fn create_request_header(&self, target_addr: &str, target_port: u16, network: &str) -> Result<Vec<u8>, ProtocolError> {
        let mut header = Vec::new();

        // Version
        header.push(VMESS_VERSION);

        // Data IV (16 bytes)
        let mut data_iv = [0u8; 16];
        rand::thread_rng().fill_bytes(&mut data_iv);
        header.extend_from_slice(&data_iv);

        // Data Key (16 bytes)
        let mut data_key = [0u8; 16];
        rand::thread_rng().fill_bytes(&mut data_key);
        header.extend_from_slice(&data_key);

        // Response header
        header.push(0); // V (reserved)

        // Options
        let mut options = 0u8;
        if self.global_padding {
            options |= 0x04;
        }
        if self.authenticated_length {
            options |= 0x08;
        }
        header.push(options);

        // Padding and Security
        let padding_length = if self.global_padding {
            rand::thread_rng().next_u32() % 16
        } else {
            0
        };
        header.push((padding_length << 4) as u8 | self.get_security_type() as u8);

        // Reserved
        header.push(0);

        // Command (1 = TCP, 2 = UDP)
        let command = match network {
            "tcp" => 1u8,
            "udp" => 2u8,
            _ => return Err(ProtocolError::UnsupportedProtocol(format!("unsupported network: {}", network))),
        };
        header.push(command);

        // Port (2 bytes, big endian)
        header.extend_from_slice(&target_port.to_be_bytes());

        // Address
        if let Ok(ipv4) = target_addr.parse::<Ipv4Addr>() {
            header.push(1); // IPv4
            header.extend_from_slice(&ipv4.octets());
        } else if let Ok(ipv6) = target_addr.parse::<Ipv6Addr>() {
            header.push(3); // IPv6
            header.extend_from_slice(&ipv6.octets());
        } else {
            // Domain name
            header.push(2); // Domain
            header.push(target_addr.len() as u8);
            header.extend_from_slice(target_addr.as_bytes());
        }

        // Padding
        if padding_length > 0 {
            let mut padding = vec![0u8; padding_length as usize];
            rand::thread_rng().fill_bytes(&mut padding);
            header.extend_from_slice(&padding);
        }

        // Checksum (FNV-1a hash)
        let checksum = self.calculate_fnv1a_hash(&header);
        header.extend_from_slice(&checksum.to_be_bytes());

        Ok(header)
    }

    fn get_security_type(&self) -> u8 {
        match self.security {
            VMessSecurity::None => 0,
            VMessSecurity::Aes128Gcm => 3,
            VMessSecurity::ChaCha20Poly1305 => 4,
            VMessSecurity::Aes128Ctr => 1,
            VMessSecurity::Zero => 0,
            VMessSecurity::Auto => 0, // Will be determined at runtime
        }
    }

    fn calculate_fnv1a_hash(&self, data: &[u8]) -> u32 {
        let mut hash = 0x811c9dc5u32;
        for &byte in data {
            hash ^= byte as u32;
            hash = hash.wrapping_mul(0x01000193);
        }
        hash
    }

    /// Encrypt VMess request
    pub fn encrypt_request(&self, header: &[u8]) -> Result<Vec<u8>, ProtocolError> {
        // Generate timestamp
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        // Create auth info
        let mut auth_info = Vec::new();
        auth_info.extend_from_slice(&timestamp.to_be_bytes());
        auth_info.extend_from_slice(&[0u8; 4]); // Random
        auth_info.extend_from_slice(&[0u8; 4]); // CRC32
        auth_info.push(header.len() as u8);
        auth_info.extend_from_slice(header);

        // Calculate HMAC
        let key = self.generate_auth_key(timestamp);
        let hmac = self.calculate_hmac(&key, &auth_info);

        let mut encrypted = Vec::new();
        encrypted.extend_from_slice(&hmac);
        encrypted.extend_from_slice(&auth_info);

        Ok(encrypted)
    }

    fn generate_auth_key(&self, timestamp: u64) -> [u8; 16] {
        let mut hasher = Sha256::new();
        hasher.update(self.uuid.as_bytes());
        hasher.update(b"c48619fe-8f02-49e0-b9e9-edf763e17e21");
        hasher.update(&timestamp.to_be_bytes());
        let hash = hasher.finalize();
        let mut key = [0u8; 16];
        key.copy_from_slice(&hash[..16]);
        key
    }

    fn calculate_hmac(&self, key: &[u8], data: &[u8]) -> [u8; 16] {
        use hmac::{Hmac, Mac};
        type HmacSha256 = Hmac<Sha256>;
        
        let mut mac = <HmacSha256 as hmac::Mac>::new_from_slice(key).expect("HMAC key error");
        mac.update(data);
        let result = mac.finalize().into_bytes();
        let mut hmac = [0u8; 16];
        hmac.copy_from_slice(&result[..16]);
        hmac
    }
}

/// VMess inbound handler
pub struct VMessInbound {
    tag: String,
    listen_addr: SocketAddr,
    users: HashMap<Uuid, VMessUser>,
}

impl VMessInbound {
    pub fn new(tag: String, listen_addr: SocketAddr, users: Vec<VMessUser>) -> Self {
        let user_map = users.into_iter()
            .map(|user| (user.uuid, user))
            .collect();

        Self {
            tag,
            listen_addr,
            users: user_map,
        }
    }

    /// Handle incoming VMess connection
    pub async fn handle_connection(&self, mut stream: TcpStream) -> Result<(), ProtocolError> {
        // Read and decrypt request header
        let (user, request) = self.read_request_header(&mut stream).await?;
        
        // Connect to target
        let target_addr = format!("{}:{}", request.address, request.port);
        let mut target_stream = TcpStream::connect(&target_addr).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to {}: {}", target_addr, e)))?;

        // Send response header
        self.send_response_header(&mut stream, &user).await?;

        // Start bidirectional forwarding
        let (mut client_read, mut client_write) = stream.split();
        let (mut target_read, mut target_write) = target_stream.split();

        let forward1 = tokio::io::copy(&mut client_read, &mut target_write);
        let forward2 = tokio::io::copy(&mut target_read, &mut client_write);

        // Wait for either direction to close
        tokio::select! {
            result1 = forward1 => {
                if let Err(e) = result1 {
                    eprintln!("Forward client->target error: {}", e);
                }
            }
            result2 = forward2 => {
                if let Err(e) = result2 {
                    eprintln!("Forward target->client error: {}", e);
                }
            }
        }

        Ok(())
    }

    async fn read_request_header(&self, stream: &mut TcpStream) -> Result<(VMessUser, VMessRequest), ProtocolError> {
        // Read auth info (16 bytes HMAC + variable length data)
        let mut auth_buffer = [0u8; 16];
        stream.read_exact(&mut auth_buffer).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read auth: {}", e)))?;

        // For simplicity, we'll implement a basic version
        // In a full implementation, this would verify the HMAC and decrypt the request
        
        // Read timestamp and other auth data
        let mut timestamp_buf = [0u8; 8];
        stream.read_exact(&mut timestamp_buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read timestamp: {}", e)))?;

        // Skip random and CRC32
        let mut skip_buf = [0u8; 8];
        stream.read_exact(&mut skip_buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read skip: {}", e)))?;

        // Read header length
        let mut len_buf = [0u8; 1];
        stream.read_exact(&mut len_buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read header length: {}", e)))?;
        let header_len = len_buf[0] as usize;

        // Read header
        let mut header_buf = vec![0u8; header_len];
        stream.read_exact(&mut header_buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read header: {}", e)))?;

        // Parse header (simplified)
        let request = self.parse_request_header(&header_buf)?;
        
        // For demo, use the first user
        let user = self.users.values().next()
            .ok_or_else(|| ProtocolError::AuthenticationFailed)?
            .clone();

        Ok((user, request))
    }

    fn parse_request_header(&self, header: &[u8]) -> Result<VMessRequest, ProtocolError> {
        if header.len() < 41 {
            return Err(ProtocolError::ProtocolViolation("header too short".to_string()));
        }

        let mut offset = 0;

        // Skip version, IV, key, response header, options, padding/security, reserved
        offset += 1 + 16 + 16 + 1 + 1 + 1 + 1;

        // Command
        let command = header[offset];
        offset += 1;

        // Port
        let port = u16::from_be_bytes([header[offset], header[offset + 1]]);
        offset += 2;

        // Address
        let address_type = header[offset];
        offset += 1;

        let address = match address_type {
            1 => {
                // IPv4
                let addr = Ipv4Addr::from([header[offset], header[offset + 1], header[offset + 2], header[offset + 3]]);
                addr.to_string()
            }
            2 => {
                // Domain
                let domain_len = header[offset] as usize;
                offset += 1;
                String::from_utf8(header[offset..offset + domain_len].to_vec())
                    .map_err(|_| ProtocolError::ProtocolViolation("invalid domain encoding".to_string()))?
            }
            3 => {
                // IPv6
                let mut addr_bytes = [0u8; 16];
                addr_bytes.copy_from_slice(&header[offset..offset + 16]);
                let addr = Ipv6Addr::from(addr_bytes);
                addr.to_string()
            }
            _ => return Err(ProtocolError::ProtocolViolation("invalid address type".to_string())),
        };

        Ok(VMessRequest {
            command,
            address,
            port,
        })
    }

    async fn send_response_header(&self, stream: &mut TcpStream, _user: &VMessUser) -> Result<(), ProtocolError> {
        // Send simple response header
        let response = [VMESS_VERSION, 0]; // Version + options
        stream.write_all(&response).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("write response: {}", e)))?;
        Ok(())
    }
}

/// VMess request structure
#[derive(Debug)]
struct VMessRequest {
    command: u8,
    address: String,
    port: u16,
}

impl Adapter for VMessInbound {
    fn adapter_type(&self) -> &str {
        "vmess"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

#[async_trait]
impl Lifecycle for VMessInbound {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        println!("VMess inbound {} starting", self.tag);
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        println!("VMess inbound {} closing", self.tag);
        Ok(())
    }
}

impl Inbound for VMessInbound {
    // Inherits from Adapter and Lifecycle
}

/// VMess outbound handler
pub struct VMessOutbound {
    tag: String,
    server_addr: SocketAddr,
    client: VMessClient,
    packet_encoding: PacketEncoding,
}

impl VMessOutbound {
    pub fn new(
        tag: String,
        server_addr: SocketAddr,
        uuid: &str,
        security: &str,
        alter_id: u16,
        global_padding: bool,
        authenticated_length: bool,
        packet_encoding: &str,
    ) -> Result<Self, ProtocolError> {
        let client = VMessClient::new(uuid, security, alter_id, global_padding, authenticated_length)?;
        let packet_encoding = PacketEncoding::from_str(packet_encoding)?;

        Ok(Self {
            tag,
            server_addr,
            client,
            packet_encoding,
        })
    }

    /// Dial through VMess proxy
    pub async fn dial(&self, target_host: &str, target_port: u16, network: &str) -> Result<TcpStream, ProtocolError> {
        // Connect to VMess server
        let mut stream = TcpStream::connect(&self.server_addr).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to server: {}", e)))?;

        // Create and send request header
        let header = self.client.create_request_header(target_host, target_port, network)?;
        let encrypted_header = self.client.encrypt_request(&header)?;
        
        stream.write_all(&encrypted_header).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("write request: {}", e)))?;

        // Read response header
        let mut response = [0u8; 2];
        stream.read_exact(&mut response).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read response: {}", e)))?;

        if response[0] != VMESS_VERSION {
            return Err(ProtocolError::ProtocolViolation("invalid response version".to_string()));
        }

        Ok(stream)
    }
}

impl Adapter for VMessOutbound {
    fn adapter_type(&self) -> &str {
        "vmess"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Outbound for VMessOutbound {
    fn network(&self) -> Vec<String> {
        vec!["tcp".to_string(), "udp".to_string()]
    }

    fn dependencies(&self) -> Vec<String> {
        vec![]
    }

    fn dial(&self, _network: &str, _destination: &str) -> Result<(), String> {
        // This is a simplified interface - in a real implementation,
        // this would return a connection or be async
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_vmess_security_from_str() {
        assert_eq!(VMessSecurity::from_str("auto").unwrap(), VMessSecurity::Auto);
        assert_eq!(VMessSecurity::from_str("aes-128-gcm").unwrap(), VMessSecurity::Aes128Gcm);
        assert_eq!(VMessSecurity::from_str("chacha20-poly1305").unwrap(), VMessSecurity::ChaCha20Poly1305);
        assert!(VMessSecurity::from_str("invalid").is_err());
    }

    #[test]
    fn test_packet_encoding_from_str() {
        assert_eq!(PacketEncoding::from_str("").unwrap(), PacketEncoding::None);
        assert_eq!(PacketEncoding::from_str("packetaddr").unwrap(), PacketEncoding::PacketAddr);
        assert_eq!(PacketEncoding::from_str("xudp").unwrap(), PacketEncoding::Xudp);
        assert!(PacketEncoding::from_str("invalid").is_err());
    }

    #[test]
    fn test_vmess_user_creation() {
        let user = VMessUser::new("test", "550e8400-e29b-41d4-a716-************", 0).unwrap();
        assert_eq!(user.name, "test");
        assert_eq!(user.alter_id, 0);
        assert!(VMessUser::new("test", "invalid-uuid", 0).is_err());
    }

    #[test]
    fn test_vmess_client_creation() {
        let client = VMessClient::new(
            "550e8400-e29b-41d4-a716-************",
            "aes-128-gcm",
            0,
            false,
            true,
        ).unwrap();
        
        assert_eq!(client.security, VMessSecurity::Aes128Gcm);
        assert_eq!(client.alter_id, 0);
        assert!(!client.global_padding);
        assert!(client.authenticated_length);
    }

    #[test]
    fn test_vmess_request_header_creation() {
        let client = VMessClient::new(
            "550e8400-e29b-41d4-a716-************",
            "aes-128-gcm",
            0,
            false,
            false,
        ).unwrap();

        let header = client.create_request_header("example.com", 443, "tcp").unwrap();
        assert!(!header.is_empty());
        assert_eq!(header[0], VMESS_VERSION);
    }
}
