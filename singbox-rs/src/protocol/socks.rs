use std::collections::HashMap;
use std::net::{SocketAddr, Ipv4Addr, Ipv6Addr};
// 异步I/O导入 - 保留用于完整SOCKS协议实现
#[allow(unused_imports)] // 预留用于高级I/O操作
use tokio::io::{AsyncRead, AsyncReadExt, AsyncWrite, AsyncWriteExt};
use tokio::net::TcpStream;

use async_trait::async_trait;
use crate::adapter::{Adapter, Inbound, Outbound, Lifecycle, StartStage};
use crate::protocol::ProtocolError;

/// SOCKS protocol versions
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum SocksVersion {
    V4 = 4,
    V5 = 5,
}

/// SOCKS5 authentication methods
#[derive(Debug, <PERSON>lone, Copy, PartialEq)]
pub enum AuthMethod {
    NoAuth = 0x00,
    UserPass = 0x02,
    NoAcceptable = 0xFF,
}

/// SOCKS5 command types
#[derive(Debug, <PERSON>lone, Copy, PartialEq)]
pub enum SocksCommand {
    Connect = 0x01,
    Bind = 0x02,
    UdpAssociate = 0x03,
}

/// SOCKS5 address types
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum AddressType {
    IPv4 = 0x01,
    Domain = 0x03,
    IPv6 = 0x04,
}

/// SOCKS5 reply codes
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum ReplyCode {
    Success = 0x00,
    GeneralFailure = 0x01,
    ConnectionNotAllowed = 0x02,
    NetworkUnreachable = 0x03,
    HostUnreachable = 0x04,
    ConnectionRefused = 0x05,
    TtlExpired = 0x06,
    CommandNotSupported = 0x07,
    AddressTypeNotSupported = 0x08,
}

/// SOCKS proxy inbound handler
pub struct SocksInbound {
    tag: String,
    listen_addr: SocketAddr,
    version: SocksVersion,
    users: HashMap<String, String>, // username -> password
}

impl SocksInbound {
    pub fn new(
        tag: String,
        listen_addr: SocketAddr,
        version: SocksVersion,
        users: HashMap<String, String>,
    ) -> Self {
        Self {
            tag,
            listen_addr,
            version,
            users,
        }
    }

    /// Handle SOCKS5 connection
    pub async fn handle_socks5_connection(&self, mut stream: TcpStream) -> Result<(), ProtocolError> {
        // 1. Authentication negotiation
        let auth_method = self.negotiate_auth(&mut stream).await?;
        
        // 2. User authentication (if required)
        if auth_method == AuthMethod::UserPass {
            self.authenticate_user(&mut stream).await?;
        }
        
        // 3. Handle SOCKS5 request
        let (command, target_addr, target_port) = self.read_socks5_request(&mut stream).await?;
        
        match command {
            SocksCommand::Connect => {
                self.handle_connect(&mut stream, &target_addr, target_port).await?;
            }
            SocksCommand::UdpAssociate => {
                self.handle_udp_associate(&mut stream).await?;
            }
            _ => {
                self.send_socks5_reply(&mut stream, ReplyCode::CommandNotSupported, "0.0.0.0", 0).await?;
                return Err(ProtocolError::UnsupportedProtocol("unsupported SOCKS command".to_string()));
            }
        }
        
        Ok(())
    }

    /// Negotiate authentication method
    async fn negotiate_auth(&self, stream: &mut TcpStream) -> Result<AuthMethod, ProtocolError> {
        // Read version and number of methods
        let mut buf = [0u8; 2];
        stream.read_exact(&mut buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read auth negotiation: {}", e)))?;
        
        let version = buf[0];
        let nmethods = buf[1];
        
        if version != 5 {
            return Err(ProtocolError::ProtocolViolation("invalid SOCKS version".to_string()));
        }
        
        // Read methods
        let mut methods = vec![0u8; nmethods as usize];
        stream.read_exact(&mut methods).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read auth methods: {}", e)))?;
        
        // Choose authentication method
        let chosen_method = if self.users.is_empty() {
            // No authentication required
            if methods.contains(&(AuthMethod::NoAuth as u8)) {
                AuthMethod::NoAuth
            } else {
                AuthMethod::NoAcceptable
            }
        } else {
            // Username/password authentication required
            if methods.contains(&(AuthMethod::UserPass as u8)) {
                AuthMethod::UserPass
            } else {
                AuthMethod::NoAcceptable
            }
        };
        
        // Send method selection response
        let response = [5u8, chosen_method as u8];
        stream.write_all(&response).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("write auth response: {}", e)))?;
        
        if chosen_method == AuthMethod::NoAcceptable {
            return Err(ProtocolError::AuthenticationFailed);
        }
        
        Ok(chosen_method)
    }

    /// Authenticate user with username/password
    async fn authenticate_user(&self, stream: &mut TcpStream) -> Result<(), ProtocolError> {
        // Read version
        let mut buf = [0u8; 1];
        stream.read_exact(&mut buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read auth version: {}", e)))?;
        
        if buf[0] != 1 {
            return Err(ProtocolError::ProtocolViolation("invalid auth version".to_string()));
        }
        
        // Read username length and username
        stream.read_exact(&mut buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read username length: {}", e)))?;
        let username_len = buf[0] as usize;
        
        let mut username_buf = vec![0u8; username_len];
        stream.read_exact(&mut username_buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read username: {}", e)))?;
        let username = String::from_utf8(username_buf)
            .map_err(|_| ProtocolError::ProtocolViolation("invalid username encoding".to_string()))?;
        
        // Read password length and password
        stream.read_exact(&mut buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read password length: {}", e)))?;
        let password_len = buf[0] as usize;
        
        let mut password_buf = vec![0u8; password_len];
        stream.read_exact(&mut password_buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read password: {}", e)))?;
        let password = String::from_utf8(password_buf)
            .map_err(|_| ProtocolError::ProtocolViolation("invalid password encoding".to_string()))?;
        
        // Verify credentials
        let auth_success = self.users.get(&username)
            .map(|stored_password| stored_password == &password)
            .unwrap_or(false);
        
        // Send authentication response
        let response = [1u8, if auth_success { 0 } else { 1 }];
        stream.write_all(&response).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("write auth result: {}", e)))?;
        
        if !auth_success {
            return Err(ProtocolError::AuthenticationFailed);
        }
        
        Ok(())
    }

    /// Read SOCKS5 request
    async fn read_socks5_request(&self, stream: &mut TcpStream) -> Result<(SocksCommand, String, u16), ProtocolError> {
        // Read request header
        let mut buf = [0u8; 4];
        stream.read_exact(&mut buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read request header: {}", e)))?;
        
        let version = buf[0];
        let command = buf[1];
        let _reserved = buf[2];
        let addr_type = buf[3];
        
        if version != 5 {
            return Err(ProtocolError::ProtocolViolation("invalid SOCKS version".to_string()));
        }
        
        let command = match command {
            1 => SocksCommand::Connect,
            2 => SocksCommand::Bind,
            3 => SocksCommand::UdpAssociate,
            _ => return Err(ProtocolError::ProtocolViolation("invalid command".to_string())),
        };
        
        // Read address
        let (target_addr, target_port) = match addr_type {
            0x01 => {
                // IPv4
                let mut addr_buf = [0u8; 4];
                stream.read_exact(&mut addr_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read IPv4 address: {}", e)))?;
                let addr = Ipv4Addr::from(addr_buf);
                
                let mut port_buf = [0u8; 2];
                stream.read_exact(&mut port_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read port: {}", e)))?;
                let port = u16::from_be_bytes(port_buf);
                
                (addr.to_string(), port)
            }
            0x03 => {
                // Domain name
                let mut len_buf = [0u8; 1];
                stream.read_exact(&mut len_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read domain length: {}", e)))?;
                let domain_len = len_buf[0] as usize;
                
                let mut domain_buf = vec![0u8; domain_len];
                stream.read_exact(&mut domain_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read domain: {}", e)))?;
                let domain = String::from_utf8(domain_buf)
                    .map_err(|_| ProtocolError::ProtocolViolation("invalid domain encoding".to_string()))?;
                
                let mut port_buf = [0u8; 2];
                stream.read_exact(&mut port_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read port: {}", e)))?;
                let port = u16::from_be_bytes(port_buf);
                
                (domain, port)
            }
            0x04 => {
                // IPv6
                let mut addr_buf = [0u8; 16];
                stream.read_exact(&mut addr_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read IPv6 address: {}", e)))?;
                let addr = Ipv6Addr::from(addr_buf);
                
                let mut port_buf = [0u8; 2];
                stream.read_exact(&mut port_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read port: {}", e)))?;
                let port = u16::from_be_bytes(port_buf);
                
                (addr.to_string(), port)
            }
            _ => return Err(ProtocolError::ProtocolViolation("invalid address type".to_string())),
        };
        
        Ok((command, target_addr, target_port))
    }

    /// Send SOCKS5 reply
    async fn send_socks5_reply(
        &self,
        stream: &mut TcpStream,
        reply_code: ReplyCode,
        _bind_addr: &str, // 预留用于完整SOCKS响应实现
        bind_port: u16,
    ) -> Result<(), ProtocolError> {
        let mut response = vec![5u8, reply_code as u8, 0u8]; // version, reply, reserved
        
        // Add bind address (simplified - always use IPv4 0.0.0.0)
        response.push(0x01); // IPv4
        response.extend_from_slice(&[0, 0, 0, 0]); // 0.0.0.0
        response.extend_from_slice(&bind_port.to_be_bytes()); // port
        
        stream.write_all(&response).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("write reply: {}", e)))?;
        
        Ok(())
    }

    /// Handle CONNECT command
    async fn handle_connect(
        &self,
        stream: &mut TcpStream,
        target_addr: &str,
        target_port: u16,
    ) -> Result<(), ProtocolError> {
        // Connect to target
        let target = format!("{}:{}", target_addr, target_port);
        let mut target_stream = TcpStream::connect(&target).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to {}: {}", target, e)))?;

        // Send success reply
        self.send_socks5_reply(stream, ReplyCode::Success, "0.0.0.0", 0).await?;

        // Start bidirectional forwarding
        let (mut client_read, mut client_write) = stream.split();
        let (mut target_read, mut target_write) = target_stream.split();

        let forward1 = tokio::io::copy(&mut client_read, &mut target_write);
        let forward2 = tokio::io::copy(&mut target_read, &mut client_write);

        // Wait for either direction to close
        tokio::select! {
            result1 = forward1 => {
                if let Err(e) = result1 {
                    eprintln!("Forward client->target error: {}", e);
                }
            }
            result2 = forward2 => {
                if let Err(e) = result2 {
                    eprintln!("Forward target->client error: {}", e);
                }
            }
        }

        Ok(())
    }

    /// Handle UDP ASSOCIATE command
    async fn handle_udp_associate(&self, stream: &mut TcpStream) -> Result<(), ProtocolError> {
        // For now, just send a success reply with a dummy UDP port
        // In a full implementation, this would set up UDP relay
        self.send_socks5_reply(stream, ReplyCode::Success, "0.0.0.0", 1080).await?;
        
        // Keep the TCP connection alive for UDP association
        let mut buf = [0u8; 1];
        let _ = stream.read(&mut buf).await; // Wait for client to close
        
        Ok(())
    }

    /// Handle incoming connection
    pub async fn handle_connection(&self, stream: TcpStream) -> Result<(), ProtocolError> {
        match self.version {
            SocksVersion::V5 => self.handle_socks5_connection(stream).await,
            SocksVersion::V4 => {
                // TODO: Implement SOCKS4 support
                Err(ProtocolError::UnsupportedProtocol("SOCKS4 not yet implemented".to_string()))
            }
        }
    }
}

impl Adapter for SocksInbound {
    fn adapter_type(&self) -> &str {
        "socks"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

#[async_trait]
impl Lifecycle for SocksInbound {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        println!("SOCKS inbound {} starting", self.tag);
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        println!("SOCKS inbound {} closing", self.tag);
        Ok(())
    }
}

impl Inbound for SocksInbound {
    // Inherits from Adapter and Lifecycle
}

/// SOCKS proxy outbound handler
pub struct SocksOutbound {
    tag: String,
    server_addr: SocketAddr,
    version: SocksVersion,
    username: Option<String>,
    password: Option<String>,
}

impl SocksOutbound {
    pub fn new(
        tag: String,
        server_addr: SocketAddr,
        version: SocksVersion,
        username: Option<String>,
        password: Option<String>,
    ) -> Self {
        Self {
            tag,
            server_addr,
            version,
            username,
            password,
        }
    }

    /// Dial through SOCKS5 proxy
    pub async fn dial_socks5(&self, target_host: &str, target_port: u16) -> Result<TcpStream, ProtocolError> {
        let mut stream = TcpStream::connect(&self.server_addr).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to proxy: {}", e)))?;

        // 1. Authentication negotiation
        let auth_methods = if self.username.is_some() && self.password.is_some() {
            vec![AuthMethod::UserPass as u8]
        } else {
            vec![AuthMethod::NoAuth as u8]
        };

        let mut request = vec![5u8, auth_methods.len() as u8];
        request.extend_from_slice(&auth_methods);
        
        stream.write_all(&request).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("write auth negotiation: {}", e)))?;

        // Read server response
        let mut buf = [0u8; 2];
        stream.read_exact(&mut buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read auth response: {}", e)))?;

        if buf[0] != 5 {
            return Err(ProtocolError::ProtocolViolation("invalid server response".to_string()));
        }

        let chosen_method = buf[1];
        if chosen_method == AuthMethod::NoAcceptable as u8 {
            return Err(ProtocolError::AuthenticationFailed);
        }

        // 2. User authentication (if required)
        if chosen_method == AuthMethod::UserPass as u8 {
            if let (Some(username), Some(password)) = (&self.username, &self.password) {
                let mut auth_request = vec![1u8]; // auth version
                auth_request.push(username.len() as u8);
                auth_request.extend_from_slice(username.as_bytes());
                auth_request.push(password.len() as u8);
                auth_request.extend_from_slice(password.as_bytes());

                stream.write_all(&auth_request).await
                    .map_err(|e| ProtocolError::ProtocolViolation(format!("write auth: {}", e)))?;

                let mut auth_response = [0u8; 2];
                stream.read_exact(&mut auth_response).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read auth result: {}", e)))?;

                if auth_response[1] != 0 {
                    return Err(ProtocolError::AuthenticationFailed);
                }
            } else {
                return Err(ProtocolError::InvalidOptions("username/password required".to_string()));
            }
        }

        // 3. Send CONNECT request
        let mut connect_request = vec![5u8, 1u8, 0u8]; // version, connect, reserved

        // Add target address
        if let Ok(ipv4) = target_host.parse::<Ipv4Addr>() {
            connect_request.push(0x01); // IPv4
            connect_request.extend_from_slice(&ipv4.octets());
        } else if let Ok(ipv6) = target_host.parse::<Ipv6Addr>() {
            connect_request.push(0x04); // IPv6
            connect_request.extend_from_slice(&ipv6.octets());
        } else {
            // Domain name
            connect_request.push(0x03); // Domain
            connect_request.push(target_host.len() as u8);
            connect_request.extend_from_slice(target_host.as_bytes());
        }

        // Add target port
        connect_request.extend_from_slice(&target_port.to_be_bytes());

        stream.write_all(&connect_request).await
            .map_err(|e| ProtocolError::ProtocolViolation(format!("write connect request: {}", e)))?;

        // Read connect response
        let mut response_buf = [0u8; 4];
        stream.read_exact(&mut response_buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read connect response: {}", e)))?;

        if response_buf[0] != 5 {
            return Err(ProtocolError::ProtocolViolation("invalid response version".to_string()));
        }

        if response_buf[1] != 0 {
            return Err(ProtocolError::ConnectionFailed(format!("SOCKS error: {}", response_buf[1])));
        }

        // Skip bind address and port (we don't need them for CONNECT)
        let addr_type = response_buf[3];
        let skip_len = match addr_type {
            0x01 => 6, // IPv4 (4 bytes) + port (2 bytes)
            0x04 => 18, // IPv6 (16 bytes) + port (2 bytes)
            0x03 => {
                // Domain name - read length first
                let mut len_buf = [0u8; 1];
                stream.read_exact(&mut len_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read domain length: {}", e)))?;
                len_buf[0] as usize + 2 // domain length + port (2 bytes)
            }
            _ => return Err(ProtocolError::ProtocolViolation("invalid address type in response".to_string())),
        };

        let mut skip_buf = vec![0u8; skip_len];
        stream.read_exact(&mut skip_buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read bind address: {}", e)))?;

        Ok(stream)
    }

    /// Dial through SOCKS proxy
    pub async fn dial(&self, target_host: &str, target_port: u16) -> Result<TcpStream, ProtocolError> {
        match self.version {
            SocksVersion::V5 => self.dial_socks5(target_host, target_port).await,
            SocksVersion::V4 => {
                // TODO: Implement SOCKS4 support
                Err(ProtocolError::UnsupportedProtocol("SOCKS4 not yet implemented".to_string()))
            }
        }
    }
}

impl Adapter for SocksOutbound {
    fn adapter_type(&self) -> &str {
        "socks"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

impl Outbound for SocksOutbound {
    fn network(&self) -> Vec<String> {
        vec!["tcp".to_string(), "udp".to_string()]
    }

    fn dependencies(&self) -> Vec<String> {
        vec![]
    }

    fn dial(&self, _network: &str, _destination: &str) -> Result<(), String> {
        // This is a simplified interface - in a real implementation,
        // this would return a connection or be async
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_socks_version() {
        assert_eq!(SocksVersion::V4 as u8, 4);
        assert_eq!(SocksVersion::V5 as u8, 5);
    }

    #[test]
    fn test_auth_methods() {
        assert_eq!(AuthMethod::NoAuth as u8, 0x00);
        assert_eq!(AuthMethod::UserPass as u8, 0x02);
        assert_eq!(AuthMethod::NoAcceptable as u8, 0xFF);
    }

    #[test]
    fn test_socks_commands() {
        assert_eq!(SocksCommand::Connect as u8, 0x01);
        assert_eq!(SocksCommand::Bind as u8, 0x02);
        assert_eq!(SocksCommand::UdpAssociate as u8, 0x03);
    }
}
