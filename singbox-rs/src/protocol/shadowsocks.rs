use std::collections::HashMap;
use std::net::{SocketAddr, Ipv4Addr, Ipv6Addr};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use aes_gcm::{Aes128Gcm, Aes256Gcm, Key, Nonce, AeadInPlace, KeyInit};
use chacha20poly1305::{ChaCha20Poly1305, XChaCha20Poly1305};
#[allow(unused_imports)] // 预留用于MD5密钥派生支持
use md5::Digest;

use async_trait::async_trait;
use crate::adapter::{Adapter, Inbound, Outbound, Lifecycle, StartStage};
use crate::protocol::ProtocolError;

/// Shadowsocks encryption methods
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum ShadowsocksMethod {
    // AEAD methods
    Aes128Gcm,
    Aes256Gcm,
    ChaCha20<PERSON>oly1305,
    XChaCha20Poly1305,
    
    // 2022 methods
    Aes128Gcm2022,
    Aes256Gcm2022,
    ChaCha20Poly13052022,
    
    // Legacy methods (deprecated but still supported)
    Aes128Cfb,
    Aes192Cfb,
    Aes256Cfb,
    Aes128Ctr,
    Aes192Ctr,
    Aes256Ctr,
    ChaCha20Ietf,
    XChaCha20,
    Rc4Md5,
    
    // No encryption
    None,
}

impl ShadowsocksMethod {
    pub fn from_str(method: &str) -> Result<Self, ProtocolError> {
        match method.to_lowercase().as_str() {
            "aes-128-gcm" => Ok(Self::Aes128Gcm),
            "aes-256-gcm" => Ok(Self::Aes256Gcm),
            "chacha20-ietf-poly1305" => Ok(Self::ChaCha20Poly1305),
            "xchacha20-ietf-poly1305" => Ok(Self::XChaCha20Poly1305),
            "2022-blake3-aes-128-gcm" => Ok(Self::Aes128Gcm2022),
            "2022-blake3-aes-256-gcm" => Ok(Self::Aes256Gcm2022),
            "2022-blake3-chacha20-poly1305" => Ok(Self::ChaCha20Poly13052022),
            "aes-128-cfb" => Ok(Self::Aes128Cfb),
            "aes-192-cfb" => Ok(Self::Aes192Cfb),
            "aes-256-cfb" => Ok(Self::Aes256Cfb),
            "aes-128-ctr" => Ok(Self::Aes128Ctr),
            "aes-192-ctr" => Ok(Self::Aes192Ctr),
            "aes-256-ctr" => Ok(Self::Aes256Ctr),
            "chacha20-ietf" => Ok(Self::ChaCha20Ietf),
            "xchacha20" => Ok(Self::XChaCha20),
            "rc4-md5" => Ok(Self::Rc4Md5),
            "none" => Ok(Self::None),
            _ => Err(ProtocolError::UnsupportedProtocol(format!("unsupported method: {}", method))),
        }
    }

    pub fn key_size(&self) -> usize {
        match self {
            Self::Aes128Gcm | Self::Aes128Gcm2022 | Self::Aes128Cfb | Self::Aes128Ctr => 16,
            Self::Aes192Cfb | Self::Aes192Ctr => 24,
            Self::Aes256Gcm | Self::Aes256Gcm2022 | Self::Aes256Cfb | Self::Aes256Ctr => 32,
            Self::ChaCha20Poly1305 | Self::ChaCha20Poly13052022 | Self::XChaCha20Poly1305 | 
            Self::ChaCha20Ietf | Self::XChaCha20 => 32,
            Self::Rc4Md5 => 16,
            Self::None => 0,
        }
    }

    pub fn nonce_size(&self) -> usize {
        match self {
            Self::Aes128Gcm | Self::Aes256Gcm | Self::Aes128Gcm2022 | Self::Aes256Gcm2022 => 12,
            Self::ChaCha20Poly1305 | Self::ChaCha20Poly13052022 => 12,
            Self::XChaCha20Poly1305 => 24,
            Self::ChaCha20Ietf => 12,
            Self::XChaCha20 => 24,
            Self::Aes128Cfb | Self::Aes192Cfb | Self::Aes256Cfb |
            Self::Aes128Ctr | Self::Aes192Ctr | Self::Aes256Ctr => 16,
            Self::Rc4Md5 => 16,
            Self::None => 0,
        }
    }

    pub fn tag_size(&self) -> usize {
        match self {
            Self::Aes128Gcm | Self::Aes256Gcm | Self::Aes128Gcm2022 | Self::Aes256Gcm2022 |
            Self::ChaCha20Poly1305 | Self::ChaCha20Poly13052022 | Self::XChaCha20Poly1305 => 16,
            _ => 0,
        }
    }

    pub fn is_aead(&self) -> bool {
        matches!(self, 
            Self::Aes128Gcm | Self::Aes256Gcm | Self::Aes128Gcm2022 | Self::Aes256Gcm2022 |
            Self::ChaCha20Poly1305 | Self::ChaCha20Poly13052022 | Self::XChaCha20Poly1305
        )
    }
}

/// Shadowsocks cipher
pub struct ShadowsocksCipher {
    method: ShadowsocksMethod,
    key: Vec<u8>,
}

impl ShadowsocksCipher {
    pub fn new(method: ShadowsocksMethod, password: &str) -> Result<Self, ProtocolError> {
        let key = Self::derive_key(&method, password)?;
        Ok(Self { method, key })
    }

    /// Derive key from password using method-specific KDF
    fn derive_key(method: &ShadowsocksMethod, password: &str) -> Result<Vec<u8>, ProtocolError> {
        let key_size = method.key_size();
        if key_size == 0 {
            return Ok(vec![]);
        }

        match method {
            ShadowsocksMethod::Aes128Gcm2022 | ShadowsocksMethod::Aes256Gcm2022 | 
            ShadowsocksMethod::ChaCha20Poly13052022 => {
                // 2022 methods use base64-decoded password as key
                use base64::{Engine as _, engine::general_purpose};
                general_purpose::STANDARD.decode(password)
                    .map_err(|_| ProtocolError::InvalidOptions("invalid base64 password for 2022 method".to_string()))
            }
            _ => {
                // Legacy methods use EVP_BytesToKey
                Ok(Self::evp_bytes_to_key(password.as_bytes(), key_size))
            }
        }
    }

    /// EVP_BytesToKey implementation for legacy methods
    fn evp_bytes_to_key(password: &[u8], key_len: usize) -> Vec<u8> {
        let mut key = Vec::new();
        let mut hasher_input = Vec::new();

        while key.len() < key_len {
            hasher_input.extend_from_slice(password);
            let hash = md5::compute(&hasher_input);
            key.extend_from_slice(&hash.0);
            hasher_input = hash.0.to_vec();
        }

        key.truncate(key_len);
        key
    }

    /// Encrypt data (simplified AEAD implementation)
    pub fn encrypt(&self, plaintext: &[u8], nonce: &[u8]) -> Result<Vec<u8>, ProtocolError> {
        if !self.method.is_aead() {
            return Err(ProtocolError::UnsupportedProtocol("non-AEAD methods not implemented".to_string()));
        }

        match self.method {
            ShadowsocksMethod::Aes128Gcm | ShadowsocksMethod::Aes128Gcm2022 => {
                let cipher = Aes128Gcm::new(Key::<Aes128Gcm>::from_slice(&self.key));
                let nonce = Nonce::from_slice(nonce);
                let mut buffer = plaintext.to_vec();
                cipher.encrypt_in_place(nonce, b"", &mut buffer)
                    .map_err(|_| ProtocolError::EncryptionFailed)?;
                Ok(buffer)
            }
            ShadowsocksMethod::Aes256Gcm | ShadowsocksMethod::Aes256Gcm2022 => {
                let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(&self.key));
                let nonce = Nonce::from_slice(nonce);
                let mut buffer = plaintext.to_vec();
                cipher.encrypt_in_place(nonce, b"", &mut buffer)
                    .map_err(|_| ProtocolError::EncryptionFailed)?;
                Ok(buffer)
            }
            ShadowsocksMethod::ChaCha20Poly1305 | ShadowsocksMethod::ChaCha20Poly13052022 => {
                let cipher = ChaCha20Poly1305::new(Key::<ChaCha20Poly1305>::from_slice(&self.key));
                let nonce = Nonce::from_slice(nonce);
                let mut buffer = plaintext.to_vec();
                cipher.encrypt_in_place(nonce, b"", &mut buffer)
                    .map_err(|_| ProtocolError::EncryptionFailed)?;
                Ok(buffer)
            }
            ShadowsocksMethod::XChaCha20Poly1305 => {
                let cipher = XChaCha20Poly1305::new(Key::<XChaCha20Poly1305>::from_slice(&self.key));
                let nonce = chacha20poly1305::XNonce::from_slice(nonce);
                let mut buffer = plaintext.to_vec();
                cipher.encrypt_in_place(nonce, b"", &mut buffer)
                    .map_err(|_| ProtocolError::EncryptionFailed)?;
                Ok(buffer)
            }
            _ => Err(ProtocolError::UnsupportedProtocol("method not implemented".to_string())),
        }
    }

    /// Decrypt data (simplified AEAD implementation)
    pub fn decrypt(&self, ciphertext: &[u8], nonce: &[u8]) -> Result<Vec<u8>, ProtocolError> {
        if !self.method.is_aead() {
            return Err(ProtocolError::UnsupportedProtocol("non-AEAD methods not implemented".to_string()));
        }

        match self.method {
            ShadowsocksMethod::Aes128Gcm | ShadowsocksMethod::Aes128Gcm2022 => {
                let cipher = Aes128Gcm::new(Key::<Aes128Gcm>::from_slice(&self.key));
                let nonce = Nonce::from_slice(nonce);
                let mut buffer = ciphertext.to_vec();
                cipher.decrypt_in_place(nonce, b"", &mut buffer)
                    .map_err(|_| ProtocolError::DecryptionFailed)?;
                Ok(buffer)
            }
            ShadowsocksMethod::Aes256Gcm | ShadowsocksMethod::Aes256Gcm2022 => {
                let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(&self.key));
                let nonce = Nonce::from_slice(nonce);
                let mut buffer = ciphertext.to_vec();
                cipher.decrypt_in_place(nonce, b"", &mut buffer)
                    .map_err(|_| ProtocolError::DecryptionFailed)?;
                Ok(buffer)
            }
            ShadowsocksMethod::ChaCha20Poly1305 | ShadowsocksMethod::ChaCha20Poly13052022 => {
                let cipher = ChaCha20Poly1305::new(Key::<ChaCha20Poly1305>::from_slice(&self.key));
                let nonce = Nonce::from_slice(nonce);
                let mut buffer = ciphertext.to_vec();
                cipher.decrypt_in_place(nonce, b"", &mut buffer)
                    .map_err(|_| ProtocolError::DecryptionFailed)?;
                Ok(buffer)
            }
            ShadowsocksMethod::XChaCha20Poly1305 => {
                let cipher = XChaCha20Poly1305::new(Key::<XChaCha20Poly1305>::from_slice(&self.key));
                let nonce = chacha20poly1305::XNonce::from_slice(nonce);
                let mut buffer = ciphertext.to_vec();
                cipher.decrypt_in_place(nonce, b"", &mut buffer)
                    .map_err(|_| ProtocolError::DecryptionFailed)?;
                Ok(buffer)
            }
            _ => Err(ProtocolError::UnsupportedProtocol("method not implemented".to_string())),
        }
    }
}

/// Shadowsocks outbound handler
pub struct ShadowsocksOutbound {
    tag: String,
    server_addr: SocketAddr,
    cipher: ShadowsocksCipher,
}

impl ShadowsocksOutbound {
    pub fn new(
        tag: String,
        server_addr: SocketAddr,
        method: &str,
        password: &str,
    ) -> Result<Self, ProtocolError> {
        let method = ShadowsocksMethod::from_str(method)?;
        let cipher = ShadowsocksCipher::new(method, password)?;
        
        Ok(Self {
            tag,
            server_addr,
            cipher,
        })
    }

    /// Encode target address for Shadowsocks protocol
    fn encode_address(host: &str, port: u16) -> Vec<u8> {
        let mut addr_buf = Vec::new();
        
        if let Ok(ipv4) = host.parse::<Ipv4Addr>() {
            addr_buf.push(0x01); // IPv4
            addr_buf.extend_from_slice(&ipv4.octets());
        } else if let Ok(ipv6) = host.parse::<Ipv6Addr>() {
            addr_buf.push(0x04); // IPv6
            addr_buf.extend_from_slice(&ipv6.octets());
        } else {
            // Domain name
            addr_buf.push(0x03); // Domain
            addr_buf.push(host.len() as u8);
            addr_buf.extend_from_slice(host.as_bytes());
        }
        
        addr_buf.extend_from_slice(&port.to_be_bytes());
        addr_buf
    }

    /// Dial through Shadowsocks proxy
    pub async fn dial(&self, target_host: &str, target_port: u16) -> Result<TcpStream, ProtocolError> {
        // Connect to Shadowsocks server
        let mut stream = TcpStream::connect(&self.server_addr).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to server: {}", e)))?;

        // Prepare target address
        let addr_buf = Self::encode_address(target_host, target_port);

        if self.cipher.method == ShadowsocksMethod::None {
            // No encryption - send address directly
            stream.write_all(&addr_buf).await
                .map_err(|e| ProtocolError::ProtocolViolation(format!("write address: {}", e)))?;
        } else if self.cipher.method.is_aead() {
            // AEAD encryption
            let nonce_size = self.cipher.method.nonce_size();
            let mut nonce = vec![0u8; nonce_size];
            use rand::RngCore;
            rand::rng().fill_bytes(&mut nonce);

            // Encrypt address
            let encrypted_addr = self.cipher.encrypt(&addr_buf, &nonce)
                .map_err(|e| ProtocolError::EncryptionFailed)?;

            // Send salt (nonce) + encrypted address
            stream.write_all(&nonce).await
                .map_err(|e| ProtocolError::ProtocolViolation(format!("write nonce: {}", e)))?;
            stream.write_all(&encrypted_addr).await
                .map_err(|e| ProtocolError::ProtocolViolation(format!("write encrypted address: {}", e)))?;
        } else {
            // Legacy stream ciphers (not implemented for security reasons)
            return Err(ProtocolError::UnsupportedProtocol("legacy stream ciphers not supported".to_string()));
        }

        Ok(stream)
    }
}



impl Outbound for ShadowsocksOutbound {
    fn network(&self) -> Vec<String> {
        vec!["tcp".to_string(), "udp".to_string()]
    }

    fn dependencies(&self) -> Vec<String> {
        vec![]
    }

    fn dial(&self, _network: &str, _destination: &str) -> Result<(), String> {
        // This is a simplified interface - in a real implementation,
        // this would return a connection or be async
        Ok(())
    }
}

/// Shadowsocks inbound handler
pub struct ShadowsocksInbound {
    tag: String,
    listen_addr: SocketAddr,
    cipher: ShadowsocksCipher,
    users: HashMap<String, String>, // For multi-user support
}

impl ShadowsocksInbound {
    pub fn new(
        tag: String,
        listen_addr: SocketAddr,
        method: &str,
        password: &str,
        users: HashMap<String, String>,
    ) -> Result<Self, ProtocolError> {
        let method = ShadowsocksMethod::from_str(method)?;
        let cipher = ShadowsocksCipher::new(method, password)?;
        
        Ok(Self {
            tag,
            listen_addr,
            cipher,
            users,
        })
    }

    /// Handle incoming Shadowsocks connection
    pub async fn handle_connection(&self, mut stream: TcpStream) -> Result<(), ProtocolError> {
        if self.cipher.method == ShadowsocksMethod::None {
            // No encryption - read address directly
            let (target_host, target_port) = self.read_target_address(&mut stream).await?;
            self.relay_connection(stream, &target_host, target_port).await?;
        } else if self.cipher.method.is_aead() {
            // AEAD decryption
            let nonce_size = self.cipher.method.nonce_size();
            
            // Read salt (nonce)
            let mut nonce = vec![0u8; nonce_size];
            stream.read_exact(&mut nonce).await
                .map_err(|e| ProtocolError::ConnectionFailed(format!("read nonce: {}", e)))?;

            // Read and decrypt address (simplified - assumes fixed length for demo)
            let mut encrypted_addr = vec![0u8; 32]; // Simplified
            stream.read_exact(&mut encrypted_addr).await
                .map_err(|e| ProtocolError::ConnectionFailed(format!("read encrypted address: {}", e)))?;

            let addr_buf = self.cipher.decrypt(&encrypted_addr, &nonce)?;
            let (target_host, target_port) = Self::decode_address(&addr_buf)?;
            
            self.relay_connection(stream, &target_host, target_port).await?;
        } else {
            return Err(ProtocolError::UnsupportedProtocol("legacy stream ciphers not supported".to_string()));
        }

        Ok(())
    }

    /// Read target address from stream
    async fn read_target_address(&self, stream: &mut TcpStream) -> Result<(String, u16), ProtocolError> {
        // Read address type
        let mut addr_type_buf = [0u8; 1];
        stream.read_exact(&mut addr_type_buf).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("read address type: {}", e)))?;

        let addr_type = addr_type_buf[0];
        
        let (host, port) = match addr_type {
            0x01 => {
                // IPv4
                let mut addr_buf = [0u8; 4];
                stream.read_exact(&mut addr_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read IPv4: {}", e)))?;
                let addr = Ipv4Addr::from(addr_buf);
                
                let mut port_buf = [0u8; 2];
                stream.read_exact(&mut port_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read port: {}", e)))?;
                let port = u16::from_be_bytes(port_buf);
                
                (addr.to_string(), port)
            }
            0x03 => {
                // Domain name
                let mut len_buf = [0u8; 1];
                stream.read_exact(&mut len_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read domain length: {}", e)))?;
                let domain_len = len_buf[0] as usize;
                
                let mut domain_buf = vec![0u8; domain_len];
                stream.read_exact(&mut domain_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read domain: {}", e)))?;
                let domain = String::from_utf8(domain_buf)
                    .map_err(|_| ProtocolError::ProtocolViolation("invalid domain encoding".to_string()))?;
                
                let mut port_buf = [0u8; 2];
                stream.read_exact(&mut port_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read port: {}", e)))?;
                let port = u16::from_be_bytes(port_buf);
                
                (domain, port)
            }
            0x04 => {
                // IPv6
                let mut addr_buf = [0u8; 16];
                stream.read_exact(&mut addr_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read IPv6: {}", e)))?;
                let addr = Ipv6Addr::from(addr_buf);
                
                let mut port_buf = [0u8; 2];
                stream.read_exact(&mut port_buf).await
                    .map_err(|e| ProtocolError::ConnectionFailed(format!("read port: {}", e)))?;
                let port = u16::from_be_bytes(port_buf);
                
                (addr.to_string(), port)
            }
            _ => return Err(ProtocolError::ProtocolViolation("invalid address type".to_string())),
        };

        Ok((host, port))
    }

    /// Decode address from buffer
    fn decode_address(buf: &[u8]) -> Result<(String, u16), ProtocolError> {
        if buf.is_empty() {
            return Err(ProtocolError::ProtocolViolation("empty address buffer".to_string()));
        }

        let addr_type = buf[0];
        let _offset = 1;

        let (host, port_offset) = match addr_type {
            0x01 => {
                // IPv4
                if buf.len() < 7 {
                    return Err(ProtocolError::ProtocolViolation("invalid IPv4 address".to_string()));
                }
                let addr = Ipv4Addr::from([buf[1], buf[2], buf[3], buf[4]]);
                (addr.to_string(), 5)
            }
            0x03 => {
                // Domain name
                if buf.len() < 2 {
                    return Err(ProtocolError::ProtocolViolation("invalid domain address".to_string()));
                }
                let domain_len = buf[1] as usize;
                if buf.len() < 2 + domain_len + 2 {
                    return Err(ProtocolError::ProtocolViolation("incomplete domain address".to_string()));
                }
                let domain = String::from_utf8(buf[2..2 + domain_len].to_vec())
                    .map_err(|_| ProtocolError::ProtocolViolation("invalid domain encoding".to_string()))?;
                (domain, 2 + domain_len)
            }
            0x04 => {
                // IPv6
                if buf.len() < 19 {
                    return Err(ProtocolError::ProtocolViolation("invalid IPv6 address".to_string()));
                }
                let mut addr_bytes = [0u8; 16];
                addr_bytes.copy_from_slice(&buf[1..17]);
                let addr = Ipv6Addr::from(addr_bytes);
                (addr.to_string(), 17)
            }
            _ => return Err(ProtocolError::ProtocolViolation("invalid address type".to_string())),
        };

        if buf.len() < port_offset + 2 {
            return Err(ProtocolError::ProtocolViolation("missing port".to_string()));
        }

        let port = u16::from_be_bytes([buf[port_offset], buf[port_offset + 1]]);
        Ok((host, port))
    }

    /// Relay connection to target
    async fn relay_connection(&self, mut client_stream: TcpStream, target_host: &str, target_port: u16) -> Result<(), ProtocolError> {
        // Connect to target
        let target_addr = format!("{}:{}", target_host, target_port);
        let mut target_stream = TcpStream::connect(&target_addr).await
            .map_err(|e| ProtocolError::ConnectionFailed(format!("connect to {}: {}", target_addr, e)))?;

        // Start bidirectional forwarding
        let (mut client_read, mut client_write) = client_stream.split();
        let (mut target_read, mut target_write) = target_stream.split();

        let forward1 = tokio::io::copy(&mut client_read, &mut target_write);
        let forward2 = tokio::io::copy(&mut target_read, &mut client_write);

        // Wait for either direction to close
        tokio::select! {
            result1 = forward1 => {
                if let Err(e) = result1 {
                    eprintln!("Forward client->target error: {}", e);
                }
            }
            result2 = forward2 => {
                if let Err(e) = result2 {
                    eprintln!("Forward target->client error: {}", e);
                }
            }
        }

        Ok(())
    }
}

impl Adapter for ShadowsocksInbound {
    fn adapter_type(&self) -> &str {
        "shadowsocks"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}

#[async_trait]
impl Lifecycle for ShadowsocksInbound {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        println!("Shadowsocks inbound {} starting", self.tag);
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        println!("Shadowsocks inbound {} closing", self.tag);
        Ok(())
    }
}

impl Inbound for ShadowsocksInbound {
    // Inherits from Adapter and Lifecycle
}

impl Adapter for ShadowsocksOutbound {
    fn adapter_type(&self) -> &str {
        "shadowsocks"
    }

    fn tag(&self) -> &str {
        &self.tag
    }
}



#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_shadowsocks_method_from_str() {
        assert_eq!(ShadowsocksMethod::from_str("aes-128-gcm").unwrap(), ShadowsocksMethod::Aes128Gcm);
        assert_eq!(ShadowsocksMethod::from_str("aes-256-gcm").unwrap(), ShadowsocksMethod::Aes256Gcm);
        assert_eq!(ShadowsocksMethod::from_str("chacha20-ietf-poly1305").unwrap(), ShadowsocksMethod::ChaCha20Poly1305);
        assert_eq!(ShadowsocksMethod::from_str("2022-blake3-aes-128-gcm").unwrap(), ShadowsocksMethod::Aes128Gcm2022);
        assert_eq!(ShadowsocksMethod::from_str("none").unwrap(), ShadowsocksMethod::None);
    }

    #[test]
    fn test_encode_address() {
        // Test IPv4
        let addr = ShadowsocksOutbound::encode_address("127.0.0.1", 80);
        assert_eq!(addr[0], 0x01); // IPv4 type
        assert_eq!(&addr[1..5], &[127, 0, 0, 1]); // IP
        assert_eq!(u16::from_be_bytes([addr[5], addr[6]]), 80); // Port

        // Test domain
        let addr = ShadowsocksOutbound::encode_address("example.com", 443);
        assert_eq!(addr[0], 0x03); // Domain type
        assert_eq!(addr[1], 11); // Domain length
        assert_eq!(&addr[2..13], b"example.com"); // Domain
        assert_eq!(u16::from_be_bytes([addr[13], addr[14]]), 443); // Port
    }

    #[test]
    fn test_decode_address() {
        // Test IPv4
        let buf = vec![0x01, 127, 0, 0, 1, 0, 80];
        let (host, port) = ShadowsocksInbound::decode_address(&buf).unwrap();
        assert_eq!(host, "127.0.0.1");
        assert_eq!(port, 80);

        // Test domain
        let mut buf = vec![0x03, 11];
        buf.extend_from_slice(b"example.com");
        buf.extend_from_slice(&443u16.to_be_bytes());
        let (host, port) = ShadowsocksInbound::decode_address(&buf).unwrap();
        assert_eq!(host, "example.com");
        assert_eq!(port, 443);
    }
}
