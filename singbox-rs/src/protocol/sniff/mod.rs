//! Protocol sniffing system
//!
//! This module provides automatic protocol detection and identification
//! capabilities for incoming network connections.

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

pub mod detectors;
pub mod patterns;
pub mod rules;
pub mod cache;

pub use detectors::{ProtocolDetector, DetectorResult};
pub use patterns::{ProtocolPattern, PatternMatcher};
pub use rules::{SniffRule, SniffAction};
pub use cache::{SniffCache, CacheEntry};

/// Protocol sniffing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SniffConfig {
    /// Enable protocol sniffing
    pub enabled: bool,
    
    /// Sniffing timeout
    pub timeout: Duration,
    
    /// Maximum bytes to sniff
    pub max_bytes: usize,
    
    /// Sniffing rules
    pub rules: Vec<SniffRule>,
    
    /// Protocol detectors
    pub detectors: Vec<DetectorConfig>,
    
    /// Cache configuration
    pub cache: Option<SniffCacheConfig>,
    
    /// Fallback protocol
    pub fallback_protocol: Option<String>,
    
    /// Override rules
    pub overrides: Vec<SniffOverride>,
}

/// Detector configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectorConfig {
    /// Detector name
    pub name: String,
    
    /// Detector type
    pub detector_type: DetectorType,
    
    /// Detector priority
    pub priority: u32,
    
    /// Enable detector
    pub enabled: bool,
    
    /// Detector parameters
    pub parameters: HashMap<String, serde_json::Value>,
}

/// Detector types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DetectorType {
    /// HTTP protocol detector
    Http,
    
    /// TLS/SSL protocol detector
    Tls,
    
    /// SSH protocol detector
    Ssh,
    
    /// BitTorrent protocol detector
    BitTorrent,
    
    /// DNS protocol detector
    Dns,
    
    /// QUIC protocol detector
    Quic,
    
    /// WebSocket protocol detector
    WebSocket,
    
    /// SOCKS protocol detector
    Socks,
    
    /// Custom pattern detector
    Pattern(String),
    
    /// Machine learning detector
    MachineLearning(String),
}

/// Sniff cache configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SniffCacheConfig {
    /// Enable caching
    pub enabled: bool,
    
    /// Cache size
    pub size: usize,
    
    /// Cache TTL
    pub ttl: Duration,
    
    /// Cache by IP
    pub cache_by_ip: bool,
    
    /// Cache by port
    pub cache_by_port: bool,
    
    /// Cache by pattern
    pub cache_by_pattern: bool,
}

/// Sniff override rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SniffOverride {
    /// Override name
    pub name: String,
    
    /// Source IP patterns
    pub source_ips: Option<Vec<String>>,
    
    /// Destination ports
    pub dest_ports: Option<Vec<u16>>,
    
    /// Force protocol
    pub force_protocol: String,
    
    /// Override priority
    pub priority: u32,
}

/// Detected protocol information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectedProtocol {
    /// Protocol name
    pub protocol: String,
    
    /// Detection confidence (0.0 - 1.0)
    pub confidence: f64,
    
    /// Detector that made the detection
    pub detector: String,
    
    /// Detection time
    pub detection_time: Duration,
    
    /// Additional metadata
    pub metadata: HashMap<String, String>,
    
    /// Raw data that was analyzed
    pub analyzed_data: Option<Vec<u8>>,
}

/// Sniffing context
#[derive(Debug, Clone)]
pub struct SniffContext {
    /// Source address
    pub source_addr: std::net::SocketAddr,
    
    /// Destination address
    pub dest_addr: std::net::SocketAddr,
    
    /// Connection start time
    pub start_time: Instant,
    
    /// Bytes received so far
    pub bytes_received: usize,
    
    /// Connection metadata
    pub metadata: HashMap<String, String>,
}

/// Protocol sniffer
pub struct ProtocolSniffer {
    /// Configuration
    config: SniffConfig,
    
    /// Registered detectors
    detectors: Vec<Arc<dyn ProtocolDetector + Send + Sync>>,
    
    /// Pattern matchers
    pattern_matchers: Vec<Arc<PatternMatcher>>,
    
    /// Sniff cache
    cache: Option<Arc<SniffCache>>,
    
    /// Detection statistics
    stats: Arc<RwLock<SniffStats>>,
    
    /// Active sniff sessions
    sessions: Arc<RwLock<HashMap<String, SniffSession>>>,
}

/// Sniffing statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SniffStats {
    /// Total sniff attempts
    pub total_attempts: u64,
    
    /// Successful detections
    pub successful_detections: u64,
    
    /// Failed detections
    pub failed_detections: u64,
    
    /// Cache hits
    pub cache_hits: u64,
    
    /// Cache misses
    pub cache_misses: u64,
    
    /// Average detection time
    pub avg_detection_time: Duration,
    
    /// Detections by protocol
    pub detections_by_protocol: HashMap<String, u64>,
    
    /// Detections by detector
    pub detections_by_detector: HashMap<String, u64>,
    
    /// Detection accuracy
    pub accuracy: f64,
}

/// Active sniff session
#[derive(Debug, Clone)]
pub struct SniffSession {
    /// Session ID
    pub session_id: String,
    
    /// Sniff context
    pub context: SniffContext,
    
    /// Accumulated data
    pub data_buffer: Vec<u8>,
    
    /// Session start time
    pub start_time: Instant,
    
    /// Current state
    pub state: SniffState,
    
    /// Partial results
    pub partial_results: Vec<DetectedProtocol>,
}

/// Sniff session states
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum SniffState {
    /// Waiting for data
    WaitingForData,
    
    /// Analyzing data
    Analyzing,
    
    /// Detection completed
    Completed,
    
    /// Detection failed
    Failed,
    
    /// Session timed out
    TimedOut,
}

impl ProtocolSniffer {
    /// Create a new protocol sniffer
    pub fn new(config: SniffConfig) -> Self {
        let cache = if let Some(ref cache_config) = config.cache {
            if cache_config.enabled {
                Some(Arc::new(SniffCache::new(cache_config.clone())))
            } else {
                None
            }
        } else {
            None
        };
        
        Self {
            config,
            detectors: Vec::new(),
            pattern_matchers: Vec::new(),
            cache,
            stats: Arc::new(RwLock::new(SniffStats {
                total_attempts: 0,
                successful_detections: 0,
                failed_detections: 0,
                cache_hits: 0,
                cache_misses: 0,
                avg_detection_time: Duration::ZERO,
                detections_by_protocol: HashMap::new(),
                detections_by_detector: HashMap::new(),
                accuracy: 0.0,
            })),
            sessions: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// Start the protocol sniffer
    pub async fn start(&mut self) -> Result<(), String> {
        if !self.config.enabled {
            return Ok(());
        }
        
        // Initialize detectors
        self.initialize_detectors().await?;
        
        // Initialize pattern matchers
        self.initialize_pattern_matchers().await?;
        
        // Start cleanup task
        self.start_cleanup_task().await;
        
        println!("🔍 Protocol sniffer started with {} detectors", self.detectors.len());
        Ok(())
    }
    
    /// Stop the protocol sniffer
    pub async fn stop(&self) {
        // Clear active sessions
        self.sessions.write().await.clear();
        
        println!("🔍 Protocol sniffer stopped");
    }
    
    /// Sniff protocol from data
    pub async fn sniff_protocol(
        &self,
        data: &[u8],
        context: SniffContext,
    ) -> Result<Option<DetectedProtocol>, String> {
        if !self.config.enabled {
            return Ok(None);
        }
        
        let start_time = Instant::now();
        
        // Update statistics
        {
            let mut stats = self.stats.write().await;
            stats.total_attempts += 1;
        }
        
        // Check cache first
        if let Some(ref cache) = self.cache {
            let cache_key = self.generate_cache_key(&context, data);
            if let Some(cached_result) = cache.get(&cache_key).await {
                let mut stats = self.stats.write().await;
                stats.cache_hits += 1;
                return Ok(Some(cached_result.protocol.clone()));
            } else {
                let mut stats = self.stats.write().await;
                stats.cache_misses += 1;
            }
        }
        
        // Check overrides first
        if let Some(override_protocol) = self.check_overrides(&context).await {
            let detected = DetectedProtocol {
                protocol: override_protocol,
                confidence: 1.0,
                detector: "override".to_string(),
                detection_time: start_time.elapsed(),
                metadata: HashMap::new(),
                analyzed_data: Some(data.to_vec()),
            };
            
            self.update_stats(&detected).await;
            return Ok(Some(detected));
        }
        
        // Run detectors
        let mut best_result: Option<DetectedProtocol> = None;
        let mut detector_results = Vec::new();
        
        // Sort detectors by priority
        let mut sorted_detectors = self.detectors.clone();
        sorted_detectors.sort_by_key(|d| std::cmp::Reverse(d.priority()));
        
        for detector in &sorted_detectors {
            match detector.detect(data, &context).await {
                Ok(Some(result)) => {
                    detector_results.push(result.clone());
                    
                    if best_result.is_none() || result.confidence > best_result.as_ref().unwrap().confidence {
                        best_result = Some(result);
                    }
                    
                    // If we have high confidence, stop here
                    if result.confidence >= 0.9 {
                        break;
                    }
                },
                Ok(None) => {
                    // Detector didn't match
                },
                Err(e) => {
                    eprintln!("Detector {} failed: {}", detector.name(), e);
                }
            }
        }
        
        // Run pattern matchers if no detector matched
        if best_result.is_none() {
            for matcher in &self.pattern_matchers {
                if let Some(result) = matcher.match_pattern(data, &context).await {
                    if best_result.is_none() || result.confidence > best_result.as_ref().unwrap().confidence {
                        best_result = Some(result);
                    }
                }
            }
        }
        
        // Use fallback if no detection
        if best_result.is_none() {
            if let Some(ref fallback) = self.config.fallback_protocol {
                best_result = Some(DetectedProtocol {
                    protocol: fallback.clone(),
                    confidence: 0.1,
                    detector: "fallback".to_string(),
                    detection_time: start_time.elapsed(),
                    metadata: HashMap::new(),
                    analyzed_data: Some(data.to_vec()),
                });
            }
        }
        
        // Update statistics and cache
        if let Some(ref result) = best_result {
            self.update_stats(result).await;
            
            // Cache the result
            if let Some(ref cache) = self.cache {
                let cache_key = self.generate_cache_key(&context, data);
                cache.put(cache_key, CacheEntry {
                    protocol: result.clone(),
                    timestamp: Instant::now(),
                }).await;
            }
        } else {
            let mut stats = self.stats.write().await;
            stats.failed_detections += 1;
        }
        
        Ok(best_result)
    }
    
    /// Start sniff session
    pub async fn start_session(&self, context: SniffContext) -> String {
        let session_id = uuid::Uuid::new_v4().to_string();
        
        let session = SniffSession {
            session_id: session_id.clone(),
            context,
            data_buffer: Vec::new(),
            start_time: Instant::now(),
            state: SniffState::WaitingForData,
            partial_results: Vec::new(),
        };
        
        self.sessions.write().await.insert(session_id.clone(), session);
        session_id
    }
    
    /// Add data to sniff session
    pub async fn add_session_data(&self, session_id: &str, data: &[u8]) -> Result<Option<DetectedProtocol>, String> {
        let mut sessions = self.sessions.write().await;
        
        if let Some(session) = sessions.get_mut(session_id) {
            session.data_buffer.extend_from_slice(data);
            session.state = SniffState::Analyzing;
            
            // Check if we have enough data or reached timeout
            let should_analyze = session.data_buffer.len() >= self.config.max_bytes ||
                                session.start_time.elapsed() >= self.config.timeout;
            
            if should_analyze {
                let result = self.sniff_protocol(&session.data_buffer, session.context.clone()).await?;
                
                if result.is_some() {
                    session.state = SniffState::Completed;
                } else {
                    session.state = SniffState::Failed;
                }
                
                return Ok(result);
            }
        } else {
            return Err(format!("Session not found: {}", session_id));
        }
        
        Ok(None)
    }
    
    /// End sniff session
    pub async fn end_session(&self, session_id: &str) {
        self.sessions.write().await.remove(session_id);
    }
    
    /// Get sniffing statistics
    pub async fn get_stats(&self) -> SniffStats {
        self.stats.read().await.clone()
    }
    
    /// Initialize detectors
    async fn initialize_detectors(&mut self) -> Result<(), String> {
        for detector_config in &self.config.detectors {
            if !detector_config.enabled {
                continue;
            }
            
            let detector = self.create_detector(detector_config)?;
            self.detectors.push(detector);
        }
        
        Ok(())
    }
    
    /// Create detector from configuration
    fn create_detector(&self, config: &DetectorConfig) -> Result<Arc<dyn ProtocolDetector + Send + Sync>, String> {
        match config.detector_type {
            DetectorType::Http => Ok(Arc::new(detectors::HttpDetector::new(config.clone()))),
            DetectorType::Tls => Ok(Arc::new(detectors::TlsDetector::new(config.clone()))),
            DetectorType::Ssh => Ok(Arc::new(detectors::SshDetector::new(config.clone()))),
            DetectorType::BitTorrent => Ok(Arc::new(detectors::BitTorrentDetector::new(config.clone()))),
            DetectorType::Dns => Ok(Arc::new(detectors::DnsDetector::new(config.clone()))),
            DetectorType::Quic => Ok(Arc::new(detectors::QuicDetector::new(config.clone()))),
            DetectorType::WebSocket => Ok(Arc::new(detectors::WebSocketDetector::new(config.clone()))),
            DetectorType::Socks => Ok(Arc::new(detectors::SocksDetector::new(config.clone()))),
            DetectorType::Pattern(ref pattern) => {
                Ok(Arc::new(detectors::PatternDetector::new(config.clone(), pattern.clone())))
            },
            DetectorType::MachineLearning(ref model) => {
                Ok(Arc::new(detectors::MlDetector::new(config.clone(), model.clone())))
            },
        }
    }
    
    /// Initialize pattern matchers
    async fn initialize_pattern_matchers(&mut self) -> Result<(), String> {
        // Load built-in patterns
        let patterns = patterns::load_builtin_patterns();
        
        for pattern in patterns {
            let matcher = Arc::new(PatternMatcher::new(pattern));
            self.pattern_matchers.push(matcher);
        }
        
        Ok(())
    }
    
    /// Check override rules
    async fn check_overrides(&self, context: &SniffContext) -> Option<String> {
        for override_rule in &self.config.overrides {
            if self.matches_override(override_rule, context) {
                return Some(override_rule.force_protocol.clone());
            }
        }
        None
    }
    
    /// Check if override rule matches context
    fn matches_override(&self, override_rule: &SniffOverride, context: &SniffContext) -> bool {
        // Check source IPs
        if let Some(ref source_ips) = override_rule.source_ips {
            let source_ip = context.source_addr.ip().to_string();
            if !source_ips.iter().any(|pattern| self.matches_ip_pattern(pattern, &source_ip)) {
                return false;
            }
        }
        
        // Check destination ports
        if let Some(ref dest_ports) = override_rule.dest_ports {
            if !dest_ports.contains(&context.dest_addr.port()) {
                return false;
            }
        }
        
        true
    }
    
    /// Check if IP matches pattern
    fn matches_ip_pattern(&self, pattern: &str, ip: &str) -> bool {
        // Simple pattern matching (could be enhanced with CIDR, wildcards, etc.)
        pattern == ip || pattern == "*"
    }
    
    /// Generate cache key
    fn generate_cache_key(&self, context: &SniffContext, data: &[u8]) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        
        if self.config.cache.as_ref().map_or(false, |c| c.cache_by_ip) {
            context.source_addr.ip().hash(&mut hasher);
        }
        
        if self.config.cache.as_ref().map_or(false, |c| c.cache_by_port) {
            context.dest_addr.port().hash(&mut hasher);
        }
        
        if self.config.cache.as_ref().map_or(false, |c| c.cache_by_pattern) {
            // Hash first few bytes for pattern-based caching
            let sample_size = std::cmp::min(data.len(), 64);
            data[..sample_size].hash(&mut hasher);
        }
        
        format!("{:x}", hasher.finish())
    }
    
    /// Update statistics
    async fn update_stats(&self, result: &DetectedProtocol) {
        let mut stats = self.stats.write().await;
        
        stats.successful_detections += 1;
        
        // Update protocol statistics
        *stats.detections_by_protocol.entry(result.protocol.clone()).or_insert(0) += 1;
        
        // Update detector statistics
        *stats.detections_by_detector.entry(result.detector.clone()).or_insert(0) += 1;
        
        // Update average detection time
        let total_detections = stats.successful_detections + stats.failed_detections;
        if total_detections > 0 {
            stats.avg_detection_time = Duration::from_nanos(
                (stats.avg_detection_time.as_nanos() as u64 * (total_detections - 1) + 
                 result.detection_time.as_nanos() as u64) / total_detections
            );
        }
        
        // Update accuracy
        stats.accuracy = stats.successful_detections as f64 / stats.total_attempts as f64;
    }
    
    /// Start cleanup task for expired sessions
    async fn start_cleanup_task(&self) {
        let sessions = Arc::clone(&self.sessions);
        let timeout = self.config.timeout;
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));
            
            loop {
                interval.tick().await;
                
                let mut sessions_guard = sessions.write().await;
                let now = Instant::now();
                
                sessions_guard.retain(|_, session| {
                    if now.duration_since(session.start_time) < timeout {
                        true
                    } else {
                        false // Remove expired sessions
                    }
                });
            }
        });
    }
}

impl Default for SniffConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            timeout: Duration::from_secs(5),
            max_bytes: 4096,
            rules: Vec::new(),
            detectors: vec![
                DetectorConfig {
                    name: "http".to_string(),
                    detector_type: DetectorType::Http,
                    priority: 100,
                    enabled: true,
                    parameters: HashMap::new(),
                },
                DetectorConfig {
                    name: "tls".to_string(),
                    detector_type: DetectorType::Tls,
                    priority: 90,
                    enabled: true,
                    parameters: HashMap::new(),
                },
                DetectorConfig {
                    name: "ssh".to_string(),
                    detector_type: DetectorType::Ssh,
                    priority: 80,
                    enabled: true,
                    parameters: HashMap::new(),
                },
            ],
            cache: Some(SniffCacheConfig {
                enabled: true,
                size: 1000,
                ttl: Duration::from_secs(300),
                cache_by_ip: true,
                cache_by_port: true,
                cache_by_pattern: true,
            }),
            fallback_protocol: Some("tcp".to_string()),
            overrides: Vec::new(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_sniff_config_default() {
        let config = SniffConfig::default();
        
        assert!(config.enabled);
        assert_eq!(config.timeout, Duration::from_secs(5));
        assert_eq!(config.max_bytes, 4096);
        assert_eq!(config.detectors.len(), 3);
        assert!(config.cache.is_some());
        assert_eq!(config.fallback_protocol, Some("tcp".to_string()));
    }
    
    #[tokio::test]
    async fn test_protocol_sniffer_creation() {
        let config = SniffConfig::default();
        let sniffer = ProtocolSniffer::new(config);
        
        let stats = sniffer.get_stats().await;
        assert_eq!(stats.total_attempts, 0);
        assert_eq!(stats.successful_detections, 0);
        assert_eq!(stats.failed_detections, 0);
    }
    
    #[tokio::test]
    async fn test_sniff_session() {
        let config = SniffConfig::default();
        let sniffer = ProtocolSniffer::new(config);
        
        let context = SniffContext {
            source_addr: "127.0.0.1:12345".parse().unwrap(),
            dest_addr: "127.0.0.1:80".parse().unwrap(),
            start_time: Instant::now(),
            bytes_received: 0,
            metadata: HashMap::new(),
        };
        
        let session_id = sniffer.start_session(context).await;
        assert!(!session_id.is_empty());
        
        sniffer.end_session(&session_id).await;
    }
    
    #[test]
    fn test_detected_protocol_creation() {
        let protocol = DetectedProtocol {
            protocol: "http".to_string(),
            confidence: 0.95,
            detector: "http_detector".to_string(),
            detection_time: Duration::from_millis(10),
            metadata: HashMap::new(),
            analyzed_data: Some(b"GET / HTTP/1.1\r\n".to_vec()),
        };
        
        assert_eq!(protocol.protocol, "http");
        assert_eq!(protocol.confidence, 0.95);
        assert_eq!(protocol.detector, "http_detector");
        assert!(protocol.analyzed_data.is_some());
    }
}
