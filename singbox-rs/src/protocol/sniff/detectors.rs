//! Protocol detectors for automatic protocol identification
//!
//! This module contains various protocol detectors that can identify
//! different network protocols based on packet data patterns.

use std::collections::HashMap;
use std::time::Duration;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};

use super::{DetectorConfig, Sniff<PERSON>ontext, DetectedProtocol};

/// Protocol detector trait
#[async_trait]
pub trait ProtocolDetector {
    /// Get detector name
    fn name(&self) -> &str;
    
    /// Get detector priority
    fn priority(&self) -> u32;
    
    /// Detect protocol from data
    async fn detect(&self, data: &[u8], context: &SniffContext) -> Result<Option<DetectedProtocol>, String>;
    
    /// Get minimum data size required for detection
    fn min_data_size(&self) -> usize {
        1
    }
    
    /// Get maximum data size to analyze
    fn max_data_size(&self) -> usize {
        4096
    }
}

/// Detection result
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct DetectorResult {
    /// Detected protocol
    pub protocol: String,
    
    /// Detection confidence (0.0 - 1.0)
    pub confidence: f64,
    
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

/// HTTP protocol detector
pub struct HttpDetector {
    config: DetectorConfig,
}

impl HttpDetector {
    pub fn new(config: DetectorConfig) -> Self {
        Self { config }
    }
    
    /// Check if data looks like HTTP request
    fn is_http_request(&self, data: &[u8]) -> Option<f64> {
        let data_str = String::from_utf8_lossy(data);
        let lines: Vec<&str> = data_str.lines().collect();
        
        if lines.is_empty() {
            return None;
        }
        
        let first_line = lines[0];
        let methods = ["GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH", "CONNECT", "TRACE"];
        
        for method in &methods {
            if first_line.starts_with(method) && first_line.contains("HTTP/") {
                return Some(0.95);
            }
        }
        
        // Check for HTTP response
        if first_line.starts_with("HTTP/") && first_line.contains(" ") {
            return Some(0.90);
        }
        
        None
    }
}

#[async_trait]
impl ProtocolDetector for HttpDetector {
    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn priority(&self) -> u32 {
        self.config.priority
    }
    
    async fn detect(&self, data: &[u8], _context: &SniffContext) -> Result<Option<DetectedProtocol>, String> {
        if data.len() < 8 {
            return Ok(None);
        }
        
        if let Some(confidence) = self.is_http_request(data) {
            let mut metadata = HashMap::new();
            
            // Extract HTTP method and version
            let data_str = String::from_utf8_lossy(data);
            if let Some(first_line) = data_str.lines().next() {
                let parts: Vec<&str> = first_line.split_whitespace().collect();
                if parts.len() >= 3 {
                    metadata.insert("method".to_string(), parts[0].to_string());
                    metadata.insert("version".to_string(), parts[2].to_string());
                }
            }
            
            return Ok(Some(DetectedProtocol {
                protocol: "http".to_string(),
                confidence,
                detector: self.name().to_string(),
                detection_time: Duration::from_millis(1),
                metadata,
                analyzed_data: Some(data.to_vec()),
            }));
        }
        
        Ok(None)
    }
    
    fn min_data_size(&self) -> usize {
        8
    }
}

/// TLS/SSL protocol detector
pub struct TlsDetector {
    config: DetectorConfig,
}

impl TlsDetector {
    pub fn new(config: DetectorConfig) -> Self {
        Self { config }
    }
    
    /// Check if data looks like TLS handshake
    fn is_tls_handshake(&self, data: &[u8]) -> Option<f64> {
        if data.len() < 6 {
            return None;
        }
        
        // TLS record header: [content_type, version_major, version_minor, length_high, length_low]
        let content_type = data[0];
        let version_major = data[1];
        let version_minor = data[2];
        
        // Check for TLS content types
        match content_type {
            0x16 => { // Handshake
                // Check for supported TLS versions
                if (version_major == 0x03 && version_minor >= 0x01) || // TLS 1.0+
                   (version_major == 0x03 && version_minor == 0x00) {  // SSL 3.0
                    return Some(0.95);
                }
            },
            0x14 => Some(0.80), // Change Cipher Spec
            0x15 => Some(0.85), // Alert
            0x17 => Some(0.90), // Application Data
            _ => return None,
        }
        
        None
    }
}

#[async_trait]
impl ProtocolDetector for TlsDetector {
    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn priority(&self) -> u32 {
        self.config.priority
    }
    
    async fn detect(&self, data: &[u8], _context: &SniffContext) -> Result<Option<DetectedProtocol>, String> {
        if let Some(confidence) = self.is_tls_handshake(data) {
            let mut metadata = HashMap::new();
            
            if data.len() >= 3 {
                let version_major = data[1];
                let version_minor = data[2];
                metadata.insert("version".to_string(), format!("{}.{}", version_major, version_minor));
                metadata.insert("content_type".to_string(), format!("0x{:02x}", data[0]));
            }
            
            return Ok(Some(DetectedProtocol {
                protocol: "tls".to_string(),
                confidence,
                detector: self.name().to_string(),
                detection_time: Duration::from_millis(1),
                metadata,
                analyzed_data: Some(data.to_vec()),
            }));
        }
        
        Ok(None)
    }
    
    fn min_data_size(&self) -> usize {
        6
    }
}

/// SSH protocol detector
pub struct SshDetector {
    config: DetectorConfig,
}

impl SshDetector {
    pub fn new(config: DetectorConfig) -> Self {
        Self { config }
    }
    
    /// Check if data looks like SSH protocol
    fn is_ssh_protocol(&self, data: &[u8]) -> Option<f64> {
        let data_str = String::from_utf8_lossy(data);
        
        if data_str.starts_with("SSH-2.0") {
            return Some(0.98);
        }
        
        if data_str.starts_with("SSH-1.") {
            return Some(0.95);
        }
        
        None
    }
}

#[async_trait]
impl ProtocolDetector for SshDetector {
    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn priority(&self) -> u32 {
        self.config.priority
    }
    
    async fn detect(&self, data: &[u8], _context: &SniffContext) -> Result<Option<DetectedProtocol>, String> {
        if let Some(confidence) = self.is_ssh_protocol(data) {
            let mut metadata = HashMap::new();
            
            let data_str = String::from_utf8_lossy(data);
            if let Some(first_line) = data_str.lines().next() {
                if first_line.starts_with("SSH-") {
                    let parts: Vec<&str> = first_line.split('-').collect();
                    if parts.len() >= 2 {
                        metadata.insert("version".to_string(), parts[1].to_string());
                    }
                    if parts.len() >= 3 {
                        metadata.insert("software".to_string(), parts[2].to_string());
                    }
                }
            }
            
            return Ok(Some(DetectedProtocol {
                protocol: "ssh".to_string(),
                confidence,
                detector: self.name().to_string(),
                detection_time: Duration::from_millis(1),
                metadata,
                analyzed_data: Some(data.to_vec()),
            }));
        }
        
        Ok(None)
    }
    
    fn min_data_size(&self) -> usize {
        7
    }
}

/// BitTorrent protocol detector
pub struct BitTorrentDetector {
    config: DetectorConfig,
}

impl BitTorrentDetector {
    pub fn new(config: DetectorConfig) -> Self {
        Self { config }
    }
    
    /// Check if data looks like BitTorrent protocol
    fn is_bittorrent_protocol(&self, data: &[u8]) -> Option<f64> {
        if data.len() < 20 {
            return None;
        }
        
        // BitTorrent handshake starts with protocol length (19) and "BitTorrent protocol"
        if data[0] == 19 && data.len() >= 20 {
            let protocol_str = String::from_utf8_lossy(&data[1..20]);
            if protocol_str == "BitTorrent protocol" {
                return Some(0.99);
            }
        }
        
        // Check for DHT packets
        if data.len() >= 2 {
            let data_str = String::from_utf8_lossy(data);
            if data_str.starts_with("d1:") || data_str.contains("announce") {
                return Some(0.80);
            }
        }
        
        None
    }
}

#[async_trait]
impl ProtocolDetector for BitTorrentDetector {
    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn priority(&self) -> u32 {
        self.config.priority
    }
    
    async fn detect(&self, data: &[u8], _context: &SniffContext) -> Result<Option<DetectedProtocol>, String> {
        if let Some(confidence) = self.is_bittorrent_protocol(data) {
            let mut metadata = HashMap::new();
            
            if data.len() >= 20 && data[0] == 19 {
                metadata.insert("handshake".to_string(), "true".to_string());
            } else {
                metadata.insert("dht".to_string(), "true".to_string());
            }
            
            return Ok(Some(DetectedProtocol {
                protocol: "bittorrent".to_string(),
                confidence,
                detector: self.name().to_string(),
                detection_time: Duration::from_millis(1),
                metadata,
                analyzed_data: Some(data.to_vec()),
            }));
        }
        
        Ok(None)
    }
    
    fn min_data_size(&self) -> usize {
        2
    }
}

/// DNS protocol detector
pub struct DnsDetector {
    config: DetectorConfig,
}

impl DnsDetector {
    pub fn new(config: DetectorConfig) -> Self {
        Self { config }
    }
    
    /// Check if data looks like DNS query/response
    fn is_dns_packet(&self, data: &[u8]) -> Option<f64> {
        if data.len() < 12 {
            return None;
        }
        
        // DNS header is 12 bytes
        let flags = u16::from_be_bytes([data[2], data[3]]);
        let qdcount = u16::from_be_bytes([data[4], data[5]]);
        let ancount = u16::from_be_bytes([data[6], data[7]]);
        let nscount = u16::from_be_bytes([data[8], data[9]]);
        let arcount = u16::from_be_bytes([data[10], data[11]]);
        
        // Check if it looks like a valid DNS packet
        let qr = (flags >> 15) & 1; // Query/Response flag
        let opcode = (flags >> 11) & 0xF; // Opcode
        let rcode = flags & 0xF; // Response code
        
        // Basic validation
        if opcode <= 2 && rcode <= 5 && (qdcount > 0 || ancount > 0) {
            let mut confidence = 0.7;
            
            // Higher confidence for standard queries
            if opcode == 0 && qdcount == 1 {
                confidence = 0.85;
            }
            
            // Even higher for responses with answers
            if qr == 1 && ancount > 0 {
                confidence = 0.90;
            }
            
            return Some(confidence);
        }
        
        None
    }
}

#[async_trait]
impl ProtocolDetector for DnsDetector {
    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn priority(&self) -> u32 {
        self.config.priority
    }
    
    async fn detect(&self, data: &[u8], _context: &SniffContext) -> Result<Option<DetectedProtocol>, String> {
        if let Some(confidence) = self.is_dns_packet(data) {
            let mut metadata = HashMap::new();
            
            if data.len() >= 12 {
                let flags = u16::from_be_bytes([data[2], data[3]]);
                let qr = (flags >> 15) & 1;
                let opcode = (flags >> 11) & 0xF;
                let qdcount = u16::from_be_bytes([data[4], data[5]]);
                let ancount = u16::from_be_bytes([data[6], data[7]]);
                
                metadata.insert("type".to_string(), if qr == 0 { "query".to_string() } else { "response".to_string() });
                metadata.insert("opcode".to_string(), opcode.to_string());
                metadata.insert("questions".to_string(), qdcount.to_string());
                metadata.insert("answers".to_string(), ancount.to_string());
            }
            
            return Ok(Some(DetectedProtocol {
                protocol: "dns".to_string(),
                confidence,
                detector: self.name().to_string(),
                detection_time: Duration::from_millis(1),
                metadata,
                analyzed_data: Some(data.to_vec()),
            }));
        }
        
        Ok(None)
    }
    
    fn min_data_size(&self) -> usize {
        12
    }
}

/// QUIC protocol detector
pub struct QuicDetector {
    config: DetectorConfig,
}

impl QuicDetector {
    pub fn new(config: DetectorConfig) -> Self {
        Self { config }
    }
    
    /// Check if data looks like QUIC packet
    fn is_quic_packet(&self, data: &[u8]) -> Option<f64> {
        if data.len() < 1 {
            return None;
        }
        
        let first_byte = data[0];
        
        // QUIC long header packets
        if (first_byte & 0x80) != 0 {
            if data.len() < 6 {
                return None;
            }
            
            // Check version
            let version = u32::from_be_bytes([data[1], data[2], data[3], data[4]]);
            
            // Known QUIC versions
            match version {
                0x00000001 => return Some(0.95), // QUIC v1
                0xff000020..=0xff00002f => return Some(0.90), // Draft versions
                0x00000000 => return Some(0.85), // Version negotiation
                _ => {}
            }
        }
        
        // QUIC short header packets (harder to detect reliably)
        if (first_byte & 0x80) == 0 && (first_byte & 0x40) != 0 {
            return Some(0.60);
        }
        
        None
    }
}

#[async_trait]
impl ProtocolDetector for QuicDetector {
    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn priority(&self) -> u32 {
        self.config.priority
    }
    
    async fn detect(&self, data: &[u8], _context: &SniffContext) -> Result<Option<DetectedProtocol>, String> {
        if let Some(confidence) = self.is_quic_packet(data) {
            let mut metadata = HashMap::new();
            
            if data.len() >= 5 {
                let first_byte = data[0];
                let is_long_header = (first_byte & 0x80) != 0;
                
                metadata.insert("header_type".to_string(), 
                    if is_long_header { "long".to_string() } else { "short".to_string() });
                
                if is_long_header && data.len() >= 5 {
                    let version = u32::from_be_bytes([data[1], data[2], data[3], data[4]]);
                    metadata.insert("version".to_string(), format!("0x{:08x}", version));
                }
            }
            
            return Ok(Some(DetectedProtocol {
                protocol: "quic".to_string(),
                confidence,
                detector: self.name().to_string(),
                detection_time: Duration::from_millis(1),
                metadata,
                analyzed_data: Some(data.to_vec()),
            }));
        }
        
        Ok(None)
    }
    
    fn min_data_size(&self) -> usize {
        1
    }
}

/// WebSocket protocol detector
pub struct WebSocketDetector {
    config: DetectorConfig,
}

impl WebSocketDetector {
    pub fn new(config: DetectorConfig) -> Self {
        Self { config }
    }
    
    /// Check if data looks like WebSocket handshake
    fn is_websocket_handshake(&self, data: &[u8]) -> Option<f64> {
        let data_str = String::from_utf8_lossy(data);
        let lines: Vec<&str> = data_str.lines().collect();
        
        if lines.is_empty() {
            return None;
        }
        
        // Check for WebSocket upgrade request
        let first_line = lines[0];
        if first_line.starts_with("GET") && first_line.contains("HTTP/1.1") {
            let mut has_upgrade = false;
            let mut has_connection = false;
            let mut has_websocket_key = false;
            
            for line in &lines[1..] {
                let line_lower = line.to_lowercase();
                if line_lower.starts_with("upgrade:") && line_lower.contains("websocket") {
                    has_upgrade = true;
                } else if line_lower.starts_with("connection:") && line_lower.contains("upgrade") {
                    has_connection = true;
                } else if line_lower.starts_with("sec-websocket-key:") {
                    has_websocket_key = true;
                }
            }
            
            if has_upgrade && has_connection && has_websocket_key {
                return Some(0.98);
            }
        }
        
        // Check for WebSocket upgrade response
        if first_line.starts_with("HTTP/1.1 101") {
            for line in &lines[1..] {
                let line_lower = line.to_lowercase();
                if line_lower.starts_with("upgrade:") && line_lower.contains("websocket") {
                    return Some(0.95);
                }
            }
        }
        
        None
    }
}

#[async_trait]
impl ProtocolDetector for WebSocketDetector {
    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn priority(&self) -> u32 {
        self.config.priority
    }
    
    async fn detect(&self, data: &[u8], _context: &SniffContext) -> Result<Option<DetectedProtocol>, String> {
        if let Some(confidence) = self.is_websocket_handshake(data) {
            let mut metadata = HashMap::new();
            
            let data_str = String::from_utf8_lossy(data);
            if let Some(first_line) = data_str.lines().next() {
                if first_line.starts_with("GET") {
                    metadata.insert("type".to_string(), "request".to_string());
                } else if first_line.starts_with("HTTP/1.1 101") {
                    metadata.insert("type".to_string(), "response".to_string());
                }
            }
            
            return Ok(Some(DetectedProtocol {
                protocol: "websocket".to_string(),
                confidence,
                detector: self.name().to_string(),
                detection_time: Duration::from_millis(1),
                metadata,
                analyzed_data: Some(data.to_vec()),
            }));
        }
        
        Ok(None)
    }
    
    fn min_data_size(&self) -> usize {
        20
    }
}

/// SOCKS protocol detector
pub struct SocksDetector {
    config: DetectorConfig,
}

impl SocksDetector {
    pub fn new(config: DetectorConfig) -> Self {
        Self { config }
    }
    
    /// Check if data looks like SOCKS protocol
    fn is_socks_protocol(&self, data: &[u8]) -> Option<f64> {
        if data.len() < 3 {
            return None;
        }
        
        // SOCKS5 greeting
        if data[0] == 0x05 && data.len() >= 3 {
            let nmethods = data[1] as usize;
            if data.len() >= 2 + nmethods && nmethods > 0 && nmethods <= 255 {
                return Some(0.90);
            }
        }
        
        // SOCKS4 connect request
        if data[0] == 0x04 && data.len() >= 8 {
            let command = data[1];
            if command == 0x01 { // CONNECT
                return Some(0.85);
            }
        }
        
        None
    }
}

#[async_trait]
impl ProtocolDetector for SocksDetector {
    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn priority(&self) -> u32 {
        self.config.priority
    }
    
    async fn detect(&self, data: &[u8], _context: &SniffContext) -> Result<Option<DetectedProtocol>, String> {
        if let Some(confidence) = self.is_socks_protocol(data) {
            let mut metadata = HashMap::new();
            
            if data.len() >= 1 {
                let version = data[0];
                metadata.insert("version".to_string(), version.to_string());
                
                if version == 0x05 && data.len() >= 2 {
                    metadata.insert("nmethods".to_string(), data[1].to_string());
                } else if version == 0x04 && data.len() >= 2 {
                    metadata.insert("command".to_string(), data[1].to_string());
                }
            }
            
            return Ok(Some(DetectedProtocol {
                protocol: "socks".to_string(),
                confidence,
                detector: self.name().to_string(),
                detection_time: Duration::from_millis(1),
                metadata,
                analyzed_data: Some(data.to_vec()),
            }));
        }
        
        Ok(None)
    }
    
    fn min_data_size(&self) -> usize {
        3
    }
}

/// Pattern-based detector
pub struct PatternDetector {
    config: DetectorConfig,
    pattern: String,
}

impl PatternDetector {
    pub fn new(config: DetectorConfig, pattern: String) -> Self {
        Self { config, pattern }
    }
}

#[async_trait]
impl ProtocolDetector for PatternDetector {
    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn priority(&self) -> u32 {
        self.config.priority
    }
    
    async fn detect(&self, data: &[u8], _context: &SniffContext) -> Result<Option<DetectedProtocol>, String> {
        // Simple pattern matching (could be enhanced with regex)
        let data_str = String::from_utf8_lossy(data);
        
        if data_str.contains(&self.pattern) {
            return Ok(Some(DetectedProtocol {
                protocol: "custom".to_string(),
                confidence: 0.70,
                detector: self.name().to_string(),
                detection_time: Duration::from_millis(1),
                metadata: HashMap::new(),
                analyzed_data: Some(data.to_vec()),
            }));
        }
        
        Ok(None)
    }
}

/// Machine learning-based detector (placeholder)
pub struct MlDetector {
    config: DetectorConfig,
    model: String,
}

impl MlDetector {
    pub fn new(config: DetectorConfig, model: String) -> Self {
        Self { config, model }
    }
}

#[async_trait]
impl ProtocolDetector for MlDetector {
    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn priority(&self) -> u32 {
        self.config.priority
    }
    
    async fn detect(&self, _data: &[u8], _context: &SniffContext) -> Result<Option<DetectedProtocol>, String> {
        // Placeholder for ML-based detection
        // In a real implementation, this would use a trained model
        Ok(None)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::SocketAddr;
    use std::time::Instant;
    
    fn create_test_context() -> SniffContext {
        SniffContext {
            source_addr: "127.0.0.1:12345".parse().unwrap(),
            dest_addr: "127.0.0.1:80".parse().unwrap(),
            start_time: Instant::now(),
            bytes_received: 0,
            metadata: HashMap::new(),
        }
    }
    
    #[tokio::test]
    async fn test_http_detector() {
        let config = DetectorConfig {
            name: "http".to_string(),
            detector_type: super::super::DetectorType::Http,
            priority: 100,
            enabled: true,
            parameters: HashMap::new(),
        };
        
        let detector = HttpDetector::new(config);
        let context = create_test_context();
        
        // Test HTTP request
        let http_data = b"GET / HTTP/1.1\r\nHost: example.com\r\n\r\n";
        let result = detector.detect(http_data, &context).await.unwrap();
        
        assert!(result.is_some());
        let detected = result.unwrap();
        assert_eq!(detected.protocol, "http");
        assert!(detected.confidence > 0.9);
    }
    
    #[tokio::test]
    async fn test_tls_detector() {
        let config = DetectorConfig {
            name: "tls".to_string(),
            detector_type: super::super::DetectorType::Tls,
            priority: 90,
            enabled: true,
            parameters: HashMap::new(),
        };
        
        let detector = TlsDetector::new(config);
        let context = create_test_context();
        
        // Test TLS handshake (simplified)
        let tls_data = &[0x16, 0x03, 0x01, 0x00, 0x20]; // Handshake, TLS 1.0
        let result = detector.detect(tls_data, &context).await.unwrap();
        
        assert!(result.is_some());
        let detected = result.unwrap();
        assert_eq!(detected.protocol, "tls");
        assert!(detected.confidence > 0.9);
    }
    
    #[tokio::test]
    async fn test_ssh_detector() {
        let config = DetectorConfig {
            name: "ssh".to_string(),
            detector_type: super::super::DetectorType::Ssh,
            priority: 80,
            enabled: true,
            parameters: HashMap::new(),
        };
        
        let detector = SshDetector::new(config);
        let context = create_test_context();
        
        // Test SSH banner
        let ssh_data = b"SSH-2.0-OpenSSH_8.0\r\n";
        let result = detector.detect(ssh_data, &context).await.unwrap();
        
        assert!(result.is_some());
        let detected = result.unwrap();
        assert_eq!(detected.protocol, "ssh");
        assert!(detected.confidence > 0.9);
    }
    
    #[tokio::test]
    async fn test_dns_detector() {
        let config = DetectorConfig {
            name: "dns".to_string(),
            detector_type: super::super::DetectorType::Dns,
            priority: 70,
            enabled: true,
            parameters: HashMap::new(),
        };
        
        let detector = DnsDetector::new(config);
        let context = create_test_context();
        
        // Test DNS query (simplified)
        let dns_data = &[
            0x12, 0x34, // Transaction ID
            0x01, 0x00, // Flags (standard query)
            0x00, 0x01, // Questions: 1
            0x00, 0x00, // Answer RRs: 0
            0x00, 0x00, // Authority RRs: 0
            0x00, 0x00, // Additional RRs: 0
        ];
        let result = detector.detect(dns_data, &context).await.unwrap();
        
        assert!(result.is_some());
        let detected = result.unwrap();
        assert_eq!(detected.protocol, "dns");
        assert!(detected.confidence > 0.7);
    }
}
