//! Sniffing rules and actions
//!
//! This module defines rules for protocol sniffing behavior
//! and actions to take based on detection results.

use std::collections::HashMap;
use std::net::{IpAddr, SocketAddr};
use std::time::Duration;
use serde::{Deserialize, Serialize};

use super::{SniffContext, DetectedProtocol};

/// Sniffing rule
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SniffRule {
    /// Rule name
    pub name: String,
    
    /// Rule description
    pub description: String,
    
    /// Rule conditions
    pub conditions: Vec<RuleCondition>,
    
    /// Rule actions
    pub actions: Vec<SniffAction>,
    
    /// Rule priority (higher = more important)
    pub priority: u32,
    
    /// Rule enabled
    pub enabled: bool,
    
    /// Rule tags
    pub tags: Vec<String>,
}

/// Rule condition
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum RuleCondition {
    /// Source IP condition
    SourceIp {
        /// IP addresses or CIDR ranges
        addresses: Vec<String>,
        /// Negate condition
        negate: bool,
    },
    
    /// Destination IP condition
    DestinationIp {
        /// IP addresses or CIDR ranges
        addresses: Vec<String>,
        /// Negate condition
        negate: bool,
    },
    
    /// Source port condition
    SourcePort {
        /// Port numbers or ranges
        ports: Vec<PortRange>,
        /// Negate condition
        negate: bool,
    },
    
    /// Destination port condition
    DestinationPort {
        /// Port numbers or ranges
        ports: Vec<PortRange>,
        /// Negate condition
        negate: bool,
    },
    
    /// Detected protocol condition
    DetectedProtocol {
        /// Protocol names
        protocols: Vec<String>,
        /// Minimum confidence
        min_confidence: Option<f64>,
        /// Negate condition
        negate: bool,
    },
    
    /// Time-based condition
    TimeRange {
        /// Start time (HH:MM)
        start: String,
        /// End time (HH:MM)
        end: String,
        /// Days of week (0=Sunday, 6=Saturday)
        days: Vec<u8>,
        /// Negate condition
        negate: bool,
    },
    
    /// Connection count condition
    ConnectionCount {
        /// Minimum connections
        min: Option<u32>,
        /// Maximum connections
        max: Option<u32>,
        /// Time window
        window: Duration,
        /// Negate condition
        negate: bool,
    },
    
    /// Data size condition
    DataSize {
        /// Minimum size
        min: Option<usize>,
        /// Maximum size
        max: Option<usize>,
        /// Negate condition
        negate: bool,
    },
    
    /// Custom condition
    Custom {
        /// Condition name
        name: String,
        /// Condition parameters
        parameters: HashMap<String, serde_json::Value>,
        /// Negate condition
        negate: bool,
    },
}

/// Port range specification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PortRange {
    /// Single port
    Single(u16),
    
    /// Port range (inclusive)
    Range(u16, u16),
    
    /// Well-known port names
    Named(String),
}

/// Sniffing actions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SniffAction {
    /// Allow the connection
    Allow,
    
    /// Block the connection
    Block,
    
    /// Force specific protocol detection
    ForceProtocol {
        /// Protocol to force
        protocol: String,
        /// Confidence to assign
        confidence: f64,
    },
    
    /// Skip further sniffing
    SkipSniffing,
    
    /// Continue sniffing with modified parameters
    ContinueSniffing {
        /// New timeout
        timeout: Option<Duration>,
        /// New max bytes
        max_bytes: Option<usize>,
    },
    
    /// Log the event
    Log {
        /// Log level
        level: LogLevel,
        /// Log message template
        message: String,
    },
    
    /// Send alert
    Alert {
        /// Alert severity
        severity: AlertSeverity,
        /// Alert message
        message: String,
        /// Alert recipients
        recipients: Vec<String>,
    },
    
    /// Apply rate limiting
    RateLimit {
        /// Requests per second
        rps: u32,
        /// Time window
        window: Duration,
    },
    
    /// Redirect to different handler
    Redirect {
        /// Target handler
        handler: String,
        /// Redirect parameters
        parameters: HashMap<String, String>,
    },
    
    /// Execute custom action
    Custom {
        /// Action name
        name: String,
        /// Action parameters
        parameters: HashMap<String, serde_json::Value>,
    },
}

/// Log levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogLevel {
    Debug,
    Info,
    Warning,
    Error,
    Critical,
}

/// Alert severities
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Rule engine for processing sniffing rules
pub struct RuleEngine {
    /// Configured rules
    rules: Vec<SniffRule>,
    
    /// Rule statistics
    stats: HashMap<String, RuleStats>,
    
    /// Connection tracking
    connection_tracker: ConnectionTracker,
}

/// Rule execution statistics
#[derive(Debug, Clone)]
pub struct RuleStats {
    /// Rule name
    pub rule_name: String,
    
    /// Times rule was evaluated
    pub evaluations: u64,
    
    /// Times rule matched
    pub matches: u64,
    
    /// Times rule actions were executed
    pub actions_executed: u64,
    
    /// Last match time
    pub last_match: Option<std::time::SystemTime>,
    
    /// Average execution time
    pub avg_execution_time: Duration,
}

/// Connection tracker for rate limiting and counting
pub struct ConnectionTracker {
    /// Connection counts by IP
    ip_connections: HashMap<IpAddr, ConnectionCount>,
    
    /// Connection history
    connection_history: Vec<ConnectionRecord>,
    
    /// Cleanup interval
    cleanup_interval: Duration,
}

/// Connection count information
#[derive(Debug, Clone)]
pub struct ConnectionCount {
    /// Current active connections
    pub active: u32,
    
    /// Total connections in time window
    pub total: u32,
    
    /// Last connection time
    pub last_connection: std::time::SystemTime,
    
    /// Connection timestamps
    pub timestamps: Vec<std::time::SystemTime>,
}

/// Connection record
#[derive(Debug, Clone)]
pub struct ConnectionRecord {
    /// Source address
    pub source: SocketAddr,
    
    /// Destination address
    pub destination: SocketAddr,
    
    /// Connection time
    pub timestamp: std::time::SystemTime,
    
    /// Detected protocol
    pub protocol: Option<String>,
    
    /// Connection duration
    pub duration: Option<Duration>,
}

impl RuleEngine {
    /// Create a new rule engine
    pub fn new(rules: Vec<SniffRule>) -> Self {
        Self {
            rules,
            stats: HashMap::new(),
            connection_tracker: ConnectionTracker::new(),
        }
    }
    
    /// Evaluate rules for a sniffing context
    pub async fn evaluate_rules(
        &mut self,
        context: &SniffContext,
        detected: Option<&DetectedProtocol>,
        data: &[u8],
    ) -> Vec<SniffAction> {
        let mut actions = Vec::new();
        
        // Sort rules by priority (higher first)
        let mut sorted_rules = self.rules.clone();
        sorted_rules.sort_by(|a, b| b.priority.cmp(&a.priority));
        
        for rule in &sorted_rules {
            if !rule.enabled {
                continue;
            }
            
            let start_time = std::time::Instant::now();
            
            // Update evaluation count
            let stats = self.stats.entry(rule.name.clone()).or_insert_with(|| RuleStats {
                rule_name: rule.name.clone(),
                evaluations: 0,
                matches: 0,
                actions_executed: 0,
                last_match: None,
                avg_execution_time: Duration::ZERO,
            });
            stats.evaluations += 1;
            
            // Check if rule matches
            if self.rule_matches(rule, context, detected, data).await {
                stats.matches += 1;
                stats.last_match = Some(std::time::SystemTime::now());
                
                // Execute rule actions
                for action in &rule.actions {
                    actions.push(action.clone());
                    stats.actions_executed += 1;
                }
            }
            
            // Update execution time
            let execution_time = start_time.elapsed();
            stats.avg_execution_time = Duration::from_nanos(
                (stats.avg_execution_time.as_nanos() as u64 * (stats.evaluations - 1) + 
                 execution_time.as_nanos() as u64) / stats.evaluations
            );
        }
        
        actions
    }
    
    /// Check if a rule matches the current context
    async fn rule_matches(
        &mut self,
        rule: &SniffRule,
        context: &SniffContext,
        detected: Option<&DetectedProtocol>,
        data: &[u8],
    ) -> bool {
        for condition in &rule.conditions {
            if !self.condition_matches(condition, context, detected, data).await {
                return false;
            }
        }
        true
    }
    
    /// Check if a condition matches
    async fn condition_matches(
        &mut self,
        condition: &RuleCondition,
        context: &SniffContext,
        detected: Option<&DetectedProtocol>,
        data: &[u8],
    ) -> bool {
        let matches = match condition {
            RuleCondition::SourceIp { addresses, negate } => {
                self.ip_matches(&context.source_addr.ip(), addresses) != *negate
            },
            RuleCondition::DestinationIp { addresses, negate } => {
                self.ip_matches(&context.dest_addr.ip(), addresses) != *negate
            },
            RuleCondition::SourcePort { ports, negate } => {
                self.port_matches(context.source_addr.port(), ports) != *negate
            },
            RuleCondition::DestinationPort { ports, negate } => {
                self.port_matches(context.dest_addr.port(), ports) != *negate
            },
            RuleCondition::DetectedProtocol { protocols, min_confidence, negate } => {
                if let Some(detected_proto) = detected {
                    let protocol_matches = protocols.contains(&detected_proto.protocol);
                    let confidence_matches = min_confidence.map_or(true, |min| detected_proto.confidence >= min);
                    (protocol_matches && confidence_matches) != *negate
                } else {
                    *negate // If no protocol detected, only match if negated
                }
            },
            RuleCondition::TimeRange { start, end, days, negate } => {
                self.time_matches(start, end, days) != *negate
            },
            RuleCondition::ConnectionCount { min, max, window, negate } => {
                let count = self.connection_tracker.get_connection_count(&context.source_addr.ip(), *window);
                let matches_min = min.map_or(true, |m| count >= m);
                let matches_max = max.map_or(true, |m| count <= m);
                (matches_min && matches_max) != *negate
            },
            RuleCondition::DataSize { min, max, negate } => {
                let size = data.len();
                let matches_min = min.map_or(true, |m| size >= m);
                let matches_max = max.map_or(true, |m| size <= m);
                (matches_min && matches_max) != *negate
            },
            RuleCondition::Custom { name: _, parameters: _, negate } => {
                // Custom condition evaluation would be implemented here
                !negate // Default to not matching unless negated
            },
        };
        
        matches
    }
    
    /// Check if IP matches any of the given addresses/ranges
    fn ip_matches(&self, ip: &IpAddr, addresses: &[String]) -> bool {
        for addr_str in addresses {
            if addr_str == "*" {
                return true;
            }
            
            // Try exact IP match
            if let Ok(addr) = addr_str.parse::<IpAddr>() {
                if *ip == addr {
                    return true;
                }
            }
            
            // Try CIDR match
            if let Ok(network) = addr_str.parse::<ipnet::IpNet>() {
                if network.contains(ip) {
                    return true;
                }
            }
        }
        false
    }
    
    /// Check if port matches any of the given port ranges
    fn port_matches(&self, port: u16, ranges: &[PortRange]) -> bool {
        for range in ranges {
            match range {
                PortRange::Single(p) => {
                    if port == *p {
                        return true;
                    }
                },
                PortRange::Range(start, end) => {
                    if port >= *start && port <= *end {
                        return true;
                    }
                },
                PortRange::Named(name) => {
                    if let Some(well_known_port) = self.resolve_well_known_port(name) {
                        if port == well_known_port {
                            return true;
                        }
                    }
                },
            }
        }
        false
    }
    
    /// Resolve well-known port names
    fn resolve_well_known_port(&self, name: &str) -> Option<u16> {
        match name.to_lowercase().as_str() {
            "http" => Some(80),
            "https" => Some(443),
            "ftp" => Some(21),
            "ssh" => Some(22),
            "telnet" => Some(23),
            "smtp" => Some(25),
            "dns" => Some(53),
            "pop3" => Some(110),
            "imap" => Some(143),
            "snmp" => Some(161),
            "ldap" => Some(389),
            "smtps" => Some(465),
            "imaps" => Some(993),
            "pop3s" => Some(995),
            "socks" => Some(1080),
            _ => None,
        }
    }
    
    /// Check if current time matches time range
    fn time_matches(&self, start: &str, end: &str, days: &[u8]) -> bool {
        let now = chrono::Local::now();
        let current_day = now.weekday().num_days_from_sunday() as u8;
        let current_time = now.time();
        
        // Check day
        if !days.is_empty() && !days.contains(&current_day) {
            return false;
        }
        
        // Parse time ranges
        let start_time = if let Ok(time) = chrono::NaiveTime::parse_from_str(start, "%H:%M") {
            time
        } else {
            return false;
        };
        
        let end_time = if let Ok(time) = chrono::NaiveTime::parse_from_str(end, "%H:%M") {
            time
        } else {
            return false;
        };
        
        // Check time range
        if start_time <= end_time {
            current_time >= start_time && current_time <= end_time
        } else {
            // Crosses midnight
            current_time >= start_time || current_time <= end_time
        }
    }
    
    /// Get rule statistics
    pub fn get_rule_stats(&self) -> Vec<RuleStats> {
        self.stats.values().cloned().collect()
    }
    
    /// Reset rule statistics
    pub fn reset_stats(&mut self) {
        self.stats.clear();
    }
}

impl ConnectionTracker {
    /// Create a new connection tracker
    pub fn new() -> Self {
        Self {
            ip_connections: HashMap::new(),
            connection_history: Vec::new(),
            cleanup_interval: Duration::from_secs(300), // 5 minutes
        }
    }
    
    /// Record a new connection
    pub fn record_connection(&mut self, source: SocketAddr, destination: SocketAddr, protocol: Option<String>) {
        let now = std::time::SystemTime::now();
        
        // Update IP connection count
        let ip_count = self.ip_connections.entry(source.ip()).or_insert_with(|| ConnectionCount {
            active: 0,
            total: 0,
            last_connection: now,
            timestamps: Vec::new(),
        });
        
        ip_count.active += 1;
        ip_count.total += 1;
        ip_count.last_connection = now;
        ip_count.timestamps.push(now);
        
        // Add to history
        self.connection_history.push(ConnectionRecord {
            source,
            destination,
            timestamp: now,
            protocol,
            duration: None,
        });
        
        // Cleanup old entries
        self.cleanup_old_entries();
    }
    
    /// Get connection count for IP in time window
    pub fn get_connection_count(&self, ip: &IpAddr, window: Duration) -> u32 {
        if let Some(count_info) = self.ip_connections.get(ip) {
            let cutoff_time = std::time::SystemTime::now() - window;
            count_info.timestamps.iter()
                .filter(|&&timestamp| timestamp > cutoff_time)
                .count() as u32
        } else {
            0
        }
    }
    
    /// Cleanup old connection entries
    fn cleanup_old_entries(&mut self) {
        let cutoff_time = std::time::SystemTime::now() - self.cleanup_interval;
        
        // Clean up IP connections
        for (_, count_info) in self.ip_connections.iter_mut() {
            count_info.timestamps.retain(|&timestamp| timestamp > cutoff_time);
            count_info.total = count_info.timestamps.len() as u32;
        }
        
        // Remove empty entries
        self.ip_connections.retain(|_, count_info| !count_info.timestamps.is_empty());
        
        // Clean up connection history
        self.connection_history.retain(|record| record.timestamp > cutoff_time);
    }
}

impl Default for RuleEngine {
    fn default() -> Self {
        Self::new(Vec::new())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Instant;
    
    #[test]
    fn test_port_range_matching() {
        let engine = RuleEngine::default();
        
        let ranges = vec![
            PortRange::Single(80),
            PortRange::Range(8000, 8999),
            PortRange::Named("https".to_string()),
        ];
        
        assert!(engine.port_matches(80, &ranges));
        assert!(engine.port_matches(8080, &ranges));
        assert!(engine.port_matches(443, &ranges));
        assert!(!engine.port_matches(22, &ranges));
    }
    
    #[test]
    fn test_ip_matching() {
        let engine = RuleEngine::default();
        
        let addresses = vec![
            "***********".to_string(),
            "10.0.0.0/8".to_string(),
            "*".to_string(),
        ];
        
        let ip1: IpAddr = "***********".parse().unwrap();
        let ip2: IpAddr = "**********".parse().unwrap();
        let ip3: IpAddr = "**********".parse().unwrap();
        
        assert!(engine.ip_matches(&ip1, &addresses));
        assert!(engine.ip_matches(&ip2, &addresses));
        assert!(engine.ip_matches(&ip3, &addresses)); // Matches wildcard
    }
    
    #[test]
    fn test_well_known_ports() {
        let engine = RuleEngine::default();
        
        assert_eq!(engine.resolve_well_known_port("http"), Some(80));
        assert_eq!(engine.resolve_well_known_port("https"), Some(443));
        assert_eq!(engine.resolve_well_known_port("ssh"), Some(22));
        assert_eq!(engine.resolve_well_known_port("unknown"), None);
    }
    
    #[tokio::test]
    async fn test_rule_evaluation() {
        let rule = SniffRule {
            name: "test_rule".to_string(),
            description: "Test rule".to_string(),
            conditions: vec![
                RuleCondition::DestinationPort {
                    ports: vec![PortRange::Single(80)],
                    negate: false,
                },
            ],
            actions: vec![SniffAction::Allow],
            priority: 100,
            enabled: true,
            tags: vec!["test".to_string()],
        };
        
        let mut engine = RuleEngine::new(vec![rule]);
        
        let context = super::super::SniffContext {
            source_addr: "127.0.0.1:12345".parse().unwrap(),
            dest_addr: "127.0.0.1:80".parse().unwrap(),
            start_time: Instant::now(),
            bytes_received: 0,
            metadata: HashMap::new(),
        };
        
        let actions = engine.evaluate_rules(&context, None, b"test data").await;
        
        assert_eq!(actions.len(), 1);
        assert!(matches!(actions[0], SniffAction::Allow));
    }
    
    #[test]
    fn test_connection_tracker() {
        let mut tracker = ConnectionTracker::new();
        
        let source: SocketAddr = "127.0.0.1:12345".parse().unwrap();
        let dest: SocketAddr = "127.0.0.1:80".parse().unwrap();
        
        tracker.record_connection(source, dest, Some("http".to_string()));
        
        let count = tracker.get_connection_count(&source.ip(), Duration::from_secs(60));
        assert_eq!(count, 1);
    }
}
