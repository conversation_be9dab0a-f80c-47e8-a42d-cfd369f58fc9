//! Protocol pattern matching for sniffing
//!
//! This module provides pattern-based protocol detection using
//! predefined patterns and signatures.

use std::collections::HashMap;
use std::time::Duration;
use serde::{Deserialize, Serialize};

use super::{Sniff<PERSON>ontext, DetectedProtocol};

/// Protocol pattern definition
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProtocolPattern {
    /// Pattern name
    pub name: String,
    
    /// Protocol name
    pub protocol: String,
    
    /// Pattern type
    pub pattern_type: PatternType,
    
    /// Pattern data
    pub pattern: String,
    
    /// Pattern offset (bytes from start)
    pub offset: usize,
    
    /// Pattern confidence
    pub confidence: f64,
    
    /// Minimum data size required
    pub min_size: usize,
    
    /// Maximum data size to check
    pub max_size: usize,
    
    /// Case sensitive matching
    pub case_sensitive: bool,
    
    /// Pattern description
    pub description: String,
}

/// Pattern types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PatternType {
    /// Exact byte sequence
    Bytes,
    
    /// String pattern
    String,
    
    /// Regular expression
    Regex,
    
    /// Magic number
    Magic,
    
    /// Header pattern
    Header,
    
    /// Composite pattern (multiple conditions)
    Composite(Vec<PatternCondition>),
}

/// Pattern condition for composite patterns
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternCondition {
    /// Condition type
    pub condition_type: ConditionType,
    
    /// Pattern to match
    pub pattern: String,
    
    /// Offset for this condition
    pub offset: usize,
    
    /// Length to check
    pub length: Option<usize>,
}

/// Condition types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConditionType {
    /// Must match
    Must,
    
    /// Should match (optional)
    Should,
    
    /// Must not match
    MustNot,
}

/// Pattern matcher
pub struct PatternMatcher {
    /// Pattern definition
    pattern: ProtocolPattern,
    
    /// Compiled regex (if applicable)
    regex: Option<regex::Regex>,
}

impl PatternMatcher {
    /// Create a new pattern matcher
    pub fn new(pattern: ProtocolPattern) -> Self {
        let regex = if let PatternType::Regex = pattern.pattern_type {
            regex::Regex::new(&pattern.pattern).ok()
        } else {
            None
        };
        
        Self { pattern, regex }
    }
    
    /// Match pattern against data
    pub async fn match_pattern(&self, data: &[u8], context: &SniffContext) -> Option<DetectedProtocol> {
        if data.len() < self.pattern.min_size {
            return None;
        }
        
        let check_size = std::cmp::min(data.len(), self.pattern.max_size);
        let check_data = &data[..check_size];
        
        let matches = match &self.pattern.pattern_type {
            PatternType::Bytes => self.match_bytes(check_data),
            PatternType::String => self.match_string(check_data),
            PatternType::Regex => self.match_regex(check_data),
            PatternType::Magic => self.match_magic(check_data),
            PatternType::Header => self.match_header(check_data),
            PatternType::Composite(conditions) => self.match_composite(check_data, conditions),
        };
        
        if matches {
            let mut metadata = HashMap::new();
            metadata.insert("pattern_name".to_string(), self.pattern.name.clone());
            metadata.insert("pattern_type".to_string(), format!("{:?}", self.pattern.pattern_type));
            
            Some(DetectedProtocol {
                protocol: self.pattern.protocol.clone(),
                confidence: self.pattern.confidence,
                detector: format!("pattern_{}", self.pattern.name),
                detection_time: Duration::from_millis(1),
                metadata,
                analyzed_data: Some(check_data.to_vec()),
            })
        } else {
            None
        }
    }
    
    /// Match byte pattern
    fn match_bytes(&self, data: &[u8]) -> bool {
        let pattern_bytes = hex::decode(&self.pattern.pattern).unwrap_or_default();
        
        if pattern_bytes.is_empty() {
            return false;
        }
        
        if self.pattern.offset + pattern_bytes.len() > data.len() {
            return false;
        }
        
        let data_slice = &data[self.pattern.offset..self.pattern.offset + pattern_bytes.len()];
        data_slice == pattern_bytes
    }
    
    /// Match string pattern
    fn match_string(&self, data: &[u8]) -> bool {
        let data_str = String::from_utf8_lossy(data);
        
        if self.pattern.case_sensitive {
            data_str.contains(&self.pattern.pattern)
        } else {
            data_str.to_lowercase().contains(&self.pattern.pattern.to_lowercase())
        }
    }
    
    /// Match regex pattern
    fn match_regex(&self, data: &[u8]) -> bool {
        if let Some(ref regex) = self.regex {
            let data_str = String::from_utf8_lossy(data);
            regex.is_match(&data_str)
        } else {
            false
        }
    }
    
    /// Match magic number
    fn match_magic(&self, data: &[u8]) -> bool {
        let magic_bytes = hex::decode(&self.pattern.pattern).unwrap_or_default();
        
        if magic_bytes.is_empty() || data.len() < magic_bytes.len() {
            return false;
        }
        
        &data[..magic_bytes.len()] == magic_bytes
    }
    
    /// Match header pattern
    fn match_header(&self, data: &[u8]) -> bool {
        let data_str = String::from_utf8_lossy(data);
        let lines: Vec<&str> = data_str.lines().collect();
        
        if lines.is_empty() {
            return false;
        }
        
        // Check first line for header pattern
        let first_line = lines[0];
        if self.pattern.case_sensitive {
            first_line.contains(&self.pattern.pattern)
        } else {
            first_line.to_lowercase().contains(&self.pattern.pattern.to_lowercase())
        }
    }
    
    /// Match composite pattern
    fn match_composite(&self, data: &[u8], conditions: &[PatternCondition]) -> bool {
        let mut must_matches = 0;
        let mut must_total = 0;
        let mut should_matches = 0;
        let mut must_not_violations = 0;
        
        for condition in conditions {
            let matches = self.match_condition(data, condition);
            
            match condition.condition_type {
                ConditionType::Must => {
                    must_total += 1;
                    if matches {
                        must_matches += 1;
                    }
                },
                ConditionType::Should => {
                    if matches {
                        should_matches += 1;
                    }
                },
                ConditionType::MustNot => {
                    if matches {
                        must_not_violations += 1;
                    }
                },
            }
        }
        
        // All MUST conditions must match, no MUST_NOT violations
        must_matches == must_total && must_not_violations == 0
    }
    
    /// Match individual condition
    fn match_condition(&self, data: &[u8], condition: &PatternCondition) -> bool {
        let start = condition.offset;
        let end = if let Some(length) = condition.length {
            std::cmp::min(start + length, data.len())
        } else {
            data.len()
        };
        
        if start >= data.len() {
            return false;
        }
        
        let check_data = &data[start..end];
        let data_str = String::from_utf8_lossy(check_data);
        
        if self.pattern.case_sensitive {
            data_str.contains(&condition.pattern)
        } else {
            data_str.to_lowercase().contains(&condition.pattern.to_lowercase())
        }
    }
}

/// Load built-in protocol patterns
pub fn load_builtin_patterns() -> Vec<ProtocolPattern> {
    vec![
        // HTTP patterns
        ProtocolPattern {
            name: "http_get".to_string(),
            protocol: "http".to_string(),
            pattern_type: PatternType::String,
            pattern: "GET ".to_string(),
            offset: 0,
            confidence: 0.95,
            min_size: 4,
            max_size: 1024,
            case_sensitive: false,
            description: "HTTP GET request".to_string(),
        },
        ProtocolPattern {
            name: "http_post".to_string(),
            protocol: "http".to_string(),
            pattern_type: PatternType::String,
            pattern: "POST ".to_string(),
            offset: 0,
            confidence: 0.95,
            min_size: 5,
            max_size: 1024,
            case_sensitive: false,
            description: "HTTP POST request".to_string(),
        },
        ProtocolPattern {
            name: "http_response".to_string(),
            protocol: "http".to_string(),
            pattern_type: PatternType::String,
            pattern: "HTTP/".to_string(),
            offset: 0,
            confidence: 0.90,
            min_size: 5,
            max_size: 1024,
            case_sensitive: false,
            description: "HTTP response".to_string(),
        },
        
        // TLS patterns
        ProtocolPattern {
            name: "tls_handshake".to_string(),
            protocol: "tls".to_string(),
            pattern_type: PatternType::Bytes,
            pattern: "160301".to_string(), // TLS 1.0 handshake
            offset: 0,
            confidence: 0.95,
            min_size: 3,
            max_size: 64,
            case_sensitive: true,
            description: "TLS handshake".to_string(),
        },
        ProtocolPattern {
            name: "tls12_handshake".to_string(),
            protocol: "tls".to_string(),
            pattern_type: PatternType::Bytes,
            pattern: "160303".to_string(), // TLS 1.2 handshake
            offset: 0,
            confidence: 0.95,
            min_size: 3,
            max_size: 64,
            case_sensitive: true,
            description: "TLS 1.2 handshake".to_string(),
        },
        
        // SSH patterns
        ProtocolPattern {
            name: "ssh_banner".to_string(),
            protocol: "ssh".to_string(),
            pattern_type: PatternType::String,
            pattern: "SSH-".to_string(),
            offset: 0,
            confidence: 0.98,
            min_size: 4,
            max_size: 256,
            case_sensitive: true,
            description: "SSH protocol banner".to_string(),
        },
        
        // FTP patterns
        ProtocolPattern {
            name: "ftp_banner".to_string(),
            protocol: "ftp".to_string(),
            pattern_type: PatternType::String,
            pattern: "220 ".to_string(),
            offset: 0,
            confidence: 0.85,
            min_size: 4,
            max_size: 512,
            case_sensitive: false,
            description: "FTP welcome banner".to_string(),
        },
        
        // SMTP patterns
        ProtocolPattern {
            name: "smtp_banner".to_string(),
            protocol: "smtp".to_string(),
            pattern_type: PatternType::String,
            pattern: "220 ".to_string(),
            offset: 0,
            confidence: 0.80,
            min_size: 4,
            max_size: 512,
            case_sensitive: false,
            description: "SMTP welcome banner".to_string(),
        },
        ProtocolPattern {
            name: "smtp_ehlo".to_string(),
            protocol: "smtp".to_string(),
            pattern_type: PatternType::String,
            pattern: "EHLO ".to_string(),
            offset: 0,
            confidence: 0.90,
            min_size: 5,
            max_size: 512,
            case_sensitive: false,
            description: "SMTP EHLO command".to_string(),
        },
        
        // POP3 patterns
        ProtocolPattern {
            name: "pop3_banner".to_string(),
            protocol: "pop3".to_string(),
            pattern_type: PatternType::String,
            pattern: "+OK ".to_string(),
            offset: 0,
            confidence: 0.85,
            min_size: 4,
            max_size: 512,
            case_sensitive: false,
            description: "POP3 welcome banner".to_string(),
        },
        
        // IMAP patterns
        ProtocolPattern {
            name: "imap_banner".to_string(),
            protocol: "imap".to_string(),
            pattern_type: PatternType::String,
            pattern: "* OK ".to_string(),
            offset: 0,
            confidence: 0.85,
            min_size: 5,
            max_size: 512,
            case_sensitive: false,
            description: "IMAP welcome banner".to_string(),
        },
        
        // BitTorrent patterns
        ProtocolPattern {
            name: "bittorrent_handshake".to_string(),
            protocol: "bittorrent".to_string(),
            pattern_type: PatternType::Composite(vec![
                PatternCondition {
                    condition_type: ConditionType::Must,
                    pattern: "13".to_string(), // Protocol length
                    offset: 0,
                    length: Some(1),
                },
                PatternCondition {
                    condition_type: ConditionType::Must,
                    pattern: "BitTorrent protocol".to_string(),
                    offset: 1,
                    length: Some(19),
                },
            ]),
            pattern: "".to_string(),
            offset: 0,
            confidence: 0.99,
            min_size: 20,
            max_size: 68,
            case_sensitive: true,
            description: "BitTorrent handshake".to_string(),
        },
        
        // DNS patterns
        ProtocolPattern {
            name: "dns_query".to_string(),
            protocol: "dns".to_string(),
            pattern_type: PatternType::Magic,
            pattern: "".to_string(), // Custom logic in detector
            offset: 0,
            confidence: 0.80,
            min_size: 12,
            max_size: 512,
            case_sensitive: true,
            description: "DNS query packet".to_string(),
        },
        
        // SOCKS patterns
        ProtocolPattern {
            name: "socks5_greeting".to_string(),
            protocol: "socks".to_string(),
            pattern_type: PatternType::Bytes,
            pattern: "05".to_string(), // SOCKS5 version
            offset: 0,
            confidence: 0.90,
            min_size: 3,
            max_size: 257,
            case_sensitive: true,
            description: "SOCKS5 greeting".to_string(),
        },
        ProtocolPattern {
            name: "socks4_connect".to_string(),
            protocol: "socks".to_string(),
            pattern_type: PatternType::Bytes,
            pattern: "0401".to_string(), // SOCKS4 CONNECT
            offset: 0,
            confidence: 0.85,
            min_size: 8,
            max_size: 1024,
            case_sensitive: true,
            description: "SOCKS4 connect request".to_string(),
        },
        
        // WebSocket patterns
        ProtocolPattern {
            name: "websocket_upgrade".to_string(),
            protocol: "websocket".to_string(),
            pattern_type: PatternType::Composite(vec![
                PatternCondition {
                    condition_type: ConditionType::Must,
                    pattern: "Upgrade: websocket".to_string(),
                    offset: 0,
                    length: None,
                },
                PatternCondition {
                    condition_type: ConditionType::Must,
                    pattern: "Connection: Upgrade".to_string(),
                    offset: 0,
                    length: None,
                },
                PatternCondition {
                    condition_type: ConditionType::Must,
                    pattern: "Sec-WebSocket-Key:".to_string(),
                    offset: 0,
                    length: None,
                },
            ]),
            pattern: "".to_string(),
            offset: 0,
            confidence: 0.98,
            min_size: 100,
            max_size: 4096,
            case_sensitive: false,
            description: "WebSocket upgrade request".to_string(),
        },
        
        // QUIC patterns
        ProtocolPattern {
            name: "quic_v1".to_string(),
            protocol: "quic".to_string(),
            pattern_type: PatternType::Bytes,
            pattern: "c0000001".to_string(), // QUIC v1 long header
            offset: 0,
            confidence: 0.95,
            min_size: 4,
            max_size: 64,
            case_sensitive: true,
            description: "QUIC version 1".to_string(),
        },
        
        // RDP patterns
        ProtocolPattern {
            name: "rdp_connection".to_string(),
            protocol: "rdp".to_string(),
            pattern_type: PatternType::Bytes,
            pattern: "030000".to_string(), // RDP connection request
            offset: 0,
            confidence: 0.85,
            min_size: 3,
            max_size: 64,
            case_sensitive: true,
            description: "RDP connection request".to_string(),
        },
        
        // VNC patterns
        ProtocolPattern {
            name: "vnc_handshake".to_string(),
            protocol: "vnc".to_string(),
            pattern_type: PatternType::String,
            pattern: "RFB ".to_string(),
            offset: 0,
            confidence: 0.95,
            min_size: 4,
            max_size: 32,
            case_sensitive: true,
            description: "VNC handshake".to_string(),
        },
    ]
}

/// Load custom patterns from configuration
pub fn load_custom_patterns(patterns_config: &[ProtocolPattern]) -> Vec<ProtocolPattern> {
    patterns_config.to_vec()
}

/// Validate pattern configuration
pub fn validate_pattern(pattern: &ProtocolPattern) -> Result<(), String> {
    if pattern.name.is_empty() {
        return Err("Pattern name cannot be empty".to_string());
    }
    
    if pattern.protocol.is_empty() {
        return Err("Protocol name cannot be empty".to_string());
    }
    
    if pattern.confidence < 0.0 || pattern.confidence > 1.0 {
        return Err("Confidence must be between 0.0 and 1.0".to_string());
    }
    
    if pattern.min_size > pattern.max_size {
        return Err("Minimum size cannot be greater than maximum size".to_string());
    }
    
    match &pattern.pattern_type {
        PatternType::Bytes => {
            if hex::decode(&pattern.pattern).is_err() {
                return Err("Invalid hex pattern".to_string());
            }
        },
        PatternType::Regex => {
            if regex::Regex::new(&pattern.pattern).is_err() {
                return Err("Invalid regex pattern".to_string());
            }
        },
        PatternType::Composite(conditions) => {
            if conditions.is_empty() {
                return Err("Composite pattern must have at least one condition".to_string());
            }
        },
        _ => {}
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_load_builtin_patterns() {
        let patterns = load_builtin_patterns();
        
        assert!(!patterns.is_empty());
        assert!(patterns.iter().any(|p| p.protocol == "http"));
        assert!(patterns.iter().any(|p| p.protocol == "tls"));
        assert!(patterns.iter().any(|p| p.protocol == "ssh"));
    }
    
    #[test]
    fn test_pattern_validation() {
        let valid_pattern = ProtocolPattern {
            name: "test".to_string(),
            protocol: "test".to_string(),
            pattern_type: PatternType::String,
            pattern: "test".to_string(),
            offset: 0,
            confidence: 0.8,
            min_size: 4,
            max_size: 1024,
            case_sensitive: false,
            description: "Test pattern".to_string(),
        };
        
        assert!(validate_pattern(&valid_pattern).is_ok());
        
        let invalid_pattern = ProtocolPattern {
            name: "".to_string(), // Empty name
            protocol: "test".to_string(),
            pattern_type: PatternType::String,
            pattern: "test".to_string(),
            offset: 0,
            confidence: 0.8,
            min_size: 4,
            max_size: 1024,
            case_sensitive: false,
            description: "Test pattern".to_string(),
        };
        
        assert!(validate_pattern(&invalid_pattern).is_err());
    }
    
    #[tokio::test]
    async fn test_pattern_matcher() {
        let pattern = ProtocolPattern {
            name: "http_get".to_string(),
            protocol: "http".to_string(),
            pattern_type: PatternType::String,
            pattern: "GET ".to_string(),
            offset: 0,
            confidence: 0.95,
            min_size: 4,
            max_size: 1024,
            case_sensitive: false,
            description: "HTTP GET request".to_string(),
        };
        
        let matcher = PatternMatcher::new(pattern);
        let context = SniffContext {
            source_addr: "127.0.0.1:12345".parse().unwrap(),
            dest_addr: "127.0.0.1:80".parse().unwrap(),
            start_time: std::time::Instant::now(),
            bytes_received: 0,
            metadata: HashMap::new(),
        };
        
        let http_data = b"GET / HTTP/1.1\r\nHost: example.com\r\n\r\n";
        let result = matcher.match_pattern(http_data, &context).await;
        
        assert!(result.is_some());
        let detected = result.unwrap();
        assert_eq!(detected.protocol, "http");
        assert_eq!(detected.confidence, 0.95);
    }
}
