//! Sniffing cache for performance optimization
//!
//! This module provides caching mechanisms to avoid repeated
//! protocol detection for similar connections.

use std::collections::HashMap;
use std::hash::{Hash, Hasher};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

use super::{DetectedProtocol, SniffCacheConfig};

/// Cache entry
#[derive(Debug, Clone)]
pub struct CacheEntry {
    /// Detected protocol
    pub protocol: DetectedProtocol,
    
    /// Cache timestamp
    pub timestamp: Instant,
    
    /// Hit count
    pub hit_count: u64,
    
    /// Last access time
    pub last_access: Instant,
    
    /// Entry TTL
    pub ttl: Duration,
}

/// Cache key for sniffing results
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct CacheKey {
    /// Source IP (optional)
    pub source_ip: Option<std::net::IpAddr>,
    
    /// Destination port (optional)
    pub dest_port: Option<u16>,
    
    /// Data pattern hash (optional)
    pub pattern_hash: Option<u64>,
    
    /// Custom key components
    pub custom: Vec<String>,
}

/// Cache statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    /// Total cache entries
    pub total_entries: usize,
    
    /// Cache hits
    pub hits: u64,
    
    /// Cache misses
    pub misses: u64,
    
    /// Hit rate percentage
    pub hit_rate: f64,
    
    /// Cache size in bytes (estimated)
    pub size_bytes: usize,
    
    /// Evicted entries
    pub evictions: u64,
    
    /// Average lookup time
    pub avg_lookup_time: Duration,
    
    /// Most frequent protocols
    pub top_protocols: Vec<(String, u64)>,
}

/// Cache eviction policies
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EvictionPolicy {
    /// Least Recently Used
    Lru,
    
    /// Least Frequently Used
    Lfu,
    
    /// Time-based expiration
    Ttl,
    
    /// Random eviction
    Random,
    
    /// First In, First Out
    Fifo,
}

/// Sniff cache implementation
pub struct SniffCache {
    /// Cache configuration
    config: SniffCacheConfig,
    
    /// Cache entries
    entries: Arc<RwLock<HashMap<CacheKey, CacheEntry>>>,
    
    /// Cache statistics
    stats: Arc<RwLock<CacheStats>>,
    
    /// Eviction policy
    eviction_policy: EvictionPolicy,
    
    /// Cleanup task handle
    cleanup_task: Option<tokio::task::JoinHandle<()>>,
}

impl SniffCache {
    /// Create a new sniff cache
    pub fn new(config: SniffCacheConfig) -> Self {
        Self {
            config,
            entries: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(CacheStats {
                total_entries: 0,
                hits: 0,
                misses: 0,
                hit_rate: 0.0,
                size_bytes: 0,
                evictions: 0,
                avg_lookup_time: Duration::ZERO,
                top_protocols: Vec::new(),
            })),
            eviction_policy: EvictionPolicy::Lru,
            cleanup_task: None,
        }
    }
    
    /// Start the cache (begins cleanup task)
    pub async fn start(&mut self) {
        self.start_cleanup_task().await;
        println!("🗄️ Sniff cache started (size: {})", self.config.size);
    }
    
    /// Stop the cache
    pub async fn stop(&self) {
        if let Some(ref task) = self.cleanup_task {
            task.abort();
        }
        println!("🗄️ Sniff cache stopped");
    }
    
    /// Get cached result
    pub async fn get(&self, key: &CacheKey) -> Option<CacheEntry> {
        let start_time = Instant::now();
        
        let mut entries = self.entries.write().await;
        let mut stats = self.stats.write().await;
        
        if let Some(entry) = entries.get_mut(key) {
            // Check if entry is still valid
            if entry.timestamp.elapsed() <= entry.ttl {
                // Update access information
                entry.hit_count += 1;
                entry.last_access = Instant::now();
                
                // Update statistics
                stats.hits += 1;
                stats.hit_rate = stats.hits as f64 / (stats.hits + stats.misses) as f64 * 100.0;
                
                let lookup_time = start_time.elapsed();
                stats.avg_lookup_time = Duration::from_nanos(
                    (stats.avg_lookup_time.as_nanos() as u64 * (stats.hits - 1) + 
                     lookup_time.as_nanos() as u64) / stats.hits
                );
                
                return Some(entry.clone());
            } else {
                // Entry expired, remove it
                entries.remove(key);
                stats.total_entries = entries.len();
            }
        }
        
        // Cache miss
        stats.misses += 1;
        stats.hit_rate = stats.hits as f64 / (stats.hits + stats.misses) as f64 * 100.0;
        
        None
    }
    
    /// Put result in cache
    pub async fn put(&self, key: CacheKey, entry: CacheEntry) {
        let mut entries = self.entries.write().await;
        let mut stats = self.stats.write().await;
        
        // Check if cache is full
        if entries.len() >= self.config.size && !entries.contains_key(&key) {
            self.evict_entries(&mut entries, &mut stats, 1).await;
        }
        
        // Insert or update entry
        let is_new = !entries.contains_key(&key);
        entries.insert(key, entry);
        
        if is_new {
            stats.total_entries = entries.len();
        }
        
        // Update protocol statistics
        self.update_protocol_stats(&mut stats, &entries).await;
    }
    
    /// Remove entry from cache
    pub async fn remove(&self, key: &CacheKey) -> bool {
        let mut entries = self.entries.write().await;
        let mut stats = self.stats.write().await;
        
        if entries.remove(key).is_some() {
            stats.total_entries = entries.len();
            true
        } else {
            false
        }
    }
    
    /// Clear all cache entries
    pub async fn clear(&self) {
        let mut entries = self.entries.write().await;
        let mut stats = self.stats.write().await;
        
        entries.clear();
        stats.total_entries = 0;
        stats.evictions += entries.len() as u64;
    }
    
    /// Get cache statistics
    pub async fn get_stats(&self) -> CacheStats {
        let stats = self.stats.read().await;
        let entries = self.entries.read().await;
        
        let mut stats_clone = stats.clone();
        stats_clone.total_entries = entries.len();
        stats_clone.size_bytes = self.estimate_cache_size(&entries).await;
        
        stats_clone
    }
    
    /// Generate cache key from context and data
    pub fn generate_key(
        &self,
        source_ip: Option<std::net::IpAddr>,
        dest_port: Option<u16>,
        data: Option<&[u8]>,
        custom: Vec<String>,
    ) -> CacheKey {
        let pattern_hash = if self.config.cache_by_pattern {
            data.map(|d| {
                let mut hasher = std::collections::hash_map::DefaultHasher::new();
                // Hash first 64 bytes for pattern-based caching
                let sample_size = std::cmp::min(d.len(), 64);
                d[..sample_size].hash(&mut hasher);
                hasher.finish()
            })
        } else {
            None
        };
        
        CacheKey {
            source_ip: if self.config.cache_by_ip { source_ip } else { None },
            dest_port: if self.config.cache_by_port { dest_port } else { None },
            pattern_hash,
            custom,
        }
    }
    
    /// Evict entries based on policy
    async fn evict_entries(
        &self,
        entries: &mut HashMap<CacheKey, CacheEntry>,
        stats: &mut CacheStats,
        count: usize,
    ) {
        let keys_to_remove = match self.eviction_policy {
            EvictionPolicy::Lru => self.select_lru_entries(entries, count),
            EvictionPolicy::Lfu => self.select_lfu_entries(entries, count),
            EvictionPolicy::Ttl => self.select_expired_entries(entries, count),
            EvictionPolicy::Random => self.select_random_entries(entries, count),
            EvictionPolicy::Fifo => self.select_fifo_entries(entries, count),
        };
        
        for key in keys_to_remove {
            entries.remove(&key);
            stats.evictions += 1;
        }
        
        stats.total_entries = entries.len();
    }
    
    /// Select LRU entries for eviction
    fn select_lru_entries(&self, entries: &HashMap<CacheKey, CacheEntry>, count: usize) -> Vec<CacheKey> {
        let mut entries_vec: Vec<_> = entries.iter().collect();
        entries_vec.sort_by_key(|(_, entry)| entry.last_access);
        
        entries_vec.into_iter()
            .take(count)
            .map(|(key, _)| key.clone())
            .collect()
    }
    
    /// Select LFU entries for eviction
    fn select_lfu_entries(&self, entries: &HashMap<CacheKey, CacheEntry>, count: usize) -> Vec<CacheKey> {
        let mut entries_vec: Vec<_> = entries.iter().collect();
        entries_vec.sort_by_key(|(_, entry)| entry.hit_count);
        
        entries_vec.into_iter()
            .take(count)
            .map(|(key, _)| key.clone())
            .collect()
    }
    
    /// Select expired entries for eviction
    fn select_expired_entries(&self, entries: &HashMap<CacheKey, CacheEntry>, count: usize) -> Vec<CacheKey> {
        let now = Instant::now();
        let mut expired: Vec<_> = entries.iter()
            .filter(|(_, entry)| now.duration_since(entry.timestamp) > entry.ttl)
            .map(|(key, _)| key.clone())
            .collect();
        
        expired.truncate(count);
        expired
    }
    
    /// Select random entries for eviction
    fn select_random_entries(&self, entries: &HashMap<CacheKey, CacheEntry>, count: usize) -> Vec<CacheKey> {
        use rand::seq::SliceRandom;
        
        let keys: Vec<_> = entries.keys().cloned().collect();
        let mut rng = rand::thread_rng();
        
        keys.choose_multiple(&mut rng, count).cloned().collect()
    }
    
    /// Select FIFO entries for eviction
    fn select_fifo_entries(&self, entries: &HashMap<CacheKey, CacheEntry>, count: usize) -> Vec<CacheKey> {
        let mut entries_vec: Vec<_> = entries.iter().collect();
        entries_vec.sort_by_key(|(_, entry)| entry.timestamp);
        
        entries_vec.into_iter()
            .take(count)
            .map(|(key, _)| key.clone())
            .collect()
    }
    
    /// Update protocol statistics
    async fn update_protocol_stats(
        &self,
        stats: &mut CacheStats,
        entries: &HashMap<CacheKey, CacheEntry>,
    ) {
        let mut protocol_counts: HashMap<String, u64> = HashMap::new();
        
        for entry in entries.values() {
            *protocol_counts.entry(entry.protocol.protocol.clone()).or_insert(0) += entry.hit_count;
        }
        
        let mut top_protocols: Vec<_> = protocol_counts.into_iter().collect();
        top_protocols.sort_by(|a, b| b.1.cmp(&a.1));
        top_protocols.truncate(10); // Keep top 10
        
        stats.top_protocols = top_protocols;
    }
    
    /// Estimate cache size in bytes
    async fn estimate_cache_size(&self, entries: &HashMap<CacheKey, CacheEntry>) -> usize {
        let mut size = 0;
        
        for (key, entry) in entries {
            // Estimate key size
            size += std::mem::size_of::<CacheKey>();
            size += key.custom.iter().map(|s| s.len()).sum::<usize>();
            
            // Estimate entry size
            size += std::mem::size_of::<CacheEntry>();
            size += entry.protocol.protocol.len();
            size += entry.protocol.detector.len();
            size += entry.protocol.metadata.iter()
                .map(|(k, v)| k.len() + v.len())
                .sum::<usize>();
            
            if let Some(ref data) = entry.protocol.analyzed_data {
                size += data.len();
            }
        }
        
        size
    }
    
    /// Start cleanup task
    async fn start_cleanup_task(&mut self) {
        let entries = Arc::clone(&self.entries);
        let stats = Arc::clone(&self.stats);
        let ttl = self.config.ttl;
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));
            
            loop {
                interval.tick().await;
                
                let mut entries_guard = entries.write().await;
                let mut stats_guard = stats.write().await;
                let now = Instant::now();
                
                let initial_count = entries_guard.len();
                
                // Remove expired entries
                entries_guard.retain(|_, entry| {
                    now.duration_since(entry.timestamp) <= entry.ttl
                });
                
                let removed_count = initial_count - entries_guard.len();
                stats_guard.evictions += removed_count as u64;
                stats_guard.total_entries = entries_guard.len();
            }
        });
        
        self.cleanup_task = Some(task);
    }
}

impl CacheEntry {
    /// Create a new cache entry
    pub fn new(protocol: DetectedProtocol, ttl: Duration) -> Self {
        let now = Instant::now();
        
        Self {
            protocol,
            timestamp: now,
            hit_count: 0,
            last_access: now,
            ttl,
        }
    }
    
    /// Check if entry is expired
    pub fn is_expired(&self) -> bool {
        self.timestamp.elapsed() > self.ttl
    }
    
    /// Get entry age
    pub fn age(&self) -> Duration {
        self.timestamp.elapsed()
    }
    
    /// Get time since last access
    pub fn idle_time(&self) -> Duration {
        self.last_access.elapsed()
    }
}

impl CacheKey {
    /// Create a simple cache key
    pub fn simple(source_ip: std::net::IpAddr, dest_port: u16) -> Self {
        Self {
            source_ip: Some(source_ip),
            dest_port: Some(dest_port),
            pattern_hash: None,
            custom: Vec::new(),
        }
    }
    
    /// Create a pattern-based cache key
    pub fn pattern(pattern_hash: u64) -> Self {
        Self {
            source_ip: None,
            dest_port: None,
            pattern_hash: Some(pattern_hash),
            custom: Vec::new(),
        }
    }
    
    /// Create a custom cache key
    pub fn custom(components: Vec<String>) -> Self {
        Self {
            source_ip: None,
            dest_port: None,
            pattern_hash: None,
            custom: components,
        }
    }
}

impl Default for EvictionPolicy {
    fn default() -> Self {
        EvictionPolicy::Lru
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;
    
    fn create_test_config() -> SniffCacheConfig {
        SniffCacheConfig {
            enabled: true,
            size: 100,
            ttl: Duration::from_secs(300),
            cache_by_ip: true,
            cache_by_port: true,
            cache_by_pattern: true,
        }
    }
    
    fn create_test_protocol() -> DetectedProtocol {
        DetectedProtocol {
            protocol: "http".to_string(),
            confidence: 0.95,
            detector: "http_detector".to_string(),
            detection_time: Duration::from_millis(10),
            metadata: HashMap::new(),
            analyzed_data: Some(b"GET / HTTP/1.1".to_vec()),
        }
    }
    
    #[tokio::test]
    async fn test_cache_creation() {
        let config = create_test_config();
        let cache = SniffCache::new(config);
        
        let stats = cache.get_stats().await;
        assert_eq!(stats.total_entries, 0);
        assert_eq!(stats.hits, 0);
        assert_eq!(stats.misses, 0);
    }
    
    #[tokio::test]
    async fn test_cache_put_get() {
        let config = create_test_config();
        let cache = SniffCache::new(config);
        
        let key = CacheKey::simple("127.0.0.1".parse().unwrap(), 80);
        let protocol = create_test_protocol();
        let entry = CacheEntry::new(protocol, Duration::from_secs(300));
        
        // Put entry
        cache.put(key.clone(), entry).await;
        
        // Get entry
        let retrieved = cache.get(&key).await;
        assert!(retrieved.is_some());
        
        let retrieved_entry = retrieved.unwrap();
        assert_eq!(retrieved_entry.protocol.protocol, "http");
        assert_eq!(retrieved_entry.hit_count, 1);
    }
    
    #[tokio::test]
    async fn test_cache_miss() {
        let config = create_test_config();
        let cache = SniffCache::new(config);
        
        let key = CacheKey::simple("127.0.0.1".parse().unwrap(), 80);
        
        // Try to get non-existent entry
        let result = cache.get(&key).await;
        assert!(result.is_none());
        
        let stats = cache.get_stats().await;
        assert_eq!(stats.misses, 1);
    }
    
    #[tokio::test]
    async fn test_cache_expiration() {
        let config = create_test_config();
        let cache = SniffCache::new(config);
        
        let key = CacheKey::simple("127.0.0.1".parse().unwrap(), 80);
        let protocol = create_test_protocol();
        let entry = CacheEntry::new(protocol, Duration::from_millis(10)); // Very short TTL
        
        // Put entry
        cache.put(key.clone(), entry).await;
        
        // Wait for expiration
        tokio::time::sleep(Duration::from_millis(20)).await;
        
        // Try to get expired entry
        let result = cache.get(&key).await;
        assert!(result.is_none());
    }
    
    #[tokio::test]
    async fn test_cache_clear() {
        let config = create_test_config();
        let cache = SniffCache::new(config);
        
        let key = CacheKey::simple("127.0.0.1".parse().unwrap(), 80);
        let protocol = create_test_protocol();
        let entry = CacheEntry::new(protocol, Duration::from_secs(300));
        
        // Put entry
        cache.put(key.clone(), entry).await;
        
        // Verify entry exists
        assert!(cache.get(&key).await.is_some());
        
        // Clear cache
        cache.clear().await;
        
        // Verify entry is gone
        assert!(cache.get(&key).await.is_none());
        
        let stats = cache.get_stats().await;
        assert_eq!(stats.total_entries, 0);
    }
    
    #[test]
    fn test_cache_key_creation() {
        let ip: std::net::IpAddr = "127.0.0.1".parse().unwrap();
        let port = 80;
        
        let key1 = CacheKey::simple(ip, port);
        assert_eq!(key1.source_ip, Some(ip));
        assert_eq!(key1.dest_port, Some(port));
        
        let key2 = CacheKey::pattern(12345);
        assert_eq!(key2.pattern_hash, Some(12345));
        
        let key3 = CacheKey::custom(vec!["test".to_string(), "key".to_string()]);
        assert_eq!(key3.custom, vec!["test", "key"]);
    }
    
    #[test]
    fn test_cache_entry_expiration() {
        let protocol = create_test_protocol();
        let entry = CacheEntry::new(protocol, Duration::from_millis(10));
        
        assert!(!entry.is_expired());
        
        std::thread::sleep(Duration::from_millis(20));
        assert!(entry.is_expired());
    }
}
