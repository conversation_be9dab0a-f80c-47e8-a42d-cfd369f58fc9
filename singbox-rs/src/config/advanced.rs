//! Advanced configuration management system
//!
//! This module provides advanced configuration features including hot reload,
//! validation, templating, and configuration merging capabilities.

use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::{RwLock, watch};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use notify::{Watcher, RecursiveMode, RecommendedWatcher, Event};

use crate::config::{Config, ConfigError};
use crate::error::{SingBoxError, SingBoxResult};

/// Advanced configuration manager
pub struct AdvancedConfigManager {
    /// Current configuration
    config: Arc<RwLock<Config>>,
    
    /// Configuration file path
    config_path: PathBuf,
    
    /// Configuration watcher
    watcher: Option<notify::RecommendedWatcher>,
    
    /// Configuration change notifier
    change_notifier: watch::Sender<ConfigChangeEvent>,
    
    /// Configuration change receiver
    change_receiver: watch::Receiver<ConfigChangeEvent>,
    
    /// Configuration validator
    validator: ConfigValidator,
    
    /// Configuration templates
    templates: HashMap<String, ConfigTemplate>,
    
    /// Hot reload enabled
    hot_reload_enabled: bool,
    
    /// Configuration history
    history: Arc<RwLock<Vec<ConfigHistoryEntry>>>,
    
    /// Maximum history size
    max_history_size: usize,
}

/// Configuration change event
#[derive(Debug, Clone)]
pub struct ConfigChangeEvent {
    /// Event type
    pub event_type: ConfigChangeType,
    
    /// Timestamp
    pub timestamp: SystemTime,
    
    /// Changed sections
    pub changed_sections: Vec<String>,
    
    /// Validation result
    pub validation_result: Option<ValidationResult>,
}

/// Configuration change types
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ConfigChangeType {
    /// Configuration loaded
    Loaded,
    
    /// Configuration reloaded
    Reloaded,
    
    /// Configuration updated
    Updated,
    
    /// Configuration validation failed
    ValidationFailed,
    
    /// Configuration file changed
    FileChanged,
}

/// Configuration validator
pub struct ConfigValidator {
    /// Validation rules
    rules: Vec<ValidationRule>,
    
    /// Schema definitions
    schemas: HashMap<String, serde_json::Value>,
    
    /// Custom validators
    custom_validators: HashMap<String, Box<dyn Fn(&Value) -> ValidationResult + Send + Sync>>,
}

/// Validation rule
#[derive(Debug, Clone)]
pub struct ValidationRule {
    /// Rule name
    pub name: String,
    
    /// JSON path to validate
    pub path: String,
    
    /// Rule type
    pub rule_type: ValidationRuleType,
    
    /// Error message
    pub error_message: String,
    
    /// Severity level
    pub severity: ValidationSeverity,
}

/// Validation rule types
#[derive(Debug, Clone)]
pub enum ValidationRuleType {
    /// Required field
    Required,
    
    /// Type check
    Type(String),
    
    /// Range check
    Range { min: Option<f64>, max: Option<f64> },
    
    /// Length check
    Length { min: Option<usize>, max: Option<usize> },
    
    /// Pattern match
    Pattern(regex::Regex),
    
    /// Enum values
    Enum(Vec<String>),
    
    /// Custom validation
    Custom(String),
    
    /// Dependency check
    Dependency { depends_on: String, condition: String },
}

/// Validation severity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum ValidationSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

/// Validation result
#[derive(Debug, Clone)]
pub struct ValidationResult {
    /// Validation passed
    pub valid: bool,
    
    /// Validation issues
    pub issues: Vec<ValidationIssue>,
    
    /// Validation warnings
    pub warnings: Vec<ValidationIssue>,
    
    /// Validation time
    pub validation_time: Duration,
}

/// Validation issue
#[derive(Debug, Clone)]
pub struct ValidationIssue {
    /// Issue path
    pub path: String,
    
    /// Issue message
    pub message: String,
    
    /// Issue severity
    pub severity: ValidationSeverity,
    
    /// Suggested fix
    pub suggestion: Option<String>,
}

/// Configuration template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigTemplate {
    /// Template name
    pub name: String,
    
    /// Template description
    pub description: String,
    
    /// Template content
    pub template: String,
    
    /// Template variables
    pub variables: HashMap<String, TemplateVariable>,
    
    /// Template tags
    pub tags: Vec<String>,
}

/// Template variable
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateVariable {
    /// Variable name
    pub name: String,
    
    /// Variable description
    pub description: String,
    
    /// Variable type
    pub var_type: TemplateVariableType,
    
    /// Default value
    pub default: Option<Value>,
    
    /// Required flag
    pub required: bool,
    
    /// Validation pattern
    pub pattern: Option<String>,
}

/// Template variable types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TemplateVariableType {
    String,
    Number,
    Boolean,
    Array,
    Object,
}

/// Configuration history entry
#[derive(Debug, Clone)]
pub struct ConfigHistoryEntry {
    /// Timestamp
    pub timestamp: SystemTime,
    
    /// Configuration snapshot
    pub config: Config,
    
    /// Change description
    pub description: String,
    
    /// Changed by
    pub changed_by: Option<String>,
    
    /// Validation result
    pub validation_result: ValidationResult,
}

impl AdvancedConfigManager {
    /// Create a new advanced configuration manager
    pub fn new(config_path: PathBuf) -> SingBoxResult<Self> {
        let (change_notifier, change_receiver) = watch::channel(ConfigChangeEvent {
            event_type: ConfigChangeType::Loaded,
            timestamp: SystemTime::now(),
            changed_sections: Vec::new(),
            validation_result: None,
        });
        
        let config = Arc::new(RwLock::new(Config::default()));
        
        Ok(Self {
            config,
            config_path,
            watcher: None,
            change_notifier,
            change_receiver,
            validator: ConfigValidator::new(),
            templates: HashMap::new(),
            hot_reload_enabled: false,
            history: Arc::new(RwLock::new(Vec::new())),
            max_history_size: 100,
        })
    }
    
    /// Load configuration from file
    pub async fn load_config(&mut self) -> SingBoxResult<()> {
        let content = fs::read_to_string(&self.config_path)
            .map_err(|e| SingBoxError::Config(format!("Failed to read config file: {}", e)))?;
        
        let config: Config = if self.config_path.extension().and_then(|s| s.to_str()) == Some("yaml") {
            serde_yaml::from_str(&content)?
        } else {
            serde_json::from_str(&content)?
        };
        
        // Validate configuration
        let validation_result = self.validator.validate(&config).await?;
        
        if !validation_result.valid {
            return Err(SingBoxError::Config(format!(
                "Configuration validation failed: {:?}",
                validation_result.issues
            )));
        }
        
        // Update configuration
        *self.config.write().await = config.clone();
        
        // Add to history
        self.add_to_history(config, "Configuration loaded".to_string(), None, validation_result.clone()).await;
        
        // Notify change
        let change_event = ConfigChangeEvent {
            event_type: ConfigChangeType::Loaded,
            timestamp: SystemTime::now(),
            changed_sections: vec!["all".to_string()],
            validation_result: Some(validation_result),
        };
        
        let _ = self.change_notifier.send(change_event);
        
        Ok(())
    }
    
    /// Enable hot reload
    pub fn enable_hot_reload(&mut self) -> SingBoxResult<()> {
        if self.hot_reload_enabled {
            return Ok(());
        }
        
        let (tx, mut rx) = tokio::sync::mpsc::channel(100);
        let mut watcher = notify::recommended_watcher(move |res: Result<Event, notify::Error>| {
            if let Ok(event) = res {
                if let Err(_) = tx.blocking_send(event) {
                    // Channel closed, stop watching
                }
            }
        }).map_err(|e| SingBoxError::Config(format!("Failed to create file watcher: {}", e)))?;

        watcher.watch(&self.config_path, RecursiveMode::NonRecursive)
            .map_err(|e| SingBoxError::Config(format!("Failed to watch config file: {}", e)))?;

        let config_path = self.config_path.clone();
        let config = Arc::clone(&self.config);
        let change_notifier = self.change_notifier.clone();
        let validator = self.validator.clone();
        let history = Arc::clone(&self.history);
        let max_history_size = self.max_history_size;

        tokio::spawn(async move {
            while let Some(event) = rx.recv().await {
                if matches!(event.kind, notify::EventKind::Modify(_) | notify::EventKind::Create(_)) {
                    if let Err(e) = Self::handle_config_change(
                        &config_path,
                        &config,
                        &change_notifier,
                        &validator,
                        &history,
                        max_history_size,
                    ).await {
                        eprintln!("Failed to reload configuration: {}", e);
                    }
                }
            }
        });
        
        self.watcher = Some(watcher);
        self.hot_reload_enabled = true;
        
        println!("Hot reload enabled for config file: {:?}", self.config_path);
        Ok(())
    }
    
    /// Handle configuration file change
    async fn handle_config_change(
        config_path: &Path,
        config: &Arc<RwLock<Config>>,
        change_notifier: &watch::Sender<ConfigChangeEvent>,
        validator: &ConfigValidator,
        history: &Arc<RwLock<Vec<ConfigHistoryEntry>>>,
        max_history_size: usize,
    ) -> SingBoxResult<()> {
        let content = fs::read_to_string(config_path)
            .map_err(|e| SingBoxError::Config(format!("Failed to read config file: {}", e)))?;
        
        let new_config: Config = if config_path.extension().and_then(|s| s.to_str()) == Some("yaml") {
            serde_yaml::from_str(&content)?
        } else {
            serde_json::from_str(&content)?
        };
        
        // Validate new configuration
        let validation_result = validator.validate(&new_config).await?;
        
        let change_event = if validation_result.valid {
            // Update configuration
            let old_config = config.read().await.clone();
            *config.write().await = new_config.clone();
            
            // Detect changed sections
            let changed_sections = Self::detect_config_changes(&old_config, &new_config);
            
            // Add to history
            let mut history_guard = history.write().await;
            history_guard.push(ConfigHistoryEntry {
                timestamp: SystemTime::now(),
                config: new_config,
                description: "Configuration hot reloaded".to_string(),
                changed_by: None,
                validation_result: validation_result.clone(),
            });
            
            // Maintain history size
            if history_guard.len() > max_history_size {
                history_guard.remove(0);
            }
            
            ConfigChangeEvent {
                event_type: ConfigChangeType::Reloaded,
                timestamp: SystemTime::now(),
                changed_sections,
                validation_result: Some(validation_result),
            }
        } else {
            ConfigChangeEvent {
                event_type: ConfigChangeType::ValidationFailed,
                timestamp: SystemTime::now(),
                changed_sections: Vec::new(),
                validation_result: Some(validation_result),
            }
        };
        
        let _ = change_notifier.send(change_event);
        Ok(())
    }
    
    /// Detect configuration changes
    fn detect_config_changes(old_config: &Config, new_config: &Config) -> Vec<String> {
        let mut changed_sections = Vec::new();
        
        // Convert configs to JSON for comparison
        if let (Ok(old_json), Ok(new_json)) = (
            serde_json::to_value(old_config),
            serde_json::to_value(new_config),
        ) {
            Self::compare_json_values("", &old_json, &new_json, &mut changed_sections);
        }
        
        changed_sections
    }
    
    /// Compare JSON values recursively
    fn compare_json_values(
        path: &str,
        old_value: &Value,
        new_value: &Value,
        changed_sections: &mut Vec<String>,
    ) {
        match (old_value, new_value) {
            (Value::Object(old_obj), Value::Object(new_obj)) => {
                for (key, old_val) in old_obj {
                    let new_path = if path.is_empty() {
                        key.clone()
                    } else {
                        format!("{}.{}", path, key)
                    };
                    
                    if let Some(new_val) = new_obj.get(key) {
                        Self::compare_json_values(&new_path, old_val, new_val, changed_sections);
                    } else {
                        changed_sections.push(new_path);
                    }
                }
                
                for key in new_obj.keys() {
                    if !old_obj.contains_key(key) {
                        let new_path = if path.is_empty() {
                            key.clone()
                        } else {
                            format!("{}.{}", path, key)
                        };
                        changed_sections.push(new_path);
                    }
                }
            },
            (old_val, new_val) if old_val != new_val => {
                changed_sections.push(path.to_string());
            },
            _ => {}
        }
    }
    
    /// Get configuration change receiver
    pub fn get_change_receiver(&self) -> watch::Receiver<ConfigChangeEvent> {
        self.change_receiver.clone()
    }
    
    /// Get current configuration
    pub async fn get_config(&self) -> Config {
        self.config.read().await.clone()
    }
    
    /// Update configuration
    pub async fn update_config(&mut self, new_config: Config, changed_by: Option<String>) -> SingBoxResult<()> {
        // Validate new configuration
        let validation_result = self.validator.validate(&new_config).await?;
        
        if !validation_result.valid {
            return Err(SingBoxError::Config(format!(
                "Configuration validation failed: {:?}",
                validation_result.issues
            )));
        }
        
        // Detect changes
        let old_config = self.config.read().await.clone();
        let changed_sections = Self::detect_config_changes(&old_config, &new_config);
        
        // Update configuration
        *self.config.write().await = new_config.clone();
        
        // Add to history
        self.add_to_history(
            new_config,
            "Configuration updated".to_string(),
            changed_by,
            validation_result.clone(),
        ).await;
        
        // Notify change
        let change_event = ConfigChangeEvent {
            event_type: ConfigChangeType::Updated,
            timestamp: SystemTime::now(),
            changed_sections,
            validation_result: Some(validation_result),
        };
        
        let _ = self.change_notifier.send(change_event);
        
        Ok(())
    }
    
    /// Add configuration to history
    async fn add_to_history(
        &self,
        config: Config,
        description: String,
        changed_by: Option<String>,
        validation_result: ValidationResult,
    ) {
        let mut history = self.history.write().await;
        
        history.push(ConfigHistoryEntry {
            timestamp: SystemTime::now(),
            config,
            description,
            changed_by,
            validation_result,
        });
        
        // Maintain history size
        if history.len() > self.max_history_size {
            history.remove(0);
        }
    }
    
    /// Get configuration history
    pub async fn get_history(&self, limit: Option<usize>) -> Vec<ConfigHistoryEntry> {
        let history = self.history.read().await;
        if let Some(limit) = limit {
            history.iter().rev().take(limit).cloned().collect()
        } else {
            history.clone()
        }
    }
    
    /// Add configuration template
    pub fn add_template(&mut self, template: ConfigTemplate) {
        self.templates.insert(template.name.clone(), template);
    }
    
    /// Generate configuration from template
    pub fn generate_from_template(
        &self,
        template_name: &str,
        variables: HashMap<String, Value>,
    ) -> SingBoxResult<Config> {
        let template = self.templates.get(template_name)
            .ok_or_else(|| SingBoxError::Config(format!("Template '{}' not found", template_name)))?;
        
        // Validate required variables
        for (var_name, var_def) in &template.variables {
            if var_def.required && !variables.contains_key(var_name) {
                return Err(SingBoxError::Config(format!(
                    "Required template variable '{}' not provided",
                    var_name
                )));
            }
        }
        
        // Replace template variables
        let mut content = template.template.clone();
        for (var_name, value) in variables {
            let placeholder = format!("{{{{{}}}}}", var_name);
            let value_str = match value {
                Value::String(s) => s,
                _ => value.to_string(),
            };
            content = content.replace(&placeholder, &value_str);
        }
        
        // Parse generated configuration
        let config: Config = serde_json::from_str(&content)
            .map_err(|e| SingBoxError::Config(format!("Failed to parse generated config: {}", e)))?;
        
        Ok(config)
    }
    
    /// Get available templates
    pub fn get_templates(&self) -> Vec<&ConfigTemplate> {
        self.templates.values().collect()
    }
}

impl ConfigValidator {
    /// Create a new configuration validator
    pub fn new() -> Self {
        Self {
            rules: Vec::new(),
            schemas: HashMap::new(),
            custom_validators: HashMap::new(),
        }
    }
    
    /// Add validation rule
    pub fn add_rule(&mut self, rule: ValidationRule) {
        self.rules.push(rule);
    }
    
    /// Validate configuration
    pub async fn validate(&self, config: &Config) -> SingBoxResult<ValidationResult> {
        let start_time = std::time::Instant::now();
        let mut issues = Vec::new();
        let mut warnings = Vec::new();
        
        // Convert config to JSON for validation
        let config_json = serde_json::to_value(config)
            .map_err(|e| SingBoxError::Config(format!("Failed to serialize config: {}", e)))?;
        
        // Apply validation rules
        for rule in &self.rules {
            if let Some(issue) = self.apply_rule(rule, &config_json) {
                match issue.severity {
                    ValidationSeverity::Error | ValidationSeverity::Critical => {
                        issues.push(issue);
                    },
                    ValidationSeverity::Warning | ValidationSeverity::Info => {
                        warnings.push(issue);
                    },
                }
            }
        }
        
        let validation_time = start_time.elapsed();
        let valid = issues.is_empty();
        
        Ok(ValidationResult {
            valid,
            issues,
            warnings,
            validation_time,
        })
    }
    
    /// Apply single validation rule
    fn apply_rule(&self, rule: &ValidationRule, config_json: &Value) -> Option<ValidationIssue> {
        // Get value at path
        let value = self.get_value_at_path(config_json, &rule.path);
        
        let is_valid = match &rule.rule_type {
            ValidationRuleType::Required => value.is_some(),
            ValidationRuleType::Type(expected_type) => {
                if let Some(val) = value {
                    self.check_type(val, expected_type)
                } else {
                    true // If value doesn't exist, type check passes
                }
            },
            ValidationRuleType::Range { min, max } => {
                if let Some(Value::Number(num)) = value {
                    let num_f64 = num.as_f64().unwrap_or(0.0);
                    let min_ok = min.map_or(true, |m| num_f64 >= m);
                    let max_ok = max.map_or(true, |m| num_f64 <= m);
                    min_ok && max_ok
                } else {
                    true
                }
            },
            ValidationRuleType::Length { min, max } => {
                if let Some(val) = value {
                    let len = match val {
                        Value::String(s) => s.len(),
                        Value::Array(arr) => arr.len(),
                        Value::Object(obj) => obj.len(),
                        _ => return None,
                    };
                    let min_ok = min.map_or(true, |m| len >= m);
                    let max_ok = max.map_or(true, |m| len <= m);
                    min_ok && max_ok
                } else {
                    true
                }
            },
            ValidationRuleType::Pattern(regex) => {
                if let Some(Value::String(s)) = value {
                    regex.is_match(s)
                } else {
                    true
                }
            },
            ValidationRuleType::Enum(allowed_values) => {
                if let Some(Value::String(s)) = value {
                    allowed_values.contains(s)
                } else {
                    true
                }
            },
            ValidationRuleType::Custom(validator_name) => {
                if let Some(validator) = self.custom_validators.get(validator_name) {
                    if let Some(val) = value {
                        validator(val).valid
                    } else {
                        true
                    }
                } else {
                    true
                }
            },
            ValidationRuleType::Dependency { depends_on, condition: _ } => {
                // Simplified dependency check
                let dependency_value = self.get_value_at_path(config_json, depends_on);
                dependency_value.is_some()
            },
        };
        
        if !is_valid {
            Some(ValidationIssue {
                path: rule.path.clone(),
                message: rule.error_message.clone(),
                severity: rule.severity,
                suggestion: None,
            })
        } else {
            None
        }
    }
    
    /// Get value at JSON path
    fn get_value_at_path<'a>(&self, json: &'a Value, path: &str) -> Option<&'a Value> {
        let parts: Vec<&str> = path.split('.').collect();
        let mut current = json;
        
        for part in parts {
            match current {
                Value::Object(obj) => {
                    current = obj.get(part)?;
                },
                Value::Array(arr) => {
                    if let Ok(index) = part.parse::<usize>() {
                        current = arr.get(index)?;
                    } else {
                        return None;
                    }
                },
                _ => return None,
            }
        }
        
        Some(current)
    }
    
    /// Check value type
    fn check_type(&self, value: &Value, expected_type: &str) -> bool {
        match expected_type {
            "string" => value.is_string(),
            "number" => value.is_number(),
            "boolean" => value.is_boolean(),
            "array" => value.is_array(),
            "object" => value.is_object(),
            "null" => value.is_null(),
            _ => true,
        }
    }
}

impl Clone for ConfigValidator {
    fn clone(&self) -> Self {
        Self {
            rules: self.rules.clone(),
            schemas: self.schemas.clone(),
            custom_validators: HashMap::new(), // Can't clone closures
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::types::LogConfig;
    use tempfile::NamedTempFile;
    
    #[tokio::test]
    async fn test_config_manager_creation() {
        let temp_file = NamedTempFile::new().unwrap();
        let manager = AdvancedConfigManager::new(temp_file.path().to_path_buf());
        
        assert!(manager.is_ok());
    }
    
    #[tokio::test]
    async fn test_config_validation() {
        let mut validator = ConfigValidator::new();

        validator.add_rule(ValidationRule {
            name: "test_rule".to_string(),
            path: "log.level".to_string(),
            rule_type: ValidationRuleType::Required,
            error_message: "Log level is required".to_string(),
            severity: ValidationSeverity::Error,
        });

        // Create a config with log configuration
        let mut config = Config::default();
        config.log = Some(LogConfig::default());
        let result = validator.validate(&config).await.unwrap();

        // Should pass since config has log level
        assert!(result.valid);
    }
    
    #[test]
    fn test_template_variable_creation() {
        let var = TemplateVariable {
            name: "server_port".to_string(),
            description: "Server port number".to_string(),
            var_type: TemplateVariableType::Number,
            default: Some(Value::Number(8080.into())),
            required: true,
            pattern: None,
        };
        
        assert_eq!(var.name, "server_port");
        assert!(var.required);
    }
    
    #[test]
    fn test_validation_rule_creation() {
        let rule = ValidationRule {
            name: "port_range".to_string(),
            path: "inbounds.0.listen_port".to_string(),
            rule_type: ValidationRuleType::Range {
                min: Some(1.0),
                max: Some(65535.0),
            },
            error_message: "Port must be between 1 and 65535".to_string(),
            severity: ValidationSeverity::Error,
        };
        
        assert_eq!(rule.name, "port_range");
        assert_eq!(rule.severity, ValidationSeverity::Error);
    }
}
