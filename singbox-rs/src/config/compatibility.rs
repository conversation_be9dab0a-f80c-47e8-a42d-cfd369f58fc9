//! Configuration compatibility layer
//!
//! This module provides compatibility with the original sing-box
//! configuration format and handles migration between versions.

use std::collections::HashMap;
use std::path::Path;
use serde::{Deserialize, Serialize};
use serde_json::Value;

use super::types::{Config, InboundConfig, OutboundConfig, RouteConfig, DnsConfig, ExperimentalConfig};

/// Configuration version information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ConfigVersion {
    /// Configuration format version
    pub version: String,
    
    /// sing-box version that created this config
    pub created_by: Option<String>,
    
    /// Last modified timestamp
    pub last_modified: Option<String>,
    
    /// Migration history
    pub migrations: Vec<MigrationRecord>,
}

/// Migration record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MigrationRecord {
    /// Source version
    pub from_version: String,
    
    /// Target version
    pub to_version: String,
    
    /// Migration timestamp
    pub timestamp: String,
    
    /// Migration notes
    pub notes: Option<String>,
}

/// Legacy configuration format (sing-box v1.x)
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LegacyConfig {
    /// Log configuration
    pub log: Option<LegacyLogConfig>,
    
    /// DNS configuration
    pub dns: Option<LegacyDnsConfig>,
    
    /// Inbound configurations
    pub inbounds: Option<Vec<LegacyInboundConfig>>,
    
    /// Outbound configurations
    pub outbounds: Option<Vec<LegacyOutboundConfig>>,
    
    /// Route configuration
    pub route: Option<LegacyRouteConfig>,
    
    /// Experimental features
    pub experimental: Option<LegacyExperimentalConfig>,
    
    /// Custom fields for unknown properties
    #[serde(flatten)]
    pub custom: HashMap<String, Value>,
}

/// Legacy log configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyLogConfig {
    /// Log level
    pub level: Option<String>,
    
    /// Log output
    pub output: Option<String>,
    
    /// Disable color
    pub disabled: Option<bool>,
    
    /// Timestamp format
    pub timestamp: Option<bool>,
}

/// Legacy DNS configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyDnsConfig {
    /// DNS servers
    pub servers: Option<Vec<LegacyDnsServer>>,
    
    /// DNS rules
    pub rules: Option<Vec<LegacyDnsRule>>,
    
    /// Final DNS server
    #[serde(rename = "final")]
    pub final_dns: Option<String>,
    
    /// Disable cache
    pub disable_cache: Option<bool>,
    
    /// Disable expire
    pub disable_expire: Option<bool>,
    
    /// Independent cache
    pub independent_cache: Option<bool>,
    
    /// Reverse mapping
    pub reverse_mapping: Option<bool>,
    
    /// Fake IP configuration
    pub fakeip: Option<LegacyFakeIpConfig>,
}

/// Legacy DNS server
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyDnsServer {
    /// Server tag
    pub tag: Option<String>,
    
    /// Server address
    pub address: String,
    
    /// Address resolver
    pub address_resolver: Option<String>,
    
    /// Address strategy
    pub address_strategy: Option<String>,
    
    /// Strategy
    pub strategy: Option<String>,
    
    /// Detour
    pub detour: Option<String>,
}

/// Legacy DNS rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyDnsRule {
    /// Rule type
    #[serde(rename = "type")]
    pub rule_type: Option<String>,
    
    /// Inbound tags
    pub inbound: Option<Vec<String>>,
    
    /// IP version
    pub ip_version: Option<u8>,
    
    /// Query type
    pub query_type: Option<Vec<String>>,
    
    /// Network type
    pub network: Option<String>,
    
    /// Auth user
    pub auth_user: Option<Vec<String>>,
    
    /// Protocol
    pub protocol: Option<Vec<String>>,
    
    /// Domain
    pub domain: Option<Vec<String>>,
    
    /// Domain suffix
    pub domain_suffix: Option<Vec<String>>,
    
    /// Domain keyword
    pub domain_keyword: Option<Vec<String>>,
    
    /// Domain regex
    pub domain_regex: Option<Vec<String>>,
    
    /// GeoSite
    pub geosite: Option<Vec<String>>,
    
    /// Source GeoIP
    pub source_geoip: Option<Vec<String>>,
    
    /// GeoIP
    pub geoip: Option<Vec<String>>,
    
    /// Source IP CIDR
    pub source_ip_cidr: Option<Vec<String>>,
    
    /// IP CIDR
    pub ip_cidr: Option<Vec<String>>,
    
    /// Source port
    pub source_port: Option<Vec<u16>>,
    
    /// Source port range
    pub source_port_range: Option<Vec<String>>,
    
    /// Port
    pub port: Option<Vec<u16>>,
    
    /// Port range
    pub port_range: Option<Vec<String>>,
    
    /// Process name
    pub process_name: Option<Vec<String>>,
    
    /// Process path
    pub process_path: Option<Vec<String>>,
    
    /// Package name
    pub package_name: Option<Vec<String>>,
    
    /// User
    pub user: Option<Vec<String>>,
    
    /// User ID
    pub user_id: Option<Vec<u32>>,
    
    /// Clash mode
    pub clash_mode: Option<String>,
    
    /// Invert
    pub invert: Option<bool>,
    
    /// Server
    pub server: String,
    
    /// Disable cache
    pub disable_cache: Option<bool>,
    
    /// Rewrite TTL
    pub rewrite_ttl: Option<u32>,
}

/// Legacy Fake IP configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyFakeIpConfig {
    /// Enable fake IP
    pub enabled: Option<bool>,
    
    /// IPv4 range
    pub inet4_range: Option<String>,
    
    /// IPv6 range
    pub inet6_range: Option<String>,
}

/// Legacy inbound configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyInboundConfig {
    /// Inbound type
    #[serde(rename = "type")]
    pub inbound_type: String,
    
    /// Inbound tag
    pub tag: Option<String>,
    
    /// Listen address
    pub listen: Option<String>,
    
    /// Listen port
    pub listen_port: Option<u16>,
    
    /// TCP fast open
    pub tcp_fast_open: Option<bool>,
    
    /// TCP multi path
    pub tcp_multi_path: Option<bool>,
    
    /// UDP fragment
    pub udp_fragment: Option<bool>,
    
    /// UDP timeout
    pub udp_timeout: Option<String>,
    
    /// Proxy protocol
    pub proxy_protocol: Option<bool>,
    
    /// Proxy protocol accept no header
    pub proxy_protocol_accept_no_header: Option<bool>,
    
    /// Detour
    pub detour: Option<String>,
    
    /// Sniff enabled
    pub sniff: Option<bool>,
    
    /// Sniff override destination
    pub sniff_override_destination: Option<bool>,
    
    /// Sniff timeout
    pub sniff_timeout: Option<String>,
    
    /// Domain strategy
    pub domain_strategy: Option<String>,
    
    /// Additional fields for specific inbound types
    #[serde(flatten)]
    pub additional: HashMap<String, Value>,
}

/// Legacy outbound configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyOutboundConfig {
    /// Outbound type
    #[serde(rename = "type")]
    pub outbound_type: String,
    
    /// Outbound tag
    pub tag: Option<String>,
    
    /// Server address
    pub server: Option<String>,
    
    /// Server port
    pub server_port: Option<u16>,
    
    /// Dial timeout
    pub dial_timeout: Option<String>,
    
    /// TCP fast open
    pub tcp_fast_open: Option<bool>,
    
    /// TCP multi path
    pub tcp_multi_path: Option<bool>,
    
    /// UDP fragment
    pub udp_fragment: Option<bool>,
    
    /// Connect timeout
    pub connect_timeout: Option<String>,
    
    /// Domain strategy
    pub domain_strategy: Option<String>,
    
    /// Fallback delay
    pub fallback_delay: Option<String>,
    
    /// Bind interface
    pub bind_interface: Option<String>,
    
    /// Inet4 bind address
    pub inet4_bind_address: Option<String>,
    
    /// Inet6 bind address
    pub inet6_bind_address: Option<String>,
    
    /// Routing mark
    pub routing_mark: Option<u32>,
    
    /// Reuse address
    pub reuse_addr: Option<bool>,
    
    /// Connect timeout
    pub tcp_connect_timeout: Option<String>,
    
    /// TCP keep alive
    pub tcp_keep_alive: Option<bool>,
    
    /// TCP keep alive initial
    pub tcp_keep_alive_initial: Option<String>,
    
    /// TCP keep alive interval
    pub tcp_keep_alive_interval: Option<String>,
    
    /// Additional fields for specific outbound types
    #[serde(flatten)]
    pub additional: HashMap<String, Value>,
}

/// Legacy route configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyRouteConfig {
    /// GeoIP configuration
    pub geoip: Option<LegacyGeoIpConfig>,
    
    /// GeoSite configuration
    pub geosite: Option<LegacyGeoSiteConfig>,
    
    /// Route rules
    pub rules: Option<Vec<LegacyRouteRule>>,
    
    /// Final outbound
    #[serde(rename = "final")]
    pub final_outbound: Option<String>,
    
    /// Auto detect interface
    pub auto_detect_interface: Option<bool>,
    
    /// Override Android VPN
    pub override_android_vpn: Option<bool>,
    
    /// Default interface
    pub default_interface: Option<String>,
    
    /// Default mark
    pub default_mark: Option<u32>,
}

/// Legacy GeoIP configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyGeoIpConfig {
    /// Path to GeoIP database
    pub path: Option<String>,
    
    /// Download URL
    pub download_url: Option<String>,
    
    /// Download detour
    pub download_detour: Option<String>,
}

/// Legacy GeoSite configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyGeoSiteConfig {
    /// Path to GeoSite database
    pub path: Option<String>,
    
    /// Download URL
    pub download_url: Option<String>,
    
    /// Download detour
    pub download_detour: Option<String>,
}

/// Legacy route rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyRouteRule {
    /// Rule type
    #[serde(rename = "type")]
    pub rule_type: Option<String>,
    
    /// Inbound tags
    pub inbound: Option<Vec<String>>,
    
    /// IP version
    pub ip_version: Option<u8>,
    
    /// Network type
    pub network: Option<String>,
    
    /// Auth user
    pub auth_user: Option<Vec<String>>,
    
    /// Protocol
    pub protocol: Option<Vec<String>>,
    
    /// Domain
    pub domain: Option<Vec<String>>,
    
    /// Domain suffix
    pub domain_suffix: Option<Vec<String>>,
    
    /// Domain keyword
    pub domain_keyword: Option<Vec<String>>,
    
    /// Domain regex
    pub domain_regex: Option<Vec<String>>,
    
    /// GeoSite
    pub geosite: Option<Vec<String>>,
    
    /// Source GeoIP
    pub source_geoip: Option<Vec<String>>,
    
    /// GeoIP
    pub geoip: Option<Vec<String>>,
    
    /// Source IP CIDR
    pub source_ip_cidr: Option<Vec<String>>,
    
    /// IP CIDR
    pub ip_cidr: Option<Vec<String>>,
    
    /// Source port
    pub source_port: Option<Vec<u16>>,
    
    /// Source port range
    pub source_port_range: Option<Vec<String>>,
    
    /// Port
    pub port: Option<Vec<u16>>,
    
    /// Port range
    pub port_range: Option<Vec<String>>,
    
    /// Process name
    pub process_name: Option<Vec<String>>,
    
    /// Process path
    pub process_path: Option<Vec<String>>,
    
    /// Package name
    pub package_name: Option<Vec<String>>,
    
    /// User
    pub user: Option<Vec<String>>,
    
    /// User ID
    pub user_id: Option<Vec<u32>>,
    
    /// Clash mode
    pub clash_mode: Option<String>,
    
    /// Invert
    pub invert: Option<bool>,
    
    /// Outbound
    pub outbound: String,
}

/// Legacy experimental configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyExperimentalConfig {
    /// Clash API configuration
    pub clash_api: Option<LegacyClashApiConfig>,
    
    /// V2Ray API configuration
    pub v2ray_api: Option<LegacyV2RayApiConfig>,
    
    /// Cache file
    pub cache_file: Option<LegacyCacheFileConfig>,
}

/// Legacy Clash API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyClashApiConfig {
    /// External controller
    pub external_controller: Option<String>,
    
    /// External UI
    pub external_ui: Option<String>,
    
    /// External UI download URL
    pub external_ui_download_url: Option<String>,
    
    /// External UI download detour
    pub external_ui_download_detour: Option<String>,
    
    /// Secret
    pub secret: Option<String>,
    
    /// Default mode
    pub default_mode: Option<String>,
    
    /// Store mode
    pub store_mode: Option<bool>,
    
    /// Store selected
    pub store_selected: Option<bool>,
    
    /// Store fake IP
    pub store_fakeip: Option<bool>,
    
    /// Cache file
    pub cache_file: Option<String>,
    
    /// Cache ID
    pub cache_id: Option<String>,
}

/// Legacy V2Ray API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyV2RayApiConfig {
    /// Listen address
    pub listen: Option<String>,
    
    /// Stats configuration
    pub stats: Option<LegacyV2RayStatsConfig>,
}

/// Legacy V2Ray stats configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyV2RayStatsConfig {
    /// Enable stats
    pub enabled: Option<bool>,
    
    /// Inbound stats
    pub inbounds: Option<Vec<String>>,
    
    /// Outbound stats
    pub outbounds: Option<Vec<String>>,
    
    /// User stats
    pub users: Option<Vec<String>>,
}

/// Legacy cache file configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyCacheFileConfig {
    /// Enable cache file
    pub enabled: Option<bool>,
    
    /// Cache file path
    pub path: Option<String>,
    
    /// Cache ID
    pub cache_id: Option<String>,
    
    /// Store fake IP
    pub store_fakeip: Option<bool>,
    
    /// Store RDRC
    pub store_rdrc: Option<bool>,
}

/// Configuration compatibility manager
pub struct CompatibilityManager {
    /// Current configuration version
    current_version: String,
    
    /// Supported legacy versions
    supported_versions: Vec<String>,
    
    /// Migration handlers
    migration_handlers: HashMap<String, Box<dyn Fn(&Value) -> Result<Value, String> + Send + Sync>>,
}

impl CompatibilityManager {
    /// Create a new compatibility manager
    pub fn new() -> Self {
        let mut manager = Self {
            current_version: "2.0.0".to_string(),
            supported_versions: vec![
                "1.0.0".to_string(),
                "1.1.0".to_string(),
                "1.2.0".to_string(),
                "1.3.0".to_string(),
                "1.4.0".to_string(),
                "1.5.0".to_string(),
                "1.6.0".to_string(),
                "1.7.0".to_string(),
                "1.8.0".to_string(),
                "1.9.0".to_string(),
            ],
            migration_handlers: HashMap::new(),
        };
        
        // Register migration handlers
        manager.register_migration_handlers();
        manager
    }
    
    /// Load configuration with compatibility handling
    pub async fn load_config<P: AsRef<Path>>(&self, path: P) -> Result<Config, String> {
        let content = tokio::fs::read_to_string(path).await
            .map_err(|e| format!("Failed to read config file: {}", e))?;
        
        // Try to parse as current format first
        if let Ok(config) = serde_json::from_str::<Config>(&content) {
            return Ok(config);
        }
        
        // Try to parse as legacy format
        let legacy_value: Value = serde_json::from_str(&content)
            .map_err(|e| format!("Failed to parse config JSON: {}", e))?;
        
        // Detect version and migrate
        let version = self.detect_config_version(&legacy_value);
        let migrated_value = self.migrate_config(&legacy_value, &version)?;
        
        // Parse migrated configuration
        let config: Config = serde_json::from_value(migrated_value)
            .map_err(|e| format!("Failed to parse migrated config: {}", e))?;
        
        Ok(config)
    }
    
    /// Save configuration with version information
    pub async fn save_config<P: AsRef<Path>>(&self, config: &Config, path: P) -> Result<(), String> {
        let mut config_with_version = serde_json::to_value(config)
            .map_err(|e| format!("Failed to serialize config: {}", e))?;
        
        // Add version information
        if let Value::Object(ref mut map) = config_with_version {
            map.insert("_version".to_string(), Value::Object({
                let mut version_map = serde_json::Map::new();
                version_map.insert("version".to_string(), Value::String(self.current_version.clone()));
                version_map.insert("created_by".to_string(), Value::String("sing-box-rs".to_string()));
                version_map.insert("last_modified".to_string(), Value::String(
                    chrono::Utc::now().to_rfc3339()
                ));
                version_map
            }));
        }
        
        let content = serde_json::to_string_pretty(&config_with_version)
            .map_err(|e| format!("Failed to serialize config: {}", e))?;
        
        tokio::fs::write(path, content).await
            .map_err(|e| format!("Failed to write config file: {}", e))?;
        
        Ok(())
    }
    
    /// Detect configuration version
    fn detect_config_version(&self, config: &Value) -> String {
        // Check for explicit version information
        if let Some(version_info) = config.get("_version") {
            if let Some(version) = version_info.get("version") {
                if let Some(version_str) = version.as_str() {
                    return version_str.to_string();
                }
            }
        }
        
        // Heuristic detection based on structure
        if config.get("experimental").is_some() {
            if config.get("experimental").unwrap().get("clash_api").is_some() {
                return "1.8.0".to_string(); // Clash API introduced in 1.8.0
            }
        }
        
        if config.get("route").is_some() {
            if config.get("route").unwrap().get("geoip").is_some() {
                return "1.5.0".to_string(); // GeoIP config restructured in 1.5.0
            }
        }
        
        if config.get("dns").is_some() {
            if config.get("dns").unwrap().get("fakeip").is_some() {
                return "1.3.0".to_string(); // FakeIP introduced in 1.3.0
            }
        }
        
        // Default to oldest supported version
        "1.0.0".to_string()
    }
    
    /// Migrate configuration to current version
    fn migrate_config(&self, config: &Value, from_version: &str) -> Result<Value, String> {
        let mut current_config = config.clone();
        let mut current_version = from_version.to_string();
        
        // Apply migrations in sequence
        while current_version != self.current_version {
            let next_version = self.get_next_version(&current_version)?;
            let migration_key = format!("{}_{}", current_version, next_version);
            
            if let Some(handler) = self.migration_handlers.get(&migration_key) {
                current_config = handler(&current_config)?;
                current_version = next_version;
            } else {
                return Err(format!("No migration handler for {} -> {}", current_version, next_version));
            }
        }
        
        Ok(current_config)
    }
    
    /// Get next version in migration chain
    fn get_next_version(&self, current: &str) -> Result<String, String> {
        let current_index = self.supported_versions.iter()
            .position(|v| v == current)
            .ok_or_else(|| format!("Unsupported version: {}", current))?;
        
        if current_index + 1 < self.supported_versions.len() {
            Ok(self.supported_versions[current_index + 1].clone())
        } else {
            Ok(self.current_version.clone())
        }
    }
    
    /// Register migration handlers
    fn register_migration_handlers(&mut self) {
        // Example migration from 1.0.0 to 1.1.0
        self.migration_handlers.insert(
            "1.0.0_1.1.0".to_string(),
            Box::new(|config| {
                let mut migrated = config.clone();
                
                // Add new fields with default values
                if let Value::Object(ref mut map) = migrated {
                    if let Some(Value::Object(log)) = map.get_mut("log") {
                        log.entry("timestamp".to_string())
                            .or_insert(Value::Bool(true));
                    }
                }
                
                Ok(migrated)
            })
        );
        
        // Add more migration handlers as needed
        // Each handler should transform config from one version to the next
    }
    
    /// Validate configuration compatibility
    pub fn validate_compatibility(&self, config: &Config) -> Result<Vec<String>, String> {
        let mut warnings = Vec::new();
        
        // Check for deprecated features
        if let Some(ref experimental) = config.experimental {
            if let Some(ref clash_api) = experimental.clash_api {
                // Check for deprecated Clash API features
                warnings.push("Clash API is experimental and may change in future versions".to_string());
            }
        }
        
        // Check for unsupported combinations
        if config.inbounds.as_ref().map_or(true, |v| v.is_empty()) {
            warnings.push("No inbounds configured - this may not work as expected".to_string());
        }
        
        if config.outbounds.as_ref().map_or(true, |v| v.is_empty()) {
            warnings.push("No outbounds configured - this may not work as expected".to_string());
        }
        
        Ok(warnings)
    }
    
    /// Get supported versions
    pub fn get_supported_versions(&self) -> &[String] {
        &self.supported_versions
    }
    
    /// Get current version
    pub fn get_current_version(&self) -> &str {
        &self.current_version
    }
}

impl Default for CompatibilityManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    
    #[test]
    fn test_compatibility_manager_creation() {
        let manager = CompatibilityManager::new();
        
        assert_eq!(manager.get_current_version(), "2.0.0");
        assert!(!manager.get_supported_versions().is_empty());
    }
    
    #[test]
    fn test_version_detection() {
        let manager = CompatibilityManager::new();
        
        // Test explicit version
        let config_with_version = serde_json::json!({
            "_version": {
                "version": "1.5.0"
            },
            "inbounds": []
        });
        
        let detected = manager.detect_config_version(&config_with_version);
        assert_eq!(detected, "1.5.0");
        
        // Test heuristic detection
        let config_with_fakeip = serde_json::json!({
            "dns": {
                "fakeip": {
                    "enabled": true
                }
            }
        });
        
        let detected = manager.detect_config_version(&config_with_fakeip);
        assert_eq!(detected, "1.3.0");
    }
    
    #[tokio::test]
    async fn test_config_save_load() {
        let manager = CompatibilityManager::new();
        let temp_file = NamedTempFile::new().unwrap();
        
        let config = Config::default();
        
        // Save config
        manager.save_config(&config, temp_file.path()).await.unwrap();
        
        // Load config
        let loaded_config = manager.load_config(temp_file.path()).await.unwrap();
        
        // Basic validation
        assert_eq!(config.inbounds.as_ref().map(|v| v.len()), loaded_config.inbounds.as_ref().map(|v| v.len()));
        assert_eq!(config.outbounds.as_ref().map(|v| v.len()), loaded_config.outbounds.as_ref().map(|v| v.len()));
    }
    
    #[test]
    fn test_compatibility_validation() {
        let manager = CompatibilityManager::new();
        let config = Config::default();
        
        let warnings = manager.validate_compatibility(&config).unwrap();
        
        // Should have warnings about empty inbounds/outbounds
        assert!(warnings.len() >= 2);
        assert!(warnings.iter().any(|w| w.contains("inbounds")));
        assert!(warnings.iter().any(|w| w.contains("outbounds")));
    }
}
