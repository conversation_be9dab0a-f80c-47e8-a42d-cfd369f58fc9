//! Configuration module for sing-box
//! 
//! This module provides configuration parsing, validation, and management
//! capabilities for sing-box.

use std::collections::HashMap;
use std::path::Path;
use serde::{Deserialize, Serialize};
use crate::option::Options;

pub mod parser;
pub mod types;
pub mod advanced;
pub mod compatibility;

// Re-export the main Config type
pub use types::Config;

/// Configuration errors
#[derive(Debug, Clone)]
pub enum ConfigError {
    FileError(String),
    ParseError(String),
    ValidationError(String),
    VariableError(String),
    IncludeError(String),
}

impl std::fmt::Display for ConfigError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ConfigError::FileError(msg) => write!(f, "File error: {}", msg),
            ConfigError::ParseError(msg) => write!(f, "Parse error: {}", msg),
            ConfigError::ValidationError(msg) => write!(f, "Validation error: {}", msg),
            ConfigError::VariableError(msg) => write!(f, "Variable error: {}", msg),
            ConfigError::IncludeError(msg) => write!(f, "Include error: {}", msg),
        }
    }
}

impl std::error::Error for ConfigError {}

/// Configuration manager for handling multiple config sources
pub struct ConfigManager {
    parser: parser::ConfigParser,
    loaded_configs: HashMap<String, Options>,
}

impl ConfigManager {
    pub fn new() -> Self {
        Self {
            parser: parser::ConfigParser::new(),
            loaded_configs: HashMap::new(),
        }
    }

    /// Load configuration from file with caching
    pub async fn load_config<P: AsRef<Path>>(&mut self, path: P) -> Result<Options, ConfigError> {
        let path_str = path.as_ref().to_string_lossy().to_string();
        
        // Check cache first
        if let Some(cached) = self.loaded_configs.get(&path_str) {
            return Ok(cached.clone());
        }

        // Load and parse configuration
        let config = self.parser.load_config(path).await?;

        // Cache the result
        self.loaded_configs.insert(path_str, config.clone());

        Ok(config)
    }

    /// Clear configuration cache
    pub fn clear_cache(&mut self) {
        self.loaded_configs.clear();
    }

    /// Get parser for advanced configuration
    pub fn parser_mut(&mut self) -> &mut parser::ConfigParser {
        &mut self.parser
    }

    /// Validate configuration without loading
    pub async fn validate_file<P: AsRef<Path>>(&self, path: P) -> Result<(), ConfigError> {
        let options = self.parser.parse_file(path).await?;
        // Additional validation could be performed here
        Ok(())
    }
}

impl Default for ConfigManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_error_display() {
        let error = ConfigError::FileError("test error".to_string());
        assert_eq!(error.to_string(), "File error: test error");

        let error = ConfigError::ValidationError("validation failed".to_string());
        assert_eq!(error.to_string(), "Validation error: validation failed");
    }

    #[test]
    fn test_config_manager_creation() {
        let manager = ConfigManager::new();
        assert!(manager.loaded_configs.is_empty());
    }

    #[test]
    fn test_config_manager_cache() {
        let mut manager = ConfigManager::new();
        assert!(manager.loaded_configs.is_empty());
        
        manager.clear_cache();
        assert!(manager.loaded_configs.is_empty());
    }
}
