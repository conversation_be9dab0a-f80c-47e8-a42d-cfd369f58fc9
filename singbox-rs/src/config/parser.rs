use std::collections::HashMap;
use std::fs;
use std::path::Path;
use serde_json;

use crate::option::{Options, Inbound, Outbound, RouteOptions, DNSOptions};
use crate::config::ConfigError;

/// Configuration parser for sing-box
pub struct ConfigParser {
    include_paths: Vec<String>,
    variables: HashMap<String, String>,
}

impl ConfigParser {
    pub fn new() -> Self {
        Self {
            include_paths: vec![".".to_string()],
            variables: HashMap::new(),
        }
    }

    /// Add include path for configuration files
    pub fn add_include_path<P: AsRef<Path>>(&mut self, path: P) {
        self.include_paths.push(path.as_ref().to_string_lossy().to_string());
    }

    /// Set variable for template substitution
    pub fn set_variable(&mut self, key: &str, value: &str) {
        self.variables.insert(key.to_string(), value.to_string());
    }

    /// Parse configuration from file
    pub async fn parse_file<P: AsRef<Path>>(&self, path: P) -> Result<Options, ConfigError> {
        let content = fs::read_to_string(&path)
            .map_err(|e| ConfigError::FileError(format!("read config file: {}", e)))?;
        
        self.parse_content(&content).await
    }

    /// Parse configuration from string content
    pub async fn parse_content(&self, content: &str) -> Result<Options, ConfigError> {
        // Perform variable substitution
        let processed_content = self.substitute_variables(content)?;
        
        // Parse JSON
        let mut options: Options = serde_json::from_str(&processed_content)
            .map_err(|e| ConfigError::ParseError(format!("JSON parse error: {}", e)))?;

        // Process includes
        self.process_includes(&mut options).await?;

        // Validate configuration
        self.validate_config(&options)?;

        Ok(options)
    }

    /// Substitute variables in configuration content
    fn substitute_variables(&self, content: &str) -> Result<String, ConfigError> {
        let mut result = content.to_string();
        
        for (key, value) in &self.variables {
            let placeholder = format!("${{{}}}", key);
            result = result.replace(&placeholder, value);
        }

        // Check for unresolved variables
        if result.contains("${") {
            return Err(ConfigError::VariableError("unresolved variables found".to_string()));
        }

        Ok(result)
    }

    /// Process include directives in configuration
    async fn process_includes(&self, options: &mut Options) -> Result<(), ConfigError> {
        // For now, this is a placeholder for include processing
        // In a full implementation, this would:
        // 1. Find include directives in the config
        // 2. Load included files
        // 3. Merge configurations
        // 4. Handle circular dependencies
        
        Ok(())
    }

    /// Validate parsed configuration
    fn validate_config(&self, options: &Options) -> Result<(), ConfigError> {
        // Validate inbounds
        self.validate_inbounds(&options.inbounds)?;
        
        // Validate outbounds
        self.validate_outbounds(&options.outbounds)?;
        
        // Validate routes
        if let Some(route) = &options.route {
            self.validate_routes(route, &options.outbounds)?;
        }

        // Validate DNS configuration
        if let Some(dns) = &options.dns {
            self.validate_dns(dns)?;
        }

        Ok(())
    }

    /// Validate inbound configurations
    fn validate_inbounds(&self, inbounds: &[Inbound]) -> Result<(), ConfigError> {
        let mut seen_tags = HashMap::new();
        let _seen_listen_addrs: HashMap<String, usize> = HashMap::new();

        for (index, inbound) in inbounds.iter().enumerate() {
            // Check for duplicate tags
            if !inbound.tag.is_empty() {
                if seen_tags.contains_key(&inbound.tag) {
                    return Err(ConfigError::ValidationError(
                        format!("duplicate inbound tag '{}' at index {}", inbound.tag, index)
                    ));
                }
                seen_tags.insert(inbound.tag.clone(), index);
            }

            // For now, skip listen address validation since the Inbound struct
            // doesn't have listen/listen_port fields in the current implementation

            // Validate inbound type
            match inbound.inbound_type.as_str() {
                "mixed" | "http" | "socks" | "shadowsocks" | "vmess" | "trojan" | "direct" => {}
                _ => return Err(ConfigError::ValidationError(
                    format!("unsupported inbound type '{}' at index {}", inbound.inbound_type, index)
                )),
            }
        }

        Ok(())
    }

    /// Validate outbound configurations
    fn validate_outbounds(&self, outbounds: &[Outbound]) -> Result<(), ConfigError> {
        let mut seen_tags = HashMap::new();

        for (index, outbound) in outbounds.iter().enumerate() {
            // Check for duplicate tags
            if !outbound.tag.is_empty() {
                if seen_tags.contains_key(&outbound.tag) {
                    return Err(ConfigError::ValidationError(
                        format!("duplicate outbound tag '{}' at index {}", outbound.tag, index)
                    ));
                }
                seen_tags.insert(outbound.tag.clone(), index);
            }

            // Validate outbound type
            match outbound.outbound_type.as_str() {
                "direct" | "block" | "dns" | "http" | "socks" | "shadowsocks" | 
                "vmess" | "trojan" | "wireguard" | "hysteria" | "hysteria2" => {}
                _ => return Err(ConfigError::ValidationError(
                    format!("unsupported outbound type '{}' at index {}", outbound.outbound_type, index)
                )),
            }
        }

        // Ensure at least one outbound exists
        if outbounds.is_empty() {
            return Err(ConfigError::ValidationError("no outbounds configured".to_string()));
        }

        Ok(())
    }

    /// Validate route configuration
    fn validate_routes(&self, route: &RouteOptions, outbounds: &[Outbound]) -> Result<(), ConfigError> {
        let outbound_tags: HashMap<String, bool> = outbounds.iter()
            .filter(|o| !o.tag.is_empty())
            .map(|o| (o.tag.clone(), true))
            .collect();

        // Validate final outbound
        if let Some(final_outbound) = &route.final_ {
            if !final_outbound.is_empty() && !outbound_tags.contains_key(final_outbound) {
                return Err(ConfigError::ValidationError(
                    format!("final outbound '{}' not found", final_outbound)
                ));
            }
        }

        // For now, skip rule validation since RouteOptions doesn't have rules field
        // in the current implementation

        Ok(())
    }

    /// Validate DNS configuration
    fn validate_dns(&self, dns: &DNSOptions) -> Result<(), ConfigError> {
        // Validate DNS servers
        for (index, server) in dns.servers.iter().enumerate() {
            if server.address.is_empty() {
                return Err(ConfigError::ValidationError(
                    format!("DNS server {} has empty address", index)
                ));
            }

            // Basic address validation
            if !server.address.contains(':') && !server.address.contains('.') {
                return Err(ConfigError::ValidationError(
                    format!("DNS server {} has invalid address format", index)
                ));
            }
        }

        // Validate final DNS server
        if let Some(final_server) = &dns.final_ {
            if !final_server.is_empty() {
                let server_exists = dns.servers.iter()
                    .any(|s| s.tag == *final_server);
                
                if !server_exists {
                    return Err(ConfigError::ValidationError(
                        format!("final DNS server '{}' not found", final_server)
                    ));
                }
            }
        }

        Ok(())
    }

    /// Merge multiple configuration files
    pub async fn merge_configs(&self, configs: Vec<Options>) -> Result<Options, ConfigError> {
        if configs.is_empty() {
            return Err(ConfigError::ValidationError("no configurations to merge".to_string()));
        }

        let mut merged = configs[0].clone();

        for config in configs.into_iter().skip(1) {
            // Merge inbounds
            merged.inbounds.extend(config.inbounds);

            // Merge outbounds
            merged.outbounds.extend(config.outbounds);

            // Merge routes (take the last one for now)
            if config.route.is_some() {
                merged.route = config.route;
            }

            // Merge DNS (take the last one for now)
            if config.dns.is_some() {
                merged.dns = config.dns;
            }

            // Merge log (take the last one for now)
            if config.log.is_some() {
                merged.log = config.log;
            }
        }

        // Validate merged configuration
        self.validate_config(&merged)?;

        Ok(merged)
    }

    /// Load configuration with includes and validation
    pub async fn load_config<P: AsRef<Path>>(&self, path: P) -> Result<Options, ConfigError> {
        let options = self.parse_file(path).await?;
        
        // Additional post-processing could go here
        // - Environment variable substitution
        // - Template processing
        // - Configuration normalization
        
        Ok(options)
    }
}

impl Default for ConfigParser {
    fn default() -> Self {
        Self::new()
    }
}

/// Configuration validation utilities
pub struct ConfigValidator;

impl ConfigValidator {
    /// Validate that all referenced outbounds exist
    pub fn validate_outbound_references(options: &Options) -> Result<(), ConfigError> {
        let outbound_tags: HashMap<String, bool> = options.outbounds.iter()
            .filter(|o| !o.tag.is_empty())
            .map(|o| (o.tag.clone(), true))
            .collect();

        // Check route references
        if let Some(route) = &options.route {
            if let Some(final_outbound) = &route.final_ {
                if !final_outbound.is_empty() && !outbound_tags.contains_key(final_outbound) {
                    return Err(ConfigError::ValidationError(
                        format!("final outbound '{}' not found", final_outbound)
                    ));
                }
            }
            // Skip rule validation for now since RouteOptions doesn't have rules field
        }

        Ok(())
    }

    /// Validate network compatibility
    pub fn validate_network_compatibility(options: &Options) -> Result<(), ConfigError> {
        // Check that inbounds and outbounds have compatible networks
        for _inbound in &options.inbounds {
            // For now, assume all inbounds support TCP
            // In a full implementation, this would check specific protocol capabilities
        }

        for outbound in &options.outbounds {
            // For now, assume all outbounds support TCP
            // In a full implementation, this would check specific protocol capabilities
        }

        Ok(())
    }

    /// Check for common configuration mistakes
    pub fn check_common_issues(options: &Options) -> Vec<String> {
        let mut warnings = Vec::new();

        // Check for empty tags
        for (index, inbound) in options.inbounds.iter().enumerate() {
            if inbound.tag.is_empty() {
                warnings.push(format!("inbound {} has no tag", index));
            }
        }

        for (index, outbound) in options.outbounds.iter().enumerate() {
            if outbound.tag.is_empty() {
                warnings.push(format!("outbound {} has no tag", index));
            }
        }

        // Check for unused outbounds
        let mut used_outbounds = HashMap::new();
        if let Some(route) = &options.route {
            if let Some(final_outbound) = &route.final_ {
                used_outbounds.insert(final_outbound.clone(), true);
            }
            // Skip rule checking for now since RouteOptions doesn't have rules field
        }

        for outbound in &options.outbounds {
            if !outbound.tag.is_empty() && !used_outbounds.contains_key(&outbound.tag) {
                warnings.push(format!("outbound '{}' is defined but never used", outbound.tag));
            }
        }

        warnings
    }
}

#[cfg(test)]
mod tests {
    use super::*;


    #[test]
    fn test_config_parser_creation() {
        let parser = ConfigParser::new();
        assert_eq!(parser.include_paths.len(), 1);
        assert_eq!(parser.include_paths[0], ".");
        assert!(parser.variables.is_empty());
    }

    #[test]
    fn test_variable_substitution() {
        let mut parser = ConfigParser::new();
        parser.set_variable("SERVER_PORT", "8080");
        parser.set_variable("SERVER_HOST", "127.0.0.1");

        let content = r#"{"listen": "${SERVER_HOST}", "port": ${SERVER_PORT}}"#;
        let result = parser.substitute_variables(content).unwrap();
        assert_eq!(result, r#"{"listen": "127.0.0.1", "port": 8080}"#);
    }

    #[test]
    fn test_unresolved_variables() {
        let parser = ConfigParser::new();
        let content = r#"{"server": "${UNDEFINED_VAR}"}"#;
        let result = parser.substitute_variables(content);
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_parse_simple_config() {
        let parser = ConfigParser::new();
        let content = r#"{
            "log": {"level": "info", "disable_color": false, "timestamp": true},
            "inbounds": [{"type": "mixed", "tag": "mixed-in"}],
            "outbounds": [{"type": "direct", "tag": "direct"}]
        }"#;

        let result = parser.parse_content(content).await;
        if let Err(ref e) = result {
            println!("Parse error: {:?}", e);
        }
        assert!(result.is_ok());

        let options = result.unwrap();
        assert!(options.log.is_some());
        assert_eq!(options.inbounds.len(), 1);
        assert_eq!(options.outbounds.len(), 1);
    }

    #[test]
    fn test_validate_outbound_references() {
        let mut options = Options::default();

        // Add outbound
        let outbound = Outbound {
            outbound_type: "http".to_string(),
            tag: "proxy".to_string(),
            server: None,
            server_port: None,
            local_address: None,
            network: None,
            domain_strategy: None,
            fallback_delay: None,
            bind_interface: None,
            inet4_bind_address: None,
            inet6_bind_address: None,
            routing_mark: None,
            reuse_addr: None,
            connect_timeout: None,
            tcp_fast_open: None,
            tcp_multi_path: None,
            udp_fragment: None,
            udp_timeout: None,
            config: HashMap::new(),
        };
        options.outbounds.push(outbound);

        // Add route with valid reference
        let route = RouteOptions {
            auto_detect_interface: Some(false),
            final_: Some("proxy".to_string()),
            geoip: None,
            geosite: None,
            rules: Vec::new(),
            rule_set: Vec::new(),
            default_mark: None,
            default_interface: None,
            override_android_vpn: None,
        };
        options.route = Some(route);

        let result = ConfigValidator::validate_outbound_references(&options);
        assert!(result.is_ok());

        // Test invalid reference
        let route = RouteOptions {
            auto_detect_interface: Some(false),
            final_: Some("nonexistent".to_string()),
            geoip: None,
            geosite: None,
            rules: Vec::new(),
            rule_set: Vec::new(),
            default_mark: None,
            default_interface: None,
            override_android_vpn: None,
        };
        options.route = Some(route);

        let result = ConfigValidator::validate_outbound_references(&options);
        assert!(result.is_err());
    }

    #[test]
    fn test_check_common_issues() {
        let mut options = Options::default();

        // Add inbound without tag
        let inbound = Inbound {
            inbound_type: "mixed".to_string(),
            tag: String::new(),
            listen: None,
            listen_port: None,
            tcp_fast_open: None,
            tcp_multi_path: None,
            udp_fragment: None,
            udp_timeout: None,
            proxy_protocol: None,
            proxy_protocol_accept_no_header: None,
            network: None,
            override_address: None,
            override_port: None,
            config: HashMap::new(),
        };
        options.inbounds.push(inbound);

        // Add outbound without tag
        let outbound = Outbound {
            outbound_type: "direct".to_string(),
            tag: String::new(),
            server: None,
            server_port: None,
            local_address: None,
            network: None,
            domain_strategy: None,
            fallback_delay: None,
            bind_interface: None,
            inet4_bind_address: None,
            inet6_bind_address: None,
            routing_mark: None,
            reuse_addr: None,
            connect_timeout: None,
            tcp_fast_open: None,
            tcp_multi_path: None,
            udp_fragment: None,
            udp_timeout: None,
            config: HashMap::new(),
        };
        options.outbounds.push(outbound);

        let warnings = ConfigValidator::check_common_issues(&options);
        assert!(warnings.len() >= 2);
        assert!(warnings.iter().any(|w| w.contains("inbound 0 has no tag")));
        assert!(warnings.iter().any(|w| w.contains("outbound 0 has no tag")));
    }
}
