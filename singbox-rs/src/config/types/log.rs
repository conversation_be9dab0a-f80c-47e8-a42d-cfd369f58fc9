//! Log configuration types

use serde::{Deserialize, Serialize};

/// Log configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LogConfig {
    /// Log level
    #[serde(skip_serializing_if = "Option::is_none")]
    pub level: Option<String>,
    
    /// Output file path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub output: Option<String>,
    
    /// Disable color output
    #[serde(skip_serializing_if = "Option::is_none")]
    pub disabled: Option<bool>,
    
    /// Disable timestamp
    #[serde(skip_serializing_if = "Option::is_none")]
    pub timestamp: Option<bool>,
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            level: Some("info".to_string()),
            output: None,
            disabled: Some(false),
            timestamp: Some(true),
        }
    }
}
