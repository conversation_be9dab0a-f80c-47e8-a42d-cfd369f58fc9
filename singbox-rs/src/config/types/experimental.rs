//! Experimental configuration types

use serde::{Deserialize, Serialize};

/// Experimental configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct ExperimentalConfig {
    /// Clash API configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub clash_api: Option<ClashApiConfig>,
    
    /// V2Ray API configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub v2ray_api: Option<V2RayApiConfig>,
    
    /// Cache file configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cache_file: Option<CacheFileConfig>,
}

/// Clash API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClashApiConfig {
    /// External controller address
    pub external_controller: String,
    
    /// External UI path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub external_ui: Option<String>,
    
    /// External UI download URL
    #[serde(skip_serializing_if = "Option::is_none")]
    pub external_ui_download_url: Option<String>,
    
    /// External UI download detour
    #[serde(skip_serializing_if = "Option::is_none")]
    pub external_ui_download_detour: Option<String>,
    
    /// Secret for authentication
    #[serde(skip_serializing_if = "Option::is_none")]
    pub secret: Option<String>,
    
    /// Default mode
    #[serde(skip_serializing_if = "Option::is_none")]
    pub default_mode: Option<String>,
    
    /// Store mode
    #[serde(skip_serializing_if = "Option::is_none")]
    pub store_mode: Option<bool>,
    
    /// Store selected
    #[serde(skip_serializing_if = "Option::is_none")]
    pub store_selected: Option<bool>,
    
    /// Store fake IP
    #[serde(skip_serializing_if = "Option::is_none")]
    pub store_fakeip: Option<bool>,
    
    /// Cache file path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cache_file: Option<String>,
    
    /// Cache ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cache_id: Option<String>,
}

impl Default for ClashApiConfig {
    fn default() -> Self {
        Self {
            external_controller: "127.0.0.1:9090".to_string(),
            external_ui: None,
            external_ui_download_url: None,
            external_ui_download_detour: None,
            secret: None,
            default_mode: Some("rule".to_string()),
            store_mode: Some(true),
            store_selected: Some(true),
            store_fakeip: Some(true),
            cache_file: None,
            cache_id: None,
        }
    }
}

/// V2Ray API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct V2RayApiConfig {
    /// API listen address
    pub listen: String,
    
    /// API tag
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tag: Option<String>,
    
    /// Statistics configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub stats: Option<V2RayStatsConfig>,
}

/// V2Ray statistics configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct V2RayStatsConfig {
    /// Enable inbound statistics
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inbounds: Option<Vec<String>>,
    
    /// Enable outbound statistics
    #[serde(skip_serializing_if = "Option::is_none")]
    pub outbounds: Option<Vec<String>>,
    
    /// Enable user statistics
    #[serde(skip_serializing_if = "Option::is_none")]
    pub users: Option<Vec<String>>,
}

impl Default for V2RayApiConfig {
    fn default() -> Self {
        Self {
            listen: "127.0.0.1:8080".to_string(),
            tag: Some("api".to_string()),
            stats: None,
        }
    }
}

/// Cache file configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheFileConfig {
    /// Cache file path
    pub path: String,
    
    /// Cache ID for identification
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cache_id: Option<String>,
    
    /// Store fake IP mappings
    #[serde(skip_serializing_if = "Option::is_none")]
    pub store_fakeip: Option<bool>,
    
    /// Store RDRC (Remote DNS Response Cache)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub store_rdrc: Option<bool>,
    
    /// RDRC timeout
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rdrc_timeout: Option<String>,
}

impl Default for CacheFileConfig {
    fn default() -> Self {
        Self {
            path: "cache.db".to_string(),
            cache_id: None,
            store_fakeip: Some(true),
            store_rdrc: Some(true),
            rdrc_timeout: Some("7d".to_string()),
        }
    }
}
