//! Inbound configuration types

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Inbound configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct InboundConfig {
    /// Inbound tag
    pub tag: String,
    
    /// Inbound type
    pub r#type: String,
    
    /// Listen address
    #[serde(skip_serializing_if = "Option::is_none")]
    pub listen: Option<String>,
    
    /// Listen port
    #[serde(skip_serializing_if = "Option::is_none")]
    pub listen_port: Option<u16>,
    
    /// TCP fast open
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tcp_fast_open: Option<bool>,
    
    /// TCP multi path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tcp_multi_path: Option<bool>,
    
    /// UDP fragment
    #[serde(skip_serializing_if = "Option::is_none")]
    pub udp_fragment: Option<bool>,
    
    /// UDP timeout
    #[serde(skip_serializing_if = "Option::is_none")]
    pub udp_timeout: Option<String>,
    
    /// Proxy protocol
    #[serde(skip_serializing_if = "Option::is_none")]
    pub proxy_protocol: Option<bool>,
    
    /// Proxy protocol accept no header
    #[serde(skip_serializing_if = "Option::is_none")]
    pub proxy_protocol_accept_no_header: Option<bool>,
    
    /// Detour
    #[serde(skip_serializing_if = "Option::is_none")]
    pub detour: Option<String>,
    
    /// Sniff enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sniff: Option<bool>,
    
    /// Sniff override destination
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sniff_override_destination: Option<bool>,
    
    /// Sniff timeout
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sniff_timeout: Option<String>,
    
    /// Domain strategy
    #[serde(skip_serializing_if = "Option::is_none")]
    pub domain_strategy: Option<String>,
    
    /// Users
    #[serde(skip_serializing_if = "Option::is_none")]
    pub users: Option<Vec<InboundUser>>,
    
    /// TLS configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tls: Option<InboundTlsConfig>,
    
    /// Transport configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub transport: Option<serde_json::Value>,
    
    /// Additional protocol-specific options
    #[serde(flatten)]
    pub options: serde_json::Value,
}

/// Inbound user configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InboundUser {
    /// Username
    #[serde(skip_serializing_if = "Option::is_none")]
    pub name: Option<String>,
    
    /// User UUID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub uuid: Option<String>,
    
    /// Password
    #[serde(skip_serializing_if = "Option::is_none")]
    pub password: Option<String>,
    
    /// Flow
    #[serde(skip_serializing_if = "Option::is_none")]
    pub flow: Option<String>,
}

/// Inbound TLS configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InboundTlsConfig {
    /// Enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    
    /// Server name
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server_name: Option<String>,
    
    /// ALPN
    #[serde(skip_serializing_if = "Option::is_none")]
    pub alpn: Option<Vec<String>>,
    
    /// Min version
    #[serde(skip_serializing_if = "Option::is_none")]
    pub min_version: Option<String>,
    
    /// Max version
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_version: Option<String>,
    
    /// Cipher suites
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cipher_suites: Option<Vec<String>>,
    
    /// Certificate path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub certificate: Option<String>,
    
    /// Certificate PEM
    #[serde(skip_serializing_if = "Option::is_none")]
    pub certificate_pem: Option<String>,
    
    /// Key path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub key: Option<String>,
    
    /// Key PEM
    #[serde(skip_serializing_if = "Option::is_none")]
    pub key_pem: Option<String>,
    
    /// ACME configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub acme: Option<AcmeConfig>,
    
    /// ECH configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ech: Option<EchConfig>,
    
    /// Reality configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub reality: Option<RealityConfig>,
}

/// ACME configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AcmeConfig {
    /// Domain
    pub domain: Vec<String>,
    
    /// Data directory
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data_directory: Option<String>,
    
    /// Default server name
    #[serde(skip_serializing_if = "Option::is_none")]
    pub default_server_name: Option<String>,
    
    /// Email
    #[serde(skip_serializing_if = "Option::is_none")]
    pub email: Option<String>,
    
    /// Provider
    #[serde(skip_serializing_if = "Option::is_none")]
    pub provider: Option<String>,
    
    /// Disable HTTP challenge
    #[serde(skip_serializing_if = "Option::is_none")]
    pub disable_http_challenge: Option<bool>,
    
    /// Disable TLS ALPN challenge
    #[serde(skip_serializing_if = "Option::is_none")]
    pub disable_tls_alpn_challenge: Option<bool>,
    
    /// Alternative HTTP port
    #[serde(skip_serializing_if = "Option::is_none")]
    pub alternative_http_port: Option<u16>,
    
    /// Alternative TLS port
    #[serde(skip_serializing_if = "Option::is_none")]
    pub alternative_tls_port: Option<u16>,
    
    /// External account
    #[serde(skip_serializing_if = "Option::is_none")]
    pub external_account: Option<ExternalAccountConfig>,
}

/// External account configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExternalAccountConfig {
    /// Key ID
    pub key_id: String,
    
    /// MAC key
    pub mac_key: String,
}

/// ECH configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EchConfig {
    /// Enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    
    /// PQ signature schemes enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pq_signature_schemes_enabled: Option<bool>,
    
    /// Dynamic record sizing disabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub dynamic_record_sizing_disabled: Option<bool>,
    
    /// Key
    #[serde(skip_serializing_if = "Option::is_none")]
    pub key: Option<Vec<String>>,
}

/// Reality configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RealityConfig {
    /// Enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    
    /// Handshake
    #[serde(skip_serializing_if = "Option::is_none")]
    pub handshake: Option<RealityHandshakeConfig>,
    
    /// Private key
    #[serde(skip_serializing_if = "Option::is_none")]
    pub private_key: Option<String>,
    
    /// Short ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub short_id: Option<Vec<String>>,
    
    /// Max time difference
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_time_difference: Option<String>,
}

/// Reality handshake configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RealityHandshakeConfig {
    /// Server
    pub server: String,
    
    /// Server port
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server_port: Option<u16>,
}

impl Default for InboundConfig {
    fn default() -> Self {
        Self {
            tag: "default-in".to_string(),
            r#type: "direct".to_string(),
            listen: Some("127.0.0.1".to_string()),
            listen_port: Some(8080),
            tcp_fast_open: None,
            tcp_multi_path: None,
            udp_fragment: None,
            udp_timeout: None,
            proxy_protocol: None,
            proxy_protocol_accept_no_header: None,
            detour: None,
            sniff: None,
            sniff_override_destination: None,
            sniff_timeout: None,
            domain_strategy: None,
            users: None,
            tls: None,
            transport: None,
            options: serde_json::Value::Object(serde_json::Map::new()),
        }
    }
}
