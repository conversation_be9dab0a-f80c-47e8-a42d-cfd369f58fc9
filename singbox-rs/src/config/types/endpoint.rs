//! Endpoint configuration types

use serde::{Deserialize, Serialize};

/// Endpoint configuration
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EndpointConfig {
    /// Endpoint tag
    pub tag: String,
    
    /// Endpoint type
    pub r#type: String,
    
    /// Server address
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server: Option<String>,
    
    /// Server port
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server_port: Option<u16>,
    
    /// Additional options
    #[serde(flatten)]
    pub options: serde_json::Value,
}

impl Default for EndpointConfig {
    fn default() -> Self {
        Self {
            tag: "default".to_string(),
            r#type: "direct".to_string(),
            server: None,
            server_port: None,
            options: serde_json::Value::Object(serde_json::Map::new()),
        }
    }
}
