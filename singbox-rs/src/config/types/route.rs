//! Route configuration types

use serde::{Deserialize, Serialize};

/// Route configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct RouteConfig {
    /// GeoIP configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub geoip: Option<GeoIpConfig>,
    
    /// GeoSite configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub geosite: Option<GeoSiteConfig>,
    
    /// Rule set
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rule_set: Option<Vec<RuleSetConfig>>,
    
    /// Rules
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rules: Option<Vec<RouteRule>>,
    
    /// Final outbound
    #[serde(skip_serializing_if = "Option::is_none")]
    pub r#final: Option<String>,
    
    /// Final outbound (alternative field name)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub final_outbound: Option<String>,
    
    /// Auto detect interface
    #[serde(skip_serializing_if = "Option::is_none")]
    pub auto_detect_interface: Option<bool>,
    
    /// Override Android VPN
    #[serde(skip_serializing_if = "Option::is_none")]
    pub override_android_vpn: Option<bool>,
    
    /// Default interface
    #[serde(skip_serializing_if = "Option::is_none")]
    pub default_interface: Option<String>,
    
    /// Default mark
    #[serde(skip_serializing_if = "Option::is_none")]
    pub default_mark: Option<u32>,
}

/// GeoIP configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeoIpConfig {
    /// Path to GeoIP database
    #[serde(skip_serializing_if = "Option::is_none")]
    pub path: Option<String>,
    
    /// Download URL
    #[serde(skip_serializing_if = "Option::is_none")]
    pub download_url: Option<String>,
    
    /// Download detour
    #[serde(skip_serializing_if = "Option::is_none")]
    pub download_detour: Option<String>,
}

/// GeoSite configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeoSiteConfig {
    /// Path to GeoSite database
    #[serde(skip_serializing_if = "Option::is_none")]
    pub path: Option<String>,
    
    /// Download URL
    #[serde(skip_serializing_if = "Option::is_none")]
    pub download_url: Option<String>,
    
    /// Download detour
    #[serde(skip_serializing_if = "Option::is_none")]
    pub download_detour: Option<String>,
}

/// Rule set configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleSetConfig {
    /// Rule set tag
    pub tag: String,
    
    /// Rule set type
    pub r#type: String,
    
    /// Format
    #[serde(skip_serializing_if = "Option::is_none")]
    pub format: Option<String>,
    
    /// Path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub path: Option<String>,
    
    /// URL
    #[serde(skip_serializing_if = "Option::is_none")]
    pub url: Option<String>,
    
    /// Download detour
    #[serde(skip_serializing_if = "Option::is_none")]
    pub download_detour: Option<String>,
    
    /// Update interval
    #[serde(skip_serializing_if = "Option::is_none")]
    pub update_interval: Option<String>,
}

/// Route rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RouteRule {
    /// Inbound tags
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inbound: Option<Vec<String>>,
    
    /// IP version
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ip_version: Option<u8>,
    
    /// Network
    #[serde(skip_serializing_if = "Option::is_none")]
    pub network: Option<Vec<String>>,
    
    /// Auth user
    #[serde(skip_serializing_if = "Option::is_none")]
    pub auth_user: Option<Vec<String>>,
    
    /// Protocol
    #[serde(skip_serializing_if = "Option::is_none")]
    pub protocol: Option<Vec<String>>,
    
    /// Client
    #[serde(skip_serializing_if = "Option::is_none")]
    pub client: Option<Vec<String>>,
    
    /// Domain
    #[serde(skip_serializing_if = "Option::is_none")]
    pub domain: Option<Vec<String>>,
    
    /// Domain suffix
    #[serde(skip_serializing_if = "Option::is_none")]
    pub domain_suffix: Option<Vec<String>>,
    
    /// Domain keyword
    #[serde(skip_serializing_if = "Option::is_none")]
    pub domain_keyword: Option<Vec<String>>,
    
    /// Domain regex
    #[serde(skip_serializing_if = "Option::is_none")]
    pub domain_regex: Option<Vec<String>>,
    
    /// GeoSite
    #[serde(skip_serializing_if = "Option::is_none")]
    pub geosite: Option<Vec<String>>,
    
    /// Source GeoIP
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source_geoip: Option<Vec<String>>,
    
    /// GeoIP
    #[serde(skip_serializing_if = "Option::is_none")]
    pub geoip: Option<Vec<String>>,
    
    /// Source IP CIDR
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source_ip_cidr: Option<Vec<String>>,
    
    /// Source IP is private
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source_ip_is_private: Option<bool>,
    
    /// IP CIDR
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ip_cidr: Option<Vec<String>>,
    
    /// IP is private
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ip_is_private: Option<bool>,
    
    /// Source port
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source_port: Option<Vec<u16>>,
    
    /// Source port range
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source_port_range: Option<Vec<String>>,
    
    /// Port
    #[serde(skip_serializing_if = "Option::is_none")]
    pub port: Option<Vec<u16>>,
    
    /// Port range
    #[serde(skip_serializing_if = "Option::is_none")]
    pub port_range: Option<Vec<String>>,
    
    /// Process name
    #[serde(skip_serializing_if = "Option::is_none")]
    pub process_name: Option<Vec<String>>,
    
    /// Process path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub process_path: Option<Vec<String>>,
    
    /// Process path regex
    #[serde(skip_serializing_if = "Option::is_none")]
    pub process_path_regex: Option<Vec<String>>,
    
    /// Package name
    #[serde(skip_serializing_if = "Option::is_none")]
    pub package_name: Option<Vec<String>>,
    
    /// User
    #[serde(skip_serializing_if = "Option::is_none")]
    pub user: Option<Vec<String>>,
    
    /// User ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub user_id: Option<Vec<u32>>,
    
    /// Clash mode
    #[serde(skip_serializing_if = "Option::is_none")]
    pub clash_mode: Option<String>,
    
    /// Network type
    #[serde(skip_serializing_if = "Option::is_none")]
    pub network_type: Option<Vec<String>>,
    
    /// Network is expensive
    #[serde(skip_serializing_if = "Option::is_none")]
    pub network_is_expensive: Option<bool>,
    
    /// Network is constrained
    #[serde(skip_serializing_if = "Option::is_none")]
    pub network_is_constrained: Option<bool>,
    
    /// WiFi SSID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub wifi_ssid: Option<Vec<String>>,
    
    /// WiFi BSSID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub wifi_bssid: Option<Vec<String>>,
    
    /// Rule set
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rule_set: Option<Vec<String>>,
    
    /// Rule set IP CIDR match source
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rule_set_ip_cidr_match_source: Option<bool>,
    
    /// Rule set IP CIDR accept empty
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rule_set_ip_cidr_accept_empty: Option<bool>,
    
    /// Invert
    #[serde(skip_serializing_if = "Option::is_none")]
    pub invert: Option<bool>,
    
    /// Outbound
    pub outbound: String,
}

impl Default for RouteRule {
    fn default() -> Self {
        Self {
            inbound: None,
            ip_version: None,
            network: None,
            auth_user: None,
            protocol: None,
            client: None,
            domain: None,
            domain_suffix: None,
            domain_keyword: None,
            domain_regex: None,
            geosite: None,
            source_geoip: None,
            geoip: None,
            source_ip_cidr: None,
            source_ip_is_private: None,
            ip_cidr: None,
            ip_is_private: None,
            source_port: None,
            source_port_range: None,
            port: None,
            port_range: None,
            process_name: None,
            process_path: None,
            process_path_regex: None,
            package_name: None,
            user: None,
            user_id: None,
            clash_mode: None,
            network_type: None,
            network_is_expensive: None,
            network_is_constrained: None,
            wifi_ssid: None,
            wifi_bssid: None,
            rule_set: None,
            rule_set_ip_cidr_match_source: None,
            rule_set_ip_cidr_accept_empty: None,
            invert: None,
            outbound: "direct".to_string(),
        }
    }
}
