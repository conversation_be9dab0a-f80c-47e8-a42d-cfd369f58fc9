//! Service configuration types

use serde::{Deserialize, Serialize};

/// Service configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServiceConfig {
    /// Service type
    pub r#type: String,
    
    /// Service tag
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tag: Option<String>,
    
    /// Additional service-specific options
    #[serde(flatten)]
    pub options: serde_json::Value,
}

impl Default for ServiceConfig {
    fn default() -> Self {
        Self {
            r#type: "derp".to_string(),
            tag: None,
            options: serde_json::Value::Object(serde_json::Map::new()),
        }
    }
}
