//! NTP configuration types

use serde::{Deserialize, Serialize};

/// NTP configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NtpConfig {
    /// Enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    
    /// Server address
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server: Option<String>,
    
    /// Server port
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server_port: Option<u16>,
    
    /// Interval
    #[serde(skip_serializing_if = "Option::is_none")]
    pub interval: Option<String>,
    
    /// Write to system
    #[serde(skip_serializing_if = "Option::is_none")]
    pub write_to_system: Option<bool>,
    
    /// Detour
    #[serde(skip_serializing_if = "Option::is_none")]
    pub detour: Option<String>,
}

impl Default for NtpConfig {
    fn default() -> Self {
        Self {
            enabled: Some(false),
            server: Some("time.cloudflare.com".to_string()),
            server_port: Some(123),
            interval: Some("30m".to_string()),
            write_to_system: Some(false),
            detour: None,
        }
    }
}
