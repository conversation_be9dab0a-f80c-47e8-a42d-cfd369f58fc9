//! Configuration types module
//!
//! This module contains all configuration type definitions that match
//! the original sing-box configuration format exactly.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

pub mod log;
pub mod dns;
pub mod ntp;
pub mod certificate;
pub mod endpoint;
pub mod inbound;
pub mod outbound;
pub mod route;
pub mod service;
pub mod experimental;

pub use self::log::LogConfig;
pub use self::dns::DnsConfig;
pub use self::ntp::NtpConfig;
pub use self::certificate::CertificateConfig;
pub use self::endpoint::EndpointConfig;
pub use self::inbound::InboundConfig;
pub use self::outbound::OutboundConfig;
pub use self::route::RouteConfig;
pub use self::service::ServiceConfig;
pub use self::experimental::ExperimentalConfig;

/// Main configuration structure that matches sing-box exactly
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct Config {
    /// Log configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub log: Option<LogConfig>,
    
    /// DNS configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub dns: Option<DnsConfig>,
    
    /// NTP configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ntp: Option<NtpConfig>,
    
    /// Certificate configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub certificate: Option<CertificateConfig>,
    
    /// Endpoint configurations
    #[serde(skip_serializing_if = "Option::is_none")]
    pub endpoints: Option<Vec<EndpointConfig>>,
    
    /// Inbound configurations
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inbounds: Option<Vec<InboundConfig>>,
    
    /// Outbound configurations
    #[serde(skip_serializing_if = "Option::is_none")]
    pub outbounds: Option<Vec<OutboundConfig>>,
    
    /// Route configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub route: Option<RouteConfig>,
    
    /// Service configurations
    #[serde(skip_serializing_if = "Option::is_none")]
    pub services: Option<Vec<ServiceConfig>>,
    
    /// Experimental configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub experimental: Option<ExperimentalConfig>,
}

impl Config {
    /// Create a new empty configuration
    pub fn new() -> Self {
        Self::default()
    }
    
    /// Load configuration from JSON string
    pub fn from_json(json: &str) -> Result<Self, serde_json::Error> {
        serde_json::from_str(json)
    }
    
    /// Convert configuration to JSON string
    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string_pretty(self)
    }
    
    /// Validate the configuration
    pub fn validate(&self) -> Result<(), String> {
        // Validate inbounds
        if let Some(inbounds) = &self.inbounds {
            let mut tags = std::collections::HashSet::new();
            for inbound in inbounds {
                if !tags.insert(&inbound.tag) {
                    return Err(format!("Duplicate inbound tag: {}", inbound.tag));
                }
            }
        }
        
        // Validate outbounds
        if let Some(outbounds) = &self.outbounds {
            let mut tags = std::collections::HashSet::new();
            for outbound in outbounds {
                if !tags.insert(&outbound.tag) {
                    return Err(format!("Duplicate outbound tag: {}", outbound.tag));
                }
            }
        }
        
        // Validate route references
        if let Some(route) = &self.route {
            if let Some(outbounds) = &self.outbounds {
                let outbound_tags: std::collections::HashSet<_> = 
                    outbounds.iter().map(|o| &o.tag).collect();
                
                // Check final outbound
                if let Some(final_outbound) = &route.final_outbound {
                    if !outbound_tags.contains(final_outbound) {
                        return Err(format!("Final outbound '{}' not found", final_outbound));
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// Merge with another configuration
    pub fn merge(&mut self, other: Config) {
        if other.log.is_some() {
            self.log = other.log;
        }
        if other.dns.is_some() {
            self.dns = other.dns;
        }
        if other.ntp.is_some() {
            self.ntp = other.ntp;
        }
        if other.certificate.is_some() {
            self.certificate = other.certificate;
        }
        if let Some(endpoints) = other.endpoints {
            self.endpoints = Some(endpoints);
        }
        if let Some(inbounds) = other.inbounds {
            self.inbounds = Some(inbounds);
        }
        if let Some(outbounds) = other.outbounds {
            self.outbounds = Some(outbounds);
        }
        if other.route.is_some() {
            self.route = other.route;
        }
        if let Some(services) = other.services {
            self.services = Some(services);
        }
        if other.experimental.is_some() {
            self.experimental = other.experimental;
        }
    }
}
