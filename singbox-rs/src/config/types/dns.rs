//! DNS configuration types

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// DNS configuration
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct DnsConfig {
    /// DNS servers
    #[serde(skip_serializing_if = "Option::is_none")]
    pub servers: Option<Vec<DnsServer>>,
    
    /// DNS rules
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rules: Option<Vec<DnsRule>>,
    
    /// Final DNS server
    #[serde(skip_serializing_if = "Option::is_none")]
    pub r#final: Option<String>,
    
    /// Strategy
    #[serde(skip_serializing_if = "Option::is_none")]
    pub strategy: Option<String>,
    
    /// Disable cache
    #[serde(skip_serializing_if = "Option::is_none")]
    pub disable_cache: Option<bool>,
    
    /// Disable expire
    #[serde(skip_serializing_if = "Option::is_none")]
    pub disable_expire: Option<bool>,
    
    /// Independent cache
    #[serde(skip_serializing_if = "Option::is_none")]
    pub independent_cache: Option<bool>,
    
    /// Reverse mapping
    #[serde(skip_serializing_if = "Option::is_none")]
    pub reverse_mapping: Option<bool>,
    
    /// FakeIP configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub fakeip: Option<FakeIpConfig>,
}

/// DNS server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DnsServer {
    /// Server tag
    pub tag: String,
    
    /// Server address
    pub address: String,
    
    /// Address resolver
    #[serde(skip_serializing_if = "Option::is_none")]
    pub address_resolver: Option<String>,
    
    /// Address strategy
    #[serde(skip_serializing_if = "Option::is_none")]
    pub address_strategy: Option<String>,
    
    /// Strategy
    #[serde(skip_serializing_if = "Option::is_none")]
    pub strategy: Option<String>,
    
    /// Detour
    #[serde(skip_serializing_if = "Option::is_none")]
    pub detour: Option<String>,
}

/// DNS rule configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DnsRule {
    /// Inbound tags
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inbound: Option<Vec<String>>,
    
    /// IP version
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ip_version: Option<u8>,
    
    /// Query type
    #[serde(skip_serializing_if = "Option::is_none")]
    pub query_type: Option<Vec<String>>,
    
    /// Network
    #[serde(skip_serializing_if = "Option::is_none")]
    pub network: Option<Vec<String>>,
    
    /// Auth user
    #[serde(skip_serializing_if = "Option::is_none")]
    pub auth_user: Option<Vec<String>>,
    
    /// Protocol
    #[serde(skip_serializing_if = "Option::is_none")]
    pub protocol: Option<Vec<String>>,
    
    /// Domain
    #[serde(skip_serializing_if = "Option::is_none")]
    pub domain: Option<Vec<String>>,
    
    /// Domain suffix
    #[serde(skip_serializing_if = "Option::is_none")]
    pub domain_suffix: Option<Vec<String>>,
    
    /// Domain keyword
    #[serde(skip_serializing_if = "Option::is_none")]
    pub domain_keyword: Option<Vec<String>>,
    
    /// Domain regex
    #[serde(skip_serializing_if = "Option::is_none")]
    pub domain_regex: Option<Vec<String>>,
    
    /// GeoSite
    #[serde(skip_serializing_if = "Option::is_none")]
    pub geosite: Option<Vec<String>>,
    
    /// Source GeoIP
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source_geoip: Option<Vec<String>>,
    
    /// GeoIP
    #[serde(skip_serializing_if = "Option::is_none")]
    pub geoip: Option<Vec<String>>,
    
    /// Source IP CIDR
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source_ip_cidr: Option<Vec<String>>,
    
    /// Source IP is private
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source_ip_is_private: Option<bool>,
    
    /// IP CIDR
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ip_cidr: Option<Vec<String>>,
    
    /// IP is private
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ip_is_private: Option<bool>,
    
    /// Source port
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source_port: Option<Vec<u16>>,
    
    /// Source port range
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source_port_range: Option<Vec<String>>,
    
    /// Port
    #[serde(skip_serializing_if = "Option::is_none")]
    pub port: Option<Vec<u16>>,
    
    /// Port range
    #[serde(skip_serializing_if = "Option::is_none")]
    pub port_range: Option<Vec<String>>,
    
    /// Process name
    #[serde(skip_serializing_if = "Option::is_none")]
    pub process_name: Option<Vec<String>>,
    
    /// Process path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub process_path: Option<Vec<String>>,
    
    /// Package name
    #[serde(skip_serializing_if = "Option::is_none")]
    pub package_name: Option<Vec<String>>,
    
    /// User
    #[serde(skip_serializing_if = "Option::is_none")]
    pub user: Option<Vec<String>>,
    
    /// User ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub user_id: Option<Vec<u32>>,
    
    /// Outbound
    #[serde(skip_serializing_if = "Option::is_none")]
    pub outbound: Option<Vec<String>>,
    
    /// Clash mode
    #[serde(skip_serializing_if = "Option::is_none")]
    pub clash_mode: Option<String>,
    
    /// WiFi SSID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub wifi_ssid: Option<Vec<String>>,
    
    /// WiFi BSSID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub wifi_bssid: Option<Vec<String>>,
    
    /// Rule set
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rule_set: Option<Vec<String>>,
    
    /// Invert
    #[serde(skip_serializing_if = "Option::is_none")]
    pub invert: Option<bool>,
    
    /// Server
    pub server: String,
    
    /// Disable cache
    #[serde(skip_serializing_if = "Option::is_none")]
    pub disable_cache: Option<bool>,
    
    /// Rewrite TTL
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rewrite_ttl: Option<u32>,
}

/// FakeIP configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FakeIpConfig {
    /// Enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    
    /// Inet4 range
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inet4_range: Option<String>,
    
    /// Inet6 range
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inet6_range: Option<String>,
}
