//! Certificate configuration types

use serde::{Deserialize, Serialize};

/// Certificate configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CertificateConfig {
    /// Certificate path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub certificate: Option<String>,
    
    /// Certificate PEM data
    #[serde(skip_serializing_if = "Option::is_none")]
    pub certificate_pem: Option<String>,
    
    /// Private key path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub key: Option<String>,
    
    /// Private key PEM data
    #[serde(skip_serializing_if = "Option::is_none")]
    pub key_pem: Option<String>,
}

impl Default for CertificateConfig {
    fn default() -> Self {
        Self {
            certificate: None,
            certificate_pem: None,
            key: None,
            key_pem: None,
        }
    }
}
