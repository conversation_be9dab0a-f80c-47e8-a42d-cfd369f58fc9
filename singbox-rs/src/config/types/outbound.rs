//! Outbound configuration types

use serde::{Deserialize, Serialize};

/// Outbound configuration
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OutboundConfig {
    /// Outbound tag
    pub tag: String,
    
    /// Outbound type
    pub r#type: String,
    
    /// Server address
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server: Option<String>,
    
    /// Server port
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server_port: Option<u16>,
    
    /// Username
    #[serde(skip_serializing_if = "Option::is_none")]
    pub username: Option<String>,
    
    /// Password
    #[serde(skip_serializing_if = "Option::is_none")]
    pub password: Option<String>,
    
    /// UUID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub uuid: Option<String>,
    
    /// Flow
    #[serde(skip_serializing_if = "Option::is_none")]
    pub flow: Option<String>,
    
    /// Method
    #[serde(skip_serializing_if = "Option::is_none")]
    pub method: Option<String>,
    
    /// Network
    #[serde(skip_serializing_if = "Option::is_none")]
    pub network: Option<String>,
    
    /// TCP fast open
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tcp_fast_open: Option<bool>,
    
    /// TCP multi path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tcp_multi_path: Option<bool>,
    
    /// UDP fragment
    #[serde(skip_serializing_if = "Option::is_none")]
    pub udp_fragment: Option<bool>,
    
    /// Connect timeout
    #[serde(skip_serializing_if = "Option::is_none")]
    pub connect_timeout: Option<String>,
    
    /// Domain strategy
    #[serde(skip_serializing_if = "Option::is_none")]
    pub domain_strategy: Option<String>,
    
    /// Fallback delay
    #[serde(skip_serializing_if = "Option::is_none")]
    pub fallback_delay: Option<String>,
    
    /// Bind interface
    #[serde(skip_serializing_if = "Option::is_none")]
    pub bind_interface: Option<String>,
    
    /// Inet4 bind address
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inet4_bind_address: Option<String>,
    
    /// Inet6 bind address
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inet6_bind_address: Option<String>,
    
    /// Routing mark
    #[serde(skip_serializing_if = "Option::is_none")]
    pub routing_mark: Option<u32>,
    
    /// Reuse address
    #[serde(skip_serializing_if = "Option::is_none")]
    pub reuse_addr: Option<bool>,
    
    /// Protect path
    #[serde(skip_serializing_if = "Option::is_none")]
    pub protect_path: Option<String>,
    
    /// Detour
    #[serde(skip_serializing_if = "Option::is_none")]
    pub detour: Option<String>,
    
    /// TLS configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tls: Option<OutboundTlsConfig>,
    
    /// Transport configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub transport: Option<serde_json::Value>,
    
    /// Multiplex configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub multiplex: Option<MultiplexConfig>,
    
    /// Additional protocol-specific options
    #[serde(flatten)]
    pub options: serde_json::Value,
}

/// Outbound TLS configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutboundTlsConfig {
    /// Enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    
    /// Disable SNI
    #[serde(skip_serializing_if = "Option::is_none")]
    pub disable_sni: Option<bool>,
    
    /// Server name
    #[serde(skip_serializing_if = "Option::is_none")]
    pub server_name: Option<String>,
    
    /// Insecure
    #[serde(skip_serializing_if = "Option::is_none")]
    pub insecure: Option<bool>,
    
    /// ALPN
    #[serde(skip_serializing_if = "Option::is_none")]
    pub alpn: Option<Vec<String>>,
    
    /// Min version
    #[serde(skip_serializing_if = "Option::is_none")]
    pub min_version: Option<String>,
    
    /// Max version
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_version: Option<String>,
    
    /// Cipher suites
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cipher_suites: Option<Vec<String>>,
    
    /// Certificate
    #[serde(skip_serializing_if = "Option::is_none")]
    pub certificate: Option<String>,
    
    /// Certificate PEM
    #[serde(skip_serializing_if = "Option::is_none")]
    pub certificate_pem: Option<String>,
    
    /// ECH configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ech: Option<OutboundEchConfig>,
    
    /// uTLS configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub utls: Option<UtlsConfig>,
    
    /// Reality configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub reality: Option<OutboundRealityConfig>,
}

/// Outbound ECH configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutboundEchConfig {
    /// Enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    
    /// PQ signature schemes enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pq_signature_schemes_enabled: Option<bool>,
    
    /// Dynamic record sizing disabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub dynamic_record_sizing_disabled: Option<bool>,
    
    /// Config
    #[serde(skip_serializing_if = "Option::is_none")]
    pub config: Option<String>,
}

/// uTLS configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UtlsConfig {
    /// Enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    
    /// Fingerprint
    #[serde(skip_serializing_if = "Option::is_none")]
    pub fingerprint: Option<String>,
}

/// Outbound Reality configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutboundRealityConfig {
    /// Enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    
    /// Public key
    #[serde(skip_serializing_if = "Option::is_none")]
    pub public_key: Option<String>,
    
    /// Short ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub short_id: Option<String>,
}

/// Multiplex configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MultiplexConfig {
    /// Enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    
    /// Protocol
    #[serde(skip_serializing_if = "Option::is_none")]
    pub protocol: Option<String>,
    
    /// Max connections
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_connections: Option<u32>,
    
    /// Min streams
    #[serde(skip_serializing_if = "Option::is_none")]
    pub min_streams: Option<u32>,
    
    /// Max streams
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_streams: Option<u32>,
    
    /// Padding
    #[serde(skip_serializing_if = "Option::is_none")]
    pub padding: Option<bool>,
    
    /// Brutal configuration
    #[serde(skip_serializing_if = "Option::is_none")]
    pub brutal: Option<BrutalConfig>,
}

/// Brutal configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrutalConfig {
    /// Enabled
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    
    /// Up Mbps
    #[serde(skip_serializing_if = "Option::is_none")]
    pub up_mbps: Option<u32>,
    
    /// Down Mbps
    #[serde(skip_serializing_if = "Option::is_none")]
    pub down_mbps: Option<u32>,
}

impl Default for OutboundConfig {
    fn default() -> Self {
        Self {
            tag: "default-out".to_string(),
            r#type: "direct".to_string(),
            server: None,
            server_port: None,
            username: None,
            password: None,
            uuid: None,
            flow: None,
            method: None,
            network: None,
            tcp_fast_open: None,
            tcp_multi_path: None,
            udp_fragment: None,
            connect_timeout: None,
            domain_strategy: None,
            fallback_delay: None,
            bind_interface: None,
            inet4_bind_address: None,
            inet6_bind_address: None,
            routing_mark: None,
            reuse_addr: None,
            protect_path: None,
            detour: None,
            tls: None,
            transport: None,
            multiplex: None,
            options: serde_json::Value::Object(serde_json::Map::new()),
        }
    }
}
