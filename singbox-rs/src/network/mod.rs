//! Network module for sing-box
//! 
//! This module provides network transport functionality including dialers,
//! listeners, and connection management.

use std::collections::HashMap;
use std::net::{SocketAddr, TcpListener, UdpSocket};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use async_trait::async_trait;
use crate::adapter::{Lifecycle, StartStage};

pub mod multiplex;

/// Network errors
#[derive(Debug, Clone)]
pub enum NetworkError {
    DialFailed(String),
    ListenFailed(String),
    ConnectionClosed,
    InvalidAddress(String),
    Timeout,
    PermissionDenied,
}

impl std::fmt::Display for NetworkError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            NetworkError::DialFailed(addr) => write!(f, "dial failed: {}", addr),
            NetworkError::ListenFailed(addr) => write!(f, "listen failed: {}", addr),
            NetworkError::ConnectionClosed => write!(f, "connection closed"),
            NetworkError::InvalidAddress(addr) => write!(f, "invalid address: {}", addr),
            NetworkError::Timeout => write!(f, "network timeout"),
            NetworkError::PermissionDenied => write!(f, "permission denied"),
        }
    }
}

impl std::error::Error for NetworkError {}

/// Dialer trait for creating outbound connections
pub trait Dialer: Send + Sync {
    fn dial(&self, network: &str, address: &str) -> Result<Connection, NetworkError>;
    fn dial_timeout(&self, network: &str, address: &str, timeout: Duration) -> Result<Connection, NetworkError>;
}

/// Listener trait for accepting inbound connections
pub trait Listener: Send + Sync {
    fn listen(&self, network: &str, address: &str) -> Result<Box<dyn AcceptLoop>, NetworkError>;
}

/// Accept loop trait for handling incoming connections
pub trait AcceptLoop: Send + Sync {
    fn accept(&mut self) -> Result<Connection, NetworkError>;
    fn close(&mut self) -> Result<(), NetworkError>;
    fn local_addr(&self) -> Result<SocketAddr, NetworkError>;
}

/// Connection abstraction
#[derive(Debug)]
pub struct Connection {
    pub local_addr: SocketAddr,
    pub remote_addr: SocketAddr,
    pub network: String,
    pub established_at: std::time::Instant,
}

impl Connection {
    pub fn new(network: &str, local_addr: SocketAddr, remote_addr: SocketAddr) -> Self {
        Self {
            local_addr,
            remote_addr,
            network: network.to_string(),
            established_at: std::time::Instant::now(),
        }
    }

    pub fn duration(&self) -> Duration {
        self.established_at.elapsed()
    }

    pub fn is_tcp(&self) -> bool {
        self.network == "tcp" || self.network == "tcp4" || self.network == "tcp6"
    }

    pub fn is_udp(&self) -> bool {
        self.network == "udp" || self.network == "udp4" || self.network == "udp6"
    }
}

/// Default dialer implementation
#[derive(Debug)]
pub struct DefaultDialer {
    timeout: Duration,
    bind_interface: Option<String>,
    routing_mark: Option<u32>,
}

impl DefaultDialer {
    pub fn new() -> Self {
        Self {
            timeout: Duration::from_secs(10),
            bind_interface: None,
            routing_mark: None,
        }
    }

    pub fn with_timeout(mut self, timeout: Duration) -> Self {
        self.timeout = timeout;
        self
    }

    pub fn with_bind_interface(mut self, interface: &str) -> Self {
        self.bind_interface = Some(interface.to_string());
        self
    }

    pub fn with_routing_mark(mut self, mark: u32) -> Self {
        self.routing_mark = Some(mark);
        self
    }
}

impl Default for DefaultDialer {
    fn default() -> Self {
        Self::new()
    }
}

impl Dialer for DefaultDialer {
    fn dial(&self, network: &str, address: &str) -> Result<Connection, NetworkError> {
        self.dial_timeout(network, address, self.timeout)
    }

    fn dial_timeout(&self, network: &str, address: &str, _timeout: Duration) -> Result<Connection, NetworkError> {
        let socket_addr: SocketAddr = address.parse()
            .map_err(|_| NetworkError::InvalidAddress(address.to_string()))?;

        match network {
            "tcp" | "tcp4" | "tcp6" => {
                // Simulate TCP connection
                let local_addr = if socket_addr.is_ipv6() {
                    "[::]:0".parse().unwrap()
                } else {
                    "0.0.0.0:0".parse().unwrap()
                };
                Ok(Connection::new(network, local_addr, socket_addr))
            }
            "udp" | "udp4" | "udp6" => {
                // Simulate UDP connection
                let local_addr = if socket_addr.is_ipv6() {
                    "[::]:0".parse().unwrap()
                } else {
                    "0.0.0.0:0".parse().unwrap()
                };
                Ok(Connection::new(network, local_addr, socket_addr))
            }
            _ => Err(NetworkError::DialFailed(format!("unsupported network: {}", network))),
        }
    }
}

/// Default listener implementation
#[derive(Debug)]
pub struct DefaultListener {
    bind_interface: Option<String>,
    reuse_port: bool,
}

impl DefaultListener {
    pub fn new() -> Self {
        Self {
            bind_interface: None,
            reuse_port: false,
        }
    }

    pub fn with_bind_interface(mut self, interface: &str) -> Self {
        self.bind_interface = Some(interface.to_string());
        self
    }

    pub fn with_reuse_port(mut self, reuse: bool) -> Self {
        self.reuse_port = reuse;
        self
    }
}

impl Default for DefaultListener {
    fn default() -> Self {
        Self::new()
    }
}

impl Listener for DefaultListener {
    fn listen(&self, network: &str, address: &str) -> Result<Box<dyn AcceptLoop>, NetworkError> {
        let socket_addr: SocketAddr = address.parse()
            .map_err(|_| NetworkError::InvalidAddress(address.to_string()))?;

        match network {
            "tcp" | "tcp4" | "tcp6" => {
                Ok(Box::new(TcpAcceptLoop::new(socket_addr)?))
            }
            "udp" | "udp4" | "udp6" => {
                Ok(Box::new(UdpAcceptLoop::new(socket_addr)?))
            }
            _ => Err(NetworkError::ListenFailed(format!("unsupported network: {}", network))),
        }
    }
}

/// TCP accept loop implementation
struct TcpAcceptLoop {
    listener: TcpListener,
}

impl TcpAcceptLoop {
    fn new(addr: SocketAddr) -> Result<Self, NetworkError> {
        let listener = TcpListener::bind(addr)
            .map_err(|e| NetworkError::ListenFailed(e.to_string()))?;
        Ok(Self { listener })
    }
}

impl AcceptLoop for TcpAcceptLoop {
    fn accept(&mut self) -> Result<Connection, NetworkError> {
        let (stream, remote_addr) = self.listener.accept()
            .map_err(|e| NetworkError::DialFailed(e.to_string()))?;
        
        let local_addr = stream.local_addr()
            .map_err(|e| NetworkError::DialFailed(e.to_string()))?;
        
        Ok(Connection::new("tcp", local_addr, remote_addr))
    }

    fn close(&mut self) -> Result<(), NetworkError> {
        // TcpListener doesn't have an explicit close method in std
        // In a real implementation, we'd store the listener differently
        Ok(())
    }

    fn local_addr(&self) -> Result<SocketAddr, NetworkError> {
        self.listener.local_addr()
            .map_err(|e| NetworkError::DialFailed(e.to_string()))
    }
}

/// UDP accept loop implementation (simplified)
struct UdpAcceptLoop {
    socket: UdpSocket,
    addr: SocketAddr,
}

impl UdpAcceptLoop {
    fn new(addr: SocketAddr) -> Result<Self, NetworkError> {
        let socket = UdpSocket::bind(addr)
            .map_err(|e| NetworkError::ListenFailed(e.to_string()))?;
        Ok(Self { socket, addr })
    }
}

impl AcceptLoop for UdpAcceptLoop {
    fn accept(&mut self) -> Result<Connection, NetworkError> {
        // UDP is connectionless, so this is a simplified simulation
        // In a real implementation, this would handle packet reception
        Ok(Connection::new("udp", self.addr, "0.0.0.0:0".parse().unwrap()))
    }

    fn close(&mut self) -> Result<(), NetworkError> {
        // UdpSocket doesn't have an explicit close method in std
        Ok(())
    }

    fn local_addr(&self) -> Result<SocketAddr, NetworkError> {
        Ok(self.addr)
    }
}

/// Network manager for handling network interfaces and routing
pub struct NetworkManager {
    dialers: Arc<RwLock<HashMap<String, Box<dyn Dialer>>>>,
    listeners: Arc<RwLock<HashMap<String, Box<dyn Listener>>>>,
    default_dialer: Box<dyn Dialer>,
    default_listener: Box<dyn Listener>,
}

impl NetworkManager {
    pub fn new() -> Self {
        Self {
            dialers: Arc::new(RwLock::new(HashMap::new())),
            listeners: Arc::new(RwLock::new(HashMap::new())),
            default_dialer: Box::new(DefaultDialer::new()),
            default_listener: Box::new(DefaultListener::new()),
        }
    }

    pub async fn register_dialer(&self, name: &str, dialer: Box<dyn Dialer>) {
        self.dialers.write().await.insert(name.to_string(), dialer);
    }

    pub async fn register_listener(&self, name: &str, listener: Box<dyn Listener>) {
        self.listeners.write().await.insert(name.to_string(), listener);
    }

    pub async fn has_dialer(&self, name: &str) -> bool {
        self.dialers.read().await.contains_key(name)
    }

    pub async fn has_listener(&self, name: &str) -> bool {
        self.listeners.read().await.contains_key(name)
    }

    pub fn default_dialer(&self) -> &dyn Dialer {
        self.default_dialer.as_ref()
    }

    pub fn default_listener(&self) -> &dyn Listener {
        self.default_listener.as_ref()
    }

    pub async fn get_dialer(&self, name: &str) -> bool {
        self.dialers.read().await.contains_key(name)
    }

    pub async fn get_listener(&self, name: &str) -> bool {
        self.listeners.read().await.contains_key(name)
    }
}

impl Default for NetworkManager {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Lifecycle for NetworkManager {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        // Network manager initialization
        Ok(())
    }

    async fn close(&self) -> Result<(), String> {
        // Network manager cleanup
        self.dialers.write().await.clear();
        self.listeners.write().await.clear();
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_network_error_display() {
        let err1 = NetworkError::DialFailed("test".to_string());
        assert_eq!(format!("{}", err1), "dial failed: test");
        
        let err2 = NetworkError::ListenFailed("addr".to_string());
        assert_eq!(format!("{}", err2), "listen failed: addr");
        
        let err3 = NetworkError::ConnectionClosed;
        assert_eq!(format!("{}", err3), "connection closed");
        
        let err4 = NetworkError::Timeout;
        assert_eq!(format!("{}", err4), "network timeout");
    }

    #[test]
    fn test_connection_creation() {
        let local_addr = "127.0.0.1:8080".parse().unwrap();
        let remote_addr = "192.168.1.1:80".parse().unwrap();
        let conn = Connection::new("tcp", local_addr, remote_addr);
        
        assert_eq!(conn.network, "tcp");
        assert_eq!(conn.local_addr, local_addr);
        assert_eq!(conn.remote_addr, remote_addr);
        assert!(conn.is_tcp());
        assert!(!conn.is_udp());
    }

    #[test]
    fn test_connection_network_detection() {
        let addr = "127.0.0.1:8080".parse().unwrap();
        
        let tcp_conn = Connection::new("tcp", addr, addr);
        assert!(tcp_conn.is_tcp());
        assert!(!tcp_conn.is_udp());
        
        let udp_conn = Connection::new("udp", addr, addr);
        assert!(!udp_conn.is_tcp());
        assert!(udp_conn.is_udp());
    }

    #[test]
    fn test_default_dialer_creation() {
        let dialer = DefaultDialer::new();
        assert_eq!(dialer.timeout, Duration::from_secs(10));
        assert!(dialer.bind_interface.is_none());
        assert!(dialer.routing_mark.is_none());
    }

    #[test]
    fn test_default_dialer_builder() {
        let dialer = DefaultDialer::new()
            .with_timeout(Duration::from_secs(5))
            .with_bind_interface("eth0")
            .with_routing_mark(100);
        
        assert_eq!(dialer.timeout, Duration::from_secs(5));
        assert_eq!(dialer.bind_interface, Some("eth0".to_string()));
        assert_eq!(dialer.routing_mark, Some(100));
    }

    #[test]
    fn test_default_dialer_dial() {
        let dialer = DefaultDialer::new();
        
        // Test valid address
        let result = dialer.dial("tcp", "127.0.0.1:8080");
        assert!(result.is_ok());
        
        let conn = result.unwrap();
        assert_eq!(conn.network, "tcp");
        assert_eq!(conn.remote_addr.to_string(), "127.0.0.1:8080");
    }

    #[test]
    fn test_default_dialer_invalid_address() {
        let dialer = DefaultDialer::new();
        
        let result = dialer.dial("tcp", "invalid-address");
        assert!(result.is_err());
        
        match result.unwrap_err() {
            NetworkError::InvalidAddress(addr) => assert_eq!(addr, "invalid-address"),
            _ => panic!("Expected InvalidAddress error"),
        }
    }

    #[test]
    fn test_default_dialer_unsupported_network() {
        let dialer = DefaultDialer::new();
        
        let result = dialer.dial("unknown", "127.0.0.1:8080");
        assert!(result.is_err());
        
        match result.unwrap_err() {
            NetworkError::DialFailed(msg) => assert!(msg.contains("unsupported network")),
            _ => panic!("Expected DialFailed error"),
        }
    }

    #[test]
    fn test_default_listener_creation() {
        let listener = DefaultListener::new();
        assert!(listener.bind_interface.is_none());
        assert!(!listener.reuse_port);
    }

    #[test]
    fn test_default_listener_builder() {
        let listener = DefaultListener::new()
            .with_bind_interface("eth0")
            .with_reuse_port(true);
        
        assert_eq!(listener.bind_interface, Some("eth0".to_string()));
        assert!(listener.reuse_port);
    }

    #[tokio::test]
    async fn test_network_manager_creation() {
        let manager = NetworkManager::new();
        assert!(manager.dialers.read().await.is_empty());
        assert!(manager.listeners.read().await.is_empty());
    }

    #[tokio::test]
    async fn test_network_manager_register() {
        let manager = NetworkManager::new();

        let dialer = Box::new(DefaultDialer::new());
        let listener = Box::new(DefaultListener::new());

        manager.register_dialer("test_dialer", dialer).await;
        manager.register_listener("test_listener", listener).await;

        assert!(manager.get_dialer("test_dialer").await);
        assert!(manager.get_listener("test_listener").await);
        assert!(!manager.get_dialer("nonexistent").await);
    }

    #[test]
    fn test_network_manager_defaults() {
        let manager = NetworkManager::new();
        
        // Test that default dialer and listener are available
        let _default_dialer = manager.default_dialer();
        let _default_listener = manager.default_listener();
    }

    #[tokio::test]
    async fn test_network_manager_lifecycle() {
        let mut manager = NetworkManager::new();

        assert!(manager.start(StartStage::Initialize).await.is_ok());
        assert!(manager.start(StartStage::Start).await.is_ok());
        assert!(manager.close().await.is_ok());
        
        // After close, registered components should be cleared
        assert!(manager.dialers.read().await.is_empty());
        assert!(manager.listeners.read().await.is_empty());
    }
}
