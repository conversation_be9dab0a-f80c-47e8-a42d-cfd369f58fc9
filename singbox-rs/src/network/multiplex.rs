//! Connection multiplexing and pooling implementation
//!
//! This module provides advanced connection management including connection
//! pooling, multiplexing, and load balancing for improved performance.

use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{Rw<PERSON><PERSON>, <PERSON>maph<PERSON>, Mutex};
use serde::{Deserialize, Serialize};

use crate::network::Connection;

/// Connection pool configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionPoolConfig {
    /// Maximum connections per target
    pub max_connections: usize,
    
    /// Minimum idle connections
    pub min_idle: usize,
    
    /// Maximum idle connections
    pub max_idle: usize,
    
    /// Connection timeout
    pub connect_timeout: Duration,
    
    /// Idle timeout
    pub idle_timeout: Duration,
    
    /// Keep-alive interval
    pub keep_alive: Option<Duration>,
    
    /// Enable connection multiplexing
    pub enable_multiplex: bool,
    
    /// Maximum streams per connection (for multiplexing)
    pub max_streams: usize,
    
    /// Connection retry attempts
    pub retry_attempts: usize,
    
    /// Retry delay
    pub retry_delay: Duration,
}

/// Connection pool statistics
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct PoolStats {
    /// Total connections created
    pub total_created: u64,
    
    /// Total connections closed
    pub total_closed: u64,
    
    /// Current active connections
    pub active_connections: u64,
    
    /// Current idle connections
    pub idle_connections: u64,
    
    /// Pool hits (reused connections)
    pub pool_hits: u64,
    
    /// Pool misses (new connections)
    pub pool_misses: u64,
    
    /// Connection errors
    pub connection_errors: u64,
    
    /// Average connection time
    pub avg_connect_time: Duration,
}

/// Pooled connection wrapper
#[derive(Debug)]
pub struct PooledConnection {
    /// Underlying connection
    pub connection: Arc<Connection>,
    
    /// Connection ID
    pub id: String,
    
    /// Target address
    pub target: SocketAddr,
    
    /// Creation time
    pub created_at: Instant,
    
    /// Last used time
    pub last_used: Instant,
    
    /// Usage count
    pub usage_count: u64,
    
    /// Is multiplexed
    pub is_multiplexed: bool,
    
    /// Active streams (for multiplexed connections)
    pub active_streams: u32,
    
    /// Maximum streams allowed
    pub max_streams: u32,
}

impl PooledConnection {
    /// Create a new pooled connection
    pub fn new(
        connection: Arc<Connection>,
        target: SocketAddr,
        max_streams: u32,
        is_multiplexed: bool,
    ) -> Self {
        let now = Instant::now();
        Self {
            connection,
            id: uuid::Uuid::new_v4().to_string(),
            target,
            created_at: now,
            last_used: now,
            usage_count: 0,
            is_multiplexed,
            active_streams: 0,
            max_streams,
        }
    }
    
    /// Check if connection is idle
    pub fn is_idle(&self, idle_timeout: Duration) -> bool {
        self.last_used.elapsed() > idle_timeout
    }
    
    /// Check if connection can accept more streams
    pub fn can_accept_stream(&self) -> bool {
        if self.is_multiplexed {
            self.active_streams < self.max_streams
        } else {
            self.active_streams == 0
        }
    }
    
    /// Acquire a stream
    pub fn acquire_stream(&mut self) -> Result<(), String> {
        if !self.can_accept_stream() {
            return Err("Connection at maximum capacity".to_string());
        }
        
        self.active_streams += 1;
        self.last_used = Instant::now();
        self.usage_count += 1;
        
        Ok(())
    }
    
    /// Release a stream
    pub fn release_stream(&mut self) {
        if self.active_streams > 0 {
            self.active_streams -= 1;
        }
        self.last_used = Instant::now();
    }
    
    /// Check if connection is healthy
    pub async fn is_healthy(&self) -> bool {
        // In a real implementation, would perform health check
        // For now, just check if connection is not too old
        self.created_at.elapsed() < Duration::from_secs(3600)
    }
}

/// Connection pool manager
pub struct ConnectionPool {
    /// Configuration
    config: ConnectionPoolConfig,
    
    /// Connection pools by target
    pools: Arc<RwLock<HashMap<SocketAddr, Vec<PooledConnection>>>>,
    
    /// Connection semaphores by target
    semaphores: Arc<RwLock<HashMap<SocketAddr, Arc<Semaphore>>>>,
    
    /// Pool statistics
    stats: Arc<RwLock<PoolStats>>,
    
    /// Cleanup task handle
    cleanup_handle: Option<tokio::task::JoinHandle<()>>,
}

impl ConnectionPool {
    /// Create a new connection pool
    pub fn new(config: ConnectionPoolConfig) -> Self {
        Self {
            config,
            pools: Arc::new(RwLock::new(HashMap::new())),
            semaphores: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(PoolStats {
                total_created: 0,
                total_closed: 0,
                active_connections: 0,
                idle_connections: 0,
                pool_hits: 0,
                pool_misses: 0,
                connection_errors: 0,
                avg_connect_time: Duration::ZERO,
            })),
            cleanup_handle: None,
        }
    }
    
    /// Start the connection pool
    pub async fn start(&mut self) {
        self.start_cleanup_task().await;
        println!("Connection pool started");
    }
    
    /// Stop the connection pool
    pub async fn stop(&mut self) {
        if let Some(handle) = self.cleanup_handle.take() {
            handle.abort();
        }
        
        // Close all connections
        let mut pools = self.pools.write().await;
        for (_, pool) in pools.iter_mut() {
            for conn in pool.drain(..) {
                // Connection will be dropped and closed
                drop(conn);
            }
        }
        pools.clear();
        
        println!("Connection pool stopped");
    }
    
    /// Get a connection from the pool
    pub async fn get_connection(&self, target: SocketAddr) -> Result<PooledConnection, String> {
        // Try to get from pool first
        if let Some(conn) = self.try_get_from_pool(target).await {
            let mut stats = self.stats.write().await;
            stats.pool_hits += 1;
            return Ok(conn);
        }
        
        // Create new connection
        self.create_new_connection(target).await
    }
    
    /// Return a connection to the pool
    pub async fn return_connection(&self, mut conn: PooledConnection) {
        // Release the stream
        conn.release_stream();
        
        // If connection still has active streams or is not reusable, close it
        if conn.active_streams > 0 || !conn.is_multiplexed {
            self.close_connection(conn).await;
            return;
        }
        
        // Check if connection is still healthy
        if !conn.is_healthy().await {
            self.close_connection(conn).await;
            return;
        }
        
        // Return to pool
        let mut pools = self.pools.write().await;
        let pool = pools.entry(conn.target).or_insert_with(Vec::new);
        
        // Check pool size limits
        if pool.len() >= self.config.max_idle {
            // Pool is full, close the connection
            drop(conn);
            let mut stats = self.stats.write().await;
            stats.total_closed += 1;
            stats.active_connections -= 1;
        } else {
            // Add to pool
            pool.push(conn);
            let mut stats = self.stats.write().await;
            stats.idle_connections += 1;
            stats.active_connections -= 1;
        }
    }
    
    /// Try to get connection from existing pool
    async fn try_get_from_pool(&self, target: SocketAddr) -> Option<PooledConnection> {
        let mut pools = self.pools.write().await;
        let pool = pools.get_mut(&target)?;
        
        // Find a suitable connection
        let mut conn_index = None;
        for (i, conn) in pool.iter().enumerate() {
            if conn.can_accept_stream() && !conn.is_idle(self.config.idle_timeout) {
                conn_index = Some(i);
                break;
            }
        }
        
        if let Some(index) = conn_index {
            let mut conn = pool.remove(index);
            if conn.acquire_stream().is_ok() {
                let mut stats = self.stats.write().await;
                stats.idle_connections -= 1;
                stats.active_connections += 1;
                return Some(conn);
            }
        }
        
        None
    }
    
    /// Create a new connection
    async fn create_new_connection(&self, target: SocketAddr) -> Result<PooledConnection, String> {
        // Get or create semaphore for this target
        let semaphore = {
            let mut semaphores = self.semaphores.write().await;
            semaphores.entry(target)
                .or_insert_with(|| Arc::new(Semaphore::new(self.config.max_connections)))
                .clone()
        };
        
        // Acquire permit
        let _permit = semaphore.acquire().await
            .map_err(|_| "Failed to acquire connection permit")?;
        
        let start_time = Instant::now();
        
        // Create connection with timeout
        let connection_result = tokio::time::timeout(
            self.config.connect_timeout,
            self.create_connection(target)
        ).await;
        
        let connect_time = start_time.elapsed();
        
        match connection_result {
            Ok(Ok(connection)) => {
                let mut pooled_conn = PooledConnection::new(
                    connection,
                    target,
                    self.config.max_streams as u32,
                    self.config.enable_multiplex,
                );
                
                // Acquire initial stream
                pooled_conn.acquire_stream()
                    .map_err(|e| format!("Failed to acquire stream: {}", e))?;
                
                // Update statistics
                let mut stats = self.stats.write().await;
                stats.total_created += 1;
                stats.active_connections += 1;
                stats.pool_misses += 1;
                
                // Update average connect time
                let total_connects = stats.total_created;
                let current_avg = stats.avg_connect_time;
                stats.avg_connect_time = Duration::from_nanos(
                    (current_avg.as_nanos() as u64 * (total_connects - 1) + connect_time.as_nanos() as u64) / total_connects
                );
                
                Ok(pooled_conn)
            },
            Ok(Err(e)) => {
                let mut stats = self.stats.write().await;
                stats.connection_errors += 1;
                Err(format!("Connection failed: {}", e))
            },
            Err(_) => {
                let mut stats = self.stats.write().await;
                stats.connection_errors += 1;
                Err("Connection timeout".to_string())
            }
        }
    }
    
    /// Create actual connection (to be implemented by specific protocols)
    async fn create_connection(&self, _target: SocketAddr) -> Result<Arc<Connection>, String> {
        // This would be implemented by specific connection types
        // For now, return an error
        Err("Connection creation not implemented".to_string())
    }
    
    /// Close a connection
    async fn close_connection(&self, conn: PooledConnection) {
        drop(conn);
        let mut stats = self.stats.write().await;
        stats.total_closed += 1;
        if stats.active_connections > 0 {
            stats.active_connections -= 1;
        }
    }
    
    /// Start cleanup task
    async fn start_cleanup_task(&mut self) {
        let pools = Arc::clone(&self.pools);
        let stats = Arc::clone(&self.stats);
        let idle_timeout = self.config.idle_timeout;
        let min_idle = self.config.min_idle;
        
        let handle = tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(30));
            
            loop {
                interval.tick().await;
                
                let mut pools_guard = pools.write().await;
                let mut stats_guard = stats.write().await;
                
                for (_, pool) in pools_guard.iter_mut() {
                    let mut to_remove = Vec::new();
                    
                    // Find idle connections to remove
                    for (i, conn) in pool.iter().enumerate() {
                        if conn.is_idle(idle_timeout) && pool.len() > min_idle {
                            to_remove.push(i);
                        }
                    }
                    
                    // Remove idle connections (in reverse order to maintain indices)
                    for &index in to_remove.iter().rev() {
                        pool.remove(index);
                        stats_guard.total_closed += 1;
                        stats_guard.idle_connections -= 1;
                    }
                }
                
                println!("Connection pool cleanup completed");
            }
        });
        
        self.cleanup_handle = Some(handle);
    }
    
    /// Get pool statistics
    pub async fn get_stats(&self) -> PoolStats {
        self.stats.read().await.clone()
    }
    
    /// Get pool status for a specific target
    pub async fn get_target_status(&self, target: SocketAddr) -> Option<(usize, usize)> {
        let pools = self.pools.read().await;
        pools.get(&target).map(|pool| {
            let total = pool.len();
            let available = pool.iter().filter(|conn| conn.can_accept_stream()).count();
            (total, available)
        })
    }
}

impl Default for ConnectionPoolConfig {
    fn default() -> Self {
        Self {
            max_connections: 100,
            min_idle: 5,
            max_idle: 20,
            connect_timeout: Duration::from_secs(10),
            idle_timeout: Duration::from_secs(300), // 5 minutes
            keep_alive: Some(Duration::from_secs(60)),
            enable_multiplex: true,
            max_streams: 10,
            retry_attempts: 3,
            retry_delay: Duration::from_millis(100),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr};
    
    #[test]
    fn test_connection_pool_config_default() {
        let config = ConnectionPoolConfig::default();
        
        assert_eq!(config.max_connections, 100);
        assert_eq!(config.min_idle, 5);
        assert_eq!(config.max_idle, 20);
        assert!(config.enable_multiplex);
        assert_eq!(config.max_streams, 10);
    }
    
    #[tokio::test]
    async fn test_connection_pool_creation() {
        let config = ConnectionPoolConfig::default();
        let pool = ConnectionPool::new(config);
        
        let stats = pool.get_stats().await;
        assert_eq!(stats.total_created, 0);
        assert_eq!(stats.active_connections, 0);
    }
    
    #[test]
    fn test_pooled_connection_stream_management() {
        // Mock connection for testing
        let mock_connection = Connection {
            local_addr: "127.0.0.1:0".parse().unwrap(),
            remote_addr: "127.0.0.1:8080".parse().unwrap(),
            network: "tcp".to_string(),
            established_at: std::time::Instant::now(),
        };
        
        let target = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);
        let mut conn = PooledConnection::new(
            Arc::new(mock_connection),
            target,
            2,
            true,
        );
        
        // Test stream acquisition
        assert!(conn.can_accept_stream());
        assert!(conn.acquire_stream().is_ok());
        assert_eq!(conn.active_streams, 1);
        assert_eq!(conn.usage_count, 1);
        
        // Test second stream
        assert!(conn.can_accept_stream());
        assert!(conn.acquire_stream().is_ok());
        assert_eq!(conn.active_streams, 2);
        
        // Test maximum capacity
        assert!(!conn.can_accept_stream());
        assert!(conn.acquire_stream().is_err());
        
        // Test stream release
        conn.release_stream();
        assert_eq!(conn.active_streams, 1);
        assert!(conn.can_accept_stream());
    }
    
    #[test]
    fn test_pooled_connection_idle_check() {
        let mock_connection = Connection {
            local_addr: "127.0.0.1:0".parse().unwrap(),
            remote_addr: "127.0.0.1:8080".parse().unwrap(),
            network: "tcp".to_string(),
            established_at: std::time::Instant::now(),
        };
        
        let target = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);
        let conn = PooledConnection::new(
            Arc::new(mock_connection),
            target,
            1,
            false,
        );
        
        // Fresh connection should not be idle
        assert!(!conn.is_idle(Duration::from_secs(1)));
        
        // Connection should be idle after timeout
        std::thread::sleep(Duration::from_millis(10));
        assert!(conn.is_idle(Duration::from_millis(5)));
    }
}
