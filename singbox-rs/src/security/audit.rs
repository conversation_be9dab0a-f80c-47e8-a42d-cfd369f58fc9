//! Security auditing and logging system
//!
//! This module provides comprehensive security auditing including event logging,
//! compliance reporting, and security monitoring.

use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Arc;
use std::time::SystemTime;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

/// Audit configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuditConfig {
    /// Enable auditing
    pub enabled: bool,
    
    /// Audit log file path
    pub log_file: Option<String>,
    
    /// Audit levels to log
    pub log_levels: Vec<AuditLevel>,
    
    /// Maximum log file size (MB)
    pub max_file_size: Option<u64>,
    
    /// Log rotation count
    pub rotation_count: Option<u32>,
    
    /// Remote syslog configuration
    pub syslog: Option<SyslogConfig>,
    
    /// Audit filters
    pub filters: Vec<AuditFilter>,
}

/// Audit levels
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum AuditLevel {
    Debug,
    Info,
    Warning,
    Error,
    Critical,
    Security,
}

/// Syslog configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SyslogConfig {
    /// Syslog server address
    pub server: String,
    
    /// Syslog port
    pub port: u16,
    
    /// Syslog protocol (UDP/TCP)
    pub protocol: String,
    
    /// Facility
    pub facility: String,
}

/// Audit filter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditFilter {
    /// Filter name
    pub name: String,
    
    /// Filter conditions
    pub conditions: Vec<FilterCondition>,
    
    /// Filter action
    pub action: FilterAction,
}

/// Filter conditions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterCondition {
    Level(AuditLevel),
    Category(String),
    SourceIp(String),
    UserId(String),
    MessagePattern(String),
}

/// Filter actions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterAction {
    Include,
    Exclude,
    Highlight,
    Alert,
}

/// Audit event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditEvent {
    /// Event timestamp
    pub timestamp: SystemTime,
    
    /// Audit level
    pub level: AuditLevel,
    
    /// Event category
    pub category: String,
    
    /// Event message
    pub message: String,
    
    /// Source IP address
    pub source_ip: Option<IpAddr>,
    
    /// User ID
    pub user_id: Option<String>,
    
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

/// Security auditor
pub struct SecurityAuditor {
    /// Configuration
    config: AuditConfig,
    
    /// Audit log writer
    log_writer: Arc<RwLock<Option<std::fs::File>>>,
    
    /// Event buffer
    event_buffer: Arc<RwLock<Vec<AuditEvent>>>,
    
    /// Buffer flush task
    flush_task: std::sync::Mutex<Option<tokio::task::JoinHandle<()>>>,
}

impl SecurityAuditor {
    /// Create a new security auditor
    pub fn new(config: AuditConfig) -> Self {
        Self {
            config,
            log_writer: Arc::new(RwLock::new(None)),
            event_buffer: Arc::new(RwLock::new(Vec::new())),
            flush_task: std::sync::Mutex::new(None),
        }
    }
    
    /// Start the auditor
    pub async fn start(&self) -> Result<(), String> {
        if !self.config.enabled {
            return Ok(());
        }
        
        // Initialize log writer
        if let Some(ref log_file) = self.config.log_file {
            let file = std::fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(log_file)
                .map_err(|e| format!("Failed to open audit log file: {}", e))?;
            
            *self.log_writer.write().await = Some(file);
        }
        
        // Start flush task
        self.start_flush_task().await;
        
        println!("Security auditor started");
        Ok(())
    }
    
    /// Stop the auditor
    pub async fn stop(&self) {
        if let Some(ref task) = *self.flush_task.lock().unwrap() {
            task.abort();
        }
        
        // Flush remaining events
        self.flush_events().await;
        
        println!("Security auditor stopped");
    }
    
    /// Record audit event
    pub async fn record_event(&self, event: AuditEvent) {
        if !self.config.enabled {
            return;
        }
        
        // Check if event should be logged
        if !self.should_log_event(&event) {
            return;
        }
        
        // Add to buffer
        self.event_buffer.write().await.push(event);
    }
    
    /// Check if event should be logged
    fn should_log_event(&self, event: &AuditEvent) -> bool {
        // Check level filter
        if !self.config.log_levels.contains(&event.level) {
            return false;
        }
        
        // Apply filters
        for filter in &self.config.filters {
            if self.filter_matches(filter, event) {
                match filter.action {
                    FilterAction::Include => return true,
                    FilterAction::Exclude => return false,
                    FilterAction::Highlight | FilterAction::Alert => {
                        // Continue processing
                    }
                }
            }
        }
        
        true
    }
    
    /// Check if filter matches event
    fn filter_matches(&self, filter: &AuditFilter, event: &AuditEvent) -> bool {
        for condition in &filter.conditions {
            match condition {
                FilterCondition::Level(level) => {
                    if event.level != *level {
                        return false;
                    }
                },
                FilterCondition::Category(category) => {
                    if event.category != *category {
                        return false;
                    }
                },
                FilterCondition::SourceIp(ip_pattern) => {
                    if let Some(source_ip) = event.source_ip {
                        if !source_ip.to_string().contains(ip_pattern) {
                            return false;
                        }
                    } else {
                        return false;
                    }
                },
                FilterCondition::UserId(user_pattern) => {
                    if let Some(ref user_id) = event.user_id {
                        if !user_id.contains(user_pattern) {
                            return false;
                        }
                    } else {
                        return false;
                    }
                },
                FilterCondition::MessagePattern(pattern) => {
                    if !event.message.contains(pattern) {
                        return false;
                    }
                },
            }
        }
        true
    }
    
    /// Start flush task
    async fn start_flush_task(&self) {
        let event_buffer = Arc::clone(&self.event_buffer);
        let log_writer = Arc::clone(&self.log_writer);
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(5));
            
            loop {
                interval.tick().await;
                
                let events = {
                    let mut buffer = event_buffer.write().await;
                    let events = buffer.clone();
                    buffer.clear();
                    events
                };
                
                if !events.is_empty() {
                    Self::write_events(&log_writer, events).await;
                }
            }
        });
        
        *self.flush_task.lock().unwrap() = Some(task);
    }
    
    /// Write events to log
    async fn write_events(
        log_writer: &Arc<RwLock<Option<std::fs::File>>>,
        events: Vec<AuditEvent>,
    ) {
        use std::io::Write;
        
        let mut writer = log_writer.write().await;
        if let Some(ref mut file) = *writer {
            for event in events {
                let log_line = format!(
                    "{} [{}] {} - {} (source: {}, user: {})\n",
                    event.timestamp
                        .duration_since(SystemTime::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs(),
                    format!("{:?}", event.level).to_uppercase(),
                    event.category,
                    event.message,
                    event.source_ip.map(|ip| ip.to_string()).unwrap_or_else(|| "unknown".to_string()),
                    event.user_id.as_deref().unwrap_or("anonymous")
                );
                
                if let Err(e) = file.write_all(log_line.as_bytes()) {
                    eprintln!("Failed to write audit log: {}", e);
                }
            }
            
            if let Err(e) = file.flush() {
                eprintln!("Failed to flush audit log: {}", e);
            }
        }
    }
    
    /// Flush remaining events
    async fn flush_events(&self) {
        let events = {
            let mut buffer = self.event_buffer.write().await;
            let events = buffer.clone();
            buffer.clear();
            events
        };
        
        if !events.is_empty() {
            Self::write_events(&self.log_writer, events).await;
        }
    }
}

impl Default for AuditConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            log_file: None,
            log_levels: vec![
                AuditLevel::Warning,
                AuditLevel::Error,
                AuditLevel::Critical,
                AuditLevel::Security,
            ],
            max_file_size: Some(100), // 100 MB
            rotation_count: Some(5),
            syslog: None,
            filters: Vec::new(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_audit_config_default() {
        let config = AuditConfig::default();
        
        assert!(!config.enabled);
        assert!(config.log_file.is_none());
        assert_eq!(config.log_levels.len(), 4);
    }
    
    #[tokio::test]
    async fn test_security_auditor_creation() {
        let config = AuditConfig::default();
        let auditor = SecurityAuditor::new(config);
        
        let event = AuditEvent {
            timestamp: SystemTime::now(),
            level: AuditLevel::Security,
            category: "test".to_string(),
            message: "Test event".to_string(),
            source_ip: Some("127.0.0.1".parse().unwrap()),
            user_id: Some("test_user".to_string()),
            metadata: HashMap::new(),
        };
        
        auditor.record_event(event).await;
        // Should not panic when auditing is disabled
    }
    
    #[test]
    fn test_audit_event_creation() {
        let event = AuditEvent {
            timestamp: SystemTime::now(),
            level: AuditLevel::Error,
            category: "authentication".to_string(),
            message: "Login failed".to_string(),
            source_ip: Some("***********".parse().unwrap()),
            user_id: Some("user123".to_string()),
            metadata: HashMap::new(),
        };
        
        assert_eq!(event.level, AuditLevel::Error);
        assert_eq!(event.category, "authentication");
        assert!(event.source_ip.is_some());
    }
}
