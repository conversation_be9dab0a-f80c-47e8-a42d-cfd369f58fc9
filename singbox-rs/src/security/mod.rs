//! Security module for sing-box
//!
//! This module provides comprehensive security features including TLS certificate
//! management, access control, security auditing, and threat detection.

use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

pub mod tls;
pub mod access_control;
pub mod audit;
pub mod threat_detection;

pub use tls::{TlsManager, CertificateManager, CertificateInfo};
pub use access_control::{AccessController, AccessRule, AccessDecision};
pub use audit::{SecurityAuditor, AuditEvent, AuditLevel};
pub use threat_detection::{ThreatDetector, ThreatLevel, ThreatEvent};

/// Security configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// TLS configuration
    pub tls: Option<tls::TlsConfig>,
    
    /// Access control configuration
    pub access_control: Option<access_control::AccessControlConfig>,
    
    /// Audit configuration
    pub audit: Option<audit::AuditConfig>,
    
    /// Threat detection configuration
    pub threat_detection: Option<threat_detection::ThreatDetectionConfig>,
    
    /// Security policies
    pub policies: Vec<SecurityPolicy>,
    
    /// Rate limiting configuration
    pub rate_limiting: Option<RateLimitConfig>,
    
    /// IP filtering configuration
    pub ip_filtering: Option<IpFilterConfig>,
}

/// Security policy
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityPolicy {
    /// Policy name
    pub name: String,
    
    /// Policy description
    pub description: String,
    
    /// Policy rules
    pub rules: Vec<PolicyRule>,
    
    /// Policy enforcement level
    pub enforcement: PolicyEnforcement,
    
    /// Policy scope
    pub scope: PolicyScope,
}

/// Policy rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolicyRule {
    /// Rule name
    pub name: String,
    
    /// Rule condition
    pub condition: String,
    
    /// Rule action
    pub action: PolicyAction,
    
    /// Rule priority
    pub priority: u32,
}

/// Policy enforcement levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PolicyEnforcement {
    /// Log violations but don't block
    Monitor,
    
    /// Block violations
    Enforce,
    
    /// Block and alert
    EnforceAndAlert,
}

/// Policy scope
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PolicyScope {
    /// Apply to all connections
    Global,
    
    /// Apply to specific inbounds
    Inbound(Vec<String>),
    
    /// Apply to specific outbounds
    Outbound(Vec<String>),
    
    /// Apply to specific users
    User(Vec<String>),
    
    /// Apply to specific IP ranges
    IpRange(Vec<String>),
}

/// Policy actions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PolicyAction {
    /// Allow the action
    Allow,
    
    /// Deny the action
    Deny,
    
    /// Log the action
    Log,
    
    /// Rate limit the action
    RateLimit(RateLimitRule),
    
    /// Quarantine the source
    Quarantine(Duration),
    
    /// Custom action
    Custom(String),
}

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// Enable rate limiting
    pub enabled: bool,
    
    /// Default rate limit rules
    pub default_rules: Vec<RateLimitRule>,
    
    /// Per-IP rate limits
    pub per_ip_limits: HashMap<String, RateLimitRule>,
    
    /// Per-user rate limits
    pub per_user_limits: HashMap<String, RateLimitRule>,
    
    /// Burst allowance
    pub burst_allowance: u32,
    
    /// Rate limit window
    pub window: Duration,
}

/// Rate limit rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitRule {
    /// Maximum requests per window
    pub max_requests: u32,
    
    /// Time window
    pub window: Duration,
    
    /// Burst allowance
    pub burst: u32,
    
    /// Action when limit exceeded
    pub action: RateLimitAction,
}

/// Rate limit actions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RateLimitAction {
    /// Drop excess requests
    Drop,
    
    /// Delay excess requests
    Delay(Duration),
    
    /// Return error response
    Error(String),
    
    /// Temporary ban
    Ban(Duration),
}

/// IP filtering configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpFilterConfig {
    /// Enable IP filtering
    pub enabled: bool,
    
    /// Default action for unknown IPs
    pub default_action: IpFilterAction,
    
    /// Whitelist of allowed IPs
    pub whitelist: Vec<String>,
    
    /// Blacklist of blocked IPs
    pub blacklist: Vec<String>,
    
    /// GeoIP filtering rules
    pub geoip_rules: Vec<GeoIpFilterRule>,
    
    /// Dynamic IP filtering
    pub dynamic_filtering: Option<DynamicIpFilterConfig>,
}

/// IP filter actions
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum IpFilterAction {
    /// Allow the IP
    Allow,
    
    /// Block the IP
    Block,
    
    /// Monitor the IP
    Monitor,
}

/// GeoIP filter rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeoIpFilterRule {
    /// Country codes
    pub countries: Vec<String>,
    
    /// Action for these countries
    pub action: IpFilterAction,
    
    /// Rule priority
    pub priority: u32,
}

/// Dynamic IP filtering configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DynamicIpFilterConfig {
    /// Enable dynamic filtering
    pub enabled: bool,
    
    /// Failure threshold for auto-blocking
    pub failure_threshold: u32,
    
    /// Time window for failure counting
    pub failure_window: Duration,
    
    /// Auto-block duration
    pub block_duration: Duration,
    
    /// Whitelist protection
    pub whitelist_protection: bool,
}

/// Security manager
pub struct SecurityManager {
    /// Configuration
    config: SecurityConfig,
    
    /// TLS manager
    tls_manager: Option<Arc<TlsManager>>,
    
    /// Access controller
    access_controller: Option<Arc<AccessController>>,
    
    /// Security auditor
    auditor: Option<Arc<SecurityAuditor>>,
    
    /// Threat detector
    threat_detector: Option<Arc<ThreatDetector>>,
    
    /// Rate limiter state
    rate_limiter: Arc<RwLock<RateLimiter>>,
    
    /// IP filter state
    ip_filter: Arc<RwLock<IpFilter>>,
    
    /// Security policies
    policies: Arc<RwLock<Vec<SecurityPolicy>>>,
}

/// Rate limiter implementation
pub struct RateLimiter {
    /// Rate limit buckets by key
    buckets: HashMap<String, RateLimitBucket>,
    
    /// Configuration
    config: RateLimitConfig,
}

/// Rate limit bucket
#[derive(Debug, Clone)]
pub struct RateLimitBucket {
    /// Current token count
    tokens: u32,
    
    /// Last refill time
    last_refill: SystemTime,
    
    /// Bucket capacity
    capacity: u32,
    
    /// Refill rate (tokens per second)
    refill_rate: f64,
}

/// IP filter implementation
pub struct IpFilter {
    /// Whitelist IPs
    whitelist: Vec<IpAddr>,
    
    /// Blacklist IPs
    blacklist: Vec<IpAddr>,
    
    /// Dynamic blocks
    dynamic_blocks: HashMap<IpAddr, SystemTime>,
    
    /// Failure counts
    failure_counts: HashMap<IpAddr, (u32, SystemTime)>,
    
    /// Configuration
    config: IpFilterConfig,
}

impl SecurityManager {
    /// Create a new security manager
    pub fn new(config: SecurityConfig) -> Self {
        let tls_manager = config.tls.as_ref().map(|tls_config| {
            Arc::new(TlsManager::new(tls_config.clone()))
        });
        
        let access_controller = config.access_control.as_ref().map(|ac_config| {
            Arc::new(AccessController::new(ac_config.clone()))
        });
        
        let auditor = config.audit.as_ref().map(|audit_config| {
            Arc::new(SecurityAuditor::new(audit_config.clone()))
        });
        
        let threat_detector = config.threat_detection.as_ref().map(|td_config| {
            Arc::new(ThreatDetector::new(td_config.clone()))
        });
        
        let rate_limiter = Arc::new(RwLock::new(RateLimiter::new(
            config.rate_limiting.clone().unwrap_or_default()
        )));
        
        let ip_filter = Arc::new(RwLock::new(IpFilter::new(
            config.ip_filtering.clone().unwrap_or_default()
        )));
        
        let policies = Arc::new(RwLock::new(config.policies.clone()));
        
        Self {
            config,
            tls_manager,
            access_controller,
            auditor,
            threat_detector,
            rate_limiter,
            ip_filter,
            policies,
        }
    }
    
    /// Start the security manager
    pub async fn start(&mut self) -> Result<(), String> {
        // Start TLS manager
        if let Some(ref tls_manager) = self.tls_manager {
            tls_manager.start().await?;
        }
        
        // Start access controller
        if let Some(ref access_controller) = self.access_controller {
            access_controller.start().await?;
        }
        
        // Start auditor
        if let Some(ref auditor) = self.auditor {
            auditor.start().await?;
        }
        
        // Start threat detector
        if let Some(ref threat_detector) = self.threat_detector {
            threat_detector.start().await?;
        }
        
        println!("Security manager started");
        Ok(())
    }
    
    /// Stop the security manager
    pub async fn stop(&mut self) {
        // Stop components
        if let Some(ref tls_manager) = self.tls_manager {
            tls_manager.stop().await;
        }
        
        if let Some(ref access_controller) = self.access_controller {
            access_controller.stop().await;
        }
        
        if let Some(ref auditor) = self.auditor {
            auditor.stop().await;
        }
        
        if let Some(ref threat_detector) = self.threat_detector {
            threat_detector.stop().await;
        }
        
        println!("Security manager stopped");
    }
    
    /// Check if IP is allowed
    pub async fn check_ip_allowed(&self, ip: &IpAddr) -> bool {
        let ip_filter = self.ip_filter.read().await;
        ip_filter.is_allowed(ip)
    }
    
    /// Check rate limit for key
    pub async fn check_rate_limit(&self, key: &str) -> bool {
        let mut rate_limiter = self.rate_limiter.write().await;
        rate_limiter.check_limit(key)
    }
    
    /// Record security event
    pub async fn record_event(&self, event: SecurityEvent) {
        if let Some(ref auditor) = self.auditor {
            auditor.record_event(AuditEvent {
                timestamp: SystemTime::now(),
                level: AuditLevel::Security,
                category: "security".to_string(),
                message: format!("{:?}", event),
                source_ip: event.source_ip,
                user_id: event.user_id.clone(),
                metadata: event.metadata.clone(),
            }).await;
        }
        
        if let Some(ref threat_detector) = self.threat_detector {
            threat_detector.analyze_event(&event).await;
        }
    }
    
    /// Get TLS manager
    pub fn tls_manager(&self) -> Option<&Arc<TlsManager>> {
        self.tls_manager.as_ref()
    }
    
    /// Get access controller
    pub fn access_controller(&self) -> Option<&Arc<AccessController>> {
        self.access_controller.as_ref()
    }
    
    /// Get auditor
    pub fn auditor(&self) -> Option<&Arc<SecurityAuditor>> {
        self.auditor.as_ref()
    }
    
    /// Get threat detector
    pub fn threat_detector(&self) -> Option<&Arc<ThreatDetector>> {
        self.threat_detector.as_ref()
    }
}

/// Security event
#[derive(Debug, Clone)]
pub struct SecurityEvent {
    /// Event type
    pub event_type: SecurityEventType,
    
    /// Source IP address
    pub source_ip: Option<IpAddr>,
    
    /// User ID
    pub user_id: Option<String>,
    
    /// Event description
    pub description: String,
    
    /// Event metadata
    pub metadata: HashMap<String, String>,
    
    /// Timestamp
    pub timestamp: SystemTime,
}

/// Security event types
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum SecurityEventType {
    /// Authentication attempt
    AuthenticationAttempt,
    
    /// Authentication failure
    AuthenticationFailure,
    
    /// Authorization failure
    AuthorizationFailure,
    
    /// Suspicious activity
    SuspiciousActivity,
    
    /// Rate limit exceeded
    RateLimitExceeded,
    
    /// IP blocked
    IpBlocked,
    
    /// TLS handshake failure
    TlsHandshakeFailure,
    
    /// Certificate validation failure
    CertificateValidationFailure,
    
    /// Policy violation
    PolicyViolation,
    
    /// Threat detected
    ThreatDetected,
}

impl RateLimiter {
    /// Create a new rate limiter
    pub fn new(config: RateLimitConfig) -> Self {
        Self {
            buckets: HashMap::new(),
            config,
        }
    }
    
    /// Check if request is within rate limit
    pub fn check_limit(&mut self, key: &str) -> bool {
        let now = SystemTime::now();
        
        // Get or create bucket
        let bucket = self.buckets.entry(key.to_string()).or_insert_with(|| {
            RateLimitBucket {
                tokens: self.config.burst_allowance,
                last_refill: now,
                capacity: self.config.burst_allowance,
                refill_rate: 1.0, // Default rate
            }
        });
        
        // Refill tokens
        let elapsed = now.duration_since(bucket.last_refill).unwrap_or_default();
        let tokens_to_add = (elapsed.as_secs_f64() * bucket.refill_rate) as u32;
        bucket.tokens = (bucket.tokens + tokens_to_add).min(bucket.capacity);
        bucket.last_refill = now;
        
        // Check if request can be allowed
        if bucket.tokens > 0 {
            bucket.tokens -= 1;
            true
        } else {
            false
        }
    }
}

impl IpFilter {
    /// Create a new IP filter
    pub fn new(config: IpFilterConfig) -> Self {
        let whitelist = config.whitelist.iter()
            .filter_map(|ip_str| ip_str.parse().ok())
            .collect();
        
        let blacklist = config.blacklist.iter()
            .filter_map(|ip_str| ip_str.parse().ok())
            .collect();
        
        Self {
            whitelist,
            blacklist,
            dynamic_blocks: HashMap::new(),
            failure_counts: HashMap::new(),
            config,
        }
    }
    
    /// Check if IP is allowed
    pub fn is_allowed(&self, ip: &IpAddr) -> bool {
        if !self.config.enabled {
            return true;
        }
        
        // Check whitelist first
        if self.whitelist.contains(ip) {
            return true;
        }
        
        // Check blacklist
        if self.blacklist.contains(ip) {
            return false;
        }
        
        // Check dynamic blocks
        if let Some(block_time) = self.dynamic_blocks.get(ip) {
            if let Some(dynamic_config) = &self.config.dynamic_filtering {
                if block_time.elapsed().unwrap_or_default() < dynamic_config.block_duration {
                    return false;
                }
            }
        }
        
        // Default action
        match self.config.default_action {
            IpFilterAction::Allow => true,
            IpFilterAction::Block => false,
            IpFilterAction::Monitor => true,
        }
    }
    
    /// Record failure for IP
    pub fn record_failure(&mut self, ip: &IpAddr) {
        if let Some(dynamic_config) = &self.config.dynamic_filtering {
            if !dynamic_config.enabled {
                return;
            }
            
            // Skip if IP is whitelisted and whitelist protection is enabled
            if dynamic_config.whitelist_protection && self.whitelist.contains(ip) {
                return;
            }
            
            let now = SystemTime::now();
            let default_info = (0, now);
            let failure_info = self.failure_counts.get(ip).unwrap_or(&default_info);
            let (count, last_failure) = *failure_info;
            
            // Reset count if outside window
            let count = if now.duration_since(last_failure).unwrap_or_default() > dynamic_config.failure_window {
                1
            } else {
                count + 1
            };
            
            self.failure_counts.insert(*ip, (count, now));
            
            // Check if threshold exceeded
            if count >= dynamic_config.failure_threshold {
                self.dynamic_blocks.insert(*ip, now);
                println!("IP {} dynamically blocked due to {} failures", ip, count);
            }
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            tls: None,
            access_control: None,
            audit: None,
            threat_detection: None,
            policies: Vec::new(),
            rate_limiting: None,
            ip_filtering: None,
        }
    }
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            default_rules: Vec::new(),
            per_ip_limits: HashMap::new(),
            per_user_limits: HashMap::new(),
            burst_allowance: 10,
            window: Duration::from_secs(60),
        }
    }
}

impl Default for IpFilterConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            default_action: IpFilterAction::Allow,
            whitelist: Vec::new(),
            blacklist: Vec::new(),
            geoip_rules: Vec::new(),
            dynamic_filtering: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_security_config_default() {
        let config = SecurityConfig::default();
        assert!(config.tls.is_none());
        assert!(config.access_control.is_none());
        assert!(config.policies.is_empty());
    }
    
    #[test]
    fn test_rate_limiter_creation() {
        let config = RateLimitConfig::default();
        let mut limiter = RateLimiter::new(config);
        
        // Should allow first request
        assert!(limiter.check_limit("test_key"));
    }
    
    #[test]
    fn test_ip_filter_creation() {
        let config = IpFilterConfig {
            enabled: true,
            default_action: IpFilterAction::Allow,
            whitelist: vec!["127.0.0.1".to_string()],
            blacklist: vec!["***********00".to_string()],
            geoip_rules: Vec::new(),
            dynamic_filtering: None,
        };
        
        let filter = IpFilter::new(config);
        
        let localhost: IpAddr = "127.0.0.1".parse().unwrap();
        let blocked_ip: IpAddr = "***********00".parse().unwrap();
        let unknown_ip: IpAddr = "*******".parse().unwrap();
        
        assert!(filter.is_allowed(&localhost));
        assert!(!filter.is_allowed(&blocked_ip));
        assert!(filter.is_allowed(&unknown_ip)); // Default allow
    }
    
    #[test]
    fn test_security_event_creation() {
        let event = SecurityEvent {
            event_type: SecurityEventType::AuthenticationFailure,
            source_ip: Some("***********".parse().unwrap()),
            user_id: Some("test_user".to_string()),
            description: "Invalid password".to_string(),
            metadata: HashMap::new(),
            timestamp: SystemTime::now(),
        };
        
        assert_eq!(event.event_type, SecurityEventType::AuthenticationFailure);
        assert!(event.source_ip.is_some());
    }
}
