//! Access control and authorization system
//!
//! This module provides comprehensive access control including role-based
//! access control (RBAC), attribute-based access control (ABAC), and
//! fine-grained permission management.

use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Arc;
use std::time::SystemTime;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{Datelike, Timelike};

/// Access control configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessControlConfig {
    /// Enable access control
    pub enabled: bool,
    
    /// Default access policy
    pub default_policy: AccessPolicy,
    
    /// Access rules
    pub rules: Vec<AccessRule>,
    
    /// Role definitions
    pub roles: Vec<Role>,
    
    /// User assignments
    pub user_assignments: Vec<UserAssignment>,
    
    /// Resource definitions
    pub resources: Vec<Resource>,
    
    /// Permission definitions
    pub permissions: Vec<Permission>,
}

/// Access policies
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, Partial<PERSON><PERSON>, Eq, Serialize, Deserialize)]
pub enum AccessPolicy {
    /// Allow by default, deny explicitly
    AllowByDefault,
    
    /// Deny by default, allow explicitly
    DenyByDefault,
    
    /// Require explicit permission
    ExplicitOnly,
}

/// Access rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessRule {
    /// Rule name
    pub name: String,
    
    /// Rule priority (higher = more important)
    pub priority: u32,
    
    /// Rule conditions
    pub conditions: Vec<AccessCondition>,
    
    /// Rule action
    pub action: AccessAction,
    
    /// Rule effect
    pub effect: AccessEffect,
    
    /// Rule description
    pub description: Option<String>,
}

/// Access conditions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AccessCondition {
    /// User ID condition
    UserId(String),
    
    /// User role condition
    UserRole(String),
    
    /// Source IP condition
    SourceIp(String),
    
    /// Destination condition
    Destination(String),
    
    /// Time-based condition
    TimeRange {
        start: String,
        end: String,
        days: Vec<u8>,
    },
    
    /// Resource condition
    Resource(String),
    
    /// Permission condition
    Permission(String),
    
    /// Custom condition
    Custom {
        name: String,
        parameters: HashMap<String, String>,
    },
}

/// Access actions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AccessAction {
    /// Allow access
    Allow,
    
    /// Deny access
    Deny,
    
    /// Require additional authentication
    RequireAuth,
    
    /// Apply rate limiting
    RateLimit(u32),
    
    /// Log and allow
    LogAndAllow,
    
    /// Log and deny
    LogAndDeny,
}

/// Access effects
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AccessEffect {
    /// Allow effect
    Allow,
    
    /// Deny effect
    Deny,
}

/// Role definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Role {
    /// Role name
    pub name: String,
    
    /// Role description
    pub description: String,
    
    /// Role permissions
    pub permissions: Vec<String>,
    
    /// Parent roles (inheritance)
    pub parent_roles: Vec<String>,
    
    /// Role attributes
    pub attributes: HashMap<String, String>,
}

/// User assignment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserAssignment {
    /// User ID
    pub user_id: String,
    
    /// Assigned roles
    pub roles: Vec<String>,
    
    /// Direct permissions
    pub permissions: Vec<String>,
    
    /// User attributes
    pub attributes: HashMap<String, String>,
}

/// Resource definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Resource {
    /// Resource name
    pub name: String,
    
    /// Resource type
    pub resource_type: String,
    
    /// Resource attributes
    pub attributes: HashMap<String, String>,
    
    /// Parent resource
    pub parent: Option<String>,
}

/// Permission definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Permission {
    /// Permission name
    pub name: String,
    
    /// Permission description
    pub description: String,
    
    /// Permission scope
    pub scope: PermissionScope,
    
    /// Permission attributes
    pub attributes: HashMap<String, String>,
}

/// Permission scopes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PermissionScope {
    /// Global permission
    Global,
    
    /// Resource-specific permission
    Resource(String),
    
    /// Role-specific permission
    Role(String),
    
    /// Custom scope
    Custom(String),
}

/// Access decision
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum AccessDecision {
    /// Access allowed
    Allow,
    
    /// Access denied
    Deny,
    
    /// Access requires additional authentication
    RequireAuth,
    
    /// Access decision deferred
    Deferred,
}

/// Access request
#[derive(Debug, Clone)]
pub struct AccessRequest {
    /// User ID
    pub user_id: Option<String>,
    
    /// Source IP
    pub source_ip: Option<IpAddr>,
    
    /// Requested resource
    pub resource: String,
    
    /// Requested action
    pub action: String,
    
    /// Request context
    pub context: HashMap<String, String>,
    
    /// Request timestamp
    pub timestamp: SystemTime,
}

/// Access controller
pub struct AccessController {
    /// Configuration
    config: AccessControlConfig,
    
    /// Compiled rules
    rules: Arc<RwLock<Vec<CompiledRule>>>,
    
    /// Role hierarchy
    role_hierarchy: Arc<RwLock<HashMap<String, Vec<String>>>>,
    
    /// User permissions cache
    user_permissions: Arc<RwLock<HashMap<String, Vec<String>>>>,
    
    /// Access decision cache
    decision_cache: Arc<RwLock<HashMap<String, (AccessDecision, SystemTime)>>>,
    
    /// Cache TTL
    cache_ttl: std::time::Duration,
}

/// Compiled access rule
#[derive(Debug, Clone)]
pub struct CompiledRule {
    /// Original rule
    pub rule: AccessRule,
    
    /// Compiled conditions
    pub compiled_conditions: Vec<CompiledCondition>,
}

/// Compiled condition
#[derive(Debug, Clone)]
pub enum CompiledCondition {
    /// User ID matcher
    UserId(String),
    
    /// Role matcher
    Role(String),
    
    /// IP range matcher
    IpRange(ipnet::IpNet),
    
    /// Time range matcher
    TimeRange {
        start_hour: u8,
        start_minute: u8,
        end_hour: u8,
        end_minute: u8,
        days: Vec<u8>,
    },
    
    /// Resource pattern matcher
    ResourcePattern(regex::Regex),
    
    /// Permission matcher
    Permission(String),
    
    /// Custom matcher
    Custom(String),
}

impl AccessController {
    /// Create a new access controller
    pub fn new(config: AccessControlConfig) -> Self {
        Self {
            config,
            rules: Arc::new(RwLock::new(Vec::new())),
            role_hierarchy: Arc::new(RwLock::new(HashMap::new())),
            user_permissions: Arc::new(RwLock::new(HashMap::new())),
            decision_cache: Arc::new(RwLock::new(HashMap::new())),
            cache_ttl: std::time::Duration::from_secs(300), // 5 minutes
        }
    }
    
    /// Start the access controller
    pub async fn start(&self) -> Result<(), String> {
        // Compile rules
        self.compile_rules().await?;
        
        // Build role hierarchy
        self.build_role_hierarchy().await?;
        
        // Cache user permissions
        self.cache_user_permissions().await?;
        
        println!("Access controller started");
        Ok(())
    }
    
    /// Stop the access controller
    pub async fn stop(&self) {
        // Clear caches
        self.decision_cache.write().await.clear();
        self.user_permissions.write().await.clear();
        
        println!("Access controller stopped");
    }
    
    /// Check access for a request
    pub async fn check_access(&self, request: &AccessRequest) -> AccessDecision {
        if !self.config.enabled {
            return AccessDecision::Allow;
        }
        
        // Check cache first
        let cache_key = self.generate_cache_key(request);
        if let Some((decision, timestamp)) = self.decision_cache.read().await.get(&cache_key) {
            if timestamp.elapsed().unwrap_or_default() < self.cache_ttl {
                return decision.clone();
            }
        }
        
        // Evaluate rules
        let decision = self.evaluate_rules(request).await;
        
        // Cache decision
        self.decision_cache.write().await.insert(
            cache_key,
            (decision.clone(), SystemTime::now()),
        );
        
        decision
    }
    
    /// Compile access rules
    async fn compile_rules(&self) -> Result<(), String> {
        let mut compiled_rules = Vec::new();
        
        for rule in &self.config.rules {
            let mut compiled_conditions = Vec::new();
            
            for condition in &rule.conditions {
                let compiled = self.compile_condition(condition)?;
                compiled_conditions.push(compiled);
            }
            
            compiled_rules.push(CompiledRule {
                rule: rule.clone(),
                compiled_conditions,
            });
        }
        
        // Sort by priority (higher first)
        compiled_rules.sort_by(|a, b| b.rule.priority.cmp(&a.rule.priority));
        
        *self.rules.write().await = compiled_rules;
        Ok(())
    }
    
    /// Compile a single condition
    fn compile_condition(&self, condition: &AccessCondition) -> Result<CompiledCondition, String> {
        match condition {
            AccessCondition::UserId(user_id) => {
                Ok(CompiledCondition::UserId(user_id.clone()))
            },
            AccessCondition::UserRole(role) => {
                Ok(CompiledCondition::Role(role.clone()))
            },
            AccessCondition::SourceIp(ip_str) => {
                let ip_net = ip_str.parse::<ipnet::IpNet>()
                    .map_err(|e| format!("Invalid IP range '{}': {}", ip_str, e))?;
                Ok(CompiledCondition::IpRange(ip_net))
            },
            AccessCondition::TimeRange { start, end, days } => {
                let (start_hour, start_minute) = self.parse_time(start)?;
                let (end_hour, end_minute) = self.parse_time(end)?;
                
                Ok(CompiledCondition::TimeRange {
                    start_hour,
                    start_minute,
                    end_hour,
                    end_minute,
                    days: days.clone(),
                })
            },
            AccessCondition::Resource(pattern) => {
                let regex = regex::Regex::new(pattern)
                    .map_err(|e| format!("Invalid resource pattern '{}': {}", pattern, e))?;
                Ok(CompiledCondition::ResourcePattern(regex))
            },
            AccessCondition::Permission(permission) => {
                Ok(CompiledCondition::Permission(permission.clone()))
            },
            AccessCondition::Custom { name, .. } => {
                Ok(CompiledCondition::Custom(name.clone()))
            },
            _ => Err("Unsupported condition type".to_string()),
        }
    }
    
    /// Parse time string (HH:MM)
    fn parse_time(&self, time_str: &str) -> Result<(u8, u8), String> {
        let parts: Vec<&str> = time_str.split(':').collect();
        if parts.len() != 2 {
            return Err(format!("Invalid time format: {}", time_str));
        }
        
        let hour = parts[0].parse::<u8>()
            .map_err(|_| format!("Invalid hour: {}", parts[0]))?;
        let minute = parts[1].parse::<u8>()
            .map_err(|_| format!("Invalid minute: {}", parts[1]))?;
        
        if hour > 23 || minute > 59 {
            return Err(format!("Invalid time: {}", time_str));
        }
        
        Ok((hour, minute))
    }
    
    /// Build role hierarchy
    async fn build_role_hierarchy(&self) -> Result<(), String> {
        let mut hierarchy = HashMap::new();
        
        for role in &self.config.roles {
            let mut all_permissions = role.permissions.clone();
            
            // Add permissions from parent roles
            for parent_role in &role.parent_roles {
                if let Some(parent) = self.config.roles.iter().find(|r| r.name == *parent_role) {
                    all_permissions.extend(parent.permissions.clone());
                }
            }
            
            hierarchy.insert(role.name.clone(), all_permissions);
        }
        
        *self.role_hierarchy.write().await = hierarchy;
        Ok(())
    }
    
    /// Cache user permissions
    async fn cache_user_permissions(&self) -> Result<(), String> {
        let mut user_perms = HashMap::new();
        let role_hierarchy = self.role_hierarchy.read().await;
        
        for assignment in &self.config.user_assignments {
            let mut permissions = assignment.permissions.clone();
            
            // Add permissions from roles
            for role_name in &assignment.roles {
                if let Some(role_permissions) = role_hierarchy.get(role_name) {
                    permissions.extend(role_permissions.clone());
                }
            }
            
            // Remove duplicates
            permissions.sort();
            permissions.dedup();
            
            user_perms.insert(assignment.user_id.clone(), permissions);
        }
        
        *self.user_permissions.write().await = user_perms;
        Ok(())
    }
    
    /// Evaluate rules for a request
    async fn evaluate_rules(&self, request: &AccessRequest) -> AccessDecision {
        let rules = self.rules.read().await;
        
        for compiled_rule in rules.iter() {
            if self.rule_matches(compiled_rule, request).await {
                return match compiled_rule.rule.effect {
                    AccessEffect::Allow => AccessDecision::Allow,
                    AccessEffect::Deny => AccessDecision::Deny,
                };
            }
        }
        
        // Apply default policy
        match self.config.default_policy {
            AccessPolicy::AllowByDefault => AccessDecision::Allow,
            AccessPolicy::DenyByDefault => AccessDecision::Deny,
            AccessPolicy::ExplicitOnly => AccessDecision::Deny,
        }
    }
    
    /// Check if rule matches request
    async fn rule_matches(&self, rule: &CompiledRule, request: &AccessRequest) -> bool {
        for condition in &rule.compiled_conditions {
            if !self.condition_matches(condition, request).await {
                return false;
            }
        }
        true
    }
    
    /// Check if condition matches request
    async fn condition_matches(&self, condition: &CompiledCondition, request: &AccessRequest) -> bool {
        match condition {
            CompiledCondition::UserId(user_id) => {
                request.user_id.as_ref() == Some(user_id)
            },
            CompiledCondition::Role(role) => {
                if let Some(ref user_id) = request.user_id {
                    if let Some(assignment) = self.config.user_assignments.iter()
                        .find(|a| a.user_id == *user_id) {
                        return assignment.roles.contains(role);
                    }
                }
                false
            },
            CompiledCondition::IpRange(ip_net) => {
                if let Some(source_ip) = request.source_ip {
                    ip_net.contains(&source_ip)
                } else {
                    false
                }
            },
            CompiledCondition::TimeRange { start_hour, start_minute, end_hour, end_minute, days } => {
                let now = chrono::Local::now();
                let current_day = now.weekday().num_days_from_sunday() as u8;
                let current_hour = now.hour() as u8;
                let current_minute = now.minute() as u8;
                
                // Check day
                if !days.contains(&current_day) {
                    return false;
                }
                
                // Check time
                let current_time = current_hour * 60 + current_minute;
                let start_time = start_hour * 60 + start_minute;
                let end_time = end_hour * 60 + end_minute;
                
                if start_time <= end_time {
                    current_time >= start_time && current_time <= end_time
                } else {
                    // Crosses midnight
                    current_time >= start_time || current_time <= end_time
                }
            },
            CompiledCondition::ResourcePattern(regex) => {
                regex.is_match(&request.resource)
            },
            CompiledCondition::Permission(permission) => {
                if let Some(ref user_id) = request.user_id {
                    if let Some(user_permissions) = self.user_permissions.read().await.get(user_id) {
                        return user_permissions.contains(permission);
                    }
                }
                false
            },
            CompiledCondition::Custom(_) => {
                // Custom condition evaluation would be implemented here
                false
            },
        }
    }
    
    /// Generate cache key for request
    fn generate_cache_key(&self, request: &AccessRequest) -> String {
        format!(
            "{}:{}:{}:{}",
            request.user_id.as_deref().unwrap_or("anonymous"),
            request.source_ip.map(|ip| ip.to_string()).unwrap_or_else(|| "unknown".to_string()),
            request.resource,
            request.action
        )
    }
}

impl Default for AccessControlConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            default_policy: AccessPolicy::AllowByDefault,
            rules: Vec::new(),
            roles: Vec::new(),
            user_assignments: Vec::new(),
            resources: Vec::new(),
            permissions: Vec::new(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_access_control_config_default() {
        let config = AccessControlConfig::default();
        
        assert!(!config.enabled);
        assert_eq!(config.default_policy, AccessPolicy::AllowByDefault);
        assert!(config.rules.is_empty());
    }
    
    #[tokio::test]
    async fn test_access_controller_creation() {
        let config = AccessControlConfig::default();
        let controller = AccessController::new(config);
        
        let request = AccessRequest {
            user_id: Some("test_user".to_string()),
            source_ip: Some("127.0.0.1".parse().unwrap()),
            resource: "test_resource".to_string(),
            action: "read".to_string(),
            context: HashMap::new(),
            timestamp: SystemTime::now(),
        };
        
        let decision = controller.check_access(&request).await;
        assert_eq!(decision, AccessDecision::Allow); // Default allow when disabled
    }
    
    #[test]
    fn test_access_rule_creation() {
        let rule = AccessRule {
            name: "test_rule".to_string(),
            priority: 100,
            conditions: vec![
                AccessCondition::UserId("admin".to_string()),
                AccessCondition::Resource("admin/*".to_string()),
            ],
            action: AccessAction::Allow,
            effect: AccessEffect::Allow,
            description: Some("Allow admin access".to_string()),
        };
        
        assert_eq!(rule.name, "test_rule");
        assert_eq!(rule.priority, 100);
        assert_eq!(rule.conditions.len(), 2);
    }
}
