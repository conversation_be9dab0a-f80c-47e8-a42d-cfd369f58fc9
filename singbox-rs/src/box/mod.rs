//! Box core implementation
//!
//! This module provides the core Box implementation that manages the entire
//! sing-box service lifecycle, including inbounds, outbounds, routing, and services.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

use async_trait::async_trait;
use crate::adapter::{Adapter, Inbound, Outbound, Lifecycle, StartStage};
use crate::common::interrupt::Context;
use crate::config::Config;
use crate::route::Router;
use crate::dns::Client as DNSClient;
use crate::stats::StatsManager;

pub mod lifecycle;
pub mod service;
pub mod context;

/// Box error types
#[derive(Debug, Clone)]
pub enum BoxError {
    ConfigurationError(String),
    InitializationError(String),
    StartupError(String),
    ShutdownError(String),
    ServiceError(String),
}

impl std::fmt::Display for BoxError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            BoxError::ConfigurationError(msg) => write!(f, "Configuration error: {}", msg),
            BoxError::InitializationError(msg) => write!(f, "Initialization error: {}", msg),
            BoxError::StartupError(msg) => write!(f, "Startup error: {}", msg),
            BoxError::ShutdownError(msg) => write!(f, "Shutdown error: {}", msg),
            BoxError::ServiceError(msg) => write!(f, "Service error: {}", msg),
        }
    }
}

impl std::error::Error for BoxError {}

/// Box options for configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BoxOptions {
    pub config: Config,
    pub working_directory: Option<String>,
    pub disable_color: bool,
}

impl Default for BoxOptions {
    fn default() -> Self {
        Self {
            config: Config::default(),
            working_directory: None,
            disable_color: false,
        }
    }
}

/// Main SingBox struct that manages the entire sing-box service
pub struct SingBox {
    // Core components
    config: Config,
    context: Context,
    
    // Service components
    inbounds: Arc<RwLock<HashMap<String, Arc<dyn Inbound>>>>,
    outbounds: Arc<RwLock<HashMap<String, Arc<dyn Outbound>>>>,
    router: Arc<RwLock<Option<Arc<dyn crate::adapter::Router>>>>,
    dns_client: Arc<RwLock<Option<Arc<DNSClient>>>>,
    stats_manager: Arc<StatsManager>,
    
    // State management
    started: Arc<RwLock<bool>>,
    stage: Arc<RwLock<StartStage>>,
}

impl SingBox {
    /// Create a new Box instance with the given options
    pub fn new(options: BoxOptions) -> Result<Self, BoxError> {
        let context = Context::new();
        
        Ok(Self {
            config: options.config,
            context,
            inbounds: Arc::new(RwLock::new(HashMap::new())),
            outbounds: Arc::new(RwLock::new(HashMap::new())),
            router: Arc::new(RwLock::new(None)),
            dns_client: Arc::new(RwLock::new(None)),
            stats_manager: Arc::new(StatsManager::new()),
            started: Arc::new(RwLock::new(false)),
            stage: Arc::new(RwLock::new(StartStage::Initialize)),
        })
    }
    
    /// Start the Box service
    pub async fn start(&self) -> Result<(), BoxError> {
        let mut started = self.started.write().await;
        if *started {
            return Err(BoxError::StartupError("Box is already started".to_string()));
        }
        
        // Initialize stage
        *self.stage.write().await = StartStage::Initialize;
        self.initialize().await?;
        
        // Start stage
        *self.stage.write().await = StartStage::Start;
        self.start_services().await?;
        
        // Post-start stage
        *self.stage.write().await = StartStage::PostStart;
        self.post_start().await?;
        
        *started = true;
        Ok(())
    }
    
    /// Stop the Box service
    pub async fn close(&self) -> Result<(), BoxError> {
        let mut started = self.started.write().await;
        if !*started {
            return Ok(());
        }
        
        // Stop all services
        self.stop_services().await?;
        
        // Clear all components
        self.inbounds.write().await.clear();
        self.outbounds.write().await.clear();
        *self.router.write().await = None;
        *self.dns_client.write().await = None;
        
        *started = false;
        Ok(())
    }
    
    /// Check if the Box is running
    pub async fn is_running(&self) -> bool {
        *self.started.read().await
    }
    
    /// Get the current configuration
    pub fn config(&self) -> &Config {
        &self.config
    }
    
    /// Get statistics manager
    pub fn stats_manager(&self) -> Arc<StatsManager> {
        self.stats_manager.clone()
    }
    
    // Private methods
    
    async fn initialize(&self) -> Result<(), BoxError> {
        // Initialize DNS client
        if let Some(_dns_config) = &self.config.dns {
            let client_options = crate::dns::ClientOptions::default();
            let dns_client = DNSClient::new(client_options);
            *self.dns_client.write().await = Some(Arc::new(dns_client));
        }
        
        // Initialize router
        if let Some(route_config) = &self.config.route {
            let router = Router::new("direct"); // Default outbound
            *self.router.write().await = Some(Arc::new(router));
        }
        
        // Initialize inbounds
        if let Some(inbounds_config) = &self.config.inbounds {
            let mut inbounds = self.inbounds.write().await;
            for inbound_config in inbounds_config {
                // Create inbound based on configuration
                // This would be implemented with the protocol registry
                // inbounds.insert(inbound_config.tag.clone(), inbound);
            }
        }
        
        // Initialize outbounds
        if let Some(outbounds_config) = &self.config.outbounds {
            let mut outbounds = self.outbounds.write().await;
            for outbound_config in outbounds_config {
                // Create outbound based on configuration
                // This would be implemented with the protocol registry
                // outbounds.insert(outbound_config.tag.clone(), outbound);
            }
        }
        
        Ok(())
    }
    
    async fn start_services(&self) -> Result<(), BoxError> {
        let stage = *self.stage.read().await;
        
        // Start DNS client
        if let Some(dns_client) = self.dns_client.read().await.as_ref() {
            // DNS client doesn't need explicit start
        }
        
        // Start router
        if let Some(router) = self.router.read().await.as_ref() {
            // Router doesn't need explicit start
        }
        
        // Start inbounds
        let inbounds = self.inbounds.read().await;
        for (tag, inbound) in inbounds.iter() {
            if let Err(e) = inbound.start(stage).await {
                return Err(BoxError::StartupError(format!("Failed to start inbound {}: {}", tag, e)));
            }
        }
        
        // Start outbounds
        let outbounds = self.outbounds.read().await;
        for (tag, _outbound) in outbounds.iter() {
            // Outbounds don't implement Lifecycle in this simplified version
            println!("Starting outbound {} (lifecycle not implemented)", tag);
        }
        
        Ok(())
    }
    
    async fn post_start(&self) -> Result<(), BoxError> {
        // Post-start initialization
        // This is where we would start listening for connections, etc.
        Ok(())
    }
    
    async fn stop_services(&self) -> Result<(), BoxError> {
        // Stop inbounds
        let inbounds = self.inbounds.read().await;
        for (tag, inbound) in inbounds.iter() {
            if let Err(e) = inbound.close().await {
                eprintln!("Failed to stop inbound {}: {}", tag, e);
            }
        }
        
        // Stop outbounds
        let outbounds = self.outbounds.read().await;
        for (tag, _outbound) in outbounds.iter() {
            // Try to cast to Lifecycle if the outbound implements it
            println!("Stopping outbound {} (lifecycle not implemented)", tag);
        }
        
        Ok(())
    }
}

#[async_trait]
impl Lifecycle for SingBox {
    async fn start(&self, stage: StartStage) -> Result<(), String> {
        self.start().await.map_err(|e| e.to_string())
    }
    
    async fn close(&self) -> Result<(), String> {
        self.close().await.map_err(|e| e.to_string())
    }
}
