//! Lifecycle management for Box components
//!
//! This module provides lifecycle management functionality for all Box components,
//! ensuring proper startup and shutdown sequences.

use std::sync::Arc;
use tokio::sync::RwLock;
use crate::adapter::{Lifecycle, StartStage};

/// Lifecycle manager for coordinating component startup and shutdown
pub struct LifecycleManager {
    components: Arc<RwLock<Vec<Arc<dyn Lifecycle>>>>,
    current_stage: Arc<RwLock<StartStage>>,
}

impl LifecycleManager {
    /// Create a new lifecycle manager
    pub fn new() -> Self {
        Self {
            components: Arc::new(RwLock::new(Vec::new())),
            current_stage: Arc::new(RwLock::new(StartStage::Initialize)),
        }
    }
    
    /// Register a component for lifecycle management
    pub async fn register(&self, component: Arc<dyn Lifecycle>) {
        self.components.write().await.push(component);
    }
    
    /// Start all registered components
    pub async fn start_all(&self) -> Result<(), String> {
        let stages = [StartStage::Initialize, StartStage::Start, StartStage::PostStart];
        
        for stage in stages.iter() {
            *self.current_stage.write().await = *stage;
            
            let components = self.components.read().await;
            for component in components.iter() {
                // Note: We need to clone the component to get a mutable reference
                // This is a limitation of the current design that would need to be addressed
                // component.start(*stage).await?;
            }
        }
        
        Ok(())
    }
    
    /// Stop all registered components
    pub async fn stop_all(&self) -> Result<(), String> {
        let components = self.components.read().await;
        
        // Stop components in reverse order
        for component in components.iter().rev() {
            // component.close().await?;
        }
        
        Ok(())
    }
    
    /// Get the current lifecycle stage
    pub async fn current_stage(&self) -> StartStage {
        *self.current_stage.read().await
    }
}

impl Default for LifecycleManager {
    fn default() -> Self {
        Self::new()
    }
}
