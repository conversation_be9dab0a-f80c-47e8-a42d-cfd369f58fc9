//! Service management for Box
//!
//! This module provides service management functionality for the Box,
//! including service registration, lifecycle, and coordination.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use async_trait::async_trait;
use crate::adapter::{Lifecycle, StartStage};

/// Service trait for Box services
#[async_trait]
pub trait Service: Lifecycle + Send + Sync {
    /// Get the service type
    fn service_type(&self) -> &str;
    
    /// Get the service name
    fn service_name(&self) -> &str;
    
    /// Check if the service is enabled
    fn is_enabled(&self) -> bool {
        true
    }
}

/// Service manager for coordinating all Box services
pub struct ServiceManager {
    services: Arc<RwLock<HashMap<String, Arc<dyn Service>>>>,
    started: Arc<RwLock<bool>>,
}

impl ServiceManager {
    /// Create a new service manager
    pub fn new() -> Self {
        Self {
            services: Arc::new(RwLock::new(HashMap::new())),
            started: Arc::new(RwLock::new(false)),
        }
    }
    
    /// Register a service
    pub async fn register_service(&self, service: Arc<dyn Service>) {
        let service_name = service.service_name().to_string();
        self.services.write().await.insert(service_name, service);
    }
    
    /// Unregister a service
    pub async fn unregister_service(&self, service_name: &str) -> Option<Arc<dyn Service>> {
        self.services.write().await.remove(service_name)
    }
    
    /// Get a service by name
    pub async fn get_service(&self, service_name: &str) -> Option<Arc<dyn Service>> {
        self.services.read().await.get(service_name).cloned()
    }
    
    /// List all registered services
    pub async fn list_services(&self) -> Vec<String> {
        self.services.read().await.keys().cloned().collect()
    }
    
    /// Start all services
    pub async fn start_all(&self) -> Result<(), String> {
        let mut started = self.started.write().await;
        if *started {
            return Err("Services are already started".to_string());
        }
        
        let services = self.services.read().await;
        
        // Start services in phases
        let stages = [StartStage::Initialize, StartStage::Start, StartStage::PostStart];
        
        for stage in stages.iter() {
            for (name, service) in services.iter() {
                if service.is_enabled() {
                    // service.start(*stage).await
                    //     .map_err(|e| format!("Failed to start service {}: {}", name, e))?;
                }
            }
        }
        
        *started = true;
        Ok(())
    }
    
    /// Stop all services
    pub async fn stop_all(&self) -> Result<(), String> {
        let mut started = self.started.write().await;
        if !*started {
            return Ok(());
        }
        
        let services = self.services.read().await;
        
        // Stop services in reverse order
        let service_names: Vec<_> = services.keys().collect();
        for name in service_names.iter().rev() {
            if let Some(service) = services.get(*name) {
                if service.is_enabled() {
                    // service.close().await
                    //     .map_err(|e| format!("Failed to stop service {}: {}", name, e))?;
                }
            }
        }
        
        *started = false;
        Ok(())
    }
    
    /// Check if services are started
    pub async fn is_started(&self) -> bool {
        *self.started.read().await
    }
}

impl Default for ServiceManager {
    fn default() -> Self {
        Self::new()
    }
}

/// Basic service implementation
pub struct BasicService {
    service_type: String,
    service_name: String,
    enabled: bool,
}

impl BasicService {
    pub fn new(service_type: &str, service_name: &str) -> Self {
        Self {
            service_type: service_type.to_string(),
            service_name: service_name.to_string(),
            enabled: true,
        }
    }
    
    pub fn with_enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }
}

impl Service for BasicService {
    fn service_type(&self) -> &str {
        &self.service_type
    }
    
    fn service_name(&self) -> &str {
        &self.service_name
    }
    
    fn is_enabled(&self) -> bool {
        self.enabled
    }
}

#[async_trait]
impl Lifecycle for BasicService {
    async fn start(&self, _stage: StartStage) -> Result<(), String> {
        // Basic service doesn't need specific startup logic
        Ok(())
    }
    
    async fn close(&self) -> Result<(), String> {
        // Basic service doesn't need specific cleanup logic
        Ok(())
    }
}
