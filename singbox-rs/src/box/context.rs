//! Context management for Box
//!
//! This module provides context management functionality for the Box,
//! including request context, cancellation, and resource tracking.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio_util::sync::CancellationToken;

/// Box context for managing request lifecycle and cancellation
#[derive(Clone)]
pub struct BoxContext {
    /// Cancellation token for graceful shutdown
    cancellation_token: CancellationToken,
    
    /// Context metadata
    metadata: Arc<RwLock<HashMap<String, String>>>,
    
    /// Parent context (if any)
    parent: Option<Arc<BoxContext>>,
}

impl BoxContext {
    /// Create a new root context
    pub fn new() -> Self {
        Self {
            cancellation_token: CancellationToken::new(),
            metadata: Arc::new(RwLock::new(HashMap::new())),
            parent: None,
        }
    }
    
    /// Create a child context
    pub fn child(&self) -> Self {
        Self {
            cancellation_token: self.cancellation_token.child_token(),
            metadata: Arc::new(RwLock::new(HashMap::new())),
            parent: Some(Arc::new(self.clone())),
        }
    }
    
    /// Create a context with timeout
    pub fn with_timeout(&self, duration: std::time::Duration) -> Self {
        let child = self.child();
        let token = child.cancellation_token.clone();
        
        tokio::spawn(async move {
            tokio::time::sleep(duration).await;
            token.cancel();
        });
        
        child
    }
    
    /// Cancel the context
    pub fn cancel(&self) {
        self.cancellation_token.cancel();
    }
    
    /// Check if the context is cancelled
    pub fn is_cancelled(&self) -> bool {
        self.cancellation_token.is_cancelled()
    }
    
    /// Get the cancellation token
    pub fn cancellation_token(&self) -> &CancellationToken {
        &self.cancellation_token
    }
    
    /// Wait for cancellation
    pub async fn cancelled(&self) {
        self.cancellation_token.cancelled().await;
    }
    
    /// Set metadata
    pub async fn set_metadata(&self, key: &str, value: &str) {
        self.metadata.write().await.insert(key.to_string(), value.to_string());
    }
    
    /// Get metadata
    pub async fn get_metadata(&self, key: &str) -> Option<String> {
        self.metadata.read().await.get(key).cloned()
    }
    
    /// Get all metadata
    pub async fn all_metadata(&self) -> HashMap<String, String> {
        self.metadata.read().await.clone()
    }
    
    /// Get parent context
    pub fn parent(&self) -> Option<Arc<BoxContext>> {
        self.parent.clone()
    }
}

impl Default for BoxContext {
    fn default() -> Self {
        Self::new()
    }
}

/// Context manager for tracking active contexts
pub struct ContextManager {
    active_contexts: Arc<RwLock<HashMap<String, Arc<BoxContext>>>>,
    root_context: Arc<BoxContext>,
}

impl ContextManager {
    /// Create a new context manager
    pub fn new() -> Self {
        Self {
            active_contexts: Arc::new(RwLock::new(HashMap::new())),
            root_context: Arc::new(BoxContext::new()),
        }
    }
    
    /// Get the root context
    pub fn root_context(&self) -> Arc<BoxContext> {
        self.root_context.clone()
    }
    
    /// Create a new context with ID
    pub async fn create_context(&self, id: &str) -> Arc<BoxContext> {
        let context = Arc::new(self.root_context.child());
        self.active_contexts.write().await.insert(id.to_string(), context.clone());
        context
    }
    
    /// Get a context by ID
    pub async fn get_context(&self, id: &str) -> Option<Arc<BoxContext>> {
        self.active_contexts.read().await.get(id).cloned()
    }
    
    /// Remove a context
    pub async fn remove_context(&self, id: &str) -> Option<Arc<BoxContext>> {
        self.active_contexts.write().await.remove(id)
    }
    
    /// Cancel all contexts
    pub async fn cancel_all(&self) {
        self.root_context.cancel();
        self.active_contexts.write().await.clear();
    }
    
    /// Get active context count
    pub async fn active_count(&self) -> usize {
        self.active_contexts.read().await.len()
    }
}

impl Default for ContextManager {
    fn default() -> Self {
        Self::new()
    }
}
