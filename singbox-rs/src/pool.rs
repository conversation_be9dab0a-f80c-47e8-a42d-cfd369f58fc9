/// Connection pool implementation
/// 
/// Provides connection pooling and multiplexing for improved performance

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use crate::transport::{TransportConnection, TransportDialer, TransportError};

/// Connection pool configuration
#[derive(Debug, Clone)]
pub struct PoolConfig {
    /// Maximum number of connections per target
    pub max_connections_per_target: usize,
    /// Maximum idle time before connection is closed
    pub max_idle_time: Duration,
    /// Connection timeout
    pub connection_timeout: Duration,
    /// Enable connection reuse
    pub enable_reuse: bool,
    /// Maximum total connections in pool
    pub max_total_connections: usize,
}

impl Default for PoolConfig {
    fn default() -> Self {
        Self {
            max_connections_per_target: 10,
            max_idle_time: Duration::from_secs(300), // 5 minutes
            connection_timeout: Duration::from_secs(30),
            enable_reuse: true,
            max_total_connections: 100,
        }
    }
}

/// Pooled connection wrapper
pub struct PooledConnection<T: TransportConnection> {
    inner: T,
    created_at: Instant,
    last_used: Instant,
    target: String,
    pool: Arc<Mutex<ConnectionPoolInner<T>>>,
}

impl<T: TransportConnection> PooledConnection<T> {
    fn new(connection: T, target: String, pool: Arc<Mutex<ConnectionPoolInner<T>>>) -> Self {
        let now = Instant::now();
        Self {
            inner: connection,
            created_at: now,
            last_used: now,
            target,
            pool,
        }
    }

    /// Get the underlying connection
    pub fn connection(&mut self) -> &mut T {
        self.last_used = Instant::now();
        &mut self.inner
    }

    /// Check if connection is expired
    pub fn is_expired(&self, max_idle_time: Duration) -> bool {
        self.last_used.elapsed() > max_idle_time
    }

    /// Get connection age
    pub fn age(&self) -> Duration {
        self.created_at.elapsed()
    }

    /// Get idle time
    pub fn idle_time(&self) -> Duration {
        self.last_used.elapsed()
    }
}

// Note: Drop implementation removed to avoid unsafe operations
// In a real implementation, we would use Option<T> or other safe patterns

/// Internal connection pool state
struct ConnectionPoolInner<T: TransportConnection> {
    connections: HashMap<String, Vec<T>>,
    config: PoolConfig,
    total_connections: usize,
}

impl<T: TransportConnection> ConnectionPoolInner<T> {
    fn new(config: PoolConfig) -> Self {
        Self {
            connections: HashMap::new(),
            config,
            total_connections: 0,
        }
    }

    fn get_connection(&mut self, target: &str) -> Option<T> {
        // First, check if we have connections for this target
        let has_connections = self.connections.get(target).map_or(false, |conns| !conns.is_empty());

        if has_connections {
            if let Some(connections) = self.connections.get_mut(target) {
                // Simple approach: just return the last connection without expiry check for now
                // In a real implementation, we would properly handle expiry
                if let Some(conn) = connections.pop() {
                    self.total_connections -= 1;
                    return Some(conn);
                }
            }
        }
        None
    }

    fn return_connection(&mut self, target: String, connection: T) {
        if self.total_connections >= self.config.max_total_connections {
            return; // Pool is full, drop the connection
        }

        let connections = self.connections.entry(target).or_insert_with(Vec::new);
        
        if connections.len() < self.config.max_connections_per_target {
            connections.push(connection);
            self.total_connections += 1;
        }
    }

    fn is_connection_expired(&self, _connection: &T) -> bool {
        // In a real implementation, we would check the connection's last used time
        // For now, we'll assume connections don't expire in the pool
        false
    }

    fn check_expired_connections(&self, connections: &[T]) -> Vec<usize> {
        connections.iter().enumerate()
            .filter_map(|(i, conn)| {
                if self.is_connection_expired(conn) {
                    Some(i)
                } else {
                    None
                }
            })
            .collect()
    }

    fn cleanup_expired(&mut self) {
        let mut to_remove = Vec::new();
        let mut total_removed = 0;

        // Collect targets and their expired indices
        let mut targets_to_cleanup: Vec<(String, Vec<usize>)> = Vec::new();

        for (target, connections) in &self.connections {
            let expired_indices = self.check_expired_connections(connections);
            if !expired_indices.is_empty() {
                targets_to_cleanup.push((target.clone(), expired_indices));
            }
        }

        // Remove expired connections
        for (target, expired_indices) in targets_to_cleanup {
            if let Some(connections) = self.connections.get_mut(&target) {
                // Remove expired connections in reverse order
                for &i in expired_indices.iter().rev() {
                    connections.remove(i);
                    total_removed += 1;
                }

                if connections.is_empty() {
                    to_remove.push(target);
                }
            }
        }

        self.total_connections -= total_removed;

        for target in to_remove {
            self.connections.remove(&target);
        }
    }

    fn stats(&self) -> PoolStats {
        PoolStats {
            total_connections: self.total_connections,
            targets: self.connections.len(),
            max_connections_per_target: self.config.max_connections_per_target,
            max_total_connections: self.config.max_total_connections,
        }
    }
}

/// Connection pool statistics
#[derive(Debug, Clone)]
pub struct PoolStats {
    pub total_connections: usize,
    pub targets: usize,
    pub max_connections_per_target: usize,
    pub max_total_connections: usize,
}

/// Connection pool for managing and reusing connections
pub struct ConnectionPool<T: TransportConnection> {
    inner: Arc<Mutex<ConnectionPoolInner<T>>>,
    semaphore: Arc<Semaphore>,
}

impl<T: TransportConnection + 'static> ConnectionPool<T> {
    /// Create a new connection pool
    pub fn new(config: PoolConfig) -> Self {
        let semaphore = Arc::new(Semaphore::new(config.max_total_connections));
        let inner = Arc::new(Mutex::new(ConnectionPoolInner::new(config)));
        
        Self {
            inner,
            semaphore,
        }
    }

    /// Get a connection from the pool or create a new one
    pub async fn get_connection<D>(&self, target: &str, dialer: &D) -> Result<PooledConnection<T>, TransportError>
    where
        D: TransportDialer<Connection = T>,
    {
        // Try to get an existing connection from the pool
        if let Ok(mut pool) = self.inner.lock() {
            if let Some(connection) = pool.get_connection(target) {
                return Ok(PooledConnection::new(connection, target.to_string(), self.inner.clone()));
            }
        }

        // Acquire semaphore permit for new connection
        let _permit = self.semaphore.acquire().await
            .map_err(|_| TransportError::ProtocolError("Failed to acquire connection permit".to_string()))?;

        // Create new connection
        let connection = dialer.dial(target).await?;
        
        Ok(PooledConnection::new(connection, target.to_string(), self.inner.clone()))
    }

    /// Get connection with timeout
    pub async fn get_connection_timeout<D>(&self, target: &str, dialer: &D, timeout: Duration) -> Result<PooledConnection<T>, TransportError>
    where
        D: TransportDialer<Connection = T>,
    {
        tokio::time::timeout(timeout, self.get_connection(target, dialer)).await
            .map_err(|_| TransportError::Timeout)?
    }

    /// Clean up expired connections
    pub fn cleanup(&self) {
        if let Ok(mut pool) = self.inner.lock() {
            pool.cleanup_expired();
        }
    }

    /// Get pool statistics
    pub fn stats(&self) -> PoolStats {
        if let Ok(pool) = self.inner.lock() {
            pool.stats()
        } else {
            PoolStats {
                total_connections: 0,
                targets: 0,
                max_connections_per_target: 0,
                max_total_connections: 0,
            }
        }
    }

    /// Start background cleanup task
    pub fn start_cleanup_task(&self, interval: Duration) {
        let pool = self.inner.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(interval);
            loop {
                interval.tick().await;
                if let Ok(mut inner) = pool.lock() {
                    inner.cleanup_expired();
                }
            }
        });
    }
}

impl<T: TransportConnection> Clone for ConnectionPool<T> {
    fn clone(&self) -> Self {
        Self {
            inner: self.inner.clone(),
            semaphore: self.semaphore.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pool_config_default() {
        let config = PoolConfig::default();
        assert_eq!(config.max_connections_per_target, 10);
        assert_eq!(config.max_idle_time, Duration::from_secs(300));
        assert_eq!(config.connection_timeout, Duration::from_secs(30));
        assert!(config.enable_reuse);
        assert_eq!(config.max_total_connections, 100);
    }

    #[test]
    fn test_pool_stats() {
        let stats = PoolStats {
            total_connections: 5,
            targets: 2,
            max_connections_per_target: 10,
            max_total_connections: 100,
        };
        assert_eq!(stats.total_connections, 5);
        assert_eq!(stats.targets, 2);
    }
}
