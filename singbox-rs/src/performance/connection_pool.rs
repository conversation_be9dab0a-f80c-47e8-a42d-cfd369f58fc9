//! Advanced connection pool implementation
//!
//! This module provides high-performance connection pooling with
//! intelligent load balancing, health monitoring, and automatic scaling.

use std::collections::{HashMap, VecDeque};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::net::TcpStream;
use tokio::sync::{RwLock, Semaphore, Mutex};
use serde::{Deserialize, Serialize};

/// Advanced connection pool configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolConfig {
    /// Maximum connections per target
    pub max_connections_per_target: u32,
    
    /// Minimum idle connections
    pub min_idle_connections: u32,
    
    /// Maximum idle connections
    pub max_idle_connections: u32,
    
    /// Connection idle timeout
    pub idle_timeout: Duration,
    
    /// Connection max lifetime
    pub max_lifetime: Duration,
    
    /// Connection establishment timeout
    pub connect_timeout: Duration,
    
    /// Health check interval
    pub health_check_interval: Duration,
    
    /// Health check timeout
    pub health_check_timeout: Duration,
    
    /// Enable connection validation
    pub validate_connections: bool,
    
    /// Connection retry attempts
    pub retry_attempts: u32,
    
    /// Retry delay
    pub retry_delay: Duration,
    
    /// Enable connection warming
    pub enable_warming: bool,
    
    /// Warming target count
    pub warming_target: u32,
    
    /// Pool scaling configuration
    pub scaling: PoolScalingConfig,
}

/// Pool scaling configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolScalingConfig {
    /// Enable automatic scaling
    pub enabled: bool,
    
    /// Scale up threshold (utilization percentage)
    pub scale_up_threshold: f64,
    
    /// Scale down threshold (utilization percentage)
    pub scale_down_threshold: f64,
    
    /// Scale up increment
    pub scale_up_increment: u32,
    
    /// Scale down decrement
    pub scale_down_decrement: u32,
    
    /// Scaling cooldown period
    pub scaling_cooldown: Duration,
    
    /// Maximum pool size
    pub max_pool_size: u32,
    
    /// Minimum pool size
    pub min_pool_size: u32,
}

/// Advanced connection pool
pub struct ConnectionPool {
    /// Pool configuration
    config: PoolConfig,
    
    /// Connection pools by target
    pools: Arc<RwLock<HashMap<String, TargetPool>>>,
    
    /// Pool statistics
    stats: Arc<RwLock<PoolStats>>,
    
    /// Connection semaphore for rate limiting
    connection_semaphore: Arc<Semaphore>,
    
    /// Health check tasks
    health_check_tasks: Arc<RwLock<HashMap<String, tokio::task::JoinHandle<()>>>>,
    
    /// Scaling manager
    scaling_manager: Arc<Mutex<PoolScalingManager>>,
}

/// Target-specific connection pool
#[derive(Debug)]
pub struct TargetPool {
    /// Target address
    pub target: SocketAddr,
    
    /// Available connections
    pub available: VecDeque<PooledConnection>,
    
    /// Active connections
    pub active: HashMap<String, PooledConnection>,
    
    /// Pool statistics
    pub stats: TargetPoolStats,
    
    /// Last scaling action
    pub last_scaling: Instant,
    
    /// Connection factory
    pub factory: ConnectionFactory,
}

/// Pooled connection wrapper
#[derive(Debug, Clone)]
pub struct PooledConnection {
    /// Connection ID
    pub id: String,
    
    /// Target address
    pub target: SocketAddr,
    
    /// Creation time
    pub created_at: Instant,
    
    /// Last used time
    pub last_used: Instant,
    
    /// Use count
    pub use_count: u32,
    
    /// Connection health
    pub healthy: bool,
    
    /// Connection latency
    pub latency: Duration,
    
    /// Connection metadata
    pub metadata: HashMap<String, String>,
}

/// Target pool statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TargetPoolStats {
    /// Total connections created
    pub total_created: u64,
    
    /// Total connections destroyed
    pub total_destroyed: u64,
    
    /// Current available connections
    pub available_connections: u32,
    
    /// Current active connections
    pub active_connections: u32,
    
    /// Pool hits
    pub pool_hits: u64,
    
    /// Pool misses
    pub pool_misses: u64,
    
    /// Connection failures
    pub connection_failures: u64,
    
    /// Average connection time
    pub avg_connection_time: Duration,
    
    /// Average connection lifetime
    pub avg_lifetime: Duration,
    
    /// Health check failures
    pub health_check_failures: u64,
}

/// Overall pool statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStats {
    /// Total pools
    pub total_pools: u32,
    
    /// Total connections across all pools
    pub total_connections: u64,
    
    /// Total active connections
    pub active_connections: u64,
    
    /// Total available connections
    pub available_connections: u64,
    
    /// Overall pool utilization
    pub pool_utilization: f64,
    
    /// Overall hit rate
    pub hit_rate: f64,
    
    /// Total pool hits
    pub total_hits: u64,
    
    /// Total pool misses
    pub total_misses: u64,
    
    /// Connection creation rate
    pub creation_rate: f64,
    
    /// Connection destruction rate
    pub destruction_rate: f64,
    
    /// Average pool efficiency
    pub avg_efficiency: f64,
}

/// Connection factory for creating new connections
pub struct ConnectionFactory {
    /// Target address
    target: SocketAddr,
    
    /// Connection configuration
    config: ConnectionConfig,
    
    /// Factory statistics
    stats: Arc<RwLock<FactoryStats>>,
}

/// Connection configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionConfig {
    /// TCP no delay
    pub tcp_nodelay: bool,
    
    /// TCP keep alive
    pub tcp_keepalive: Option<Duration>,
    
    /// Socket buffer sizes
    pub socket_buffer_size: Option<u32>,
    
    /// Connection timeout
    pub connect_timeout: Duration,
    
    /// Read timeout
    pub read_timeout: Option<Duration>,
    
    /// Write timeout
    pub write_timeout: Option<Duration>,
    
    /// Enable TLS
    pub enable_tls: bool,
    
    /// TLS configuration
    pub tls_config: Option<TlsConfig>,
}

/// TLS configuration for connections
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TlsConfig {
    /// Server name
    pub server_name: String,
    
    /// Skip certificate verification
    pub skip_verify: bool,
    
    /// Client certificate
    pub client_cert: Option<String>,
    
    /// Client key
    pub client_key: Option<String>,
    
    /// CA certificate
    pub ca_cert: Option<String>,
    
    /// ALPN protocols
    pub alpn_protocols: Vec<String>,
}

/// Factory statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FactoryStats {
    /// Total connections created
    pub total_created: u64,
    
    /// Creation failures
    pub creation_failures: u64,
    
    /// Average creation time
    pub avg_creation_time: Duration,
    
    /// Success rate
    pub success_rate: f64,
}

/// Pool scaling manager
pub struct PoolScalingManager {
    /// Scaling configuration
    config: PoolScalingConfig,
    
    /// Scaling history
    scaling_history: VecDeque<ScalingEvent>,
    
    /// Last scaling action per pool
    last_scaling: HashMap<String, Instant>,
}

/// Scaling event
#[derive(Debug, Clone)]
pub struct ScalingEvent {
    /// Event timestamp
    pub timestamp: Instant,
    
    /// Pool target
    pub target: String,
    
    /// Scaling action
    pub action: ScalingAction,
    
    /// Previous size
    pub previous_size: u32,
    
    /// New size
    pub new_size: u32,
    
    /// Trigger reason
    pub reason: String,
}

/// Scaling actions
#[derive(Debug, Clone)]
pub enum ScalingAction {
    /// Scale up
    ScaleUp,
    
    /// Scale down
    ScaleDown,
    
    /// No action
    NoAction,
}

impl ConnectionPool {
    /// Create a new advanced connection pool
    pub async fn new(config: PoolConfig) -> Result<Self, String> {
        let max_total_connections = config.max_connections_per_target * 100; // Reasonable limit
        
        Ok(Self {
            config: config.clone(),
            pools: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(PoolStats::default())),
            connection_semaphore: Arc::new(Semaphore::new(max_total_connections as usize)),
            health_check_tasks: Arc::new(RwLock::new(HashMap::new())),
            scaling_manager: Arc::new(Mutex::new(PoolScalingManager::new(config.scaling))),
        })
    }
    
    /// Start the connection pool
    pub async fn start(&self) -> Result<(), String> {
        println!("⚡ Starting advanced connection pool...");
        
        // Start health check manager
        self.start_health_check_manager().await;
        
        // Start scaling manager
        if self.config.scaling.enabled {
            self.start_scaling_manager().await;
        }
        
        // Start statistics collector
        self.start_stats_collector().await;
        
        println!("⚡ Advanced connection pool started");
        Ok(())
    }
    
    /// Stop the connection pool
    pub async fn stop(&self) {
        println!("⚡ Stopping advanced connection pool...");
        
        // Stop health check tasks
        let mut health_tasks = self.health_check_tasks.write().await;
        for (_, task) in health_tasks.drain() {
            task.abort();
        }
        
        // Close all connections
        let mut pools = self.pools.write().await;
        for (_, pool) in pools.drain() {
            // Close connections in the pool
            // Implementation would close actual TCP connections
        }
        
        println!("⚡ Advanced connection pool stopped");
    }
    
    /// Get a connection from the pool
    pub async fn get_connection(&self, target: SocketAddr) -> Result<PooledConnection, String> {
        let target_key = target.to_string();
        
        // Acquire semaphore permit
        let _permit = self.connection_semaphore.acquire().await
            .map_err(|e| format!("Failed to acquire connection permit: {}", e))?;
        
        // Try to get from existing pool
        {
            let mut pools = self.pools.write().await;
            if let Some(pool) = pools.get_mut(&target_key) {
                if let Some(connection) = pool.available.pop_front() {
                    // Validate connection if enabled
                    if self.config.validate_connections && !self.validate_connection(&connection).await {
                        // Connection is invalid, create a new one
                        return self.create_new_connection(target).await;
                    }
                    
                    // Move to active connections
                    let mut active_conn = connection.clone();
                    active_conn.last_used = Instant::now();
                    active_conn.use_count += 1;
                    
                    pool.active.insert(active_conn.id.clone(), active_conn.clone());
                    pool.stats.pool_hits += 1;
                    
                    // Update global statistics
                    let mut stats = self.stats.write().await;
                    stats.total_hits += 1;
                    
                    return Ok(active_conn);
                }
            }
        }
        
        // No available connection, create new one
        self.create_new_connection(target).await
    }
    
    /// Create a new connection
    async fn create_new_connection(&self, target: SocketAddr) -> Result<PooledConnection, String> {
        let target_key = target.to_string();
        
        // Create connection factory if not exists
        let factory = {
            let mut pools = self.pools.write().await;
            let pool = pools.entry(target_key.clone()).or_insert_with(|| {
                TargetPool::new(target, self.config.clone())
            });
            pool.factory.clone()
        };
        
        // Create new connection
        let connection = factory.create_connection().await?;
        
        // Add to active connections
        {
            let mut pools = self.pools.write().await;
            if let Some(pool) = pools.get_mut(&target_key) {
                pool.active.insert(connection.id.clone(), connection.clone());
                pool.stats.total_created += 1;
                pool.stats.pool_misses += 1;
            }
        }
        
        // Update global statistics
        let mut stats = self.stats.write().await;
        stats.total_misses += 1;
        stats.active_connections += 1;
        
        Ok(connection)
    }
    
    /// Return a connection to the pool
    pub async fn return_connection(&self, connection: PooledConnection) -> Result<(), String> {
        let target_key = connection.target.to_string();
        
        let mut pools = self.pools.write().await;
        if let Some(pool) = pools.get_mut(&target_key) {
            // Remove from active connections
            pool.active.remove(&connection.id);
            
            // Check if connection should be returned to pool
            if connection.healthy && 
               connection.created_at.elapsed() < self.config.max_lifetime &&
               connection.last_used.elapsed() < self.config.idle_timeout &&
               pool.available.len() < self.config.max_idle_connections as usize {
                
                // Return to available pool
                pool.available.push_back(connection);
            } else {
                // Connection expired or pool full, destroy it
                pool.stats.total_destroyed += 1;
            }
        }
        
        // Update global statistics
        let mut stats = self.stats.write().await;
        stats.active_connections = stats.active_connections.saturating_sub(1);
        
        Ok(())
    }
    
    /// Validate connection health
    async fn validate_connection(&self, connection: &PooledConnection) -> bool {
        // Check if connection is expired
        if connection.created_at.elapsed() > self.config.max_lifetime ||
           connection.last_used.elapsed() > self.config.idle_timeout {
            return false;
        }
        
        // Perform health check if enabled
        if self.config.validate_connections {
            // Simple TCP connection test
            match tokio::time::timeout(
                self.config.health_check_timeout,
                TcpStream::connect(connection.target)
            ).await {
                Ok(Ok(_)) => true,
                _ => false,
            }
        } else {
            true
        }
    }
    
    /// Start health check manager
    async fn start_health_check_manager(&self) {
        let pools = Arc::clone(&self.pools);
        let health_tasks = Arc::clone(&self.health_check_tasks);
        let config = self.config.clone();
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.health_check_interval);
            
            loop {
                interval.tick().await;
                
                // Get all pool targets
                let targets: Vec<String> = {
                    let pools_guard = pools.read().await;
                    pools_guard.keys().cloned().collect()
                };
                
                // Start health checks for each target
                for target in targets {
                    let pools_clone = Arc::clone(&pools);
                    let target_clone = target.clone();
                    let config_clone = config.clone();
                    
                    let health_task = tokio::spawn(async move {
                        Self::perform_health_check(pools_clone, target_clone, config_clone).await;
                    });
                    
                    health_tasks.write().await.insert(target, health_task);
                }
            }
        });
        
        self.health_check_tasks.write().await.insert("manager".to_string(), task);
    }
    
    /// Perform health check for a target pool
    async fn perform_health_check(
        pools: Arc<RwLock<HashMap<String, TargetPool>>>,
        target: String,
        config: PoolConfig,
    ) {
        let mut pools_guard = pools.write().await;
        if let Some(pool) = pools_guard.get_mut(&target) {
            let mut unhealthy_connections = Vec::new();
            
            // Check available connections
            for (index, connection) in pool.available.iter().enumerate() {
                if !Self::is_connection_healthy(connection, &config).await {
                    unhealthy_connections.push(index);
                }
            }
            
            // Remove unhealthy connections
            for &index in unhealthy_connections.iter().rev() {
                if let Some(removed) = pool.available.remove(index) {
                    pool.stats.total_destroyed += 1;
                    pool.stats.health_check_failures += 1;
                }
            }
        }
    }
    
    /// Check if connection is healthy
    async fn is_connection_healthy(connection: &PooledConnection, config: &PoolConfig) -> bool {
        // Check expiration
        if connection.created_at.elapsed() > config.max_lifetime ||
           connection.last_used.elapsed() > config.idle_timeout {
            return false;
        }
        
        // Perform actual health check
        match tokio::time::timeout(
            config.health_check_timeout,
            TcpStream::connect(connection.target)
        ).await {
            Ok(Ok(_)) => true,
            _ => false,
        }
    }
    
    /// Start scaling manager
    async fn start_scaling_manager(&self) {
        let pools = Arc::clone(&self.pools);
        let scaling_manager = Arc::clone(&self.scaling_manager);
        let config = self.config.clone();
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(30));
            
            loop {
                interval.tick().await;
                
                let mut manager = scaling_manager.lock().await;
                manager.evaluate_scaling(Arc::clone(&pools), &config).await;
            }
        });
        
        self.health_check_tasks.write().await.insert("scaling".to_string(), task);
    }
    
    /// Start statistics collector
    async fn start_stats_collector(&self) {
        let pools = Arc::clone(&self.pools);
        let stats = Arc::clone(&self.stats);
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(10));
            
            loop {
                interval.tick().await;
                
                // Collect statistics from all pools
                let pools_guard = pools.read().await;
                let mut total_stats = PoolStats::default();
                
                total_stats.total_pools = pools_guard.len() as u32;
                
                for pool in pools_guard.values() {
                    total_stats.total_connections += pool.stats.total_created;
                    total_stats.active_connections += pool.stats.active_connections as u64;
                    total_stats.available_connections += pool.stats.available_connections as u64;
                    total_stats.total_hits += pool.stats.pool_hits;
                    total_stats.total_misses += pool.stats.pool_misses;
                }
                
                // Calculate derived metrics
                if total_stats.total_hits + total_stats.total_misses > 0 {
                    total_stats.hit_rate = total_stats.total_hits as f64 / 
                        (total_stats.total_hits + total_stats.total_misses) as f64 * 100.0;
                }
                
                if total_stats.total_connections > 0 {
                    total_stats.pool_utilization = total_stats.active_connections as f64 / 
                        total_stats.total_connections as f64 * 100.0;
                }
                
                // Update global statistics
                *stats.write().await = total_stats;
            }
        });
        
        self.health_check_tasks.write().await.insert("stats".to_string(), task);
    }
    
    /// Get pool statistics
    pub async fn get_stats(&self) -> PoolStats {
        self.stats.read().await.clone()
    }
    
    /// Get detailed statistics for a target
    pub async fn get_target_stats(&self, target: SocketAddr) -> Option<TargetPoolStats> {
        let pools = self.pools.read().await;
        pools.get(&target.to_string()).map(|pool| pool.stats.clone())
    }
    
    /// Cleanup expired connections
    pub async fn cleanup(&self) {
        let mut pools = self.pools.write().await;
        
        for pool in pools.values_mut() {
            // Remove expired available connections
            pool.available.retain(|conn| {
                conn.created_at.elapsed() < self.config.max_lifetime &&
                conn.last_used.elapsed() < self.config.idle_timeout
            });
            
            // Update statistics
            pool.stats.available_connections = pool.available.len() as u32;
        }
    }
    
    /// Increase pool size for a target
    pub async fn increase_pool_size(&self, increment: u32) {
        // Implementation would increase the pool size
        println!("⚡ Increasing connection pool size by {}", increment);
    }
    
    /// Get pool utilization
    pub async fn get_utilization(&self) -> f64 {
        let stats = self.stats.read().await;
        stats.pool_utilization
    }
}

impl TargetPool {
    /// Create a new target pool
    pub fn new(target: SocketAddr, config: PoolConfig) -> Self {
        let connection_config = ConnectionConfig {
            tcp_nodelay: true,
            tcp_keepalive: Some(Duration::from_secs(30)),
            socket_buffer_size: Some(65536),
            connect_timeout: config.connect_timeout,
            read_timeout: Some(Duration::from_secs(30)),
            write_timeout: Some(Duration::from_secs(30)),
            enable_tls: false,
            tls_config: None,
        };
        
        Self {
            target,
            available: VecDeque::new(),
            active: HashMap::new(),
            stats: TargetPoolStats::default(),
            last_scaling: Instant::now(),
            factory: ConnectionFactory::new(target, connection_config),
        }
    }
}

impl ConnectionFactory {
    /// Create a new connection factory
    pub fn new(target: SocketAddr, config: ConnectionConfig) -> Self {
        Self {
            target,
            config,
            stats: Arc::new(RwLock::new(FactoryStats::default())),
        }
    }
    
    /// Create a new connection
    pub async fn create_connection(&self) -> Result<PooledConnection, String> {
        let start_time = Instant::now();
        
        // Create TCP connection
        let stream = tokio::time::timeout(
            self.config.connect_timeout,
            TcpStream::connect(self.target)
        ).await
        .map_err(|_| "Connection timeout".to_string())?
        .map_err(|e| format!("Failed to connect: {}", e))?;
        
        // Configure TCP options
        if self.config.tcp_nodelay {
            stream.set_nodelay(true)
                .map_err(|e| format!("Failed to set TCP_NODELAY: {}", e))?;
        }
        
        let connection_time = start_time.elapsed();
        
        // Update factory statistics
        {
            let mut stats = self.stats.write().await;
            stats.total_created += 1;
            
            // Update average creation time
            let total = stats.total_created;
            stats.avg_creation_time = Duration::from_nanos(
                (stats.avg_creation_time.as_nanos() as u64 * (total - 1) + 
                 connection_time.as_nanos() as u64) / total
            );
            
            stats.success_rate = (stats.total_created as f64) / 
                (stats.total_created + stats.creation_failures) as f64 * 100.0;
        }
        
        Ok(PooledConnection {
            id: uuid::Uuid::new_v4().to_string(),
            target: self.target,
            created_at: Instant::now(),
            last_used: Instant::now(),
            use_count: 1,
            healthy: true,
            latency: connection_time,
            metadata: HashMap::new(),
        })
    }
}

impl PoolScalingManager {
    /// Create a new scaling manager
    pub fn new(config: PoolScalingConfig) -> Self {
        Self {
            config,
            scaling_history: VecDeque::new(),
            last_scaling: HashMap::new(),
        }
    }
    
    /// Evaluate scaling for all pools
    pub async fn evaluate_scaling(
        &mut self,
        pools: Arc<RwLock<HashMap<String, TargetPool>>>,
        config: &PoolConfig,
    ) {
        let pools_guard = pools.read().await;
        
        for (target, pool) in pools_guard.iter() {
            // Check if scaling cooldown has passed
            if let Some(last_scaling) = self.last_scaling.get(target) {
                if last_scaling.elapsed() < self.config.scaling_cooldown {
                    continue;
                }
            }
            
            // Calculate utilization
            let total_connections = pool.available.len() + pool.active.len();
            let utilization = if total_connections > 0 {
                pool.active.len() as f64 / total_connections as f64 * 100.0
            } else {
                0.0
            };
            
            // Determine scaling action
            let action = if utilization > self.config.scale_up_threshold {
                ScalingAction::ScaleUp
            } else if utilization < self.config.scale_down_threshold {
                ScalingAction::ScaleDown
            } else {
                ScalingAction::NoAction
            };
            
            // Record scaling event
            if !matches!(action, ScalingAction::NoAction) {
                let event = ScalingEvent {
                    timestamp: Instant::now(),
                    target: target.clone(),
                    action: action.clone(),
                    previous_size: total_connections as u32,
                    new_size: match action {
                        ScalingAction::ScaleUp => total_connections as u32 + self.config.scale_up_increment,
                        ScalingAction::ScaleDown => (total_connections as u32).saturating_sub(self.config.scale_down_decrement),
                        ScalingAction::NoAction => total_connections as u32,
                    },
                    reason: format!("Utilization: {:.1}%", utilization),
                };
                
                self.scaling_history.push_back(event);
                self.last_scaling.insert(target.clone(), Instant::now());
                
                // Keep only recent scaling events
                if self.scaling_history.len() > 1000 {
                    self.scaling_history.pop_front();
                }
            }
        }
    }
}

impl Default for PoolConfig {
    fn default() -> Self {
        Self {
            max_connections_per_target: 100,
            min_idle_connections: 5,
            max_idle_connections: 20,
            idle_timeout: Duration::from_secs(300),
            max_lifetime: Duration::from_secs(3600),
            connect_timeout: Duration::from_secs(10),
            health_check_interval: Duration::from_secs(30),
            health_check_timeout: Duration::from_secs(5),
            validate_connections: true,
            retry_attempts: 3,
            retry_delay: Duration::from_millis(100),
            enable_warming: true,
            warming_target: 5,
            scaling: PoolScalingConfig::default(),
        }
    }
}

impl Default for PoolScalingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            scale_up_threshold: 80.0,
            scale_down_threshold: 30.0,
            scale_up_increment: 5,
            scale_down_decrement: 2,
            scaling_cooldown: Duration::from_secs(300),
            max_pool_size: 200,
            min_pool_size: 5,
        }
    }
}

impl Default for PoolStats {
    fn default() -> Self {
        Self {
            total_pools: 0,
            total_connections: 0,
            active_connections: 0,
            available_connections: 0,
            pool_utilization: 0.0,
            hit_rate: 0.0,
            total_hits: 0,
            total_misses: 0,
            creation_rate: 0.0,
            destruction_rate: 0.0,
            avg_efficiency: 100.0,
        }
    }
}

impl Default for TargetPoolStats {
    fn default() -> Self {
        Self {
            total_created: 0,
            total_destroyed: 0,
            available_connections: 0,
            active_connections: 0,
            pool_hits: 0,
            pool_misses: 0,
            connection_failures: 0,
            avg_connection_time: Duration::ZERO,
            avg_lifetime: Duration::ZERO,
            health_check_failures: 0,
        }
    }
}

impl Default for FactoryStats {
    fn default() -> Self {
        Self {
            total_created: 0,
            creation_failures: 0,
            avg_creation_time: Duration::ZERO,
            success_rate: 100.0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_connection_pool_creation() {
        let config = PoolConfig::default();
        let pool = ConnectionPool::new(config).await;
        assert!(pool.is_ok());
    }
    
    #[test]
    fn test_pool_config_default() {
        let config = PoolConfig::default();
        assert_eq!(config.max_connections_per_target, 100);
        assert_eq!(config.min_idle_connections, 5);
        assert!(config.validate_connections);
    }
    
    #[test]
    fn test_scaling_config() {
        let config = PoolScalingConfig::default();
        assert!(config.enabled);
        assert_eq!(config.scale_up_threshold, 80.0);
        assert_eq!(config.scale_down_threshold, 30.0);
    }
}
