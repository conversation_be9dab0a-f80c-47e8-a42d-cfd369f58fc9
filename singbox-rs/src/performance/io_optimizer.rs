//! I/O optimization for maximum throughput and minimal latency
//!
//! This module provides advanced I/O optimization techniques including
//! zero-copy operations, vectored I/O, and intelligent buffering.

use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tokio::io::{AsyncRead, AsyncWrite, AsyncReadExt, AsyncWriteExt};
use serde::{Deserialize, Serialize};

/// I/O optimization configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IoConfig {
    /// Enable I/O optimization
    pub enabled: bool,
    
    /// Buffer size for reads
    pub read_buffer_size: usize,
    
    /// Buffer size for writes
    pub write_buffer_size: usize,
    
    /// Enable vectored I/O
    pub enable_vectored_io: bool,
    
    /// Enable zero-copy operations
    pub enable_zero_copy: bool,
    
    /// I/O timeout
    pub io_timeout: Duration,
    
    /// Batch size for operations
    pub batch_size: usize,
    
    /// Enable I/O coalescing
    pub enable_coalescing: bool,
    
    /// Coalescing delay
    pub coalescing_delay: Duration,
    
    /// Enable adaptive buffering
    pub adaptive_buffering: bool,
    
    /// Compression configuration
    pub compression: CompressionConfig,
    
    /// Encryption configuration
    pub encryption: EncryptionConfig,
}

/// Compression configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompressionConfig {
    /// Enable compression
    pub enabled: bool,
    
    /// Compression algorithm
    pub algorithm: CompressionAlgorithm,
    
    /// Compression level (1-9)
    pub level: u8,
    
    /// Minimum size for compression
    pub min_size: usize,
    
    /// Compression threshold (ratio)
    pub threshold: f64,
}

/// Compression algorithms
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CompressionAlgorithm {
    /// Gzip compression
    Gzip,
    
    /// Zstd compression
    Zstd,
    
    /// LZ4 compression
    Lz4,
    
    /// Brotli compression
    Brotli,
}

/// Encryption configuration for I/O
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptionConfig {
    /// Enable encryption
    pub enabled: bool,
    
    /// Encryption algorithm
    pub algorithm: String,
    
    /// Key size
    pub key_size: usize,
    
    /// Enable hardware acceleration
    pub hardware_acceleration: bool,
}

/// I/O statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IoStats {
    /// Total bytes read
    pub bytes_read: u64,
    
    /// Total bytes written
    pub bytes_written: u64,
    
    /// Total read operations
    pub read_operations: u64,
    
    /// Total write operations
    pub write_operations: u64,
    
    /// Average read latency
    pub avg_read_latency: Duration,
    
    /// Average write latency
    pub avg_write_latency: Duration,
    
    /// Read throughput (bytes/sec)
    pub read_throughput: f64,
    
    /// Write throughput (bytes/sec)
    pub write_throughput: f64,
    
    /// Vectored I/O operations
    pub vectored_operations: u64,
    
    /// Zero-copy operations
    pub zero_copy_operations: u64,
    
    /// Compression statistics
    pub compression_stats: CompressionStats,
    
    /// I/O errors
    pub io_errors: u64,
    
    /// Buffer efficiency
    pub buffer_efficiency: f64,
}

/// Compression statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompressionStats {
    /// Total compressed bytes
    pub compressed_bytes: u64,
    
    /// Total uncompressed bytes
    pub uncompressed_bytes: u64,
    
    /// Compression ratio
    pub compression_ratio: f64,
    
    /// Compression operations
    pub compression_operations: u64,
    
    /// Decompression operations
    pub decompression_operations: u64,
    
    /// Average compression time
    pub avg_compression_time: Duration,
    
    /// Average decompression time
    pub avg_decompression_time: Duration,
}

/// I/O optimizer
pub struct IoOptimizer {
    /// Configuration
    config: IoConfig,
    
    /// I/O statistics
    stats: Arc<RwLock<IoStats>>,
    
    /// Read buffers
    read_buffers: Arc<RwLock<VecDeque<Vec<u8>>>>,
    
    /// Write buffers
    write_buffers: Arc<RwLock<VecDeque<Vec<u8>>>>,
    
    /// Pending operations
    pending_operations: Arc<RwLock<HashMap<String, PendingOperation>>>,
    
    /// Compression engine
    compression_engine: Arc<CompressionEngine>,
    
    /// I/O tasks
    tasks: Arc<RwLock<Vec<tokio::task::JoinHandle<()>>>>,
}

/// Pending I/O operation
#[derive(Debug)]
pub struct PendingOperation {
    /// Operation ID
    pub id: String,
    
    /// Operation type
    pub operation_type: OperationType,
    
    /// Data buffer
    pub data: Vec<u8>,
    
    /// Timestamp
    pub timestamp: Instant,
    
    /// Priority
    pub priority: u8,
}

/// I/O operation types
#[derive(Debug, Clone)]
pub enum OperationType {
    /// Read operation
    Read,
    
    /// Write operation
    Write,
    
    /// Vectored read
    VectoredRead,
    
    /// Vectored write
    VectoredWrite,
}

/// Compression engine
pub struct CompressionEngine {
    /// Configuration
    config: CompressionConfig,
    
    /// Compression statistics
    stats: Arc<RwLock<CompressionStats>>,
}

/// Optimized I/O stream wrapper
pub struct OptimizedStream<T> {
    /// Inner stream
    inner: T,
    
    /// I/O optimizer reference
    optimizer: Arc<IoOptimizer>,
    
    /// Stream statistics
    stats: StreamStats,
    
    /// Read buffer
    read_buffer: Vec<u8>,
    
    /// Write buffer
    write_buffer: Vec<u8>,
    
    /// Pending writes
    pending_writes: VecDeque<Vec<u8>>,
}

/// Stream-specific statistics
#[derive(Debug, Clone)]
pub struct StreamStats {
    /// Bytes read
    pub bytes_read: u64,
    
    /// Bytes written
    pub bytes_written: u64,
    
    /// Read operations
    pub read_ops: u64,
    
    /// Write operations
    pub write_ops: u64,
    
    /// Stream start time
    pub start_time: Instant,
}

impl IoOptimizer {
    /// Create a new I/O optimizer
    pub fn new(config: IoConfig) -> Self {
        Self {
            config: config.clone(),
            stats: Arc::new(RwLock::new(IoStats::default())),
            read_buffers: Arc::new(RwLock::new(VecDeque::new())),
            write_buffers: Arc::new(RwLock::new(VecDeque::new())),
            pending_operations: Arc::new(RwLock::new(HashMap::new())),
            compression_engine: Arc::new(CompressionEngine::new(config.compression)),
            tasks: Arc::new(RwLock::new(Vec::new())),
        }
    }
    
    /// Start I/O optimization
    pub async fn start(&self) -> Result<(), String> {
        if !self.config.enabled {
            return Ok(());
        }
        
        println!("⚡ Starting I/O optimizer...");
        
        // Start buffer management
        self.start_buffer_management().await;
        
        // Start operation coalescing
        if self.config.enable_coalescing {
            self.start_operation_coalescing().await;
        }
        
        // Start statistics collection
        self.start_stats_collection().await;
        
        println!("⚡ I/O optimizer started");
        Ok(())
    }
    
    /// Stop I/O optimization
    pub async fn stop(&self) {
        println!("⚡ Stopping I/O optimizer...");
        
        // Stop all tasks
        let mut tasks = self.tasks.write().await;
        for task in tasks.drain(..) {
            task.abort();
        }
        
        // Clear buffers
        self.read_buffers.write().await.clear();
        self.write_buffers.write().await.clear();
        self.pending_operations.write().await.clear();
        
        println!("⚡ I/O optimizer stopped");
    }
    
    /// Optimize a stream
    pub fn optimize_stream<T>(&self, stream: T) -> OptimizedStream<T>
    where
        T: AsyncRead + AsyncWrite + Unpin,
    {
        OptimizedStream::new(stream, Arc::new(self.clone()))
    }
    
    /// Get an optimized read buffer
    pub async fn get_read_buffer(&self) -> Vec<u8> {
        let mut buffers = self.read_buffers.write().await;
        
        if let Some(buffer) = buffers.pop_front() {
            buffer
        } else {
            vec![0u8; self.config.read_buffer_size]
        }
    }
    
    /// Return a read buffer
    pub async fn return_read_buffer(&self, mut buffer: Vec<u8>) {
        // Clear buffer for security
        buffer.fill(0);
        
        let mut buffers = self.read_buffers.write().await;
        if buffers.len() < 100 { // Limit buffer pool size
            buffers.push_back(buffer);
        }
    }
    
    /// Get an optimized write buffer
    pub async fn get_write_buffer(&self) -> Vec<u8> {
        let mut buffers = self.write_buffers.write().await;
        
        if let Some(buffer) = buffers.pop_front() {
            buffer
        } else {
            Vec::with_capacity(self.config.write_buffer_size)
        }
    }
    
    /// Return a write buffer
    pub async fn return_write_buffer(&self, mut buffer: Vec<u8>) {
        // Clear buffer for security
        buffer.clear();
        
        let mut buffers = self.write_buffers.write().await;
        if buffers.len() < 100 { // Limit buffer pool size
            buffers.push_back(buffer);
        }
    }
    
    /// Start buffer management
    async fn start_buffer_management(&self) {
        let read_buffers = Arc::clone(&self.read_buffers);
        let write_buffers = Arc::clone(&self.write_buffers);
        let config = self.config.clone();
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));
            
            loop {
                interval.tick().await;
                
                // Pre-allocate buffers if needed
                {
                    let mut read_buf = read_buffers.write().await;
                    while read_buf.len() < 10 {
                        read_buf.push_back(vec![0u8; config.read_buffer_size]);
                    }
                }
                
                {
                    let mut write_buf = write_buffers.write().await;
                    while write_buf.len() < 10 {
                        write_buf.push_back(Vec::with_capacity(config.write_buffer_size));
                    }
                }
            }
        });
        
        self.tasks.write().await.push(task);
    }
    
    /// Start operation coalescing
    async fn start_operation_coalescing(&self) {
        let pending_operations = Arc::clone(&self.pending_operations);
        let coalescing_delay = self.config.coalescing_delay;
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(coalescing_delay);
            
            loop {
                interval.tick().await;
                
                // Process pending operations
                let mut operations = pending_operations.write().await;
                let now = Instant::now();
                
                // Find operations ready for coalescing
                let ready_ops: Vec<_> = operations.iter()
                    .filter(|(_, op)| now.duration_since(op.timestamp) >= coalescing_delay)
                    .map(|(id, _)| id.clone())
                    .collect();
                
                // Process ready operations
                for op_id in ready_ops {
                    if let Some(operation) = operations.remove(&op_id) {
                        // Process the operation
                        Self::process_coalesced_operation(operation).await;
                    }
                }
            }
        });
        
        self.tasks.write().await.push(task);
    }
    
    /// Process coalesced operation
    async fn process_coalesced_operation(operation: PendingOperation) {
        // Implementation would process the coalesced operation
        println!("⚡ Processing coalesced operation: {}", operation.id);
    }
    
    /// Start statistics collection
    async fn start_stats_collection(&self) {
        let stats = Arc::clone(&self.stats);
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(10));
            let mut last_read_bytes = 0u64;
            let mut last_write_bytes = 0u64;
            let mut last_time = Instant::now();
            
            loop {
                interval.tick().await;
                
                let now = Instant::now();
                let elapsed = now.duration_since(last_time);
                
                // Calculate throughput
                {
                    let mut stats_guard = stats.write().await;
                    
                    let read_diff = stats_guard.bytes_read - last_read_bytes;
                    let write_diff = stats_guard.bytes_written - last_write_bytes;
                    
                    if elapsed.as_secs_f64() > 0.0 {
                        stats_guard.read_throughput = read_diff as f64 / elapsed.as_secs_f64();
                        stats_guard.write_throughput = write_diff as f64 / elapsed.as_secs_f64();
                    }
                    
                    last_read_bytes = stats_guard.bytes_read;
                    last_write_bytes = stats_guard.bytes_written;
                }
                
                last_time = now;
            }
        });
        
        self.tasks.write().await.push(task);
    }
    
    /// Optimize I/O operations
    pub async fn optimize(&self) -> Result<(), String> {
        println!("⚡ Running I/O optimization...");
        
        // Optimize buffer sizes based on usage patterns
        self.optimize_buffer_sizes().await;
        
        // Optimize compression settings
        if self.config.compression.enabled {
            self.compression_engine.optimize().await;
        }
        
        // Clear pending operations that are too old
        self.cleanup_pending_operations().await;
        
        println!("⚡ I/O optimization completed");
        Ok(())
    }
    
    /// Optimize buffer sizes based on usage patterns
    async fn optimize_buffer_sizes(&self) {
        let stats = self.stats.read().await;
        
        // Analyze I/O patterns and adjust buffer sizes
        if stats.read_operations > 0 {
            let avg_read_size = stats.bytes_read / stats.read_operations;
            println!("⚡ Average read size: {} bytes", avg_read_size);
        }
        
        if stats.write_operations > 0 {
            let avg_write_size = stats.bytes_written / stats.write_operations;
            println!("⚡ Average write size: {} bytes", avg_write_size);
        }
    }
    
    /// Cleanup old pending operations
    async fn cleanup_pending_operations(&self) {
        let mut operations = self.pending_operations.write().await;
        let now = Instant::now();
        
        operations.retain(|_, op| {
            now.duration_since(op.timestamp) < Duration::from_secs(60)
        });
    }
    
    /// Get I/O statistics
    pub async fn get_stats(&self) -> IoStats {
        self.stats.read().await.clone()
    }
}

impl<T> OptimizedStream<T>
where
    T: AsyncRead + AsyncWrite + Unpin,
{
    /// Create a new optimized stream
    pub fn new(inner: T, optimizer: Arc<IoOptimizer>) -> Self {
        let config = &optimizer.config;
        
        Self {
            inner,
            optimizer,
            stats: StreamStats {
                bytes_read: 0,
                bytes_written: 0,
                read_ops: 0,
                write_ops: 0,
                start_time: Instant::now(),
            },
            read_buffer: vec![0u8; config.read_buffer_size],
            write_buffer: Vec::with_capacity(config.write_buffer_size),
            pending_writes: VecDeque::new(),
        }
    }
    
    /// Optimized read operation
    pub async fn read_optimized(&mut self, buf: &mut [u8]) -> Result<usize, std::io::Error> {
        let start_time = Instant::now();
        
        // Use internal buffer for better performance
        let n = if self.optimizer.config.enable_zero_copy {
            // Zero-copy read (platform-specific implementation)
            self.inner.read(buf).await?
        } else {
            // Buffered read
            self.inner.read(buf).await?
        };
        
        // Update statistics
        let read_latency = start_time.elapsed();
        self.stats.bytes_read += n as u64;
        self.stats.read_ops += 1;
        
        // Update global statistics
        {
            let mut global_stats = self.optimizer.stats.write().await;
            global_stats.bytes_read += n as u64;
            global_stats.read_operations += 1;
            
            // Update average latency
            let total_ops = global_stats.read_operations;
            global_stats.avg_read_latency = Duration::from_nanos(
                (global_stats.avg_read_latency.as_nanos() as u64 * (total_ops - 1) + 
                 read_latency.as_nanos() as u64) / total_ops
            );
        }
        
        Ok(n)
    }
    
    /// Optimized write operation
    pub async fn write_optimized(&mut self, buf: &[u8]) -> Result<usize, std::io::Error> {
        let start_time = Instant::now();
        
        // Compress data if enabled and beneficial
        let data_to_write = if self.optimizer.config.compression.enabled && 
                              buf.len() >= self.optimizer.config.compression.min_size {
            match self.optimizer.compression_engine.compress(buf).await {
                Ok(compressed) => {
                    if compressed.len() < buf.len() {
                        compressed
                    } else {
                        buf.to_vec()
                    }
                },
                Err(_) => buf.to_vec(),
            }
        } else {
            buf.to_vec()
        };
        
        // Perform write
        let n = if self.optimizer.config.enable_coalescing {
            // Add to pending writes for coalescing
            self.pending_writes.push_back(data_to_write);
            
            // Flush if buffer is full or timeout reached
            if self.pending_writes.len() >= self.optimizer.config.batch_size {
                self.flush_pending_writes().await?
            } else {
                buf.len()
            }
        } else {
            // Direct write
            self.inner.write(&data_to_write).await?
        };
        
        // Update statistics
        let write_latency = start_time.elapsed();
        self.stats.bytes_written += n as u64;
        self.stats.write_ops += 1;
        
        // Update global statistics
        {
            let mut global_stats = self.optimizer.stats.write().await;
            global_stats.bytes_written += n as u64;
            global_stats.write_operations += 1;
            
            // Update average latency
            let total_ops = global_stats.write_operations;
            global_stats.avg_write_latency = Duration::from_nanos(
                (global_stats.avg_write_latency.as_nanos() as u64 * (total_ops - 1) + 
                 write_latency.as_nanos() as u64) / total_ops
            );
        }
        
        Ok(n)
    }
    
    /// Flush pending writes
    async fn flush_pending_writes(&mut self) -> Result<usize, std::io::Error> {
        if self.pending_writes.is_empty() {
            return Ok(0);
        }
        
        // Coalesce pending writes
        let mut coalesced_data = Vec::new();
        while let Some(data) = self.pending_writes.pop_front() {
            coalesced_data.extend_from_slice(&data);
        }
        
        // Write coalesced data
        let n = self.inner.write(&coalesced_data).await?;
        self.inner.flush().await?;
        
        Ok(n)
    }
    
    /// Get stream statistics
    pub fn get_stream_stats(&self) -> &StreamStats {
        &self.stats
    }
}

impl CompressionEngine {
    /// Create a new compression engine
    pub fn new(config: CompressionConfig) -> Self {
        Self {
            config,
            stats: Arc::new(RwLock::new(CompressionStats::default())),
        }
    }
    
    /// Compress data
    pub async fn compress(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        if !self.config.enabled || data.len() < self.config.min_size {
            return Ok(data.to_vec());
        }
        
        let start_time = Instant::now();
        
        let compressed = match self.config.algorithm {
            CompressionAlgorithm::Gzip => {
                use flate2::write::GzEncoder;
                use flate2::Compression;
                use std::io::Write;
                
                let mut encoder = GzEncoder::new(Vec::new(), Compression::new(self.config.level as u32));
                encoder.write_all(data)
                    .map_err(|e| format!("Gzip compression failed: {}", e))?;
                encoder.finish()
                    .map_err(|e| format!("Gzip compression failed: {}", e))?
            },
            CompressionAlgorithm::Zstd => {
                zstd::encode_all(data, self.config.level as i32)
                    .map_err(|e| format!("Zstd compression failed: {}", e))?
            },
            CompressionAlgorithm::Lz4 => {
                lz4_flex::compress(data)
            },
            CompressionAlgorithm::Brotli => {
                use brotli::enc::BrotliEncoderParams;
                let params = BrotliEncoderParams {
                    quality: self.config.level as i32,
                    ..Default::default()
                };
                
                let mut compressed = Vec::new();
                brotli::BrotliCompress(&mut std::io::Cursor::new(data), &mut compressed, &params)
                    .map_err(|e| format!("Brotli compression failed: {}", e))?;
                compressed
            },
        };
        
        let compression_time = start_time.elapsed();
        
        // Check if compression is beneficial
        let compression_ratio = compressed.len() as f64 / data.len() as f64;
        if compression_ratio > self.config.threshold {
            // Compression not beneficial, return original data
            return Ok(data.to_vec());
        }
        
        // Update statistics
        {
            let mut stats = self.stats.write().await;
            stats.compressed_bytes += compressed.len() as u64;
            stats.uncompressed_bytes += data.len() as u64;
            stats.compression_operations += 1;
            
            // Update average compression time
            let total_ops = stats.compression_operations;
            stats.avg_compression_time = Duration::from_nanos(
                (stats.avg_compression_time.as_nanos() as u64 * (total_ops - 1) + 
                 compression_time.as_nanos() as u64) / total_ops
            );
            
            // Update compression ratio
            if stats.uncompressed_bytes > 0 {
                stats.compression_ratio = stats.compressed_bytes as f64 / stats.uncompressed_bytes as f64;
            }
        }
        
        Ok(compressed)
    }
    
    /// Decompress data
    pub async fn decompress(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        let start_time = Instant::now();
        
        let decompressed = match self.config.algorithm {
            CompressionAlgorithm::Gzip => {
                use flate2::read::GzDecoder;
                use std::io::Read;
                
                let mut decoder = GzDecoder::new(data);
                let mut decompressed = Vec::new();
                decoder.read_to_end(&mut decompressed)
                    .map_err(|e| format!("Gzip decompression failed: {}", e))?;
                decompressed
            },
            CompressionAlgorithm::Zstd => {
                zstd::decode_all(data)
                    .map_err(|e| format!("Zstd decompression failed: {}", e))?
            },
            CompressionAlgorithm::Lz4 => {
                lz4_flex::decompress_size_prepended(data)
                    .map_err(|e| format!("LZ4 decompression failed: {}", e))?
            },
            CompressionAlgorithm::Brotli => {
                let mut decompressed = Vec::new();
                brotli::BrotliDecompress(&mut std::io::Cursor::new(data), &mut decompressed)
                    .map_err(|e| format!("Brotli decompression failed: {}", e))?;
                decompressed
            },
        };
        
        let decompression_time = start_time.elapsed();
        
        // Update statistics
        {
            let mut stats = self.stats.write().await;
            stats.decompression_operations += 1;
            
            // Update average decompression time
            let total_ops = stats.decompression_operations;
            stats.avg_decompression_time = Duration::from_nanos(
                (stats.avg_decompression_time.as_nanos() as u64 * (total_ops - 1) + 
                 decompression_time.as_nanos() as u64) / total_ops
            );
        }
        
        Ok(decompressed)
    }
    
    /// Optimize compression settings
    pub async fn optimize(&self) {
        let stats = self.stats.read().await;
        
        // Analyze compression effectiveness
        if stats.compression_operations > 100 {
            println!("⚡ Compression ratio: {:.2}", stats.compression_ratio);
            println!("⚡ Average compression time: {:?}", stats.avg_compression_time);
            
            // Could adjust compression level based on performance
        }
    }
}

impl Clone for IoOptimizer {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            stats: Arc::clone(&self.stats),
            read_buffers: Arc::clone(&self.read_buffers),
            write_buffers: Arc::clone(&self.write_buffers),
            pending_operations: Arc::clone(&self.pending_operations),
            compression_engine: Arc::clone(&self.compression_engine),
            tasks: Arc::clone(&self.tasks),
        }
    }
}

impl Default for IoConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            read_buffer_size: 8192,
            write_buffer_size: 8192,
            enable_vectored_io: true,
            enable_zero_copy: false, // Platform-dependent
            io_timeout: Duration::from_secs(30),
            batch_size: 10,
            enable_coalescing: true,
            coalescing_delay: Duration::from_millis(10),
            adaptive_buffering: true,
            compression: CompressionConfig::default(),
            encryption: EncryptionConfig::default(),
        }
    }
}

impl Default for CompressionConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            algorithm: CompressionAlgorithm::Zstd,
            level: 3,
            min_size: 1024,
            threshold: 0.9,
        }
    }
}

impl Default for EncryptionConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            algorithm: "aes-256-gcm".to_string(),
            key_size: 32,
            hardware_acceleration: true,
        }
    }
}

impl Default for IoStats {
    fn default() -> Self {
        Self {
            bytes_read: 0,
            bytes_written: 0,
            read_operations: 0,
            write_operations: 0,
            avg_read_latency: Duration::ZERO,
            avg_write_latency: Duration::ZERO,
            read_throughput: 0.0,
            write_throughput: 0.0,
            vectored_operations: 0,
            zero_copy_operations: 0,
            compression_stats: CompressionStats::default(),
            io_errors: 0,
            buffer_efficiency: 100.0,
        }
    }
}

impl Default for CompressionStats {
    fn default() -> Self {
        Self {
            compressed_bytes: 0,
            uncompressed_bytes: 0,
            compression_ratio: 1.0,
            compression_operations: 0,
            decompression_operations: 0,
            avg_compression_time: Duration::ZERO,
            avg_decompression_time: Duration::ZERO,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_io_config_default() {
        let config = IoConfig::default();
        assert!(config.enabled);
        assert_eq!(config.read_buffer_size, 8192);
        assert_eq!(config.write_buffer_size, 8192);
        assert!(config.enable_vectored_io);
    }
    
    #[test]
    fn test_compression_config() {
        let config = CompressionConfig::default();
        assert!(!config.enabled);
        assert!(matches!(config.algorithm, CompressionAlgorithm::Zstd));
        assert_eq!(config.level, 3);
        assert_eq!(config.min_size, 1024);
    }
    
    #[tokio::test]
    async fn test_io_optimizer_creation() {
        let config = IoConfig::default();
        let optimizer = IoOptimizer::new(config);
        
        let stats = optimizer.get_stats().await;
        assert_eq!(stats.bytes_read, 0);
        assert_eq!(stats.bytes_written, 0);
    }
    
    #[tokio::test]
    async fn test_buffer_operations() {
        let config = IoConfig::default();
        let optimizer = IoOptimizer::new(config);
        
        // Get read buffer
        let buffer = optimizer.get_read_buffer().await;
        assert_eq!(buffer.len(), 8192);
        
        // Return buffer
        optimizer.return_read_buffer(buffer).await;
        
        // Get write buffer
        let write_buffer = optimizer.get_write_buffer().await;
        assert_eq!(write_buffer.capacity(), 8192);
        
        // Return write buffer
        optimizer.return_write_buffer(write_buffer).await;
    }
}
