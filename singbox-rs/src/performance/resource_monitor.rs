//! System resource monitoring and alerting
//!
//! This module provides comprehensive system resource monitoring
//! including CPU, memory, network, and disk usage tracking.

use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

/// Resource monitoring configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ResourceConfig {
    /// Enable resource monitoring
    pub enabled: bool,
    
    /// Monitoring interval
    pub monitoring_interval: Duration,
    
    /// CPU monitoring configuration
    pub cpu: CpuMonitorConfig,
    
    /// Memory monitoring configuration
    pub memory: MemoryMonitorConfig,
    
    /// Network monitoring configuration
    pub network: NetworkMonitorConfig,
    
    /// Disk monitoring configuration
    pub disk: DiskMonitorConfig,
    
    /// Alert configuration
    pub alerts: AlertConfig,
    
    /// History retention
    pub history_retention: Duration,
}

/// CPU monitoring configuration
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CpuMonitorConfig {
    /// Enable CPU monitoring
    pub enabled: bool,
    
    /// CPU usage alert threshold
    pub alert_threshold: f64,
    
    /// Monitor per-core usage
    pub per_core_monitoring: bool,
    
    /// CPU frequency monitoring
    pub frequency_monitoring: bool,
}

/// Memory monitoring configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryMonitorConfig {
    /// Enable memory monitoring
    pub enabled: bool,
    
    /// Memory usage alert threshold
    pub alert_threshold: f64,
    
    /// Monitor swap usage
    pub monitor_swap: bool,
    
    /// Monitor memory fragmentation
    pub monitor_fragmentation: bool,
}

/// Network monitoring configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkMonitorConfig {
    /// Enable network monitoring
    pub enabled: bool,
    
    /// Monitor network interfaces
    pub interfaces: Vec<String>,
    
    /// Bandwidth alert threshold (bytes/sec)
    pub bandwidth_threshold: u64,
    
    /// Monitor connection counts
    pub monitor_connections: bool,
}

/// Disk monitoring configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskMonitorConfig {
    /// Enable disk monitoring
    pub enabled: bool,
    
    /// Disk usage alert threshold
    pub usage_threshold: f64,
    
    /// Monitor disk I/O
    pub monitor_io: bool,
    
    /// Monitor disk paths
    pub paths: Vec<String>,
}

/// Alert configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertConfig {
    /// Enable alerts
    pub enabled: bool,
    
    /// Alert cooldown period
    pub cooldown: Duration,
    
    /// Alert channels
    pub channels: Vec<AlertChannel>,
    
    /// Alert severity levels
    pub severity_levels: HashMap<String, AlertSeverity>,
}

/// Alert channels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertChannel {
    /// Log alerts
    Log,
    
    /// Email alerts
    Email { smtp_config: SmtpConfig },
    
    /// Webhook alerts
    Webhook { url: String, headers: HashMap<String, String> },
    
    /// Discord alerts
    Discord { webhook_url: String },
    
    /// Slack alerts
    Slack { webhook_url: String },
}

/// SMTP configuration for email alerts
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SmtpConfig {
    /// SMTP server
    pub server: String,
    
    /// SMTP port
    pub port: u16,
    
    /// Username
    pub username: String,
    
    /// Password
    pub password: String,
    
    /// From address
    pub from: String,
    
    /// To addresses
    pub to: Vec<String>,
    
    /// Enable TLS
    pub tls: bool,
}

/// Alert severity levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    /// Low severity
    Low,
    
    /// Medium severity
    Medium,
    
    /// High severity
    High,
    
    /// Critical severity
    Critical,
}

/// Resource statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceStats {
    /// CPU usage percentage
    pub cpu_usage: f64,
    
    /// Per-core CPU usage
    pub cpu_per_core: Vec<f64>,
    
    /// CPU frequency (MHz)
    pub cpu_frequency: f64,
    
    /// Memory usage percentage
    pub memory_usage: f64,
    
    /// Memory usage in bytes
    pub memory_bytes: u64,
    
    /// Swap usage percentage
    pub swap_usage: f64,
    
    /// Network statistics
    pub network: NetworkStats,
    
    /// Disk statistics
    pub disk: DiskStats,
    
    /// System load average
    pub load_average: (f64, f64, f64), // 1min, 5min, 15min
    
    /// Process count
    pub process_count: u32,
    
    /// Thread count
    pub thread_count: u32,
    
    /// File descriptor count
    pub fd_count: u32,
    
    /// Timestamp
    pub timestamp: SystemTime,
}

/// Network statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkStats {
    /// Bytes received
    pub bytes_received: u64,
    
    /// Bytes sent
    pub bytes_sent: u64,
    
    /// Packets received
    pub packets_received: u64,
    
    /// Packets sent
    pub packets_sent: u64,
    
    /// Network errors
    pub errors: u64,
    
    /// Active connections
    pub active_connections: u32,
    
    /// Interface statistics
    pub interfaces: HashMap<String, InterfaceStats>,
}

/// Network interface statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InterfaceStats {
    /// Interface name
    pub name: String,
    
    /// Bytes received
    pub rx_bytes: u64,
    
    /// Bytes transmitted
    pub tx_bytes: u64,
    
    /// Packets received
    pub rx_packets: u64,
    
    /// Packets transmitted
    pub tx_packets: u64,
    
    /// Receive errors
    pub rx_errors: u64,
    
    /// Transmit errors
    pub tx_errors: u64,
    
    /// Interface speed (Mbps)
    pub speed: u64,
}

/// Disk statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskStats {
    /// Total disk space
    pub total_space: u64,
    
    /// Used disk space
    pub used_space: u64,
    
    /// Available disk space
    pub available_space: u64,
    
    /// Disk usage percentage
    pub usage_percentage: f64,
    
    /// Disk I/O statistics
    pub io: DiskIoStats,
    
    /// Per-path statistics
    pub paths: HashMap<String, PathStats>,
}

/// Disk I/O statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskIoStats {
    /// Read operations
    pub read_ops: u64,
    
    /// Write operations
    pub write_ops: u64,
    
    /// Bytes read
    pub bytes_read: u64,
    
    /// Bytes written
    pub bytes_written: u64,
    
    /// Average read latency
    pub avg_read_latency: Duration,
    
    /// Average write latency
    pub avg_write_latency: Duration,
    
    /// I/O queue depth
    pub queue_depth: u32,
}

/// Path-specific disk statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PathStats {
    /// Path
    pub path: String,
    
    /// Total space
    pub total_space: u64,
    
    /// Used space
    pub used_space: u64,
    
    /// Available space
    pub available_space: u64,
    
    /// Usage percentage
    pub usage_percentage: f64,
}

/// Resource monitor
pub struct ResourceMonitor {
    /// Configuration
    config: ResourceConfig,
    
    /// Resource statistics
    stats: Arc<RwLock<ResourceStats>>,
    
    /// Statistics history
    history: Arc<RwLock<VecDeque<ResourceStats>>>,
    
    /// Alert manager
    alert_manager: Arc<RwLock<AlertManager>>,
    
    /// Monitoring tasks
    tasks: Arc<RwLock<Vec<tokio::task::JoinHandle<()>>>>,
}

/// Alert manager
pub struct AlertManager {
    /// Alert configuration
    config: AlertConfig,
    
    /// Last alert times
    last_alerts: HashMap<String, Instant>,
    
    /// Alert history
    alert_history: VecDeque<Alert>,
}

/// Alert information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Alert {
    /// Alert ID
    pub id: String,
    
    /// Alert type
    pub alert_type: String,
    
    /// Alert message
    pub message: String,
    
    /// Severity
    pub severity: AlertSeverity,
    
    /// Timestamp
    pub timestamp: SystemTime,
    
    /// Resource values
    pub values: HashMap<String, f64>,
    
    /// Resolved
    pub resolved: bool,
}

impl ResourceMonitor {
    /// Create a new resource monitor
    pub async fn new(config: ResourceConfig) -> Result<Self, String> {
        let alert_manager = AlertManager::new(config.alerts.clone());
        
        Ok(Self {
            config,
            stats: Arc::new(RwLock::new(ResourceStats::default())),
            history: Arc::new(RwLock::new(VecDeque::new())),
            alert_manager: Arc::new(RwLock::new(alert_manager)),
            tasks: Arc::new(RwLock::new(Vec::new())),
        })
    }
    
    /// Start resource monitoring
    pub async fn start(&self) -> Result<(), String> {
        if !self.config.enabled {
            return Ok(());
        }
        
        println!("📊 Starting resource monitor...");
        
        // Start CPU monitoring
        if self.config.cpu.enabled {
            self.start_cpu_monitoring().await;
        }
        
        // Start memory monitoring
        if self.config.memory.enabled {
            self.start_memory_monitoring().await;
        }
        
        // Start network monitoring
        if self.config.network.enabled {
            self.start_network_monitoring().await;
        }
        
        // Start disk monitoring
        if self.config.disk.enabled {
            self.start_disk_monitoring().await;
        }
        
        // Start alert processing
        if self.config.alerts.enabled {
            self.start_alert_processing().await;
        }
        
        // Start history cleanup
        self.start_history_cleanup().await;
        
        println!("📊 Resource monitor started");
        Ok(())
    }
    
    /// Stop resource monitoring
    pub async fn stop(&self) {
        println!("📊 Stopping resource monitor...");
        
        // Stop all tasks
        let mut tasks = self.tasks.write().await;
        for task in tasks.drain(..) {
            task.abort();
        }
        
        println!("📊 Resource monitor stopped");
    }
    
    /// Start CPU monitoring
    async fn start_cpu_monitoring(&self) {
        let stats = Arc::clone(&self.stats);
        let config = self.config.clone();
        let alert_manager = Arc::clone(&self.alert_manager);
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.monitoring_interval);
            
            loop {
                interval.tick().await;
                
                // Get CPU usage
                let cpu_usage = Self::get_cpu_usage().await;
                let per_core_usage = if config.cpu.per_core_monitoring {
                    Self::get_per_core_cpu_usage().await
                } else {
                    Vec::new()
                };
                
                // Update statistics
                {
                    let mut stats_guard = stats.write().await;
                    stats_guard.cpu_usage = cpu_usage;
                    stats_guard.cpu_per_core = per_core_usage;
                    
                    if config.cpu.frequency_monitoring {
                        stats_guard.cpu_frequency = Self::get_cpu_frequency().await;
                    }
                }
                
                // Check for alerts
                if cpu_usage > config.cpu.alert_threshold {
                    let mut alert_mgr = alert_manager.write().await;
                    alert_mgr.trigger_alert(
                        "cpu_usage_high".to_string(),
                        format!("CPU usage is {:.1}%", cpu_usage),
                        AlertSeverity::High,
                        [("cpu_usage".to_string(), cpu_usage)].iter().cloned().collect(),
                    ).await;
                }
            }
        });
        
        self.tasks.write().await.push(task);
    }
    
    /// Start memory monitoring
    async fn start_memory_monitoring(&self) {
        let stats = Arc::clone(&self.stats);
        let config = self.config.clone();
        let alert_manager = Arc::clone(&self.alert_manager);
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.monitoring_interval);
            
            loop {
                interval.tick().await;
                
                // Get memory usage
                let (memory_usage, memory_bytes) = Self::get_memory_usage().await;
                let swap_usage = if config.memory.monitor_swap {
                    Self::get_swap_usage().await
                } else {
                    0.0
                };
                
                // Update statistics
                {
                    let mut stats_guard = stats.write().await;
                    stats_guard.memory_usage = memory_usage;
                    stats_guard.memory_bytes = memory_bytes;
                    stats_guard.swap_usage = swap_usage;
                }
                
                // Check for alerts
                if memory_usage > config.memory.alert_threshold {
                    let mut alert_mgr = alert_manager.write().await;
                    alert_mgr.trigger_alert(
                        "memory_usage_high".to_string(),
                        format!("Memory usage is {:.1}%", memory_usage),
                        AlertSeverity::High,
                        [("memory_usage".to_string(), memory_usage)].iter().cloned().collect(),
                    ).await;
                }
            }
        });
        
        self.tasks.write().await.push(task);
    }
    
    /// Start network monitoring
    async fn start_network_monitoring(&self) {
        let stats = Arc::clone(&self.stats);
        let config = self.config.clone();
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.monitoring_interval);
            let mut last_stats = NetworkStats::default();
            
            loop {
                interval.tick().await;
                
                // Get network statistics
                let network_stats = Self::get_network_stats(&config.network).await;
                
                // Calculate bandwidth
                let elapsed = config.monitoring_interval.as_secs_f64();
                let rx_bandwidth = if elapsed > 0.0 {
                    (network_stats.bytes_received - last_stats.bytes_received) as f64 / elapsed
                } else {
                    0.0
                };
                
                // Update statistics
                {
                    let mut stats_guard = stats.write().await;
                    stats_guard.network = network_stats.clone();
                }
                
                last_stats = network_stats;
            }
        });
        
        self.tasks.write().await.push(task);
    }
    
    /// Start disk monitoring
    async fn start_disk_monitoring(&self) {
        let stats = Arc::clone(&self.stats);
        let config = self.config.clone();
        let alert_manager = Arc::clone(&self.alert_manager);
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.monitoring_interval);
            
            loop {
                interval.tick().await;
                
                // Get disk statistics
                let disk_stats = Self::get_disk_stats(&config.disk).await;
                
                // Update statistics
                {
                    let mut stats_guard = stats.write().await;
                    stats_guard.disk = disk_stats.clone();
                }
                
                // Check for disk usage alerts
                if disk_stats.usage_percentage > config.disk.usage_threshold {
                    let mut alert_mgr = alert_manager.write().await;
                    alert_mgr.trigger_alert(
                        "disk_usage_high".to_string(),
                        format!("Disk usage is {:.1}%", disk_stats.usage_percentage),
                        AlertSeverity::Medium,
                        [("disk_usage".to_string(), disk_stats.usage_percentage)].iter().cloned().collect(),
                    ).await;
                }
            }
        });
        
        self.tasks.write().await.push(task);
    }
    
    /// Start alert processing
    async fn start_alert_processing(&self) {
        let alert_manager = Arc::clone(&self.alert_manager);
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(10));
            
            loop {
                interval.tick().await;
                
                // Process pending alerts
                let mut alert_mgr = alert_manager.write().await;
                alert_mgr.process_alerts().await;
            }
        });
        
        self.tasks.write().await.push(task);
    }
    
    /// Start history cleanup
    async fn start_history_cleanup(&self) {
        let history = Arc::clone(&self.history);
        let retention = self.config.history_retention;
        
        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(300)); // 5 minutes
            
            loop {
                interval.tick().await;
                
                // Clean up old history entries
                let mut history_guard = history.write().await;
                let cutoff_time = SystemTime::now() - retention;
                
                history_guard.retain(|stats| stats.timestamp > cutoff_time);
            }
        });
        
        self.tasks.write().await.push(task);
    }
    
    /// Get CPU usage
    async fn get_cpu_usage() -> f64 {
        // Platform-specific CPU usage detection
        #[cfg(target_os = "linux")]
        {
            Self::get_linux_cpu_usage().await
        }
        #[cfg(target_os = "macos")]
        {
            Self::get_macos_cpu_usage().await
        }
        #[cfg(target_os = "windows")]
        {
            Self::get_windows_cpu_usage().await
        }
        #[cfg(not(any(target_os = "linux", target_os = "macos", target_os = "windows")))]
        {
            0.0
        }
    }
    
    #[cfg(target_os = "linux")]
    async fn get_linux_cpu_usage() -> f64 {
        use std::fs;
        
        // Read /proc/stat for CPU usage
        if let Ok(stat) = fs::read_to_string("/proc/stat") {
            if let Some(cpu_line) = stat.lines().next() {
                let values: Vec<u64> = cpu_line.split_whitespace()
                    .skip(1)
                    .filter_map(|s| s.parse().ok())
                    .collect();
                
                if values.len() >= 4 {
                    let idle = values[3];
                    let total: u64 = values.iter().sum();
                    
                    if total > 0 {
                        return (total - idle) as f64 / total as f64 * 100.0;
                    }
                }
            }
        }
        0.0
    }
    
    #[cfg(target_os = "macos")]
    async fn get_macos_cpu_usage() -> f64 {
        // macOS-specific CPU usage detection
        0.0
    }
    
    #[cfg(target_os = "windows")]
    async fn get_windows_cpu_usage() -> f64 {
        // Windows-specific CPU usage detection
        0.0
    }
    
    /// Get per-core CPU usage
    async fn get_per_core_cpu_usage() -> Vec<f64> {
        // Implementation would return per-core CPU usage
        Vec::new()
    }
    
    /// Get CPU frequency
    async fn get_cpu_frequency() -> f64 {
        // Implementation would return CPU frequency
        0.0
    }
    
    /// Get memory usage
    async fn get_memory_usage() -> (f64, u64) {
        // Implementation would return memory usage percentage and bytes
        (0.0, 0)
    }
    
    /// Get swap usage
    async fn get_swap_usage() -> f64 {
        // Implementation would return swap usage percentage
        0.0
    }
    
    /// Get network statistics
    async fn get_network_stats(config: &NetworkMonitorConfig) -> NetworkStats {
        // Implementation would collect network statistics
        NetworkStats::default()
    }
    
    /// Get disk statistics
    async fn get_disk_stats(config: &DiskMonitorConfig) -> DiskStats {
        // Implementation would collect disk statistics
        DiskStats::default()
    }
    
    /// Get resource statistics
    pub async fn get_stats(&self) -> ResourceStats {
        self.stats.read().await.clone()
    }
    
    /// Get statistics history
    pub async fn get_history(&self) -> Vec<ResourceStats> {
        self.history.read().await.iter().cloned().collect()
    }
    
    /// Get active alerts
    pub async fn get_active_alerts(&self) -> Vec<Alert> {
        let alert_mgr = self.alert_manager.read().await;
        alert_mgr.get_active_alerts()
    }
}

impl AlertManager {
    /// Create a new alert manager
    pub fn new(config: AlertConfig) -> Self {
        Self {
            config,
            last_alerts: HashMap::new(),
            alert_history: VecDeque::new(),
        }
    }
    
    /// Trigger an alert
    pub async fn trigger_alert(
        &mut self,
        alert_type: String,
        message: String,
        severity: AlertSeverity,
        values: HashMap<String, f64>,
    ) {
        // Check cooldown
        if let Some(last_time) = self.last_alerts.get(&alert_type) {
            if last_time.elapsed() < self.config.cooldown {
                return;
            }
        }
        
        // Create alert
        let alert = Alert {
            id: uuid::Uuid::new_v4().to_string(),
            alert_type: alert_type.clone(),
            message: message.clone(),
            severity,
            timestamp: SystemTime::now(),
            values,
            resolved: false,
        };
        
        // Add to history
        self.alert_history.push_back(alert.clone());
        
        // Keep only recent alerts
        if self.alert_history.len() > 1000 {
            self.alert_history.pop_front();
        }
        
        // Update last alert time
        self.last_alerts.insert(alert_type, Instant::now());
        
        // Send alert through configured channels
        for channel in &self.config.channels {
            if let Err(e) = self.send_alert(channel, &alert).await {
                eprintln!("Failed to send alert through {:?}: {}", channel, e);
            }
        }
        
        println!("🚨 Alert triggered: {}", message);
    }
    
    /// Send alert through channel
    async fn send_alert(&self, channel: &AlertChannel, alert: &Alert) -> Result<(), String> {
        match channel {
            AlertChannel::Log => {
                println!("🚨 [{}] {}: {}", 
                    format!("{:?}", alert.severity).to_uppercase(),
                    alert.alert_type,
                    alert.message
                );
                Ok(())
            },
            AlertChannel::Webhook { url, headers } => {
                let client = reqwest::Client::new();
                let mut request = client.post(url)
                    .json(alert);
                
                for (key, value) in headers {
                    request = request.header(key, value);
                }
                
                request.send().await
                    .map_err(|e| format!("Webhook request failed: {}", e))?;
                
                Ok(())
            },
            AlertChannel::Discord { webhook_url } => {
                let client = reqwest::Client::new();
                let payload = serde_json::json!({
                    "content": format!("🚨 **{}**: {}", alert.alert_type, alert.message),
                    "embeds": [{
                        "title": alert.alert_type,
                        "description": alert.message,
                        "color": match alert.severity {
                            AlertSeverity::Low => 0x00ff00,
                            AlertSeverity::Medium => 0xffff00,
                            AlertSeverity::High => 0xff8000,
                            AlertSeverity::Critical => 0xff0000,
                        },
                        "timestamp": alert.timestamp,
                    }]
                });
                
                client.post(webhook_url)
                    .json(&payload)
                    .send().await
                    .map_err(|e| format!("Discord webhook failed: {}", e))?;
                
                Ok(())
            },
            _ => {
                // Other alert channels
                Ok(())
            }
        }
    }
    
    /// Process pending alerts
    pub async fn process_alerts(&mut self) {
        // Process alert resolution, escalation, etc.
        // Implementation would handle alert lifecycle
    }
    
    /// Get active alerts
    pub fn get_active_alerts(&self) -> Vec<Alert> {
        self.alert_history.iter()
            .filter(|alert| !alert.resolved)
            .cloned()
            .collect()
    }
}

impl Default for ResourceConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            monitoring_interval: Duration::from_secs(10),
            cpu: CpuMonitorConfig::default(),
            memory: MemoryMonitorConfig::default(),
            network: NetworkMonitorConfig::default(),
            disk: DiskMonitorConfig::default(),
            alerts: AlertConfig::default(),
            history_retention: Duration::from_secs(3600),
        }
    }
}

impl Default for CpuMonitorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            alert_threshold: 80.0,
            per_core_monitoring: false,
            frequency_monitoring: false,
        }
    }
}

impl Default for MemoryMonitorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            alert_threshold: 85.0,
            monitor_swap: true,
            monitor_fragmentation: false,
        }
    }
}

impl Default for NetworkMonitorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            interfaces: vec!["eth0".to_string(), "en0".to_string()],
            bandwidth_threshold: 100_000_000, // 100 MB/s
            monitor_connections: true,
        }
    }
}

impl Default for DiskMonitorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            usage_threshold: 90.0,
            monitor_io: true,
            paths: vec!["/".to_string(), "/tmp".to_string()],
        }
    }
}

impl Default for AlertConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            cooldown: Duration::from_secs(300),
            channels: vec![AlertChannel::Log],
            severity_levels: HashMap::new(),
        }
    }
}

impl Default for ResourceStats {
    fn default() -> Self {
        Self {
            cpu_usage: 0.0,
            cpu_per_core: Vec::new(),
            cpu_frequency: 0.0,
            memory_usage: 0.0,
            memory_bytes: 0,
            swap_usage: 0.0,
            network: NetworkStats::default(),
            disk: DiskStats::default(),
            load_average: (0.0, 0.0, 0.0),
            process_count: 0,
            thread_count: 0,
            fd_count: 0,
            timestamp: SystemTime::now(),
        }
    }
}

impl Default for NetworkStats {
    fn default() -> Self {
        Self {
            bytes_received: 0,
            bytes_sent: 0,
            packets_received: 0,
            packets_sent: 0,
            errors: 0,
            active_connections: 0,
            interfaces: HashMap::new(),
        }
    }
}

impl Default for DiskStats {
    fn default() -> Self {
        Self {
            total_space: 0,
            used_space: 0,
            available_space: 0,
            usage_percentage: 0.0,
            io: DiskIoStats::default(),
            paths: HashMap::new(),
        }
    }
}

impl Default for DiskIoStats {
    fn default() -> Self {
        Self {
            read_ops: 0,
            write_ops: 0,
            bytes_read: 0,
            bytes_written: 0,
            avg_read_latency: Duration::ZERO,
            avg_write_latency: Duration::ZERO,
            queue_depth: 0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_resource_monitor_creation() {
        let config = ResourceConfig::default();
        let monitor = ResourceMonitor::new(config).await;
        assert!(monitor.is_ok());
    }
    
    #[test]
    fn test_alert_manager_creation() {
        let config = AlertConfig::default();
        let manager = AlertManager::new(config);
        assert!(manager.config.enabled);
    }
    
    #[tokio::test]
    async fn test_alert_triggering() {
        let config = AlertConfig::default();
        let mut manager = AlertManager::new(config);
        
        manager.trigger_alert(
            "test_alert".to_string(),
            "Test alert message".to_string(),
            AlertSeverity::Medium,
            HashMap::new(),
        ).await;
        
        let active_alerts = manager.get_active_alerts();
        assert_eq!(active_alerts.len(), 1);
        assert_eq!(active_alerts[0].alert_type, "test_alert");
    }
}
