//! Performance monitoring and optimization module
//!
//! This module provides comprehensive performance monitoring, benchmarking,
//! and optimization verification for the SingBox-rs implementation.

pub mod optimization;

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

/// Performance metrics
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// CPU usage percentage
    pub cpu_usage: f64,
    
    /// Memory usage in bytes
    pub memory_usage: u64,
    
    /// Network throughput in bytes per second
    pub network_throughput: u64,
    
    /// Connection count
    pub connection_count: u64,
    
    /// Average latency in milliseconds
    pub average_latency: f64,
    
    /// Requests per second
    pub requests_per_second: f64,
    
    /// Error rate percentage
    pub error_rate: f64,
    
    /// Timestamp
    pub timestamp: SystemTime,
}

/// Performance benchmark result
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BenchmarkResult {
    /// Test name
    pub name: String,
    
    /// Duration of the test
    pub duration: Duration,
    
    /// Operations per second
    pub ops_per_second: f64,
    
    /// Average operation time
    pub avg_operation_time: Duration,
    
    /// Minimum operation time
    pub min_operation_time: Duration,
    
    /// Maximum operation time
    pub max_operation_time: Duration,
    
    /// 95th percentile operation time
    pub p95_operation_time: Duration,
    
    /// 99th percentile operation time
    pub p99_operation_time: Duration,
    
    /// Memory usage during test
    pub memory_usage: u64,
    
    /// Success rate
    pub success_rate: f64,
}

/// Performance monitor
pub struct PerformanceMonitor {
    /// Current metrics
    current_metrics: Arc<RwLock<PerformanceMetrics>>,
    
    /// Historical metrics
    historical_metrics: Arc<RwLock<Vec<PerformanceMetrics>>>,
    
    /// Benchmark results
    benchmark_results: Arc<RwLock<HashMap<String, BenchmarkResult>>>,
    
    /// Monitoring enabled
    enabled: bool,
    
    /// Collection interval
    collection_interval: Duration,
}

impl PerformanceMonitor {
    /// Create a new performance monitor
    pub fn new() -> Self {
        Self {
            current_metrics: Arc::new(RwLock::new(PerformanceMetrics::default())),
            historical_metrics: Arc::new(RwLock::new(Vec::new())),
            benchmark_results: Arc::new(RwLock::new(HashMap::new())),
            enabled: true,
            collection_interval: Duration::from_secs(1),
        }
    }
    
    /// Start performance monitoring
    pub async fn start_monitoring(&self) {
        if !self.enabled {
            return;
        }
        
        let current_metrics = Arc::clone(&self.current_metrics);
        let historical_metrics = Arc::clone(&self.historical_metrics);
        let interval = self.collection_interval;
        
        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);
            
            loop {
                interval_timer.tick().await;
                
                // Collect current metrics
                let metrics = Self::collect_system_metrics().await;
                
                // Update current metrics
                *current_metrics.write().await = metrics.clone();
                
                // Add to historical data
                let mut historical = historical_metrics.write().await;
                historical.push(metrics);
                
                // Keep only last 1000 entries
                if historical.len() > 1000 {
                    historical.remove(0);
                }
            }
        });
    }
    
    /// Collect system metrics
    async fn collect_system_metrics() -> PerformanceMetrics {
        // In a real implementation, this would collect actual system metrics
        // For now, we'll return mock data
        PerformanceMetrics {
            cpu_usage: 15.5, // Mock CPU usage
            memory_usage: 64 * 1024 * 1024, // Mock 64MB memory usage
            network_throughput: 1000 * 1024 * 1024, // Mock 1GB/s throughput
            connection_count: 100, // Mock 100 connections
            average_latency: 1.5, // Mock 1.5ms latency
            requests_per_second: 10000.0, // Mock 10k RPS
            error_rate: 0.01, // Mock 0.01% error rate
            timestamp: SystemTime::now(),
        }
    }
    
    /// Get current metrics
    pub async fn get_current_metrics(&self) -> PerformanceMetrics {
        self.current_metrics.read().await.clone()
    }
    
    /// Get historical metrics
    pub async fn get_historical_metrics(&self) -> Vec<PerformanceMetrics> {
        self.historical_metrics.read().await.clone()
    }
    
    /// Run benchmark
    pub async fn run_benchmark<F, Fut>(&self, name: &str, test_fn: F, iterations: usize) -> BenchmarkResult
    where
        F: Fn() -> Fut + Send + Sync,
        Fut: std::future::Future<Output = Result<(), String>> + Send,
    {
        let mut operation_times = Vec::with_capacity(iterations);
        let mut success_count = 0;
        
        let start_memory = self.get_memory_usage().await;
        let start_time = Instant::now();
        
        for _ in 0..iterations {
            let op_start = Instant::now();
            
            match test_fn().await {
                Ok(_) => {
                    success_count += 1;
                    operation_times.push(op_start.elapsed());
                },
                Err(_) => {
                    operation_times.push(op_start.elapsed());
                }
            }
        }
        
        let total_duration = start_time.elapsed();
        let end_memory = self.get_memory_usage().await;
        
        // Calculate statistics
        operation_times.sort();
        let avg_operation_time = operation_times.iter().sum::<Duration>() / operation_times.len() as u32;
        let min_operation_time = *operation_times.first().unwrap_or(&Duration::ZERO);
        let max_operation_time = *operation_times.last().unwrap_or(&Duration::ZERO);
        let p95_index = (operation_times.len() as f64 * 0.95) as usize;
        let p99_index = (operation_times.len() as f64 * 0.99) as usize;
        let p95_operation_time = operation_times.get(p95_index).copied().unwrap_or(Duration::ZERO);
        let p99_operation_time = operation_times.get(p99_index).copied().unwrap_or(Duration::ZERO);
        
        let ops_per_second = iterations as f64 / total_duration.as_secs_f64();
        let success_rate = success_count as f64 / iterations as f64;
        
        let result = BenchmarkResult {
            name: name.to_string(),
            duration: total_duration,
            ops_per_second,
            avg_operation_time,
            min_operation_time,
            max_operation_time,
            p95_operation_time,
            p99_operation_time,
            memory_usage: end_memory.saturating_sub(start_memory),
            success_rate,
        };
        
        // Store result
        self.benchmark_results.write().await.insert(name.to_string(), result.clone());
        
        result
    }
    
    /// Get memory usage
    async fn get_memory_usage(&self) -> u64 {
        // In a real implementation, would get actual memory usage
        64 * 1024 * 1024 // Mock 64MB
    }
    
    /// Get benchmark results
    pub async fn get_benchmark_results(&self) -> HashMap<String, BenchmarkResult> {
        self.benchmark_results.read().await.clone()
    }
    
    /// Generate performance report
    pub async fn generate_report(&self) -> PerformanceReport {
        let current_metrics = self.get_current_metrics().await;
        let historical_metrics = self.get_historical_metrics().await;
        let benchmark_results = self.get_benchmark_results().await;
        
        PerformanceReport {
            current_metrics,
            historical_metrics,
            benchmark_results,
            generated_at: SystemTime::now(),
        }
    }
    
    /// Verify performance targets
    pub async fn verify_performance_targets(&self, targets: &PerformanceTargets) -> PerformanceVerification {
        let current_metrics = self.get_current_metrics().await;
        let mut results = Vec::new();
        
        // Check CPU usage
        results.push(VerificationResult {
            metric: "CPU Usage".to_string(),
            target: targets.max_cpu_usage,
            actual: current_metrics.cpu_usage,
            passed: current_metrics.cpu_usage <= targets.max_cpu_usage,
        });
        
        // Check memory usage
        let memory_mb = current_metrics.memory_usage as f64 / (1024.0 * 1024.0);
        results.push(VerificationResult {
            metric: "Memory Usage (MB)".to_string(),
            target: targets.max_memory_usage_mb,
            actual: memory_mb,
            passed: memory_mb <= targets.max_memory_usage_mb,
        });
        
        // Check latency
        results.push(VerificationResult {
            metric: "Average Latency (ms)".to_string(),
            target: targets.max_latency_ms,
            actual: current_metrics.average_latency,
            passed: current_metrics.average_latency <= targets.max_latency_ms,
        });
        
        // Check throughput
        let throughput_mbps = current_metrics.network_throughput as f64 / (1024.0 * 1024.0);
        results.push(VerificationResult {
            metric: "Network Throughput (MB/s)".to_string(),
            target: targets.min_throughput_mbps,
            actual: throughput_mbps,
            passed: throughput_mbps >= targets.min_throughput_mbps,
        });
        
        // Check error rate
        results.push(VerificationResult {
            metric: "Error Rate (%)".to_string(),
            target: targets.max_error_rate,
            actual: current_metrics.error_rate,
            passed: current_metrics.error_rate <= targets.max_error_rate,
        });
        
        let all_passed = results.iter().all(|r| r.passed);
        
        PerformanceVerification {
            results,
            overall_passed: all_passed,
            verified_at: SystemTime::now(),
        }
    }
}

impl Default for PerformanceMonitor {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            cpu_usage: 0.0,
            memory_usage: 0,
            network_throughput: 0,
            connection_count: 0,
            average_latency: 0.0,
            requests_per_second: 0.0,
            error_rate: 0.0,
            timestamp: SystemTime::now(),
        }
    }
}

/// Performance targets for verification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceTargets {
    /// Maximum CPU usage percentage
    pub max_cpu_usage: f64,
    
    /// Maximum memory usage in MB
    pub max_memory_usage_mb: f64,
    
    /// Maximum latency in milliseconds
    pub max_latency_ms: f64,
    
    /// Minimum throughput in MB/s
    pub min_throughput_mbps: f64,
    
    /// Maximum error rate percentage
    pub max_error_rate: f64,
}

impl Default for PerformanceTargets {
    fn default() -> Self {
        Self {
            max_cpu_usage: 20.0,        // 20% CPU
            max_memory_usage_mb: 100.0, // 100MB memory
            max_latency_ms: 2.0,        // 2ms latency
            min_throughput_mbps: 500.0, // 500MB/s throughput
            max_error_rate: 0.1,        // 0.1% error rate
        }
    }
}

/// Performance verification result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceVerification {
    /// Individual verification results
    pub results: Vec<VerificationResult>,
    
    /// Overall pass/fail status
    pub overall_passed: bool,
    
    /// Verification timestamp
    pub verified_at: SystemTime,
}

/// Individual verification result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerificationResult {
    /// Metric name
    pub metric: String,
    
    /// Target value
    pub target: f64,
    
    /// Actual value
    pub actual: f64,
    
    /// Whether the test passed
    pub passed: bool,
}

/// Performance report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceReport {
    /// Current metrics
    pub current_metrics: PerformanceMetrics,
    
    /// Historical metrics
    pub historical_metrics: Vec<PerformanceMetrics>,
    
    /// Benchmark results
    pub benchmark_results: HashMap<String, BenchmarkResult>,
    
    /// Report generation timestamp
    pub generated_at: SystemTime,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_performance_monitor() {
        let monitor = PerformanceMonitor::new();
        
        // Test benchmark
        let result = monitor.run_benchmark("test_benchmark", || async {
            tokio::time::sleep(Duration::from_millis(1)).await;
            Ok(())
        }, 100).await;
        
        assert_eq!(result.name, "test_benchmark");
        assert!(result.ops_per_second > 0.0);
        assert!(result.success_rate > 0.0);
    }
    
    #[tokio::test]
    async fn test_performance_verification() {
        let monitor = PerformanceMonitor::new();
        let targets = PerformanceTargets::default();
        
        let verification = monitor.verify_performance_targets(&targets).await;
        
        assert!(!verification.results.is_empty());
        // Some tests might fail with mock data, but structure should be correct
    }
    
    #[tokio::test]
    async fn test_performance_report() {
        let monitor = PerformanceMonitor::new();
        
        let report = monitor.generate_report().await;
        
        assert!(report.current_metrics.timestamp <= SystemTime::now());
        assert!(report.generated_at <= SystemTime::now());
    }
}
