# SingBox-RS 开发指令文档

## 项目目标

将 Go 版本的 sing-box 完全用 Rust 重新实现，确保：
- 100% 功能兼容性
- 更高的性能表现
- 内存安全保证
- 并发处理优化
- 可在真实GUI环境中完全替代原版

## 工作流程

### 第一阶段：基础实现 (迭代1-16) ✅ 已完成
1. 项目初始化和基础架构
2. CLI接口实现
3. 配置系统
4. 核心服务框架
5. 网络和DNS组件
6. 路由系统
7. 协议支持
8. 测试套件
9. 性能基准测试

### 第二阶段：100%复刻打磨 (迭代17-32)
**目标**: 确保与原版sing-box 100%功能兼容，可在真实GUI环境中使用

#### 阶段2.1：完整CLI命令复刻 (迭代17-20)
17. **缺失命令实现**
    - `generate` 命令组 (uuid, rand, wg-keypair, tls-keypair, ech-keypair, reality-keypair, vapid)
    - `geoip` 命令组 (list, lookup, export)
    - `geosite` 命令组 (list, lookup, export)
    - `merge` 命令 (配置合并)
    - `rule-set` 命令组 (compile, decompile, format, upgrade, convert, merge, match)
    - `tools` 命令组 (connect, fetch, synctime)

18. **CLI参数完整性检查**
    - 验证所有全局选项 (-c, -C, -D, --disable-color)
    - 验证所有子命令参数和选项
    - 确保帮助文本与原版一致
    - 验证错误消息格式

19. **配置格式100%兼容**
    - 完整的配置字段支持
    - 所有inbound/outbound类型
    - 路由规则完整实现
    - DNS配置完整支持

20. **版本信息精确复刻**
    - 版本输出格式
    - 构建信息显示
    - 环境信息输出

#### 阶段2.2：核心功能深度实现 (迭代21-24)
21. **协议完整实现**
    - 所有inbound协议 (mixed, socks, http, tun, redirect等)
    - 所有outbound协议 (direct, block, dns, shadowsocks, vmess, vless, trojan, wireguard等)
    - 协议特定选项和配置

22. **路由系统完整实现**
    - 所有路由规则类型
    - 规则集支持
    - GeoIP/GeoSite集成
    - 路由策略完整实现

23. **DNS系统完整实现**
    - 所有DNS传输协议
    - DNS规则和策略
    - FakeIP支持
    - DNS缓存机制

24. **TLS/加密支持**
    - TLS配置完整支持
    - 证书管理
    - ECH支持
    - Reality协议支持

#### 阶段2.3：高级功能实现 (迭代25-28)
25. **实验性功能**
    - Clash API支持
    - 缓存文件管理
    - 统计信息收集
    - 性能监控

26. **平台特定功能**
    - 系统代理设置
    - TUN接口支持
    - 平台特定网络功能
    - 权限管理

27. **服务管理**
    - 信号处理 (SIGHUP重载等)
    - 优雅关闭
    - 资源清理
    - 错误恢复

28. **日志和监控**
    - 完整的日志系统
    - 日志格式兼容
    - 性能指标收集
    - 调试功能

#### 阶段2.4：测试和验证 (迭代29-32)
29. **全面测试覆盖**
    - 所有CLI命令测试
    - 所有配置选项测试
    - 协议兼容性测试
    - 错误场景测试

30. **真实环境验证**
    - GUI集成测试
    - 实际网络环境测试
    - 性能压力测试
    - 兼容性验证

31. **文档和示例**
    - 完整的API文档
    - 配置示例
    - 迁移指南
    - 故障排除指南

32. **最终打磨**
    - 代码质量检查
    - 性能优化
    - 安全审计
    - 发布准备

## 质量标准

### 功能兼容性
- CLI命令行为100%一致
- 配置文件格式完全兼容
- 错误消息格式一致
- 输出格式精确匹配

### 性能要求
- 启动时间优于原版
- 内存使用更低
- CPU效率更高
- 并发性能更强

### 代码质量
- 100%测试覆盖
- 零警告编译
- 文档完整
- 代码规范

## 验证方法

### 自动化测试
- 单元测试覆盖所有模块
- 集成测试验证端到端功能
- 性能基准测试
- 兼容性测试套件

### 手动验证
- GUI环境集成测试
- 真实网络场景测试
- 配置文件兼容性验证
- 错误处理验证

## 当前状态

**第一阶段已完成** ✅
- 基础架构完整
- 核心功能实现
- 测试框架建立
- 性能基准确立

**第二阶段开始** 🚀
- 开始100%复刻打磨
- 确保GUI环境兼容
- 实现所有缺失功能
