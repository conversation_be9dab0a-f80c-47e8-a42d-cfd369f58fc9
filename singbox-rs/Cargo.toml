[package]
name = "singbox-rs"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "singbox-rs"
path = "src/main.rs"

[[bench]]
name = "performance_comparison"
harness = false

[lib]
name = "singbox_rs"
path = "src/lib.rs"

[dependencies]
aes-gcm = "0.10.3"
base64 = "0.22.1"
chacha20poly1305 = "0.10.1"
clap = { version = "4.5.46", features = ["derive"] }
futures = "0.3.31"
hex = "0.4.3"
hkdf = "0.12.4"
hmac = "0.12"
md5 = "0.7.0"
rand = "0.9.2"
regex = "1.10.2"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.143"
sha1 = "0.10.6"
sha2 = "0.10.8"
tokio = { version = "1.47.1", features = ["full"] }
uuid = { version = "1.18.0", features = ["v4", "serde"] }

# Performance optimization dependencies
flate2 = "1.0"
zstd = "0.13"
lz4_flex = "0.11"
brotli = "3.4"
ring = "0.17"
async-trait = "0.1"
reqwest = { version = "0.11", features = ["json"] }

# Transport layer dependencies
tokio-rustls = "0.24"
rustls = "0.21"
rustls-pemfile = "1.0"
webpki-roots = "0.25"
tokio-tungstenite = "0.20"
futures-util = "0.3"
url = "2.4"
h2 = "0.3"
http = "0.2"
bytes = "1.0"
quinn = "0.10"

# DNS-specific dependencies
trust-dns-resolver = "0.23"
trust-dns-proto = "0.23"
warp = "0.3"
ipnet = "2.9"

# Additional dependencies
thiserror = "1.0"
tokio-util = "0.7"
notify = "6.0"
serde_yaml = "0.9"
chrono = { version = "0.4", features = ["serde"] }
num_cpus = "1.0"

[[bench]]
name = "performance_benchmarks"
harness = false

[dev-dependencies]
criterion = { version = "0.7.0", features = ["html_reports"] }
regex = "1.11.2"
tempfile = "3.21.0"
