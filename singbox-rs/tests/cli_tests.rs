//! CLI integration tests
//! 
//! Tests the command line interface to ensure it matches Go version behavior

use std::process::Command;
use std::fs;
use std::path::Path;

#[test]
fn test_cli_help() {
    let output = Command::new("./target/debug/singbox-rs")
        .arg("--help")
        .output()
        .expect("Failed to execute command");
    
    assert!(output.status.success());
    let stdout = String::from_utf8(output.stdout).unwrap();
    
    // Check for key help text
    assert!(stdout.contains("The universal proxy platform"));
    assert!(stdout.contains("Commands:"));
    assert!(stdout.contains("run"));
    assert!(stdout.contains("check"));
    assert!(stdout.contains("format"));
    assert!(stdout.contains("version"));
}

#[test]
fn test_cli_version() {
    let output = Command::new("./target/debug/singbox-rs")
        .arg("version")
        .output()
        .expect("Failed to execute command");
    
    assert!(output.status.success());
    let stdout = String::from_utf8(output.stdout).unwrap();
    
    // Check version output format
    assert!(stdout.contains("sing-box version"));
    assert!(stdout.contains("Environment:"));
    assert!(stdout.contains("CGO: disabled"));
}

#[test]
fn test_cli_version_name_only() {
    let output = Command::new("./target/debug/singbox-rs")
        .arg("version")
        .arg("--name-only")
        .output()
        .expect("Failed to execute command");
    
    assert!(output.status.success());
    let stdout = String::from_utf8(output.stdout).unwrap();
    
    // Should only contain version number
    assert!(!stdout.contains("Environment:"));
    assert!(!stdout.contains("CGO:"));
}

#[test]
fn test_cli_check_valid_config() {
    // Create a valid test config
    let config_content = r#"{
  "log": {
    "level": "info",
    "timestamp": true
  },
  "dns": {
    "servers": [
      {
        "tag": "cloudflare",
        "address": "*******",
        "address_strategy": 1,
        "strategy": 1
      }
    ],
    "strategy": 1
  },
  "inbounds": [
    {
      "inbound_type": "mixed",
      "type": "mixed",
      "tag": "mixed-in"
    }
  ],
  "outbounds": [
    {
      "outbound_type": "direct",
      "type": "direct",
      "tag": "direct"
    }
  ],
  "route": {
    "auto_detect_interface": true,
    "final_outbound": "direct"
  }
}"#;
    
    fs::write("test_config.json", config_content).unwrap();
    
    let output = Command::new("./target/debug/singbox-rs")
        .arg("check")
        .arg("-c")
        .arg("test_config.json")
        .output()
        .expect("Failed to execute command");
    
    // Clean up
    let _ = fs::remove_file("test_config.json");
    
    assert!(output.status.success());
    let stdout = String::from_utf8(output.stdout).unwrap();
    assert!(stdout.contains("Configuration is valid"));
}

#[test]
fn test_cli_check_invalid_config() {
    // Create an invalid test config (malformed JSON)
    let config_content = r#"{
  "log": {
    "level": "info"
  },
  "invalid_syntax":
}"#;
    
    fs::write("test_invalid_config.json", config_content).unwrap();
    
    let output = Command::new("./target/debug/singbox-rs")
        .arg("check")
        .arg("-c")
        .arg("test_invalid_config.json")
        .output()
        .expect("Failed to execute command");
    
    // Clean up
    let _ = fs::remove_file("test_invalid_config.json");
    
    assert!(!output.status.success());
    let stderr = String::from_utf8(output.stderr).unwrap();
    assert!(stderr.contains("Error:"));
}

#[test]
fn test_cli_format() {
    // Create a test config
    let config_content = r#"{
  "log": {
    "level": "info"
  },
  "inbounds": [
    {
      "inbound_type": "mixed",
      "type": "mixed",
      "tag": "mixed-in"
    }
  ],
  "outbounds": [
    {
      "outbound_type": "direct",
      "type": "direct",
      "tag": "direct"
    }
  ]
}"#;
    
    fs::write("test_format_config.json", config_content).unwrap();
    
    let output = Command::new("./target/debug/singbox-rs")
        .arg("format")
        .arg("-c")
        .arg("test_format_config.json")
        .output()
        .expect("Failed to execute command");
    
    // Clean up
    let _ = fs::remove_file("test_format_config.json");
    
    assert!(output.status.success());
    let stdout = String::from_utf8(output.stdout).unwrap();
    
    // Should be valid JSON
    let _: serde_json::Value = serde_json::from_str(&stdout).unwrap();
}

#[test]
fn test_cli_config_not_found() {
    let output = Command::new("./target/debug/singbox-rs")
        .arg("check")
        .arg("-c")
        .arg("nonexistent_config.json")
        .output()
        .expect("Failed to execute command");
    
    assert!(!output.status.success());
    let stderr = String::from_utf8(output.stderr).unwrap();
    assert!(stderr.contains("Error:"));
}

#[test]
fn test_cli_multiple_configs() {
    // Create multiple test configs
    let config1 = r#"{
  "log": {
    "level": "info"
  },
  "inbounds": [
    {
      "inbound_type": "mixed",
      "type": "mixed",
      "tag": "mixed-in"
    }
  ]
}"#;
    
    let config2 = r#"{
  "outbounds": [
    {
      "outbound_type": "direct",
      "type": "direct",
      "tag": "direct"
    }
  ]
}"#;
    
    fs::write("test_config1.json", config1).unwrap();
    fs::write("test_config2.json", config2).unwrap();
    
    let output = Command::new("./target/debug/singbox-rs")
        .arg("check")
        .arg("-c")
        .arg("test_config1.json")
        .arg("-c")
        .arg("test_config2.json")
        .output()
        .expect("Failed to execute command");
    
    // Clean up
    let _ = fs::remove_file("test_config1.json");
    let _ = fs::remove_file("test_config2.json");
    
    assert!(output.status.success());
    let stdout = String::from_utf8(output.stdout).unwrap();
    assert!(stdout.contains("Configuration is valid"));
}

#[test]
fn test_cli_global_options() {
    let output = Command::new("./target/debug/singbox-rs")
        .arg("--disable-color")
        .arg("version")
        .output()
        .expect("Failed to execute command");
    
    assert!(output.status.success());
}
