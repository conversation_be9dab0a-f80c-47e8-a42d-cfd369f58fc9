//! Integration tests for sing-box Rust implementation
//! 
//! These tests verify that different modules work together correctly
//! and that the overall system behaves as expected.

use singbox_rs::adapter::{InboundContext, Lifecycle, StartStage};
use singbox_rs::dns::{Client as DNSClient, ClientOptions, DNSQuery};
use singbox_rs::route::{Router, BasicRule, Rule};
use singbox_rs::protocol::{InboundRegistry, OutboundRegistry, register_builtin_protocols};
use singbox_rs::network::{NetworkManager, DefaultDialer, DefaultListener};
use singbox_rs::common::interrupt::Context;
use singbox_rs::log::{ConsoleLogger, Level, Logger};
use singbox_rs::option::{Options, LogOptions, DNSOptions};
use std::time::Duration;

/// Test complete system initialization and lifecycle
#[tokio::test]
async fn test_system_lifecycle() {
    // Create main components
    let mut dns_client = DNSClient::new(ClientOptions::default());
    let mut router = Router::new("direct");
    let mut network_manager = NetworkManager::new();

    // Test initialization stage
    assert!(dns_client.start(StartStage::Initialize).await.is_ok());
    assert!(router.start(StartStage::Initialize).await.is_ok());
    assert!(network_manager.start(StartStage::Initialize).await.is_ok());

    // Test start stage
    assert!(dns_client.start(StartStage::Start).await.is_ok());
    assert!(router.start(StartStage::Start).await.is_ok());
    assert!(network_manager.start(StartStage::Start).await.is_ok());

    // Test post-start stage
    assert!(dns_client.start(StartStage::PostStart).await.is_ok());
    assert!(router.start(StartStage::PostStart).await.is_ok());
    assert!(network_manager.start(StartStage::PostStart).await.is_ok());

    // Test started stage
    assert!(dns_client.start(StartStage::Started).await.is_ok());
    assert!(router.start(StartStage::Started).await.is_ok());
    assert!(network_manager.start(StartStage::Started).await.is_ok());

    // Test shutdown
    assert!(dns_client.close().await.is_ok());
    assert!(router.close().await.is_ok());
    assert!(network_manager.close().await.is_ok());
}

/// Test protocol registry integration
#[test]
fn test_protocol_registry_integration() {
    let ctx = Context::new();
    let inbound_registry = InboundRegistry::new();
    let outbound_registry = OutboundRegistry::new();
    
    // Register built-in protocols
    register_builtin_protocols(&inbound_registry, &outbound_registry);
    
    // Test inbound creation
    let options = serde_json::json!({});
    let inbound_result = inbound_registry.create(&ctx, "direct", "test-inbound", &options);
    assert!(inbound_result.is_ok());
    
    let inbound = inbound_result.unwrap();
    assert_eq!(inbound.tag(), "test-inbound");
    
    // Test outbound creation
    let outbound_result = outbound_registry.create(&ctx, "direct", "test-outbound", &options);
    assert!(outbound_result.is_ok());
    
    let outbound = outbound_result.unwrap();
    assert_eq!(outbound.tag(), "test-outbound");
    
    // Test block outbound
    let block_result = outbound_registry.create(&ctx, "block", "test-block", &options);
    assert!(block_result.is_ok());
    
    let block_outbound = block_result.unwrap();
    assert_eq!(block_outbound.tag(), "test-block");
}

/// Test routing with DNS integration
#[tokio::test]
async fn test_routing_dns_integration() {
    // Create router with rules
    let mut router = Router::new("direct");

    // Add rule for specific domain
    let rule = Box::new(BasicRule::new("domain_rule", "proxy")
        .with_outbound("proxy")
        .with_domain("example.com"));
    router.add_rule(rule);

    // Create inbound context
    let mut context = InboundContext::default();
    context.domain = "example.com".to_string();
    context.network = "tcp".to_string();

    // Test routing decision
    let outbound = router.find_outbound(&context).await;
    assert_eq!(outbound, "proxy");

    // Test with different domain
    context.domain = "other.com".to_string();
    let outbound = router.find_outbound(&context).await;
    assert_eq!(outbound, "direct"); // Should use default
}

/// Test network manager with dialer integration
#[tokio::test]
async fn test_network_manager_integration() {
    let mut network_manager = NetworkManager::new();

    // Register custom dialer
    let custom_dialer = Box::new(DefaultDialer::new()
        .with_timeout(Duration::from_secs(5))
        .with_bind_interface("eth0"));

    network_manager.register_dialer("custom", custom_dialer);

    // Test dialer retrieval
    let retrieved_dialer = network_manager.get_dialer("custom").await;
    assert!(retrieved_dialer);

    // Test default dialer
    let default_dialer = network_manager.default_dialer();
    let result = default_dialer.dial("tcp", "127.0.0.1:8080");
    assert!(result.is_ok());
}

/// Test configuration parsing and validation
#[test]
fn test_configuration_integration() {
    // Create main configuration
    let mut options = Options::default();

    // Configure logging
    let mut log_options = LogOptions::default();
    log_options.level = "info".to_string();
    log_options.timestamp = true;
    options.log = Some(log_options);

    // Configure DNS
    let dns_options = DNSOptions::default();
    options.dns = Some(dns_options);

    // Validate configuration
    let validation_result = singbox_rs::option::validate_options(&options);
    assert!(validation_result.is_ok());
}

/// Test logger integration with different components
#[test]
fn test_logger_integration() {
    let logger = ConsoleLogger::new(Level::Info);
    
    // Test different log levels
    logger.info("System starting up");
    logger.warn("This is a warning");
    logger.error("This is an error");
    
    // Test with context (simplified since ContextLogger is not implemented yet)
    // let ctx_logger = singbox_rs::log::ContextLogger::new(logger, "test_component");
    // ctx_logger.info("Component initialized");
}

/// Test DNS client with mock transport
#[tokio::test]
async fn test_dns_client_integration() {
    let mut dns_client = DNSClient::new(ClientOptions::default());

    // Test lifecycle
    assert!(dns_client.start(StartStage::Start).await.is_ok());

    // Test query creation
    let query = DNSQuery::a("example.com");
    assert_eq!(query.name, "example.com");
    assert_eq!(query.query_type, 1); // A record

    // Test AAAA query
    let aaaa_query = DNSQuery::aaaa("example.com");
    assert_eq!(aaaa_query.query_type, 28); // AAAA record

    assert!(dns_client.close().await.is_ok());
}

/// Test complete request flow simulation
#[tokio::test]
async fn test_complete_request_flow() {
    // 1. Initialize components
    let mut router = Router::new("direct");
    let mut network_manager = NetworkManager::new();
    let mut dns_client = DNSClient::new(ClientOptions::default());

    // 2. Configure routing rules
    let proxy_rule = Box::new(BasicRule::new("proxy_rule", "proxy")
        .with_outbound("proxy")
        .with_domain("blocked.com"));
    router.add_rule(proxy_rule);

    // 3. Start all components
    assert!(router.start(StartStage::Start).await.is_ok());
    assert!(network_manager.start(StartStage::Start).await.is_ok());
    assert!(dns_client.start(StartStage::Start).await.is_ok());

    // 4. Simulate incoming request
    let mut context = InboundContext::default();
    context.inbound = "http-in".to_string();
    context.network = "tcp".to_string();
    context.domain = "blocked.com".to_string();

    // 5. Route the request
    let outbound = router.find_outbound(&context).await;
    assert_eq!(outbound, "proxy");

    // 6. Test with allowed domain
    context.domain = "allowed.com".to_string();
    let outbound = router.find_outbound(&context).await;
    assert_eq!(outbound, "direct");

    // 7. Clean shutdown
    assert!(dns_client.close().await.is_ok());
    assert!(router.close().await.is_ok());
    assert!(network_manager.close().await.is_ok());
}

/// Test error handling across modules
#[test]
fn test_error_handling_integration() {
    // Test DNS errors
    let dns_error = singbox_rs::dns::DNSError::TransportError("test error".to_string());
    assert_eq!(format!("{}", dns_error), "transport error: test error");
    
    // Test route errors
    let route_error = singbox_rs::route::RouteError::NoRoute;
    assert_eq!(format!("{}", route_error), "no route found");
    
    // Test protocol errors
    let protocol_error = singbox_rs::protocol::ProtocolError::UnsupportedProtocol("test".to_string());
    assert_eq!(format!("{}", protocol_error), "unsupported protocol: test");
    
    // Test network errors
    let network_error = singbox_rs::network::NetworkError::DialFailed("test".to_string());
    assert_eq!(format!("{}", network_error), "dial failed: test");
}

/// Test configuration serialization/deserialization
#[test]
fn test_configuration_serialization() {
    let options = Options::default();
    
    // Test JSON serialization
    let json_result = serde_json::to_string(&options);
    assert!(json_result.is_ok());
    
    let json_str = json_result.unwrap();
    assert!(!json_str.is_empty());
    
    // Test JSON deserialization
    let parsed_result: Result<Options, _> = serde_json::from_str(&json_str);
    assert!(parsed_result.is_ok());
}

/// Test concurrent operations
#[tokio::test]
async fn test_concurrent_operations() {
    use std::sync::Arc;
    use tokio::task;

    let network_manager: Arc<NetworkManager> = Arc::new(NetworkManager::new());
    let router: Arc<Router> = Arc::new(Router::new("direct"));

    // Test concurrent access to network manager
    let handles: Vec<_> = (0..5).map(|i| {
        let nm: Arc<NetworkManager> = Arc::clone(&network_manager);
        let r: Arc<Router> = Arc::clone(&router);
        task::spawn(async move {
            // Test concurrent dialer access
            let _dialer = nm.default_dialer();

            // Test concurrent routing
            let mut context = InboundContext::default();
            context.domain = format!("test{}.com", i);
            let _outbound = r.find_outbound(&context).await;
        })
    }).collect();

    // Wait for all tasks to complete
    for handle in handles {
        assert!(handle.await.is_ok());
    }
}

/// Test memory usage and resource cleanup
#[tokio::test]
async fn test_resource_cleanup() {
    // Create components
    let mut dns_client = DNSClient::new(ClientOptions::default());
    let mut router = Router::new("direct");
    let mut network_manager = NetworkManager::new();

    // Add some data
    let rule = Box::new(BasicRule::new("test", "proxy").with_domain("test.com"));
    router.add_rule(rule);

    let dialer = Box::new(DefaultDialer::new());
    network_manager.register_dialer("test", dialer);

    // Start components
    assert!(dns_client.start(StartStage::Start).await.is_ok());
    assert!(router.start(StartStage::Start).await.is_ok());
    assert!(network_manager.start(StartStage::Start).await.is_ok());

    // Close components (should clean up resources)
    assert!(dns_client.close().await.is_ok());
    assert!(router.close().await.is_ok());
    assert!(network_manager.close().await.is_ok());

    // Verify cleanup (router should be empty after close)
    let context = InboundContext::default();
    let outbound = router.find_outbound(&context).await;
    assert_eq!(outbound, "direct"); // Should use default since rules were cleared
}
