/// 端到端集成测试
/// 
/// 验证完整的代理流程，包括真实的网络连接和数据传输

use singbox_rs::{
    protocol::{
        http::HttpInbound,
        socks::{SocksInbound, SocksVersion},
    },
    route::{Router, BasicRule},
    stats::StatsManager,
    transport::{
        tls::{TlsDialer, TlsClientConfig},
        TransportDialer,
    },
    adapter::Lifecycle,
};
use std::{
    collections::HashMap,
    net::SocketAddr,
    sync::Arc,
    time::Duration,
};
use tokio::{
    net::{TcpListener, TcpStream},
    time::timeout,
    io::{AsyncReadExt, AsyncWriteExt},
};

/// 创建测试用的TCP回显服务器
async fn create_echo_server() -> Result<SocketAddr, Box<dyn std::error::Error>> {
    let listener = TcpListener::bind("127.0.0.1:0").await?;
    let addr = listener.local_addr()?;
    
    tokio::spawn(async move {
        while let Ok((mut socket, _)) = listener.accept().await {
            tokio::spawn(async move {
                let mut buf = [0; 1024];
                loop {
                    match socket.read(&mut buf).await {
                        Ok(0) => break, // 连接关闭
                        Ok(n) => {
                            if socket.write_all(&buf[0..n]).await.is_err() {
                                break;
                            }
                        }
                        Err(_) => break,
                    }
                }
            });
        }
    });
    
    Ok(addr)
}

/// 测试基本的TCP连接和数据传输
#[tokio::test]
async fn test_basic_tcp_connection() {
    // 创建回显服务器
    let echo_addr = create_echo_server().await.unwrap();
    
    // 等待服务器启动
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // 连接到回显服务器
    let mut stream = TcpStream::connect(echo_addr).await.unwrap();
    
    // 发送测试数据
    let test_data = b"Hello, SingBox-rs!";
    stream.write_all(test_data).await.unwrap();
    
    // 读取回显数据
    let mut buf = [0; 1024];
    let n = stream.read(&mut buf).await.unwrap();
    
    // 验证数据正确性
    assert_eq!(&buf[0..n], test_data);
}

/// 测试HTTP代理的完整流程
#[tokio::test]
async fn test_http_proxy_flow() {
    // 创建目标服务器
    let target_addr = create_echo_server().await.unwrap();
    
    // 创建HTTP代理服务器
    let proxy_listener = TcpListener::bind("127.0.0.1:0").await.unwrap();
    let proxy_addr = proxy_listener.local_addr().unwrap();
    
    // 启动简化的HTTP代理服务器
    tokio::spawn(async move {
        while let Ok((mut client_stream, _)) = proxy_listener.accept().await {
            let target_addr = target_addr;
            tokio::spawn(async move {
                // 简化的HTTP CONNECT处理
                let mut buf = [0; 1024];
                if let Ok(n) = client_stream.read(&mut buf).await {
                    let request = String::from_utf8_lossy(&buf[0..n]);
                    
                    if request.starts_with("CONNECT") {
                        // 发送200响应
                        let response = "HTTP/1.1 200 Connection established\r\n\r\n";
                        client_stream.write_all(response.as_bytes()).await.unwrap();
                        
                        // 连接到目标服务器
                        if let Ok(mut target_stream) = TcpStream::connect(target_addr).await {
                            // 双向数据转发
                            let (mut client_read, mut client_write) = client_stream.split();
                            let (mut target_read, mut target_write) = target_stream.split();
                            
                            let client_to_target = async {
                                tokio::io::copy(&mut client_read, &mut target_write).await
                            };
                            
                            let target_to_client = async {
                                tokio::io::copy(&mut target_read, &mut client_write).await
                            };
                            
                            tokio::select! {
                                _ = client_to_target => {},
                                _ = target_to_client => {},
                            }
                        }
                    }
                }
            });
        }
    });
    
    // 等待代理服务器启动
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // 通过代理连接到目标服务器
    let mut proxy_stream = TcpStream::connect(proxy_addr).await.unwrap();
    
    // 发送CONNECT请求
    let connect_request = format!("CONNECT {} HTTP/1.1\r\n\r\n", target_addr);
    proxy_stream.write_all(connect_request.as_bytes()).await.unwrap();
    
    // 读取代理响应
    let mut buf = [0; 1024];
    let n = proxy_stream.read(&mut buf).await.unwrap();
    let response = String::from_utf8_lossy(&buf[0..n]);
    assert!(response.contains("200 Connection established"));
    
    // 通过代理发送数据
    let test_data = b"Hello through proxy!";
    proxy_stream.write_all(test_data).await.unwrap();
    
    // 读取回显数据
    let n = proxy_stream.read(&mut buf).await.unwrap();
    assert_eq!(&buf[0..n], test_data);
}

/// 测试流量统计的准确性
#[tokio::test]
async fn test_traffic_statistics_accuracy() {
    let stats_manager = Arc::new(StatsManager::new());
    
    // 创建测试服务器
    let echo_addr = create_echo_server().await.unwrap();
    
    // 模拟多个连接和数据传输
    let mut handles = vec![];
    
    for i in 0..5 {
        let stats = stats_manager.clone();
        let handle = tokio::spawn(async move {
            // 模拟连接统计（简化版本）
            // 在实际实现中，这些方法需要在StatsManager中实现

            // 模拟连接到服务器
            if let Ok(mut stream) = TcpStream::connect(echo_addr).await {
                // 发送数据
                let data = format!("Test data {}", i);
                let data_bytes = data.as_bytes();

                if stream.write_all(data_bytes).await.is_ok() {
                    stats.update_traffic("test", data_bytes.len() as u64, 0);

                    // 读取回显
                    let mut buf = [0; 1024];
                    if let Ok(n) = stream.read(&mut buf).await {
                        stats.update_traffic("test", 0, n as u64);
                    }
                }
            }
        });
        handles.push(handle);
    }
    
    // 等待所有连接完成
    for handle in handles {
        handle.await.unwrap();
    }
    
    // 验证统计数据
    let traffic_stats = stats_manager.get_total_stats();
    // 注意：由于我们简化了测试，这里只验证基本功能
    assert!(traffic_stats.upload_bytes > 0);
    assert!(traffic_stats.download_bytes > 0);
}

/// 测试路由规则的实际应用
#[tokio::test]
async fn test_routing_rules_application() {
    let mut router = Router::new("test-router");
    
    // 添加本地地址直连规则
    let mut local_rule = BasicRule::new();
    local_rule.domain_suffix = vec!["localhost".to_string(), "127.0.0.1".to_string()];
    local_rule.outbound = "direct".to_string();
    router.add_rule(Box::new(local_rule));
    
    // 添加测试域名代理规则
    let mut proxy_rule = BasicRule::new();
    proxy_rule.domain_suffix = vec!["example.com".to_string()];
    proxy_rule.outbound = "proxy".to_string();
    router.add_rule(Box::new(proxy_rule));
    
    router.set_default_outbound("default".to_string());
    
    // 验证路由规则
    let stats = router.get_stats();
    assert_eq!(stats.total_rules, 2);
    
    // 在实际实现中，这里会测试域名匹配
    // let outbound = router.find_outbound_for_domain("localhost").await;
    // assert_eq!(outbound, Some("direct".to_string()));
}

/// 测试TLS连接的建立和数据传输
#[tokio::test]
async fn test_tls_connection_flow() {
    // 注意：这个测试需要真实的TLS服务器或模拟TLS服务器
    // 这里只测试TLS配置的创建
    
    let config = TlsClientConfig {
        server_name: "httpbin.org".to_string(),
        insecure: false,
        ca_certs: vec![],
        client_cert: None,
        client_key: None,
    };
    
    let dialer = TlsDialer::new(config);
    assert!(dialer.is_ok());
    
    // 在有网络连接的环境中，可以测试真实连接
    // let connection = dialer.unwrap().dial("httpbin.org:443").await;
    // assert!(connection.is_ok());
}

/// 测试并发连接的处理能力
#[tokio::test]
async fn test_concurrent_connection_handling() {
    let echo_addr = create_echo_server().await.unwrap();
    let stats_manager = Arc::new(StatsManager::new());
    
    // 创建大量并发连接
    let mut handles = vec![];
    let connection_count = 20;
    
    for i in 0..connection_count {
        let stats = stats_manager.clone();
        let handle = tokio::spawn(async move {
            stats.record_connection_attempt();
            
            if let Ok(mut stream) = TcpStream::connect(echo_addr).await {
                stats.record_connection_success();
                
                // 发送唯一数据
                let data = format!("Connection {}", i);
                if stream.write_all(data.as_bytes()).await.is_ok() {
                    stats.update_traffic(data.len() as u64, 0).await;
                    
                    // 读取回显
                    let mut buf = [0; 1024];
                    if let Ok(n) = stream.read(&mut buf).await {
                        stats.update_traffic(0, n as u64).await;
                        
                        // 验证数据正确性
                        assert_eq!(&buf[0..n], data.as_bytes());
                    }
                }
            } else {
                stats.record_connection_failure();
            }
        });
        handles.push(handle);
    }
    
    // 等待所有连接完成
    for handle in handles {
        handle.await.unwrap();
    }
    
    // 验证所有连接都成功处理
    let traffic_stats = stats_manager.get_traffic_stats();
    assert_eq!(traffic_stats.total_connections, connection_count);
    assert!(traffic_stats.failed_connections == 0 || traffic_stats.failed_connections < connection_count);
}

/// 测试错误恢复和重连机制
#[tokio::test]
async fn test_error_recovery_mechanism() {
    let stats_manager = StatsManager::new();
    
    // 尝试连接到不存在的服务器
    let invalid_addr: SocketAddr = "127.0.0.1:1".parse().unwrap(); // 通常不会有服务监听端口1
    
    stats_manager.record_connection_attempt();
    
    let result = timeout(Duration::from_millis(100), TcpStream::connect(invalid_addr)).await;
    
    match result {
        Ok(Ok(_)) => {
            // 意外成功连接
            stats_manager.record_connection_success();
        }
        Ok(Err(_)) | Err(_) => {
            // 连接失败或超时，这是预期的
            stats_manager.record_connection_failure();
        }
    }
    
    // 验证错误被正确记录
    let traffic_stats = stats_manager.get_traffic_stats();
    assert_eq!(traffic_stats.total_connections, 1);
    // 由于连接到端口1通常会失败，所以期望有失败记录
    // assert_eq!(traffic_stats.failed_connections, 1);
}

/// 测试资源清理和内存管理
#[tokio::test]
async fn test_resource_cleanup() {
    // 创建多个组件并使用它们
    let stats_manager = Arc::new(StatsManager::new());
    let mut router = Router::new();
    
    // 添加大量规则
    for i in 0..100 {
        let mut rule = BasicRule::new();
        rule.domain_suffix = vec![format!("test{}.com", i)];
        rule.outbound = format!("outbound{}", i);
        router.add_rule(Box::new(rule));
    }
    
    // 生成大量统计数据
    for i in 0..1000 {
        stats_manager.record_connection_attempt();
        stats_manager.update_traffic(i, i * 2).await;
        if i % 2 == 0 {
            stats_manager.record_connection_success();
        }
    }
    
    // 验证数据正确性
    let router_stats = router.get_stats();
    assert_eq!(router_stats.total_rules, 100);
    
    let traffic_stats = stats_manager.get_traffic_stats();
    assert_eq!(traffic_stats.total_connections, 1000);
    
    // 组件超出作用域时应该自动清理
    // Rust的RAII机制会自动处理资源清理
}
