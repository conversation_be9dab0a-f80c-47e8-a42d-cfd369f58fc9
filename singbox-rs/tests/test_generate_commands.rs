use std::process::Command;
use std::str;
use regex::Regex;
use base64::{Engine as _, engine::general_purpose};

#[test]
fn test_generate_uuid() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "uuid"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = str::from_utf8(&output.stdout).unwrap();
    
    // UUID should be 36 characters with 4 hyphens
    assert_eq!(stdout.trim().len(), 36);
    assert_eq!(stdout.trim().matches('-').count(), 4);
    
    // Should be valid UUID format
    let uuid_regex = Regex::new(r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$").unwrap();
    assert!(uuid_regex.is_match(stdout.trim()));
}

#[test]
fn test_generate_rand_hex() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "rand", "16", "--hex"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = str::from_utf8(&output.stdout).unwrap();
    
    // Should be 32 hex characters (16 bytes * 2)
    assert_eq!(stdout.trim().len(), 32);
    
    // Should be valid hex
    let hex_regex = Regex::new(r"^[0-9a-f]{32}$").unwrap();
    assert!(hex_regex.is_match(stdout.trim()));
}

#[test]
fn test_generate_rand_base64() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "rand", "16", "--base64"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = str::from_utf8(&output.stdout).unwrap();
    
    // Base64 encoded 16 bytes should be 24 characters (with padding)
    assert_eq!(stdout.trim().len(), 24);
    
    // Should be valid base64
    assert!(general_purpose::STANDARD.decode(stdout.trim()).is_ok());
}

#[test]
fn test_generate_rand_raw() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "rand", "16"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    
    // Should output exactly 16 bytes
    assert_eq!(output.stdout.len(), 16);
}

#[test]
fn test_generate_help() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "--help"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = str::from_utf8(&output.stdout).unwrap();
    
    // Should contain all subcommands
    assert!(stdout.contains("uuid"));
    assert!(stdout.contains("rand"));
    assert!(stdout.contains("wg-keypair"));
    assert!(stdout.contains("tls-keypair"));
    assert!(stdout.contains("ech-keypair"));
    assert!(stdout.contains("reality-keypair"));
    assert!(stdout.contains("vapid"));
}

#[test]
fn test_generate_wg_keypair_not_implemented() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "wg-keypair"])
        .output()
        .expect("Failed to execute command");

    assert!(!output.status.success());
    let stderr = str::from_utf8(&output.stderr).unwrap();
    assert!(stderr.contains("WireGuard key generation not yet implemented"));
}

#[test]
fn test_generate_tls_keypair_not_implemented() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "tls-keypair", "example.com"])
        .output()
        .expect("Failed to execute command");

    assert!(!output.status.success());
    let stderr = str::from_utf8(&output.stderr).unwrap();
    assert!(stderr.contains("TLS key generation not yet implemented"));
}

#[test]
fn test_generate_ech_keypair_not_implemented() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "ech-keypair", "example.com"])
        .output()
        .expect("Failed to execute command");

    assert!(!output.status.success());
    let stderr = str::from_utf8(&output.stderr).unwrap();
    assert!(stderr.contains("ECH key generation not yet implemented"));
}

#[test]
fn test_generate_reality_keypair_not_implemented() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "reality-keypair"])
        .output()
        .expect("Failed to execute command");

    assert!(!output.status.success());
    let stderr = str::from_utf8(&output.stderr).unwrap();
    assert!(stderr.contains("Reality key generation not yet implemented"));
}

#[test]
fn test_generate_vapid_not_implemented() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "vapid"])
        .output()
        .expect("Failed to execute command");

    assert!(!output.status.success());
    let stderr = str::from_utf8(&output.stderr).unwrap();
    assert!(stderr.contains("VAPID key generation not yet implemented"));
}
