use std::process::Command;
use std::str;
use std::fs;
use regex::Regex;
use base64::{Engine as _, engine::general_purpose};

#[test]
fn test_generate_uuid() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "uuid"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = str::from_utf8(&output.stdout).unwrap();
    
    // UUID should be 36 characters with 4 hyphens
    assert_eq!(stdout.trim().len(), 36);
    assert_eq!(stdout.trim().matches('-').count(), 4);
    
    // Should be valid UUID format
    let uuid_regex = Regex::new(r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$").unwrap();
    assert!(uuid_regex.is_match(stdout.trim()));
}

#[test]
fn test_generate_rand_hex() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "rand", "16", "--hex"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = str::from_utf8(&output.stdout).unwrap();
    
    // Should be 32 hex characters (16 bytes * 2)
    assert_eq!(stdout.trim().len(), 32);
    
    // Should be valid hex
    let hex_regex = Regex::new(r"^[0-9a-f]{32}$").unwrap();
    assert!(hex_regex.is_match(stdout.trim()));
}

#[test]
fn test_generate_rand_base64() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["generate", "rand", "16", "--base64"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = str::from_utf8(&output.stdout).unwrap();
    
    // Base64 encoded 16 bytes should be 24 characters (with padding)
    assert_eq!(stdout.trim().len(), 24);
    
    // Should be valid base64
    assert!(general_purpose::STANDARD.decode(stdout.trim()).is_ok());
}

#[test]
fn test_merge_configs() {
    // Create test config files
    let config1 = r#"{
  "log": {
    "level": "info"
  },
  "inbounds": [
    {
      "inbound_type": "mixed",
      "tag": "mixed-in"
    }
  ],
  "outbounds": [
    {
      "outbound_type": "direct",
      "tag": "direct"
    }
  ]
}"#;

    let config2 = r#"{
  "dns": {
    "servers": [
      {
        "tag": "cloudflare",
        "address": "*******",
        "address_strategy": 0,
        "strategy": 0
      }
    ],
    "strategy": 0
  },
  "inbounds": [
    {
      "inbound_type": "socks",
      "tag": "socks-in"
    }
  ],
  "outbounds": [
    {
      "outbound_type": "shadowsocks",
      "tag": "ss-out"
    }
  ]
}"#;

    // Write test config files
    fs::write("test_merge1.json", config1).unwrap();
    fs::write("test_merge2.json", config2).unwrap();

    // Test merge command
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["merge", "test_merged.json", "-c", "test_merge1.json", "-c", "test_merge2.json"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    
    // Check that merged file was created
    assert!(fs::metadata("test_merged.json").is_ok());
    
    // Read and verify merged content
    let merged_content = fs::read_to_string("test_merged.json").unwrap();
    let merged_json: serde_json::Value = serde_json::from_str(&merged_content).unwrap();
    
    // Should have both inbounds
    assert_eq!(merged_json["inbounds"].as_array().unwrap().len(), 2);
    
    // Should have both outbounds
    assert_eq!(merged_json["outbounds"].as_array().unwrap().len(), 2);
    
    // Should have log config from first file
    assert_eq!(merged_json["log"]["level"], "info");
    
    // Should have DNS config from second file
    assert_eq!(merged_json["dns"]["servers"].as_array().unwrap().len(), 1);
    
    // Clean up test files
    let _ = fs::remove_file("test_merge1.json");
    let _ = fs::remove_file("test_merge2.json");
    let _ = fs::remove_file("test_merged.json");
}

#[test]
fn test_format_write_option() {
    // Create a test config file
    let config = r#"{"log":{"level":"info"},"inbounds":[{"inbound_type":"mixed","tag":"test"}],"outbounds":[{"outbound_type":"direct","tag":"direct"}]}"#;
    
    fs::write("test_format.json", config).unwrap();
    
    // Test format with --write option
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["format", "-c", "test_format.json", "--write"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    
    // Check that file was formatted (should be pretty-printed now)
    let formatted_content = fs::read_to_string("test_format.json").unwrap();
    assert!(formatted_content.contains("  \"log\": {"));  // Should be indented
    assert!(formatted_content.contains("\n"));  // Should have newlines
    
    // Clean up
    let _ = fs::remove_file("test_format.json");
}

#[test]
fn test_version_command() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["version"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = str::from_utf8(&output.stdout).unwrap();
    
    assert!(stdout.contains("sing-box version"));
    assert!(stdout.contains("Environment:"));
}

#[test]
fn test_version_name_only() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["version", "--name-only"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = str::from_utf8(&output.stdout).unwrap();
    
    // Should only contain version number, no extra text
    assert!(!stdout.contains("sing-box version"));
    assert!(!stdout.contains("Environment:"));
    assert!(stdout.trim().len() > 0);
}

#[test]
fn test_help_command() {
    let output = Command::new("./target/debug/singbox-rs")
        .args(&["--help"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = str::from_utf8(&output.stdout).unwrap();
    
    // Should contain all main commands
    assert!(stdout.contains("run"));
    assert!(stdout.contains("check"));
    assert!(stdout.contains("format"));
    assert!(stdout.contains("version"));
    assert!(stdout.contains("generate"));
    assert!(stdout.contains("merge"));
    assert!(stdout.contains("geoip"));
    assert!(stdout.contains("geosite"));
    assert!(stdout.contains("rule-set"));
    assert!(stdout.contains("tools"));
}

#[test]
fn test_unimplemented_commands_error_gracefully() {
    // Test that unimplemented commands return proper error messages
    let commands = vec![
        vec!["geoip", "list"],
        vec!["geosite", "list"],
        vec!["rule-set", "compile", "test.json"],
        vec!["tools", "connect", "example.com:80"],
    ];
    
    for cmd in commands {
        let output = Command::new("./target/debug/singbox-rs")
            .args(&cmd)
            .output()
            .expect("Failed to execute command");
        
        assert!(!output.status.success());
        let stderr = str::from_utf8(&output.stderr).unwrap();
        assert!(stderr.contains("not yet implemented"));
    }
}
