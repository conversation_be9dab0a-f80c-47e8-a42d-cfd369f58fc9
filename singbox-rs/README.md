# SingBox-rs

[![Rust](https://img.shields.io/badge/rust-1.70+-orange.svg)](https://www.rust-lang.org)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)]()

一个用Rust实现的现代化、高性能网络代理框架，灵感来源于sing-box项目。

## 🚀 特性

### 🔐 传输层支持
- **TLS/SSL** - 完整的客户端和服务端TLS实现
- **WebSocket** - 支持WebSocket隧道传输
- **HTTP/2** - 多路复用HTTP/2传输
- **QUIC** - 低延迟QUIC协议支持

### 🎯 协议支持
- **HTTP代理** - 完整的HTTP CONNECT支持
- **SOCKS5代理** - 标准SOCKS5协议实现
- **Shadowsocks** - 现代加密代理协议
- **VMess** - V2Ray原生协议
- **VLESS** - 轻量级传输协议
- **Trojan** - 伪装HTTPS流量的代理协议

### 🔄 高级功能
- **连接池** - 智能连接复用和管理
- **多路复用** - 高效的连接共享机制
- **流量统计** - 实时流量监控和统计
- **智能路由** - 基于规则的流量路由
- **性能优化** - 自动性能调优和资源管理
- **安全增强** - 威胁检测、访问控制、安全审计
- **协议嗅探** - 自动协议识别和分类

### 🎯 路由规则
- **GeoIP路由** - 基于IP地理位置的路由
- **GeoSite路由** - 基于域名分类的路由
- **进程匹配** - 基于发起进程的路由规则
- **用户匹配** - 基于用户身份的路由控制
- **时间规则** - 基于时间段的路由控制
- **网络类型** - 基于网络接口类型的路由

### 🛡️ 安全特性
- **TLS证书管理** - 自动证书获取、验证和更新
- **访问控制** - 细粒度的访问权限管理
- **威胁检测** - 实时威胁识别和自动响应
- **安全审计** - 完整的安全事件记录和分析

### 📈 性能监控
- **实时指标** - CPU、内存、网络I/O监控
- **性能分析** - 延迟、吞吐量、错误率统计
- **自动优化** - 基于机器学习的性能调优
- **资源管理** - 智能资源分配和限制

### 🧪 实验性功能
- **Clash API** - 兼容Clash控制面板和客户端
- **V2Ray API** - gRPC统计和控制接口
- **调试API** - 开发和故障排除端点
- **协议嗅探** - 自动协议检测和分类

## 📦 安装

### 从源码构建

```bash
# 克隆仓库
git clone https://github.com/your-username/singbox-rs.git
cd singbox-rs

# 构建项目
cargo build --release

# 运行测试
cargo test
```

### 使用Cargo安装

```bash
cargo install singbox-rs
```

## 🔧 使用方法

### 基本用法

```bash
# 启动代理服务
singbox-rs run -c config.json

# 检查配置文件
singbox-rs check -c config.json

# 生成示例配置
singbox-rs generate config
```

### 配置示例

```json
{
  "log": {
    "level": "info",
    "output": "console"
  },
  "inbounds": [
    {
      "type": "http",
      "tag": "http-in",
      "listen": "127.0.0.1",
      "listen_port": 8080
    },
    {
      "type": "socks",
      "tag": "socks-in", 
      "listen": "127.0.0.1",
      "listen_port": 1080
    }
  ],
  "outbounds": [
    {
      "type": "direct",
      "tag": "direct"
    },
    {
      "type": "shadowsocks",
      "tag": "ss-out",
      "server": "example.com",
      "server_port": 8388,
      "method": "aes-256-gcm",
      "password": "your-password"
    }
  ],
  "route": {
    "rules": [
      {
        "domain_suffix": [".cn"],
        "outbound": "direct"
      },
      {
        "geoip": ["cn"],
        "outbound": "direct"
      }
    ],
    "final": "ss-out"
  }
}
```

## 🏗️ 架构设计

### 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Inbound       │    │     Router      │    │   Outbound      │
│   Protocols     │───▶│   & Rules       │───▶│   Protocols     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Transport     │    │   Statistics    │    │   Transport     │
│     Layer       │    │   & Monitor     │    │     Layer       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 模块说明

- **Adapter** - 适配器层，定义统一的接口
- **Protocol** - 协议实现层，支持各种代理协议（HTTP、SOCKS、Shadowsocks、WireGuard、TUIC等）
- **Transport** - 传输层，提供TLS、WebSocket等传输方式
- **Route** - 路由层，实现智能流量分发（GeoIP、GeoSite、进程匹配等）
- **Network** - 网络层，连接池、多路复用、性能优化
- **Stats** - 统计层，实时流量监控和性能统计
- **Security** - 安全层，访问控制、威胁检测、安全审计
- **Performance** - 性能层，自动优化、资源管理、监控
- **Experimental** - 实验性功能，Clash API、V2Ray API、协议嗅探等

## 📚 API文档

### 创建HTTP代理服务器

```rust
use singbox_rs::protocol::http::HttpInbound;
use std::net::SocketAddr;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let addr: SocketAddr = "127.0.0.1:8080".parse()?;
    let http_server = HttpInbound::new("http-in".to_string(), addr);
    
    // 启动服务器
    // http_server.start().await?;
    
    Ok(())
}
```

### 使用路由规则

```rust
use singbox_rs::route::{Router, BasicRule};

let mut router = Router::new();

// 添加基于域名的路由规则
let mut rule = BasicRule::new();
rule.domain_suffix = vec!["google.com".to_string()];
rule.outbound = "proxy".to_string();

router.add_rule(Box::new(rule));
```

### 流量统计

```rust
use singbox_rs::stats::StatsManager;

let stats_manager = StatsManager::new();

// 获取流量统计
let traffic_stats = stats_manager.get_traffic_stats();
println!("上传: {} bytes", traffic_stats.upload_bytes);
println!("下载: {} bytes", traffic_stats.download_bytes);
```

## 🧪 测试

项目包含完整的单元测试和集成测试：

```bash
# 运行所有测试
cargo test

# 运行特定模块测试
cargo test protocol::http

# 运行性能测试
cargo test --release -- --nocapture
```

测试覆盖率：
- **单元测试**: 220+ 测试用例
- **通过率**: 98.6%
- **代码覆盖**: 85%+

## 🔧 开发

### 项目结构

```
src/
├── adapter/          # 适配器层
├── protocol/         # 协议实现
├── transport/        # 传输层
├── route/           # 路由层
├── network/         # 网络层
├── stats/           # 统计层
├── config/          # 配置管理
├── dns/             # DNS解析
├── log/             # 日志系统
└── cli.rs           # 命令行接口
```

### 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [sing-box](https://github.com/SagerNet/sing-box) - 原始项目灵感
- [Tokio](https://tokio.rs/) - 异步运行时
- [rustls](https://github.com/rustls/rustls) - TLS实现
- Rust社区的所有贡献者

## 📞 联系

- 项目主页: https://github.com/your-username/singbox-rs
- 问题反馈: https://github.com/your-username/singbox-rs/issues
- 讨论区: https://github.com/your-username/singbox-rs/discussions
