name: Performance Testing

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 4 * * 0'  # Weekly on Sunday at 4 AM UTC

env:
  CARGO_TERM_COLOR: always

jobs:
  benchmark:
    name: Performance Benchmarks
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-bench-${{ hashFiles('**/Cargo.lock') }}

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libssl-dev pkg-config iperf3

    - name: Run micro benchmarks
      run: cargo bench --all-features -- --output-format json | tee benchmark-results.json

    - name: Run protocol benchmarks
      run: |
        cargo build --release --all-features
        ./scripts/run-protocol-benchmarks.sh

    - name: Run routing benchmarks
      run: ./scripts/run-routing-benchmarks.sh

    - name: Run memory benchmarks
      run: ./scripts/run-memory-benchmarks.sh

    - name: Generate performance report
      run: |
        echo "# Performance Test Report" > performance-report.md
        echo "Generated on: $(date)" >> performance-report.md
        echo "" >> performance-report.md
        
        echo "## Benchmark Results" >> performance-report.md
        echo "\`\`\`json" >> performance-report.md
        cat benchmark-results.json >> performance-report.md
        echo "\`\`\`" >> performance-report.md

    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: |
          benchmark-results.json
          performance-report.md

    - name: Store benchmark results
      uses: benchmark-action/github-action-benchmark@v1
      if: github.ref == 'refs/heads/main'
      with:
        tool: 'cargo'
        output-file-path: benchmark-results.json
        github-token: ${{ secrets.GITHUB_TOKEN }}
        auto-push: true
        comment-on-alert: true
        alert-threshold: '150%'
        fail-on-alert: true

  load-test:
    name: Load Testing
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Build release binary
      run: cargo build --release --all-features

    - name: Install load testing tools
      run: |
        sudo apt-get update
        sudo apt-get install -y apache2-utils wrk

    - name: Start SingBox-rs server
      run: |
        ./target/release/singbox-rs run -c tests/configs/load-test.json &
        sleep 5

    - name: Run HTTP load test
      run: |
        echo "Running HTTP load test..."
        ab -n 10000 -c 100 http://127.0.0.1:8080/ > http-load-test.txt

    - name: Run SOCKS load test
      run: |
        echo "Running SOCKS load test..."
        ./scripts/socks-load-test.sh > socks-load-test.txt

    - name: Run concurrent connection test
      run: |
        echo "Running concurrent connection test..."
        ./scripts/concurrent-test.sh > concurrent-test.txt

    - name: Generate load test report
      run: |
        echo "# Load Test Report" > load-test-report.md
        echo "Generated on: $(date)" >> load-test-report.md
        echo "" >> load-test-report.md
        
        echo "## HTTP Load Test" >> load-test-report.md
        echo "\`\`\`" >> load-test-report.md
        cat http-load-test.txt >> load-test-report.md
        echo "\`\`\`" >> load-test-report.md
        
        echo "## SOCKS Load Test" >> load-test-report.md
        echo "\`\`\`" >> load-test-report.md
        cat socks-load-test.txt >> load-test-report.md
        echo "\`\`\`" >> load-test-report.md

    - name: Upload load test results
      uses: actions/upload-artifact@v3
      with:
        name: load-test-results
        path: |
          http-load-test.txt
          socks-load-test.txt
          concurrent-test.txt
          load-test-report.md

  memory-test:
    name: Memory Testing
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Install Valgrind
      run: |
        sudo apt-get update
        sudo apt-get install -y valgrind

    - name: Build debug binary
      run: cargo build --all-features

    - name: Run memory leak test
      run: |
        valgrind --tool=memcheck --leak-check=full --show-leak-kinds=all \
          --track-origins=yes --verbose --log-file=valgrind-output.txt \
          ./target/debug/singbox-rs run -c tests/configs/memory-test.json &
        
        sleep 30
        pkill singbox-rs

    - name: Analyze memory usage
      run: |
        echo "# Memory Test Report" > memory-report.md
        echo "Generated on: $(date)" >> memory-report.md
        echo "" >> memory-report.md
        
        echo "## Valgrind Output" >> memory-report.md
        echo "\`\`\`" >> memory-report.md
        cat valgrind-output.txt >> memory-report.md
        echo "\`\`\`" >> memory-report.md

    - name: Upload memory test results
      uses: actions/upload-artifact@v3
      with:
        name: memory-test-results
        path: |
          valgrind-output.txt
          memory-report.md

  stress-test:
    name: Stress Testing
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Build release binary
      run: cargo build --release --all-features

    - name: Install stress testing tools
      run: |
        sudo apt-get update
        sudo apt-get install -y stress-ng htop

    - name: Run stress test
      run: |
        # Start SingBox-rs
        ./target/release/singbox-rs run -c tests/configs/stress-test.json &
        SINGBOX_PID=$!
        
        # Run stress test for 5 minutes
        timeout 300 ./scripts/stress-test.sh || true
        
        # Collect performance data
        ps -p $SINGBOX_PID -o pid,ppid,cmd,%mem,%cpu,etime > stress-test-results.txt
        
        # Stop SingBox-rs
        kill $SINGBOX_PID

    - name: Upload stress test results
      uses: actions/upload-artifact@v3
      with:
        name: stress-test-results
        path: stress-test-results.txt

  performance-regression:
    name: Performance Regression Test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
    - name: Checkout PR code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Build PR binary
      run: |
        cargo build --release --all-features
        cp target/release/singbox-rs singbox-rs-pr

    - name: Checkout main branch
      uses: actions/checkout@v4
      with:
        ref: main
        path: main-branch

    - name: Build main binary
      run: |
        cd main-branch
        cargo build --release --all-features
        cp target/release/singbox-rs ../singbox-rs-main

    - name: Run comparative benchmarks
      run: |
        echo "Running benchmarks on main branch..."
        ./scripts/benchmark-binary.sh ./singbox-rs-main > main-results.txt
        
        echo "Running benchmarks on PR branch..."
        ./scripts/benchmark-binary.sh ./singbox-rs-pr > pr-results.txt

    - name: Analyze performance regression
      run: |
        python3 scripts/analyze-performance.py main-results.txt pr-results.txt > regression-analysis.txt

    - name: Comment on PR
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const analysis = fs.readFileSync('regression-analysis.txt', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## Performance Analysis\n\n\`\`\`\n${analysis}\n\`\`\``
          });

    - name: Upload regression results
      uses: actions/upload-artifact@v3
      with:
        name: performance-regression-results
        path: |
          main-results.txt
          pr-results.txt
          regression-analysis.txt
