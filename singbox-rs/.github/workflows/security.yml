name: Security Scan

on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM UTC
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Install cargo-audit
      run: cargo install cargo-audit

    - name: Run cargo audit
      run: cargo audit --json > audit-results.json

    - name: Upload audit results
      uses: actions/upload-artifact@v3
      with:
        name: security-audit-results
        path: audit-results.json

  dependency-check:
    name: Dependency Check
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Install cargo-deny
      run: cargo install cargo-deny

    - name: Run cargo deny
      run: cargo deny check

    - name: Check for known vulnerabilities
      run: cargo deny check advisories

    - name: Check licenses
      run: cargo deny check licenses

    - name: Check banned dependencies
      run: cargo deny check bans

  sast-scan:
    name: Static Analysis
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        components: clippy

    - name: Run Clippy security lints
      run: |
        cargo clippy --all-targets --all-features -- \
          -W clippy::suspicious \
          -W clippy::complexity \
          -W clippy::perf \
          -W clippy::style \
          -W clippy::pedantic

    - name: Install semgrep
      run: python3 -m pip install semgrep

    - name: Run semgrep security scan
      run: semgrep --config=auto --json --output=semgrep-results.json .

    - name: Upload semgrep results
      uses: actions/upload-artifact@v3
      with:
        name: semgrep-results
        path: semgrep-results.json

  secret-scan:
    name: Secret Scanning
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Run TruffleHog
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD
        extra_args: --debug --only-verified

  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Build Docker image
      run: docker build -t singbox-rs:latest .

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'singbox-rs:latest'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  codeql:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: rust

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Build project
      run: cargo build --all-features

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2

  security-report:
    name: Security Report
    runs-on: ubuntu-latest
    needs: [security-audit, dependency-check, sast-scan, secret-scan, container-scan, codeql]
    if: always()
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v3

    - name: Generate security report
      run: |
        echo "# Security Scan Report" > security-report.md
        echo "Generated on: $(date)" >> security-report.md
        echo "" >> security-report.md
        
        echo "## Audit Results" >> security-report.md
        if [ -f security-audit-results/audit-results.json ]; then
          echo "✅ Security audit completed" >> security-report.md
        else
          echo "❌ Security audit failed" >> security-report.md
        fi
        
        echo "" >> security-report.md
        echo "## SAST Results" >> security-report.md
        if [ -f semgrep-results/semgrep-results.json ]; then
          echo "✅ Static analysis completed" >> security-report.md
        else
          echo "❌ Static analysis failed" >> security-report.md
        fi

    - name: Upload security report
      uses: actions/upload-artifact@v3
      with:
        name: security-report
        path: security-report.md
