name: Release

on:
  push:
    tags:
      - 'v*'

env:
  CARGO_TERM_COLOR: always

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      release_id: ${{ steps.create_release.outputs.id }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Generate changelog
      id: changelog
      run: |
        # Extract version from tag
        VERSION=${GITHUB_REF#refs/tags/}
        echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
        
        # Generate changelog from commits
        git log --pretty=format:"- %s" $(git describe --tags --abbrev=0 HEAD^)..HEAD > CHANGELOG.md
        
        # Read changelog content
        CHANGELOG=$(cat CHANGELOG.md)
        echo "CHANGELOG<<EOF" >> $GITHUB_OUTPUT
        echo "$CHANGELOG" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ steps.changelog.outputs.VERSION }}
        body: |
          ## Changes in ${{ steps.changelog.outputs.VERSION }}
          
          ${{ steps.changelog.outputs.CHANGELOG }}
          
          ## Installation
          
          ### Binary Downloads
          Download the appropriate binary for your platform from the assets below.
          
          ### Docker
          ```bash
          docker pull singbox-rs/singbox-rs:${{ steps.changelog.outputs.VERSION }}
          ```
          
          ### Cargo
          ```bash
          cargo install singbox-rs --version ${{ steps.changelog.outputs.VERSION }}
          ```
          
          ## Verification
          
          All binaries are signed and can be verified using the provided checksums.
        draft: false
        prerelease: ${{ contains(github.ref, 'alpha') || contains(github.ref, 'beta') || contains(github.ref, 'rc') }}

  build-release:
    name: Build Release (${{ matrix.target }})
    runs-on: ${{ matrix.os }}
    needs: create-release
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            artifact_name: singbox-rs
            asset_name: singbox-rs-linux-amd64
          - os: ubuntu-latest
            target: aarch64-unknown-linux-gnu
            artifact_name: singbox-rs
            asset_name: singbox-rs-linux-arm64
          - os: ubuntu-latest
            target: x86_64-unknown-linux-musl
            artifact_name: singbox-rs
            asset_name: singbox-rs-linux-amd64-musl
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            artifact_name: singbox-rs.exe
            asset_name: singbox-rs-windows-amd64.exe
          - os: macos-latest
            target: x86_64-apple-darwin
            artifact_name: singbox-rs
            asset_name: singbox-rs-macos-amd64
          - os: macos-latest
            target: aarch64-apple-darwin
            artifact_name: singbox-rs
            asset_name: singbox-rs-macos-arm64

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: ${{ matrix.target }}

    - name: Install cross-compilation tools
      if: matrix.target == 'aarch64-unknown-linux-gnu'
      run: |
        sudo apt-get update
        sudo apt-get install -y gcc-aarch64-linux-gnu

    - name: Install musl tools
      if: matrix.target == 'x86_64-unknown-linux-musl'
      run: |
        sudo apt-get update
        sudo apt-get install -y musl-tools

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-${{ matrix.target }}-cargo-release-${{ hashFiles('**/Cargo.lock') }}

    - name: Build release binary
      run: |
        cargo build --release --target ${{ matrix.target }} --all-features
        
        # Set environment variables for cross-compilation
        if [ "${{ matrix.target }}" = "aarch64-unknown-linux-gnu" ]; then
          export CC=aarch64-linux-gnu-gcc
          export CXX=aarch64-linux-gnu-g++
        fi

    - name: Strip binary (Unix)
      if: matrix.os != 'windows-latest'
      run: |
        if [ "${{ matrix.target }}" = "aarch64-unknown-linux-gnu" ]; then
          aarch64-linux-gnu-strip target/${{ matrix.target }}/release/${{ matrix.artifact_name }}
        else
          strip target/${{ matrix.target }}/release/${{ matrix.artifact_name }}
        fi

    - name: Create archive
      run: |
        cd target/${{ matrix.target }}/release
        if [ "${{ matrix.os }}" = "windows-latest" ]; then
          7z a ../../../${{ matrix.asset_name }}.zip ${{ matrix.artifact_name }}
        else
          tar czf ../../../${{ matrix.asset_name }}.tar.gz ${{ matrix.artifact_name }}
        fi

    - name: Generate checksums
      run: |
        if [ "${{ matrix.os }}" = "windows-latest" ]; then
          sha256sum ${{ matrix.asset_name }}.zip > ${{ matrix.asset_name }}.zip.sha256
        else
          sha256sum ${{ matrix.asset_name }}.tar.gz > ${{ matrix.asset_name }}.tar.gz.sha256
        fi

    - name: Upload release asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: ${{ matrix.asset_name }}.${{ matrix.os == 'windows-latest' && 'zip' || 'tar.gz' }}
        asset_name: ${{ matrix.asset_name }}.${{ matrix.os == 'windows-latest' && 'zip' || 'tar.gz' }}
        asset_content_type: application/octet-stream

    - name: Upload checksum
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: ${{ matrix.asset_name }}.${{ matrix.os == 'windows-latest' && 'zip' || 'tar.gz' }}.sha256
        asset_name: ${{ matrix.asset_name }}.${{ matrix.os == 'windows-latest' && 'zip' || 'tar.gz' }}.sha256
        asset_content_type: text/plain

  docker-release:
    name: Docker Release
    runs-on: ubuntu-latest
    needs: create-release
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: |
          singbox-rs/singbox-rs
          ghcr.io/${{ github.repository }}
        tags: |
          type=ref,event=tag
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  publish-crate:
    name: Publish to crates.io
    runs-on: ubuntu-latest
    needs: [create-release, build-release]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-publish-${{ hashFiles('**/Cargo.lock') }}

    - name: Publish to crates.io
      run: cargo publish --token ${{ secrets.CRATES_IO_TOKEN }}

  update-homebrew:
    name: Update Homebrew Formula
    runs-on: ubuntu-latest
    needs: [build-release]
    if: "!contains(github.ref, 'alpha') && !contains(github.ref, 'beta') && !contains(github.ref, 'rc')"
    steps:
    - name: Extract version
      id: version
      run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

    - name: Update Homebrew formula
      uses: mislav/bump-homebrew-formula-action@v2
      with:
        formula-name: singbox-rs
        formula-path: Formula/singbox-rs.rb
        homebrew-tap: your-org/homebrew-tap
        download-url: https://github.com/${{ github.repository }}/releases/download/${{ github.ref_name }}/singbox-rs-macos-amd64.tar.gz
        commit-message: |
          singbox-rs ${{ steps.version.outputs.VERSION }}
          
          Created by ${{ github.actor }}
      env:
        COMMITTER_TOKEN: ${{ secrets.HOMEBREW_TAP_TOKEN }}

  notify:
    name: Release Notification
    runs-on: ubuntu-latest
    needs: [build-release, docker-release, publish-crate]
    if: always()
    steps:
    - name: Send Discord notification
      if: success()
      uses: Ilshidur/action-discord@master
      env:
        DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
      with:
        args: |
          🚀 **SingBox-rs ${{ github.ref_name }} Released!**
          
          ✅ Binaries built for all platforms
          ✅ Docker images published
          ✅ Crate published to crates.io
          
          Download: https://github.com/${{ github.repository }}/releases/tag/${{ github.ref_name }}

    - name: Send failure notification
      if: failure()
      uses: Ilshidur/action-discord@master
      env:
        DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
      with:
        args: |
          ❌ **SingBox-rs ${{ github.ref_name }} Release Failed!**
          
          Please check the GitHub Actions logs for details.
          
          Workflow: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
