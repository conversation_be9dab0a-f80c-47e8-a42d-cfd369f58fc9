# 协议实现校准报告

## 🎯 校准目标

确保Rust版本的协议实现与原始sing-box Go版本完全一致：
- **协议行为100%一致** - 所有协议的处理逻辑相同
- **数据包格式一致** - 网络数据包格式完全相同
- **握手流程一致** - 连接建立过程相同
- **错误处理一致** - 错误情况处理逻辑相同

## 📊 当前协议实现状态

### ✅ 已实现协议 (6种)
1. **HTTP** - HTTP代理协议 ✅
2. **SOCKS** - SOCKS4/5代理协议 ✅
3. **Shadowsocks** - 加密代理协议 ✅
4. **VMess** - V2Ray原生协议 ✅
5. **VLESS** - 轻量级传输协议 ✅
6. **Trojan** - 伪装HTTPS代理协议 ✅

### ❌ 需要新增协议 (14种)

#### 入站协议缺失
1. **Mixed** - 混合代理协议 (HTTP+SOCKS)
2. **Naive** - 基于Chromium的代理
3. **Hysteria** - 基于QUIC的高性能协议
4. **Hysteria2** - Hysteria第二版
5. **ShadowTLS** - 基于TLS的Shadowsocks
6. **TUIC** - 基于QUIC的代理协议
7. **AnyTLS** - 通用TLS代理
8. **Tun** - TUN接口代理
9. **Redirect** - 透明代理
10. **TProxy** - 透明代理

#### 出站协议缺失
1. **WireGuard** - VPN协议
2. **Tor** - 洋葱路由
3. **SSH** - SSH隧道
4. **DNS** - DNS查询出站

## 🔧 协议校准实施计划

### 阶段1: 混合协议实现 (Mixed)
```rust
// src/protocol/inbound/mixed.rs
pub struct MixedInbound {
    config: MixedConfig,
    http_handler: HttpInbound,
    socks_handler: SocksInbound,
}

impl MixedInbound {
    pub async fn handle_connection(&self, conn: Connection) -> Result<(), Error> {
        // 检测协议类型
        let protocol = self.detect_protocol(&conn).await?;
        
        match protocol {
            Protocol::Http => self.http_handler.handle_connection(conn).await,
            Protocol::Socks => self.socks_handler.handle_connection(conn).await,
            _ => Err(Error::UnsupportedProtocol),
        }
    }
    
    async fn detect_protocol(&self, conn: &Connection) -> Result<Protocol, Error> {
        // 协议检测逻辑
        // 读取第一个字节判断是HTTP还是SOCKS
    }
}
```

### 阶段2: Hysteria协议实现
```rust
// src/protocol/inbound/hysteria.rs
pub struct HysteriaInbound {
    config: HysteriaConfig,
    quic_listener: QuicListener,
}

impl HysteriaInbound {
    pub async fn handle_connection(&self, conn: QuicConnection) -> Result<(), Error> {
        // Hysteria握手
        let handshake = self.perform_handshake(&conn).await?;
        
        // 处理数据流
        self.handle_streams(conn, handshake).await
    }
    
    async fn perform_handshake(&self, conn: &QuicConnection) -> Result<Handshake, Error> {
        // Hysteria特定的握手逻辑
    }
}
```

### 阶段3: WireGuard协议实现
```rust
// src/protocol/outbound/wireguard.rs
pub struct WireGuardOutbound {
    config: WireGuardConfig,
    private_key: PrivateKey,
    peer_public_key: PublicKey,
    endpoint: SocketAddr,
}

impl WireGuardOutbound {
    pub async fn dial(&self, destination: Destination) -> Result<Connection, Error> {
        // WireGuard连接建立
        let tunnel = self.establish_tunnel().await?;
        
        // 通过隧道连接目标
        self.connect_through_tunnel(tunnel, destination).await
    }
}
```

## 📋 详细协议校准清单

### 1. HTTP协议校准
- ✅ **CONNECT方法** - 支持HTTPS隧道
- ✅ **认证机制** - Basic/Digest认证
- ✅ **头部处理** - 正确处理HTTP头部
- ❌ **HTTP/2支持** - 需要添加HTTP/2支持
- ❌ **WebSocket升级** - 需要支持WebSocket升级

### 2. SOCKS协议校准
- ✅ **SOCKS4支持** - 基本SOCKS4协议
- ✅ **SOCKS5支持** - 完整SOCKS5协议
- ✅ **认证方法** - 无认证、用户名密码认证
- ❌ **GSSAPI认证** - 需要添加GSSAPI支持
- ❌ **UDP关联** - 需要完善UDP支持

### 3. Shadowsocks协议校准
- ✅ **AEAD加密** - 现代AEAD加密方法
- ✅ **流加密** - 传统流加密方法
- ❌ **2022版协议** - 需要支持Shadowsocks 2022
- ❌ **多用户支持** - 需要支持多用户模式
- ❌ **UDP over TCP** - 需要支持UDP over TCP

### 4. VMess协议校准
- ✅ **基本协议** - VMess基本协议支持
- ✅ **AEAD加密** - VMess AEAD模式
- ❌ **动态端口** - 需要支持动态端口
- ❌ **mKCP传输** - 需要支持mKCP传输
- ❌ **WebSocket传输** - 需要完善WebSocket传输

### 5. VLESS协议校准
- ✅ **基本协议** - VLESS基本协议支持
- ✅ **流控制** - XTLS流控制
- ❌ **Vision流控** - 需要支持Vision流控
- ❌ **Reality伪装** - 需要支持Reality伪装
- ❌ **gRPC传输** - 需要支持gRPC传输

### 6. Trojan协议校准
- ✅ **基本协议** - Trojan基本协议支持
- ✅ **TLS伪装** - TLS流量伪装
- ❌ **Trojan-Go扩展** - 需要支持Trojan-Go扩展
- ❌ **WebSocket传输** - 需要支持WebSocket传输
- ❌ **gRPC传输** - 需要支持gRPC传输

## 🔍 协议实现验证

### 1. 兼容性测试
```rust
#[cfg(test)]
mod protocol_compatibility_tests {
    use super::*;
    
    #[tokio::test]
    async fn test_http_proxy_compatibility() {
        // 测试与标准HTTP代理的兼容性
    }
    
    #[tokio::test]
    async fn test_socks5_compatibility() {
        // 测试与标准SOCKS5的兼容性
    }
    
    #[tokio::test]
    async fn test_shadowsocks_compatibility() {
        // 测试与官方Shadowsocks的兼容性
    }
}
```

### 2. 性能基准测试
```rust
#[cfg(test)]
mod protocol_benchmarks {
    use criterion::{black_box, criterion_group, criterion_main, Criterion};
    
    fn benchmark_http_proxy(c: &mut Criterion) {
        c.bench_function("http_proxy_throughput", |b| {
            b.iter(|| {
                // HTTP代理吞吐量测试
            })
        });
    }
}
```

### 3. 互操作性测试
```rust
#[cfg(test)]
mod interoperability_tests {
    #[tokio::test]
    async fn test_with_original_singbox() {
        // 与原版sing-box的互操作性测试
    }
    
    #[tokio::test]
    async fn test_with_v2ray_core() {
        // 与V2Ray Core的互操作性测试
    }
}
```

## 🚀 协议扩展架构

### 1. 协议注册系统
```rust
// src/protocol/registry.rs
pub struct ProtocolRegistry {
    inbound_factories: HashMap<String, Box<dyn InboundFactory>>,
    outbound_factories: HashMap<String, Box<dyn OutboundFactory>>,
}

impl ProtocolRegistry {
    pub fn register_inbound<T: InboundFactory + 'static>(&mut self, name: &str, factory: T) {
        self.inbound_factories.insert(name.to_string(), Box::new(factory));
    }
    
    pub fn create_inbound(&self, protocol: &str, config: &serde_json::Value) -> Result<Box<dyn Inbound>, Error> {
        let factory = self.inbound_factories.get(protocol)
            .ok_or_else(|| Error::UnsupportedProtocol(protocol.to_string()))?;
        factory.create(config)
    }
}
```

### 2. 协议工厂模式
```rust
// src/protocol/factory.rs
pub trait InboundFactory: Send + Sync {
    fn create(&self, config: &serde_json::Value) -> Result<Box<dyn Inbound>, Error>;
    fn protocol_name(&self) -> &str;
    fn supported_versions(&self) -> Vec<String>;
}

pub trait OutboundFactory: Send + Sync {
    fn create(&self, config: &serde_json::Value) -> Result<Box<dyn Outbound>, Error>;
    fn protocol_name(&self) -> &str;
    fn supported_versions(&self) -> Vec<String>;
}
```

## 📊 实施时间表

### 第1周: 混合协议和基础扩展
- [ ] 实现Mixed协议
- [ ] 完善HTTP/2支持
- [ ] 增强SOCKS5功能

### 第2周: 现代协议实现
- [ ] 实现Hysteria协议
- [ ] 实现Hysteria2协议
- [ ] 实现TUIC协议

### 第3周: VPN和隧道协议
- [ ] 实现WireGuard协议
- [ ] 实现SSH隧道
- [ ] 实现Tor支持

### 第4周: 高级功能和优化
- [ ] 实现ShadowTLS
- [ ] 实现Naive协议
- [ ] 性能优化和测试

## 🎯 成功标准

### 1. 功能完整性
- ✅ 所有协议功能实现
- ✅ 配置选项完整
- ✅ 错误处理完善

### 2. 兼容性验证
- ✅ 与原版sing-box兼容
- ✅ 与其他客户端兼容
- ✅ 配置文件兼容

### 3. 性能指标
- ✅ 吞吐量不低于原版
- ✅ 延迟不高于原版
- ✅ 内存使用合理

### 4. 代码质量
- ✅ 单元测试覆盖率>90%
- ✅ 集成测试完整
- ✅ 文档完善

通过这个详细的协议校准计划，我们将确保Rust版本的协议实现与原版完全一致，同时利用Rust的优势提供更好的性能和安全性。
