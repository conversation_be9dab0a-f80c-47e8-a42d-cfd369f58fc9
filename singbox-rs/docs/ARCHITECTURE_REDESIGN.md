# SingBox-rs 架构重新设计

## 🎯 设计目标

基于原始sing-box Go版本的深度分析，重新设计Rust版本架构以实现：
1. **100%功能对齐** - 与原版功能完全一致
2. **配置兼容性** - 配置文件完全兼容
3. **API兼容性** - 外部接口完全兼容
4. **性能优化** - 利用Rust优势提升性能
5. **类型安全** - 编译时保证正确性

## 🏗️ 整体架构设计

### 分层架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        CLI Layer                            │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │   run   │ │  check  │ │ format  │ │generate │ │  tools  │ │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Box Core                               │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   Lifecycle     │ │   Service       │ │   Context       │ │
│  │   Manager       │ │   Manager       │ │   Manager       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Adapter Layer                            │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │    Inbound      │ │    Outbound     │ │     Router      │ │
│  │    Adapter      │ │    Adapter      │ │    Adapter      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Implementation Layer                        │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│ │Protocol │ │Transport│ │  Route  │ │   DNS   │ │  Stats  │ │
│ │ Layer   │ │  Layer  │ │  Layer  │ │  Layer  │ │  Layer  │ │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Foundation Layer                         │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│ │ Network │ │  Common │ │   Log   │ │ Config  │ │ Constant│ │
│ │  Layer  │ │  Utils  │ │ System  │ │ System  │ │ Defines │ │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构重新设计

```
singbox-rs/
├── src/
│   ├── main.rs                 # 程序入口
│   ├── lib.rs                  # 库入口
│   │
│   ├── box/                    # 核心Box实现
│   │   ├── mod.rs              # Box核心模块
│   │   ├── lifecycle.rs        # 生命周期管理
│   │   ├── service.rs          # 服务管理
│   │   └── context.rs          # 上下文管理
│   │
│   ├── cli/                    # CLI命令层
│   │   ├── mod.rs              # CLI模块入口
│   │   ├── run.rs              # run命令
│   │   ├── check.rs            # check命令
│   │   ├── format.rs           # format命令
│   │   ├── generate/           # generate命令组
│   │   │   ├── mod.rs
│   │   │   ├── uuid.rs
│   │   │   ├── rand.rs
│   │   │   ├── keypair.rs
│   │   │   └── vapid.rs
│   │   ├── geoip/              # geoip命令组
│   │   │   ├── mod.rs
│   │   │   ├── list.rs
│   │   │   ├── lookup.rs
│   │   │   └── export.rs
│   │   ├── geosite/            # geosite命令组
│   │   ├── rule_set/           # rule-set命令组
│   │   ├── tools/              # tools命令组
│   │   └── merge.rs            # merge命令
│   │
│   ├── adapter/                # 适配器层
│   │   ├── mod.rs              # 适配器模块入口
│   │   ├── inbound.rs          # 入站适配器trait
│   │   ├── outbound.rs         # 出站适配器trait
│   │   ├── router.rs           # 路由适配器trait
│   │   ├── service.rs          # 服务适配器trait
│   │   └── lifecycle.rs        # 生命周期trait
│   │
│   ├── protocol/               # 协议实现层
│   │   ├── mod.rs              # 协议模块入口
│   │   ├── registry.rs         # 协议注册器
│   │   │
│   │   ├── inbound/            # 入站协议
│   │   │   ├── mod.rs
│   │   │   ├── direct.rs       # Direct协议
│   │   │   ├── mixed.rs        # Mixed协议
│   │   │   ├── socks.rs        # SOCKS协议
│   │   │   ├── http.rs         # HTTP协议
│   │   │   ├── shadowsocks.rs  # Shadowsocks协议
│   │   │   ├── vmess.rs        # VMess协议
│   │   │   ├── vless.rs        # VLESS协议
│   │   │   ├── trojan.rs       # Trojan协议
│   │   │   ├── naive.rs        # Naive协议
│   │   │   ├── hysteria.rs     # Hysteria协议
│   │   │   ├── hysteria2.rs    # Hysteria2协议
│   │   │   ├── shadowtls.rs    # ShadowTLS协议
│   │   │   ├── tuic.rs         # TUIC协议
│   │   │   ├── anytls.rs       # AnyTLS协议
│   │   │   ├── tun.rs          # TUN协议
│   │   │   ├── redirect.rs     # Redirect协议
│   │   │   └── tproxy.rs       # TProxy协议
│   │   │
│   │   └── outbound/           # 出站协议
│   │       ├── mod.rs
│   │       ├── direct.rs       # Direct协议
│   │       ├── block.rs        # Block协议
│   │       ├── dns.rs          # DNS协议
│   │       ├── socks.rs        # SOCKS协议
│   │       ├── http.rs         # HTTP协议
│   │       ├── shadowsocks.rs  # Shadowsocks协议
│   │       ├── vmess.rs        # VMess协议
│   │       ├── vless.rs        # VLESS协议
│   │       ├── trojan.rs       # Trojan协议
│   │       ├── wireguard.rs    # WireGuard协议
│   │       ├── hysteria.rs     # Hysteria协议
│   │       ├── hysteria2.rs    # Hysteria2协议
│   │       ├── shadowtls.rs    # ShadowTLS协议
│   │       ├── tuic.rs         # TUIC协议
│   │       ├── anytls.rs       # AnyTLS协议
│   │       ├── tor.rs          # Tor协议
│   │       ├── ssh.rs          # SSH协议
│   │       ├── selector.rs     # Selector协议
│   │       └── urltest.rs      # URLTest协议
│   │
│   ├── transport/              # 传输层
│   │   ├── mod.rs              # 传输模块入口
│   │   ├── tcp.rs              # TCP传输
│   │   ├── udp.rs              # UDP传输
│   │   ├── tls.rs              # TLS传输
│   │   ├── quic.rs             # QUIC传输
│   │   ├── websocket.rs        # WebSocket传输
│   │   ├── http2.rs            # HTTP/2传输
│   │   ├── grpc.rs             # gRPC传输
│   │   └── v2ray/              # V2Ray传输
│   │       ├── mod.rs
│   │       ├── http.rs         # V2Ray HTTP传输
│   │       ├── websocket.rs    # V2Ray WebSocket传输
│   │       ├── quic.rs         # V2Ray QUIC传输
│   │       └── httpupgrade.rs  # HTTP Upgrade传输
│   │
│   ├── route/                  # 路由层
│   │   ├── mod.rs              # 路由模块入口
│   │   ├── router.rs           # 路由器实现
│   │   ├── rule.rs             # 路由规则
│   │   ├── rule_set.rs         # 规则集
│   │   ├── geoip.rs            # GeoIP支持
│   │   ├── geosite.rs          # GeoSite支持
│   │   ├── sniff.rs            # 协议嗅探
│   │   └── context.rs          # 路由上下文
│   │
│   ├── dns/                    # DNS层
│   │   ├── mod.rs              # DNS模块入口
│   │   ├── client.rs           # DNS客户端
│   │   ├── server.rs           # DNS服务器
│   │   ├── rule.rs             # DNS规则
│   │   ├── cache.rs            # DNS缓存
│   │   ├── fakeip.rs           # FakeIP支持
│   │   └── transport/          # DNS传输
│   │       ├── mod.rs
│   │       ├── tcp.rs          # TCP DNS
│   │       ├── udp.rs          # UDP DNS
│   │       ├── tls.rs          # DoT (DNS over TLS)
│   │       ├── https.rs        # DoH (DNS over HTTPS)
│   │       ├── http3.rs        # DoH3 (DNS over HTTP/3)
│   │       ├── quic.rs         # DoQ (DNS over QUIC)
│   │       ├── dhcp.rs         # DHCP DNS
│   │       ├── tailscale.rs    # Tailscale DNS
│   │       └── resolved.rs     # Resolved DNS
│   │
│   ├── stats/                  # 统计层
│   │   ├── mod.rs              # 统计模块入口
│   │   ├── manager.rs          # 统计管理器
│   │   ├── tracker.rs          # 流量跟踪
│   │   ├── counter.rs          # 计数器
│   │   └── monitor.rs          # 监控器
│   │
│   ├── service/                # 服务层
│   │   ├── mod.rs              # 服务模块入口
│   │   ├── derp.rs             # DERP服务
│   │   ├── resolved.rs         # Resolved服务
│   │   └── ssm_api.rs          # SSM API服务
│   │
│   ├── experimental/           # 实验性功能
│   │   ├── mod.rs              # 实验模块入口
│   │   ├── clash_api.rs        # Clash API
│   │   ├── v2ray_api.rs        # V2Ray API
│   │   └── cache_file.rs       # 缓存文件
│   │
│   ├── config/                 # 配置系统
│   │   ├── mod.rs              # 配置模块入口
│   │   ├── parser.rs           # 配置解析器
│   │   ├── validator.rs        # 配置验证器
│   │   ├── merger.rs           # 配置合并器
│   │   ├── formatter.rs        # 配置格式化器
│   │   └── types/              # 配置类型定义
│   │       ├── mod.rs
│   │       ├── log.rs          # 日志配置
│   │       ├── dns.rs          # DNS配置
│   │       ├── ntp.rs          # NTP配置
│   │       ├── certificate.rs  # 证书配置
│   │       ├── endpoint.rs     # 端点配置
│   │       ├── inbound.rs      # 入站配置
│   │       ├── outbound.rs     # 出站配置
│   │       ├── route.rs        # 路由配置
│   │       ├── service.rs      # 服务配置
│   │       └── experimental.rs # 实验配置
│   │
│   ├── network/                # 网络层
│   │   ├── mod.rs              # 网络模块入口
│   │   ├── dialer.rs           # 拨号器
│   │   ├── listener.rs         # 监听器
│   │   ├── connection.rs       # 连接管理
│   │   ├── multiplex.rs        # 多路复用
│   │   └── pool.rs             # 连接池
│   │
│   ├── common/                 # 通用工具
│   │   ├── mod.rs              # 通用模块入口
│   │   ├── interrupt.rs        # 中断处理
│   │   ├── bufio.rs            # 缓冲IO
│   │   ├── json.rs             # JSON工具
│   │   ├── tls.rs              # TLS工具
│   │   ├── auth.rs             # 认证工具
│   │   ├── process.rs          # 进程工具
│   │   └── platform.rs         # 平台特定
│   │
│   ├── log/                    # 日志系统
│   │   ├── mod.rs              # 日志模块入口
│   │   ├── logger.rs           # 日志器
│   │   ├── writer.rs           # 日志写入器
│   │   └── formatter.rs        # 日志格式化器
│   │
│   └── constant/               # 常量定义
│       ├── mod.rs              # 常量模块入口
│       ├── protocol.rs         # 协议常量
│       ├── transport.rs        # 传输常量
│       ├── route.rs            # 路由常量
│       └── dns.rs              # DNS常量
│
├── tests/                      # 测试目录
│   ├── integration/            # 集成测试
│   ├── unit/                   # 单元测试
│   └── fixtures/               # 测试数据
│
├── examples/                   # 示例代码
│   ├── basic_proxy.rs          # 基础代理示例
│   ├── advanced_routing.rs     # 高级路由示例
│   └── custom_protocol.rs      # 自定义协议示例
│
├── docs/                       # 文档目录
│   ├── api/                    # API文档
│   ├── config/                 # 配置文档
│   └── examples/               # 示例文档
│
├── benches/                    # 性能测试
│   ├── protocol_bench.rs       # 协议性能测试
│   ├── transport_bench.rs      # 传输性能测试
│   └── routing_bench.rs        # 路由性能测试
│
├── Cargo.toml                  # 项目配置
├── Cargo.lock                  # 依赖锁定
├── README.md                   # 项目说明
└── LICENSE                     # 许可证
```

## 🔧 核心Trait设计

### 1. 适配器层Trait
```rust
// 基础适配器trait
pub trait Adapter: Send + Sync {
    fn adapter_type(&self) -> &str;
    fn tag(&self) -> &str;
}

// 入站适配器trait
pub trait Inbound: Adapter + Lifecycle {
    async fn new_connection(&self, ctx: Context, conn: Connection) -> Result<(), Error>;
    async fn new_packet(&self, ctx: Context, packet: Packet) -> Result<(), Error>;
}

// 出站适配器trait
pub trait Outbound: Adapter + Lifecycle {
    async fn dial_connection(&self, ctx: Context, destination: Destination) -> Result<Connection, Error>;
    async fn new_packet_connection(&self, ctx: Context) -> Result<PacketConnection, Error>;
}

// 路由适配器trait
pub trait Router: Adapter + Lifecycle {
    async fn route(&self, ctx: Context, metadata: InboundContext) -> Result<String, Error>;
    async fn route_packet(&self, ctx: Context, metadata: InboundContext) -> Result<String, Error>;
}
```

### 2. 生命周期管理Trait
```rust
pub trait Lifecycle: Send + Sync {
    async fn start(&mut self, stage: StartStage) -> Result<(), Error>;
    async fn close(&mut self) -> Result<(), Error>;
}

#[derive(Debug, Clone, Copy)]
pub enum StartStage {
    Initialize,
    Start,
    PostStart,
}
```

### 3. 服务管理Trait
```rust
pub trait Service: Lifecycle {
    fn service_type(&self) -> &str;
    fn service_name(&self) -> &str;
}
```

## 📋 配置系统重新设计

### 配置结构完全对齐
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub log: Option<LogConfig>,
    pub dns: Option<DnsConfig>,
    pub ntp: Option<NtpConfig>,
    pub certificate: Option<CertificateConfig>,
    pub endpoints: Option<Vec<EndpointConfig>>,
    pub inbounds: Option<Vec<InboundConfig>>,
    pub outbounds: Option<Vec<OutboundConfig>>,
    pub route: Option<RouteConfig>,
    pub services: Option<Vec<ServiceConfig>>,
    pub experimental: Option<ExperimentalConfig>,
}
```

## 🚀 性能优化设计

### 1. 异步优先设计
- 所有IO操作使用async/await
- 基于Tokio运行时
- 零拷贝数据传输

### 2. 内存管理优化
- 使用Arc/Rc进行共享
- 避免不必要的克隆
- 智能缓冲区管理

### 3. 并发安全设计
- 使用RwLock而非Mutex
- 无锁数据结构
- 原子操作优化

## 🔒 类型安全设计

### 1. 强类型配置
- 编译时配置验证
- 类型安全的选项
- 自动类型转换

### 2. 错误处理
- 统一的错误类型
- 详细的错误信息
- 错误链追踪

### 3. 生命周期管理
- 明确的资源所有权
- 自动资源清理
- 防止内存泄漏

## 📊 兼容性保证

### 1. 配置兼容性
- 100%配置文件兼容
- 相同的配置选项
- 相同的默认值

### 2. API兼容性
- 相同的CLI接口
- 相同的输出格式
- 相同的错误码

### 3. 行为兼容性
- 相同的协议行为
- 相同的路由逻辑
- 相同的DNS处理

这个重新设计的架构确保了与原版sing-box的完全对齐，同时充分利用了Rust的优势。
