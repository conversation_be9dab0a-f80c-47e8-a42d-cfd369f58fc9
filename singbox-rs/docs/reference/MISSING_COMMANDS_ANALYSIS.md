# 缺失命令完整分析

## 当前实现状态

### ✅ 已实现的命令
- `run` - 运行服务
- `check` - 检查配置
- `format` - 格式化配置
- `version` - 显示版本信息

### ❌ 缺失的命令 (需要实现)

## 1. generate 命令组

### 1.1 generate uuid
```bash
sing-box generate uuid
```
**功能**: 生成UUID字符串
**输出**: 标准UUID格式 (例: 550e8400-e29b-41d4-a716-************)

### 1.2 generate rand
```bash
sing-box generate rand <length> [--base64] [--hex]
```
**功能**: 生成随机字节
**参数**:
- `<length>`: 字节长度
- `--base64`: 输出base64编码
- `--hex`: 输出十六进制编码
**默认**: 输出原始字节

### 1.3 generate wg-keypair
```bash
sing-box generate wg-keypair
```
**功能**: 生成WireGuard密钥对
**输出**: 私钥和公钥

### 1.4 generate tls-keypair
```bash
sing-box generate tls-keypair <server_name> [--months <months>]
```
**功能**: 生成TLS自签名证书
**参数**:
- `<server_name>`: 服务器名称
- `--months`: 有效期月数 (默认3个月)

### 1.5 generate ech-keypair
```bash
sing-box generate ech-keypair <plain_server_name>
```
**功能**: 生成TLS ECH密钥对
**参数**:
- `<plain_server_name>`: 明文服务器名称

### 1.6 generate reality-keypair
```bash
sing-box generate reality-keypair
```
**功能**: 生成Reality协议密钥对

### 1.7 generate vapid
```bash
sing-box generate vapid
```
**功能**: 生成VAPID密钥对

## 2. geoip 命令组

### 2.1 geoip list
```bash
sing-box geoip list [-f <file>]
```
**功能**: 列出GeoIP数据库中的国家代码
**参数**:
- `-f, --file`: GeoIP数据库文件路径 (默认: geoip.db)

### 2.2 geoip lookup
```bash
sing-box geoip lookup <address> [-f <file>]
```
**功能**: 查询IP地址的国家代码
**参数**:
- `<address>`: IP地址
- `-f, --file`: GeoIP数据库文件路径

### 2.3 geoip export
```bash
sing-box geoip export <country> [-o <output>] [-f <file>]
```
**功能**: 导出国家的IP段为规则集
**参数**:
- `<country>`: 国家代码
- `-o, --output`: 输出文件路径
- `-f, --file`: GeoIP数据库文件路径

## 3. geosite 命令组

### 3.1 geosite list
```bash
sing-box geosite list [-f <file>]
```
**功能**: 列出GeoSite数据库中的分类
**参数**:
- `-f, --file`: GeoSite数据库文件路径 (默认: geosite.db)

### 3.2 geosite lookup
```bash
sing-box geosite lookup [category] <domain> [-f <file>]
```
**功能**: 查询域名是否在GeoSite分类中
**参数**:
- `[category]`: 分类名称 (可选)
- `<domain>`: 域名
- `-f, --file`: GeoSite数据库文件路径

### 3.3 geosite export
```bash
sing-box geosite export <category> [-o <output>] [-f <file>]
```
**功能**: 导出分类的域名为规则集
**参数**:
- `<category>`: 分类名称
- `-o, --output`: 输出文件路径
- `-f, --file`: GeoSite数据库文件路径

## 4. merge 命令

```bash
sing-box merge <output-path> [-c <config>] [-C <config-dir>] [-D <work-dir>]
```
**功能**: 合并多个配置文件
**参数**:
- `<output-path>`: 输出文件路径
- 使用全局配置选项指定输入文件

## 5. rule-set 命令组

### 5.1 rule-set compile
```bash
sing-box rule-set compile <source-path> [-o <output>]
```
**功能**: 编译规则集为二进制格式

### 5.2 rule-set decompile
```bash
sing-box rule-set decompile <source-path> [-o <output>]
```
**功能**: 反编译二进制规则集为JSON

### 5.3 rule-set format
```bash
sing-box rule-set format <source-path> [-w]
```
**功能**: 格式化规则集JSON
**参数**:
- `-w, --write`: 写入源文件而不是stdout

### 5.4 rule-set upgrade
```bash
sing-box rule-set upgrade <source-path> [-w]
```
**功能**: 升级规则集格式

### 5.5 rule-set convert
```bash
sing-box rule-set convert <source-path> --type <type> [-o <output>]
```
**功能**: 转换其他格式到规则集
**参数**:
- `--type`: 源格式类型 (adguard等)

### 5.6 rule-set merge
```bash
sing-box rule-set merge <output-path> [-c <config>] [-C <config-dir>]
```
**功能**: 合并多个规则集文件

### 5.7 rule-set match
```bash
sing-box rule-set match <rule-set-path> <IP/domain> [-f <format>]
```
**功能**: 测试IP或域名是否匹配规则集
**参数**:
- `-f, --format`: 规则集格式 (source/binary)

## 6. tools 命令组

### 6.1 tools connect
```bash
sing-box tools connect <address> [-n <network>] [-o <outbound>]
```
**功能**: 连接到指定地址
**参数**:
- `<address>`: 目标地址
- `-n, --network`: 网络类型 (tcp/udp)
- `-o, --outbound`: 指定出站标签

### 6.2 tools fetch
```bash
sing-box tools fetch <url> [-o <outbound>]
```
**功能**: 通过代理获取URL内容
**参数**:
- `<url>`: 目标URL
- `-o, --outbound`: 指定出站标签

### 6.3 tools synctime
```bash
sing-box tools synctime [-s <server>] [-f <format>] [-w]
```
**功能**: 使用NTP协议同步时间
**参数**:
- `-s, --server`: NTP服务器地址
- `-f, --format`: 时间输出格式
- `-w, --write`: 写入系统时间

## 实现优先级

### 高优先级 (迭代17-18)
1. `generate` 命令组 - 基础工具功能
2. `merge` 命令 - 配置管理核心功能
3. `format` 命令的 `-w` 选项补充

### 中优先级 (迭代19-20)
1. `rule-set` 命令组 - 规则管理功能
2. `geoip`/`geosite` 命令组 - 地理位置功能

### 低优先级 (迭代21-22)
1. `tools` 命令组 - 实验性工具功能

## 技术要求

### 依赖库需求
- `uuid` - UUID生成
- `rand` - 随机数生成
- `base64` - Base64编码
- `hex` - 十六进制编码
- `maxminddb` - GeoIP数据库读取
- `wireguard` - WireGuard密钥生成
- `tls` - TLS证书生成
- `ntp` - NTP时间同步

### 错误处理
- 与原版完全一致的错误消息
- 相同的退出代码
- 一致的错误格式

### 输出格式
- 精确匹配原版输出
- 相同的日志格式
- 一致的进度显示

## 验证标准

每个命令实现后必须通过：
1. **功能测试** - 所有参数组合
2. **输出验证** - 与原版输出对比
3. **错误测试** - 错误场景处理
4. **性能测试** - 性能不低于原版
5. **集成测试** - 与其他命令协同工作
