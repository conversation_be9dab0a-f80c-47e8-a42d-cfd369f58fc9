# SingBox-rs 基本使用示例

## 1. 简单HTTP代理服务器

```rust
use singbox_rs::protocol::http::HttpInbound;
use singbox_rs::adapter::Lifecycle;
use std::net::SocketAddr;
use std::collections::HashMap;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建HTTP代理服务器
    let addr: SocketAddr = "127.0.0.1:8080".parse()?;
    let mut http_server = HttpInbound::new(
        "http-proxy".to_string(),
        addr,
        HashMap::new() // 无需认证
    );
    
    println!("启动HTTP代理服务器在 {}", addr);
    
    // 启动服务器
    http_server.start().await?;
    
    // 等待Ctrl+C信号
    tokio::signal::ctrl_c().await?;
    println!("收到关闭信号，正在关闭服务器...");
    
    // 优雅关闭
    http_server.close().await?;
    println!("服务器已关闭");
    
    Ok(())
}
```

## 2. SOCKS5代理服务器

```rust
use singbox_rs::protocol::socks::{SocksInbound, SocksVersion};
use singbox_rs::adapter::Lifecycle;
use std::net::SocketAddr;
use std::collections::HashMap;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建用户认证信息
    let mut users = HashMap::new();
    users.insert("user1".to_string(), "password1".to_string());
    users.insert("user2".to_string(), "password2".to_string());
    
    // 创建SOCKS5代理服务器
    let addr: SocketAddr = "127.0.0.1:1080".parse()?;
    let mut socks_server = SocksInbound::new(
        "socks5-proxy".to_string(),
        addr,
        SocksVersion::V5,
        users
    );
    
    println!("启动SOCKS5代理服务器在 {}", addr);
    
    // 启动服务器
    socks_server.start().await?;
    
    // 等待关闭信号
    tokio::signal::ctrl_c().await?;
    
    // 关闭服务器
    socks_server.close().await?;
    
    Ok(())
}
```

## 3. Shadowsocks客户端

```rust
use singbox_rs::protocol::shadowsocks::{ShadowsocksOutbound, ShadowsocksMethod};
use std::net::SocketAddr;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建Shadowsocks客户端
    let server_addr: SocketAddr = "example.com:8388".parse()?;
    let outbound = ShadowsocksOutbound::new(
        "ss-client".to_string(),
        server_addr,
        ShadowsocksMethod::Aes256Gcm,
        "your-password".to_string()
    );
    
    // 连接到目标服务器
    let connection = outbound.dial("www.google.com", 80, "tcp").await?;
    println!("成功连接到目标服务器");
    
    Ok(())
}
```

## 4. 带路由的代理服务器

```rust
use singbox_rs::route::{Router, BasicRule};
use singbox_rs::protocol::http::HttpInbound;
use singbox_rs::protocol::shadowsocks::ShadowsocksOutbound;
use singbox_rs::adapter::{Lifecycle, InboundContext};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建路由器
    let mut router = Router::new();
    
    // 添加直连规则（中国网站）
    let mut direct_rule = BasicRule::new();
    direct_rule.domain_suffix = vec![".cn".to_string(), ".baidu.com".to_string()];
    direct_rule.outbound = "direct".to_string();
    router.add_rule(Box::new(direct_rule));
    
    // 添加代理规则（其他网站）
    let mut proxy_rule = BasicRule::new();
    proxy_rule.domain_suffix = vec![".google.com".to_string(), ".youtube.com".to_string()];
    proxy_rule.outbound = "proxy".to_string();
    router.add_rule(Box::new(proxy_rule));
    
    // 设置默认出站
    router.set_default_outbound("proxy".to_string());
    
    // 创建HTTP入站
    let addr = "127.0.0.1:8080".parse()?;
    let mut http_server = HttpInbound::new("http-in".to_string(), addr);
    
    println!("启动带路由的代理服务器");
    http_server.start().await?;
    
    tokio::signal::ctrl_c().await?;
    http_server.close().await?;
    
    Ok(())
}
```

## 5. 流量统计监控

```rust
use singbox_rs::stats::StatsManager;
use tokio::time::{interval, Duration};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let stats_manager = StatsManager::new();
    
    // 模拟流量更新
    tokio::spawn({
        let stats = stats_manager.clone();
        async move {
            let mut counter = 0u64;
            loop {
                // 模拟流量数据
                stats.update_traffic(1024, 2048).await;
                counter += 1;
                
                if counter % 10 == 0 {
                    stats.record_connection_attempt();
                    if counter % 20 == 0 {
                        stats.record_connection_success();
                    } else {
                        stats.record_connection_failure();
                    }
                }
                
                tokio::time::sleep(Duration::from_millis(100)).await;
            }
        }
    });
    
    // 定期打印统计信息
    let mut interval = interval(Duration::from_secs(5));
    loop {
        interval.tick().await;
        
        let traffic_stats = stats_manager.get_traffic_stats();
        println!("=== 流量统计 ===");
        println!("上传: {} bytes ({} bytes/s)", 
                traffic_stats.upload_bytes, 
                traffic_stats.upload_speed);
        println!("下载: {} bytes ({} bytes/s)", 
                traffic_stats.download_bytes, 
                traffic_stats.download_speed);
        println!("活跃连接: {}", traffic_stats.connections);
        println!("总连接数: {}", traffic_stats.total_connections);
        println!("失败连接: {}", traffic_stats.failed_connections);
        println!();
    }
}
```

## 6. TLS传输示例

```rust
use singbox_rs::transport::tls::{TlsDialer, TlsClientConfig};
use singbox_rs::transport::TransportDialer;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建TLS配置
    let config = TlsClientConfig {
        server_name: "www.google.com".to_string(),
        insecure: false,
        ca_certs: vec![],
        client_cert: None,
        client_key: None,
    };
    
    // 创建TLS拨号器
    let dialer = TlsDialer::new(config)?;
    
    // 建立TLS连接
    let connection = dialer.dial("www.google.com:443").await?;
    println!("TLS连接建立成功");
    
    // 获取连接统计
    let stats = connection.stats();
    println!("连接统计: {:?}", stats);
    
    Ok(())
}
```

## 7. WebSocket传输示例

```rust
use singbox_rs::transport::websocket::{WebSocketDialer, WebSocketConfig};
use singbox_rs::transport::TransportDialer;
use std::collections::HashMap;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建WebSocket配置
    let mut headers = HashMap::new();
    headers.insert("User-Agent".to_string(), "SingBox-rs/1.0".to_string());
    
    let config = WebSocketConfig {
        path: "/ws".to_string(),
        headers,
        max_early_data: 0,
        early_data_header_name: None,
    };
    
    // 创建WebSocket拨号器
    let dialer = WebSocketDialer::new(config);
    
    // 建立WebSocket连接
    let connection = dialer.dial("ws://echo.websocket.org/ws").await?;
    println!("WebSocket连接建立成功");
    
    Ok(())
}
```

## 8. 完整的代理服务器

```rust
use singbox_rs::{
    protocol::{http::HttpInbound, shadowsocks::ShadowsocksOutbound},
    route::{Router, BasicRule},
    stats::StatsManager,
    adapter::Lifecycle,
};
use std::sync::Arc;
use tokio::sync::Mutex;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::init();
    
    // 创建统计管理器
    let stats_manager = Arc::new(StatsManager::new());
    
    // 创建路由器
    let mut router = Router::new();
    
    // 配置路由规则
    let mut cn_rule = BasicRule::new();
    cn_rule.domain_suffix = vec![".cn".to_string()];
    cn_rule.outbound = "direct".to_string();
    router.add_rule(Box::new(cn_rule));
    
    router.set_default_outbound("proxy".to_string());
    
    // 创建HTTP入站服务器
    let http_addr = "127.0.0.1:8080".parse()?;
    let mut http_server = HttpInbound::new("http-in".to_string(), http_addr);
    
    // 启动服务器
    println!("启动代理服务器...");
    http_server.start().await?;
    
    // 启动统计监控
    let stats_clone = stats_manager.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(30));
        loop {
            interval.tick().await;
            let stats = stats_clone.get_traffic_stats();
            tracing::info!(
                "流量统计 - 上传: {}KB, 下载: {}KB, 连接: {}",
                stats.upload_bytes / 1024,
                stats.download_bytes / 1024,
                stats.connections
            );
        }
    });
    
    println!("代理服务器运行在 http://127.0.0.1:8080");
    println!("按 Ctrl+C 停止服务器");
    
    // 等待关闭信号
    tokio::signal::ctrl_c().await?;
    
    println!("正在关闭服务器...");
    http_server.close().await?;
    
    println!("服务器已关闭");
    Ok(())
}
```

## 编译和运行

```bash
# 编译项目
cargo build --release

# 运行示例
cargo run --example basic_http_proxy
cargo run --example socks5_proxy
cargo run --example shadowsocks_client

# 运行测试
cargo test
```

## 配置文件示例

创建 `config.json`:

```json
{
  "log": {
    "level": "info"
  },
  "inbounds": [
    {
      "type": "http",
      "tag": "http-in",
      "listen": "127.0.0.1",
      "listen_port": 8080
    }
  ],
  "outbounds": [
    {
      "type": "direct",
      "tag": "direct"
    },
    {
      "type": "shadowsocks",
      "tag": "ss-out",
      "server": "example.com",
      "server_port": 8388,
      "method": "aes-256-gcm",
      "password": "your-password"
    }
  ],
  "route": {
    "rules": [
      {
        "domain_suffix": [".cn"],
        "outbound": "direct"
      }
    ],
    "final": "ss-out"
  }
}
```

然后运行：

```bash
cargo run -- run -c config.json
```
