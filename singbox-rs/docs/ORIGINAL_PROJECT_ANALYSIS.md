# SingBox 原始项目深度分析

## 🔍 项目概述

**SingBox** 是一个用Go语言编写的通用代理平台，由SagerNet组织开发。它是一个功能完整、高性能的网络代理框架，支持多种协议和传输方式。

### 基本信息
- **项目名称**: sing-box
- **开发语言**: Go
- **开源协议**: GPL v3
- **GitHub地址**: https://github.com/SagerNet/sing-box
- **官方文档**: https://sing-box.sagernet.org/
- **Star数量**: 26.3k+
- **Fork数量**: 3.1k+

## 🏗️ 源码架构分析

### 目录结构
```
sing-box/
├── adapter/           # 适配器层 - 定义统一接口
├── clients/           # 客户端实现
├── cmd/              # 命令行工具
├── common/           # 通用工具和函数
├── constant/         # 常量定义
├── dns/              # DNS解析模块
├── docs/             # 文档
├── experimental/     # 实验性功能
├── include/          # 头文件
├── log/              # 日志系统
├── option/           # 配置选项
├── protocol/         # 协议实现
├── release/          # 发布相关
├── route/            # 路由系统
├── service/          # 服务模块
├── test/             # 测试文件
├── transport/        # 传输层
├── box.go            # 核心Box实现
├── debug.go          # 调试功能
└── go.mod            # Go模块定义
```

### 核心模块分析

#### 1. Adapter 层 (适配器层)
- **作用**: 定义统一的接口规范
- **核心接口**:
  - `Inbound`: 入站连接处理
  - `Outbound`: 出站连接处理
  - `Router`: 路由处理
  - `Service`: 服务管理

#### 2. Protocol 层 (协议层)
支持的协议包括：
- **HTTP**: HTTP代理协议
- **SOCKS**: SOCKS4/SOCKS5代理
- **Shadowsocks**: 现代加密代理
- **VMess**: V2Ray原生协议
- **VLESS**: 轻量级传输协议
- **Trojan**: 伪装HTTPS的代理协议
- **Hysteria/Hysteria2**: 基于QUIC的高性能协议
- **TUIC**: 基于QUIC的代理协议
- **Naive**: 基于Chromium网络栈的代理
- **ShadowTLS**: 基于TLS的Shadowsocks变种
- **WireGuard**: VPN协议
- **Tor**: 洋葱路由
- **SSH**: SSH隧道

#### 3. Transport 层 (传输层)
支持的传输方式：
- **TCP**: 标准TCP传输
- **UDP**: UDP传输
- **TLS**: TLS加密传输
- **QUIC**: 基于UDP的多路复用传输
- **WebSocket**: WebSocket传输
- **HTTP/2**: HTTP/2多路复用
- **gRPC**: gRPC传输
- **V2Ray Transport**: V2Ray传输协议族

#### 4. Route 层 (路由层)
路由功能包括：
- **基础路由**: 基于域名、IP、端口的路由
- **GeoIP**: 基于IP地理位置的路由
- **GeoSite**: 基于域名分类的路由
- **进程匹配**: 基于进程名的路由
- **用户匹配**: 基于用户的路由
- **协议嗅探**: 自动检测协议类型
- **规则集**: 外部规则文件支持

#### 5. DNS 层 (DNS解析)
DNS功能包括：
- **多种DNS服务器**: TCP、UDP、TLS、QUIC、HTTPS、HTTP3
- **DNS规则**: 基于域名的DNS路由
- **FakeIP**: 虚拟IP分配
- **DNS缓存**: 智能DNS缓存
- **特殊DNS**: DHCP、Tailscale、Resolved

## 📋 配置格式分析

### 配置文件结构
```json
{
  "log": {},              // 日志配置
  "dns": {},              // DNS配置
  "ntp": {},              // NTP时间同步
  "certificate": {},      // 证书管理
  "endpoints": [],        // 端点配置
  "inbounds": [],         // 入站配置
  "outbounds": [],        // 出站配置
  "route": {},            // 路由配置
  "services": [],         // 服务配置
  "experimental": {}      // 实验性功能
}
```

### 关键配置字段

#### Inbound 配置
```json
{
  "type": "http|socks|shadowsocks|vmess|...",
  "tag": "inbound-tag",
  "listen": "0.0.0.0",
  "listen_port": 8080,
  "users": [],
  "tls": {},
  "transport": {}
}
```

#### Outbound 配置
```json
{
  "type": "direct|block|socks|http|shadowsocks|...",
  "tag": "outbound-tag",
  "server": "example.com",
  "server_port": 443,
  "username": "user",
  "password": "pass",
  "tls": {},
  "transport": {}
}
```

#### Route 配置
```json
{
  "geoip": {},
  "geosite": {},
  "rules": [
    {
      "inbound": ["tag"],
      "outbound": "tag",
      "domain": ["example.com"],
      "domain_suffix": [".com"],
      "domain_keyword": ["google"],
      "domain_regex": [".*\\.cn$"],
      "geoip": ["cn"],
      "geosite": ["google"],
      "source_geoip": ["cn"],
      "source_port": [80, 443],
      "port": [80, 443],
      "process_name": ["chrome"],
      "user": ["user1"]
    }
  ],
  "final": "direct",
  "auto_detect_interface": true
}
```

## 🔧 核心功能特性

### 1. 多协议支持
- 支持20+种代理协议
- 协议间可以自由组合
- 支持协议自动检测

### 2. 高级路由
- 多维度路由规则
- 支持外部规则集
- 实时规则更新

### 3. 传输层多样性
- 支持多种传输方式
- 传输层可插拔设计
- 支持传输层加密

### 4. 性能优化
- 多路复用支持
- 连接池管理
- 零拷贝优化

### 5. 可观测性
- 详细的日志系统
- 实时统计信息
- API接口支持

### 6. 扩展性
- 插件化架构
- 实验性功能支持
- 第三方集成

## 🎯 设计理念

### 1. 统一接口
通过Adapter层定义统一的接口，使得不同协议和传输方式可以无缝集成。

### 2. 模块化设计
每个功能模块都是独立的，可以单独开发、测试和维护。

### 3. 配置驱动
通过JSON配置文件驱动所有功能，无需重新编译即可改变行为。

### 4. 性能优先
在设计时优先考虑性能，使用高效的数据结构和算法。

### 5. 可扩展性
预留扩展接口，方便添加新的协议和功能。

## 📊 技术栈分析

### 核心依赖
- **Go语言**: 1.20+
- **网络库**: 自研sing系列网络库
- **加密库**: 标准库crypto + 第三方加密库
- **配置解析**: encoding/json
- **日志系统**: 自研日志框架

### 关键技术
- **协程池**: 高效的协程管理
- **内存池**: 减少GC压力
- **零拷贝**: 网络数据传输优化
- **多路复用**: 连接复用技术
- **协议嗅探**: 自动协议识别

## 🔍 与当前Rust版本的差距

### 功能完整性
1. **协议支持**: 原版支持20+协议，当前Rust版本仅支持6种
2. **传输层**: 原版支持10+传输方式，当前版本支持4种
3. **路由功能**: 原版路由功能更加完善和复杂
4. **DNS系统**: 原版DNS功能更加强大
5. **配置系统**: 原版配置更加灵活和完整

### 架构设计
1. **接口定义**: 需要重新设计Rust版本的trait系统
2. **模块组织**: 需要重新组织模块结构
3. **错误处理**: 需要统一的错误处理机制
4. **生命周期**: 需要合理的资源生命周期管理

### 性能特性
1. **并发模型**: 需要基于Tokio重新设计并发模型
2. **内存管理**: 利用Rust的所有权系统优化内存使用
3. **零拷贝**: 实现Rust版本的零拷贝优化
4. **类型安全**: 利用Rust的类型系统提供更好的安全性

## 📋 下一步行动计划

1. **功能差距识别**: 详细对比每个模块的功能差异
2. **架构重新设计**: 基于原版架构重新设计Rust版本
3. **核心功能重构**: 重构核心功能模块
4. **配置系统对齐**: 确保配置格式完全兼容
5. **协议实现校准**: 校准所有协议实现
6. **路由系统重构**: 重构路由系统
7. **完整性验证**: 全面验证功能对齐程度
