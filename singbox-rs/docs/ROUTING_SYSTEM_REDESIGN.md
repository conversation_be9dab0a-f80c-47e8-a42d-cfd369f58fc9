# 路由系统重构报告

## 🎯 重构目标

重新设计路由系统以与原始sing-box Go版本完全对齐：
- **路由规则100%兼容** - 支持所有原版路由规则
- **匹配逻辑完全一致** - 规则匹配行为相同
- **性能优化** - 利用Rust优势提升路由性能
- **扩展性增强** - 支持未来路由功能扩展

## 📊 当前路由系统状态

### ✅ 已实现功能
- **基础路由规则匹配** - 域名、IP、端口匹配
- **GeoIP基础支持** - 基本的GeoIP路由
- **GeoSite基础支持** - 基本的GeoSite路由
- **进程匹配** - 部分进程匹配功能

### ❌ 缺失功能 (需要重构实现)
- **高级路由规则** - 复杂规则组合
- **协议嗅探** - 自动协议检测
- **规则集支持** - 外部规则文件
- **动态路由** - 运行时路由更新
- **路由统计** - 路由命中统计
- **负载均衡** - 多出站负载均衡

## 🏗️ 新路由系统架构

### 1. 核心路由器设计
```rust
// src/route/router.rs
pub struct Router {
    rules: Vec<Arc<dyn RouteRule>>,
    rule_sets: HashMap<String, Arc<RuleSet>>,
    geoip_matcher: Arc<GeoIpMatcher>,
    geosite_matcher: Arc<GeoSiteMatcher>,
    protocol_sniffer: Arc<ProtocolSniffer>,
    default_outbound: String,
    stats: Arc<RouteStats>,
}

impl Router {
    pub async fn route(&self, ctx: &RouteContext) -> Result<RouteResult, RouteError> {
        // 协议嗅探
        let sniffed_protocol = self.protocol_sniffer.sniff(ctx).await?;
        
        // 规则匹配
        for rule in &self.rules {
            if rule.matches(ctx, &sniffed_protocol).await? {
                let outbound = rule.outbound();
                self.stats.record_hit(rule.name(), outbound).await;
                return Ok(RouteResult::new(outbound, rule.name()));
            }
        }
        
        // 默认路由
        Ok(RouteResult::new(&self.default_outbound, "default"))
    }
}
```

### 2. 路由规则系统
```rust
// src/route/rule.rs
#[async_trait]
pub trait RouteRule: Send + Sync {
    fn name(&self) -> &str;
    fn outbound(&self) -> &str;
    async fn matches(&self, ctx: &RouteContext, protocol: &Option<Protocol>) -> Result<bool, RouteError>;
}

pub struct CompositeRule {
    name: String,
    outbound: String,
    conditions: Vec<Box<dyn RuleCondition>>,
    logic: RuleLogic, // AND, OR
    invert: bool,
}

impl RouteRule for CompositeRule {
    async fn matches(&self, ctx: &RouteContext, protocol: &Option<Protocol>) -> Result<bool, RouteError> {
        let mut results = Vec::new();
        
        for condition in &self.conditions {
            results.push(condition.evaluate(ctx, protocol).await?);
        }
        
        let result = match self.logic {
            RuleLogic::And => results.iter().all(|&x| x),
            RuleLogic::Or => results.iter().any(|&x| x),
        };
        
        Ok(if self.invert { !result } else { result })
    }
}
```

### 3. 路由条件系统
```rust
// src/route/condition.rs
#[async_trait]
pub trait RuleCondition: Send + Sync {
    async fn evaluate(&self, ctx: &RouteContext, protocol: &Option<Protocol>) -> Result<bool, RouteError>;
}

// 域名条件
pub struct DomainCondition {
    domains: HashSet<String>,
    suffixes: Vec<String>,
    keywords: Vec<String>,
    regexes: Vec<Regex>,
}

impl RuleCondition for DomainCondition {
    async fn evaluate(&self, ctx: &RouteContext, _protocol: &Option<Protocol>) -> Result<bool, RouteError> {
        let domain = ctx.destination.domain();
        
        // 精确匹配
        if self.domains.contains(domain) {
            return Ok(true);
        }
        
        // 后缀匹配
        for suffix in &self.suffixes {
            if domain.ends_with(suffix) {
                return Ok(true);
            }
        }
        
        // 关键字匹配
        for keyword in &self.keywords {
            if domain.contains(keyword) {
                return Ok(true);
            }
        }
        
        // 正则匹配
        for regex in &self.regexes {
            if regex.is_match(domain) {
                return Ok(true);
            }
        }
        
        Ok(false)
    }
}

// GeoIP条件
pub struct GeoIpCondition {
    countries: Vec<String>,
    matcher: Arc<GeoIpMatcher>,
}

impl RuleCondition for GeoIpCondition {
    async fn evaluate(&self, ctx: &RouteContext, _protocol: &Option<Protocol>) -> Result<bool, RouteError> {
        let ip = ctx.destination.ip();
        let country = self.matcher.lookup(ip).await?;
        Ok(self.countries.contains(&country))
    }
}

// 进程条件
pub struct ProcessCondition {
    names: Vec<String>,
    paths: Vec<String>,
    path_regexes: Vec<Regex>,
}

impl RuleCondition for ProcessCondition {
    async fn evaluate(&self, ctx: &RouteContext, _protocol: &Option<Protocol>) -> Result<bool, RouteError> {
        let process_info = ctx.process_info.as_ref().ok_or(RouteError::NoProcessInfo)?;
        
        // 进程名匹配
        if self.names.contains(&process_info.name) {
            return Ok(true);
        }
        
        // 进程路径匹配
        if self.paths.contains(&process_info.path) {
            return Ok(true);
        }
        
        // 进程路径正则匹配
        for regex in &self.path_regexes {
            if regex.is_match(&process_info.path) {
                return Ok(true);
            }
        }
        
        Ok(false)
    }
}
```

## 🔍 协议嗅探系统

### 1. 协议嗅探器
```rust
// src/route/sniff.rs
pub struct ProtocolSniffer {
    sniffers: Vec<Box<dyn ProtocolDetector>>,
    timeout: Duration,
}

impl ProtocolSniffer {
    pub async fn sniff(&self, ctx: &RouteContext) -> Result<Option<Protocol>, RouteError> {
        let mut buffer = [0u8; 1024];
        let n = tokio::time::timeout(self.timeout, ctx.connection.peek(&mut buffer)).await??;
        
        for sniffer in &self.sniffers {
            if let Some(protocol) = sniffer.detect(&buffer[..n]).await? {
                return Ok(Some(protocol));
            }
        }
        
        Ok(None)
    }
}

#[async_trait]
pub trait ProtocolDetector: Send + Sync {
    async fn detect(&self, data: &[u8]) -> Result<Option<Protocol>, RouteError>;
}

// HTTP协议检测
pub struct HttpDetector;

impl ProtocolDetector for HttpDetector {
    async fn detect(&self, data: &[u8]) -> Result<Option<Protocol>, RouteError> {
        let text = std::str::from_utf8(data).ok()?;
        
        if text.starts_with("GET ") || text.starts_with("POST ") || 
           text.starts_with("PUT ") || text.starts_with("DELETE ") ||
           text.starts_with("HEAD ") || text.starts_with("OPTIONS ") ||
           text.starts_with("PATCH ") || text.starts_with("CONNECT ") {
            return Ok(Some(Protocol::Http));
        }
        
        Ok(None)
    }
}

// TLS协议检测
pub struct TlsDetector;

impl ProtocolDetector for TlsDetector {
    async fn detect(&self, data: &[u8]) -> Result<Option<Protocol>, RouteError> {
        if data.len() < 6 {
            return Ok(None);
        }
        
        // TLS握手记录
        if data[0] == 0x16 && data[1] == 0x03 && (data[2] == 0x01 || data[2] == 0x02 || data[2] == 0x03) {
            // 提取SNI
            if let Some(sni) = self.extract_sni(data).await? {
                return Ok(Some(Protocol::Tls { sni: Some(sni) }));
            }
            return Ok(Some(Protocol::Tls { sni: None }));
        }
        
        Ok(None)
    }
    
    async fn extract_sni(&self, data: &[u8]) -> Result<Option<String>, RouteError> {
        // TLS SNI提取逻辑
        // 解析TLS Client Hello消息中的SNI扩展
        Ok(None) // 简化实现
    }
}
```

## 📋 规则集系统

### 1. 规则集加载器
```rust
// src/route/rule_set.rs
pub struct RuleSet {
    tag: String,
    rules: Vec<RuleSetEntry>,
    format: RuleSetFormat,
    last_updated: SystemTime,
}

pub enum RuleSetFormat {
    Source,
    Binary,
}

pub enum RuleSetEntry {
    Domain(String),
    DomainSuffix(String),
    DomainKeyword(String),
    DomainRegex(Regex),
    IpCidr(IpNet),
    Geoip(String),
    Geosite(String),
}

impl RuleSet {
    pub async fn load_from_file(path: &Path) -> Result<Self, RouteError> {
        let content = tokio::fs::read_to_string(path).await?;
        Self::parse_content(&content).await
    }
    
    pub async fn load_from_url(url: &str, detour: Option<&str>) -> Result<Self, RouteError> {
        // 通过指定的出站下载规则集
        let content = self.download_with_detour(url, detour).await?;
        Self::parse_content(&content).await
    }
    
    pub async fn matches(&self, ctx: &RouteContext) -> Result<bool, RouteError> {
        for entry in &self.rules {
            if self.entry_matches(entry, ctx).await? {
                return Ok(true);
            }
        }
        Ok(false)
    }
}
```

### 2. GeoIP/GeoSite增强
```rust
// src/route/geo.rs
pub struct GeoIpMatcher {
    database: Arc<GeoIpDatabase>,
    cache: Arc<RwLock<LruCache<IpAddr, String>>>,
}

impl GeoIpMatcher {
    pub async fn lookup(&self, ip: IpAddr) -> Result<String, RouteError> {
        // 检查缓存
        if let Some(country) = self.cache.read().await.get(&ip) {
            return Ok(country.clone());
        }
        
        // 查询数据库
        let country = self.database.lookup(ip).await?;
        
        // 更新缓存
        self.cache.write().await.put(ip, country.clone());
        
        Ok(country)
    }
    
    pub async fn update_database(&self, path: &Path) -> Result<(), RouteError> {
        let new_db = GeoIpDatabase::load(path).await?;
        // 原子更新数据库
        // self.database = Arc::new(new_db);
        Ok(())
    }
}

pub struct GeoSiteMatcher {
    database: Arc<GeoSiteDatabase>,
    cache: Arc<RwLock<LruCache<String, Vec<String>>>>,
}

impl GeoSiteMatcher {
    pub async fn lookup(&self, domain: &str) -> Result<Vec<String>, RouteError> {
        // 检查缓存
        if let Some(categories) = self.cache.read().await.get(domain) {
            return Ok(categories.clone());
        }
        
        // 查询数据库
        let categories = self.database.lookup(domain).await?;
        
        // 更新缓存
        self.cache.write().await.put(domain.to_string(), categories.clone());
        
        Ok(categories)
    }
}
```

## 📊 路由统计系统

### 1. 路由统计收集
```rust
// src/route/stats.rs
pub struct RouteStats {
    rule_hits: Arc<RwLock<HashMap<String, u64>>>,
    outbound_usage: Arc<RwLock<HashMap<String, OutboundStats>>>,
    total_requests: Arc<AtomicU64>,
}

#[derive(Debug, Clone)]
pub struct OutboundStats {
    pub requests: u64,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub last_used: SystemTime,
}

impl RouteStats {
    pub async fn record_hit(&self, rule_name: &str, outbound: &str) {
        // 记录规则命中
        *self.rule_hits.write().await.entry(rule_name.to_string()).or_insert(0) += 1;
        
        // 记录出站使用
        let mut outbound_stats = self.outbound_usage.write().await;
        let stats = outbound_stats.entry(outbound.to_string()).or_insert(OutboundStats {
            requests: 0,
            bytes_sent: 0,
            bytes_received: 0,
            last_used: SystemTime::now(),
        });
        stats.requests += 1;
        stats.last_used = SystemTime::now();
        
        // 总请求数
        self.total_requests.fetch_add(1, Ordering::Relaxed);
    }
    
    pub async fn get_rule_stats(&self) -> HashMap<String, u64> {
        self.rule_hits.read().await.clone()
    }
    
    pub async fn get_outbound_stats(&self) -> HashMap<String, OutboundStats> {
        self.outbound_usage.read().await.clone()
    }
}
```

## 🚀 性能优化

### 1. 规则编译优化
```rust
// src/route/compiler.rs
pub struct RuleCompiler;

impl RuleCompiler {
    pub fn compile_rules(rules: &[RouteRuleConfig]) -> Result<Vec<Arc<dyn RouteRule>>, RouteError> {
        let mut compiled_rules = Vec::new();
        
        for rule_config in rules {
            let compiled_rule = Self::compile_rule(rule_config)?;
            compiled_rules.push(compiled_rule);
        }
        
        // 规则优化：将快速匹配的规则放在前面
        compiled_rules.sort_by_key(|rule| rule.priority());
        
        Ok(compiled_rules)
    }
    
    fn compile_rule(config: &RouteRuleConfig) -> Result<Arc<dyn RouteRule>, RouteError> {
        // 根据配置编译规则
        // 优化：预编译正则表达式、构建高效的数据结构
        Ok(Arc::new(CompositeRule::from_config(config)?))
    }
}
```

### 2. 缓存系统
```rust
// src/route/cache.rs
pub struct RouteCache {
    cache: Arc<RwLock<LruCache<RouteCacheKey, RouteResult>>>,
    ttl: Duration,
}

#[derive(Hash, Eq, PartialEq)]
struct RouteCacheKey {
    destination: Destination,
    source: Option<SocketAddr>,
    process_name: Option<String>,
}

impl RouteCache {
    pub async fn get(&self, key: &RouteCacheKey) -> Option<RouteResult> {
        self.cache.read().await.get(key).cloned()
    }
    
    pub async fn put(&self, key: RouteCacheKey, result: RouteResult) {
        self.cache.write().await.put(key, result);
    }
}
```

## 📋 实施计划

### 第1周: 核心架构重构
- [ ] 重构Router核心结构
- [ ] 实现新的路由规则系统
- [ ] 实现路由条件系统

### 第2周: 高级功能实现
- [ ] 实现协议嗅探系统
- [ ] 实现规则集支持
- [ ] 增强GeoIP/GeoSite功能

### 第3周: 性能优化
- [ ] 实现路由缓存
- [ ] 规则编译优化
- [ ] 并发性能优化

### 第4周: 测试和验证
- [ ] 单元测试完善
- [ ] 性能基准测试
- [ ] 兼容性验证

## 🎯 成功标准

### 1. 功能完整性
- ✅ 支持所有原版路由规则
- ✅ 协议嗅探功能完整
- ✅ 规则集功能完整

### 2. 性能指标
- ✅ 路由延迟 < 1ms
- ✅ 内存使用合理
- ✅ 并发性能优异

### 3. 兼容性
- ✅ 配置文件100%兼容
- ✅ 路由行为完全一致
- ✅ API接口兼容

通过这个全面的路由系统重构，我们将实现与原版sing-box完全一致的路由功能，同时提供更好的性能和扩展性。
