# SingBox-rs API 文档

## 概述

SingBox-rs 提供了一套完整的 API 来构建和管理网络代理服务。本文档详细介绍了各个模块的 API 使用方法。

## 核心模块

### 1. Adapter 模块

适配器模块定义了统一的接口规范。

#### Lifecycle Trait

```rust
use singbox_rs::adapter::Lifecycle;

#[async_trait]
pub trait Lifecycle: Send + Sync {
    async fn start(&mut self) -> Result<(), Box<dyn std::error::Error>>;
    async fn close(&mut self) -> Result<(), Box<dyn std::error::Error>>;
}
```

#### Inbound Trait

```rust
use singbox_rs::adapter::Inbound;

#[async_trait]
pub trait Inbound: Lifecycle {
    fn tag(&self) -> &str;
    fn network(&self) -> &[String];
    async fn new_connection(&self, ctx: InboundContext) -> Result<(), Box<dyn std::error::Error>>;
}
```

#### Outbound Trait

```rust
use singbox_rs::adapter::Outbound;

#[async_trait]
pub trait Outbound: Lifecycle {
    fn tag(&self) -> &str;
    fn network(&self) -> &[String];
    async fn dial_context(&self, ctx: &InboundContext, network: &str, destination: &str) -> Result<(), Box<dyn std::error::Error>>;
}
```

### 2. Protocol 模块

协议模块实现了各种代理协议。

#### HTTP 代理

```rust
use singbox_rs::protocol::http::HttpInbound;
use std::net::SocketAddr;

// 创建 HTTP 代理服务器
let addr: SocketAddr = "127.0.0.1:8080".parse()?;
let mut http_server = HttpInbound::new("http-in".to_string(), addr);

// 启动服务器
http_server.start().await?;
```

#### SOCKS5 代理

```rust
use singbox_rs::protocol::socks::SocksInbound;
use std::collections::HashMap;

let addr: SocketAddr = "127.0.0.1:1080".parse()?;
let mut socks_server = SocksInbound::new(
    "socks-in".to_string(),
    addr,
    HashMap::new() // 用户认证信息
);

socks_server.start().await?;
```

#### Shadowsocks

```rust
use singbox_rs::protocol::shadowsocks::{ShadowsocksOutbound, ShadowsocksMethod};

let outbound = ShadowsocksOutbound::new(
    "ss-out".to_string(),
    "example.com:8388".parse()?,
    ShadowsocksMethod::Aes256Gcm,
    "your-password".to_string()
);
```

### 3. Transport 模块

传输层模块提供各种传输方式。

#### TLS 传输

```rust
use singbox_rs::transport::tls::{TlsDialer, TlsClientConfig};

let config = TlsClientConfig {
    server_name: "example.com".to_string(),
    insecure: false,
    ca_certs: vec![],
    client_cert: None,
    client_key: None,
};

let dialer = TlsDialer::new(config)?;
let connection = dialer.dial("example.com:443").await?;
```

#### WebSocket 传输

```rust
use singbox_rs::transport::websocket::{WebSocketDialer, WebSocketConfig};

let config = WebSocketConfig {
    path: "/ws".to_string(),
    headers: std::collections::HashMap::new(),
    max_early_data: 0,
    early_data_header_name: None,
};

let dialer = WebSocketDialer::new(config);
let connection = dialer.dial("ws://example.com/ws").await?;
```

#### HTTP/2 传输

```rust
use singbox_rs::transport::http2::{Http2Dialer, Http2Config};

let config = Http2Config {
    host: "example.com".to_string(),
    path: "/h2".to_string(),
    method: "POST".to_string(),
    headers: std::collections::HashMap::new(),
};

let dialer = Http2Dialer::new(config);
let connection = dialer.dial("example.com:443").await?;
```

### 4. Route 模块

路由模块实现智能流量分发。

#### 基本路由规则

```rust
use singbox_rs::route::{Router, BasicRule};

let mut router = Router::new();

// 创建基于域名的路由规则
let mut rule = BasicRule::new();
rule.domain_suffix = vec!["google.com".to_string(), "youtube.com".to_string()];
rule.outbound = "proxy".to_string();

router.add_rule(Box::new(rule));

// 查找匹配的出站
let outbound = router.find_outbound(&context).await?;
```

#### GeoIP 路由

```rust
use singbox_rs::route::geoip::{GeoIPManager, GeoIPRule};

let manager = GeoIPManager::new();
let rule = GeoIPRule::new(vec!["cn".to_string()]);

// 检查IP是否匹配规则
let matches = manager.matches_rule("*******", &rule);
```

#### GeoSite 路由

```rust
use singbox_rs::route::geosite::{GeoSiteManager, GeoSiteRule};

let manager = GeoSiteManager::new();
let rule = GeoSiteRule::new(vec!["google".to_string()]);

// 检查域名是否匹配规则
let matches = manager.matches_rule("www.google.com", &rule);
```

### 5. Stats 模块

统计模块提供流量监控和性能统计。

#### 流量统计

```rust
use singbox_rs::stats::{StatsManager, TrafficStats};

let stats_manager = StatsManager::new();

// 更新流量统计
stats_manager.update_traffic(1024, 2048).await; // 上传1KB，下载2KB

// 获取流量统计
let stats = stats_manager.get_traffic_stats();
println!("上传: {} bytes", stats.upload_bytes);
println!("下载: {} bytes", stats.download_bytes);
println!("上传速度: {} bytes/s", stats.upload_speed);
println!("下载速度: {} bytes/s", stats.download_speed);
```

#### 连接统计

```rust
// 记录新连接
stats_manager.record_connection_attempt();

// 记录连接成功
stats_manager.record_connection_success();

// 记录连接失败
stats_manager.record_connection_failure();

// 获取连接统计
let stats = stats_manager.get_traffic_stats();
println!("活跃连接: {}", stats.connections);
println!("总连接数: {}", stats.total_connections);
println!("失败连接: {}", stats.failed_connections);
```

### 6. Config 模块

配置模块处理配置文件的解析和验证。

#### 配置管理

```rust
use singbox_rs::config::{ConfigManager, Options};

let mut config_manager = ConfigManager::new();

// 加载配置文件
let options = config_manager.load_config("config.json").await?;

// 验证配置
config_manager.validate_config(&options)?;

// 获取缓存的配置
if let Some(cached_options) = config_manager.get_cached_config("config.json") {
    println!("使用缓存的配置");
}
```

### 7. DNS 模块

DNS模块提供域名解析功能。

#### DNS 客户端

```rust
use singbox_rs::dns::{DNSClient, DNSQuery, DNSOptions};

let options = DNSOptions::default();
let mut dns_client = DNSClient::new(options);

// 启动DNS客户端
dns_client.start().await?;

// 执行DNS查询
let query = DNSQuery::new("example.com".to_string(), "A".to_string());
let response = dns_client.query(query).await?;

// 提取IP地址
let ips = response.extract_ips();
println!("解析结果: {:?}", ips);
```

## 错误处理

所有API都使用标准的Rust错误处理模式：

```rust
use singbox_rs::protocol::ProtocolError;
use singbox_rs::transport::TransportError;
use singbox_rs::route::RouteError;

// 处理协议错误
match protocol_result {
    Ok(value) => println!("成功: {:?}", value),
    Err(ProtocolError::InvalidRequest(msg)) => eprintln!("无效请求: {}", msg),
    Err(ProtocolError::AuthenticationFailed) => eprintln!("认证失败"),
    Err(e) => eprintln!("其他错误: {}", e),
}
```

## 异步编程

SingBox-rs 基于 Tokio 异步运行时，所有 I/O 操作都是异步的：

```rust
use tokio;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 异步操作
    let result = some_async_operation().await?;
    
    // 并发执行多个操作
    let (result1, result2) = tokio::join!(
        async_operation1(),
        async_operation2()
    );
    
    Ok(())
}
```

## 生命周期管理

所有服务都实现了 Lifecycle trait：

```rust
use singbox_rs::adapter::Lifecycle;

async fn manage_service<T: Lifecycle>(mut service: T) -> Result<(), Box<dyn std::error::Error>> {
    // 启动服务
    service.start().await?;
    
    // 等待信号
    tokio::signal::ctrl_c().await?;
    
    // 关闭服务
    service.close().await?;
    
    Ok(())
}
```

## 最佳实践

1. **错误处理**: 始终处理可能的错误情况
2. **资源管理**: 使用 RAII 模式管理资源
3. **异步编程**: 合理使用 async/await
4. **配置验证**: 在启动前验证所有配置
5. **日志记录**: 使用结构化日志记录重要事件
6. **性能监控**: 定期检查统计信息
7. **优雅关闭**: 实现优雅的服务关闭逻辑
