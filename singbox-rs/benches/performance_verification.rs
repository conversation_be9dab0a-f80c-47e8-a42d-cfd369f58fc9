//! Performance verification benchmarks
//!
//! This benchmark suite verifies that new implementations meet performance targets
//! and provides comprehensive performance testing for all major components.

use criterion::{criterion_group, criterion_main, Criterion, BenchmarkId, Throughput};
use std::time::Duration;
use std::hint::black_box;
use rand::RngCore;
use base64::Engine;

use singbox_rs::performance::{PerformanceMonitor, PerformanceTargets};
use singbox_rs::route::rule::advanced::{AdvancedDomainRule, IpRangeRule, PortRangeRule};
use singbox_rs::route::sniff::SniffingEngine;
use singbox_rs::protocol::inbound::mixed::MixedInbound;

/// Benchmark CLI command performance
fn benchmark_cli_commands(c: &mut Criterion) {
    c.bench_function("cli_generate_uuid", |b| {
        b.iter(|| {
            // Simulate UUID generation
            let uuid = uuid::Uuid::new_v4();
            black_box(uuid.to_string())
        })
    });

    c.bench_function("cli_generate_keypair", |b| {
        b.iter(|| {
            // Simulate keypair generation
            let mut private_key = [0u8; 32];
            rand::rng().fill_bytes(&mut private_key);
            black_box(base64::engine::general_purpose::STANDARD.encode(&private_key))
        })
    });
}

/// Benchmark Mixed protocol performance
fn benchmark_mixed_protocol(c: &mut Criterion) {
    c.bench_function("mixed_protocol_creation", |b| {
        b.iter(|| {
            let config = singbox_rs::protocol::inbound::mixed::MixedConfig {
                listen: "127.0.0.1".to_string(),
                listen_port: 8080,
                users: None,
                set_system_proxy: Some(false),
            };

            let mixed = MixedInbound::new("test".to_string(), config);
            black_box(mixed)
        })
    });

    c.bench_function("mixed_protocol_detection", |b| {
        b.iter(|| {
            // Simulate protocol detection
            let http_data = b"GET / HTTP/1.1\r\nHost: example.com\r\n\r\n";
            let is_http = std::str::from_utf8(http_data).unwrap().starts_with("GET ");
            black_box(is_http)
        })
    });
}

/// Benchmark Hysteria protocol performance
fn benchmark_hysteria_protocol(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    c.bench_function("hysteria_config_parsing", |b| {
        b.iter(|| {
            let config = singbox_rs::protocol::inbound::hysteria::HysteriaConfig {
                listen: "0.0.0.0".to_string(),
                listen_port: 443,
                tls: None,
                auth: None,
                quic: None,
                bandwidth: None,
                obfs: None,
                disable_mtu_discovery: Some(false),
            };
            black_box(config)
        })
    });
    
    c.bench_function("hysteria_authentication", |b| {
        b.to_async(&rt).iter(|| async {
            let config = singbox_rs::protocol::inbound::hysteria::HysteriaConfig::default();
            let hysteria = singbox_rs::protocol::inbound::hysteria::HysteriaInbound::new("test".to_string(), config).unwrap();
            
            // Simulate authentication
            let auth_data = b"test_password";
            let result = std::str::from_utf8(auth_data).is_ok();
            black_box(result)
        })
    });
}

/// Benchmark advanced routing rules
fn benchmark_advanced_routing(c: &mut Criterion) {
    let mut group = c.benchmark_group("advanced_routing");
    
    // Domain rule benchmarks
    group.bench_function("domain_rule_exact_match", |b| {
        let mut rule = AdvancedDomainRule::new("test".to_string(), "proxy".to_string());
        rule.add_domain("example.com".to_string());
        
        b.iter(|| {
            let matches = rule.matches_domain("example.com");
            black_box(matches)
        })
    });
    
    group.bench_function("domain_rule_suffix_match", |b| {
        let mut rule = AdvancedDomainRule::new("test".to_string(), "proxy".to_string());
        rule.add_domain_suffix(".google.com".to_string());
        
        b.iter(|| {
            let matches = rule.matches_domain("www.google.com");
            black_box(matches)
        })
    });
    
    group.bench_function("domain_rule_keyword_match", |b| {
        let mut rule = AdvancedDomainRule::new("test".to_string(), "proxy".to_string());
        rule.add_domain_keyword("youtube".to_string());
        
        b.iter(|| {
            let matches = rule.matches_domain("m.youtube.com");
            black_box(matches)
        })
    });
    
    group.bench_function("domain_rule_regex_match", |b| {
        let mut rule = AdvancedDomainRule::new("test".to_string(), "proxy".to_string());
        rule.add_domain_regex(r".*\.github\.com$").unwrap();
        
        b.iter(|| {
            let matches = rule.matches_domain("api.github.com");
            black_box(matches)
        })
    });
    
    // IP range rule benchmarks
    group.bench_function("ip_range_rule_ipv4", |b| {
        let mut rule = IpRangeRule::new("test".to_string(), "proxy".to_string());
        rule.add_ipv4_cidr("***********/24").unwrap();
        
        let ip = std::net::IpAddr::V4(std::net::Ipv4Addr::new(192, 168, 1, 100));
        
        b.iter(|| {
            let matches = rule.matches_ip(&ip);
            black_box(matches)
        })
    });
    
    // Port range rule benchmarks
    group.bench_function("port_range_rule", |b| {
        let mut rule = PortRangeRule::new("test".to_string(), "proxy".to_string());
        rule.add_port_range(8000, 8999).unwrap();
        
        b.iter(|| {
            let matches = rule.matches_port(8080);
            black_box(matches)
        })
    });
    
    group.finish();
}

/// Benchmark protocol sniffing
fn benchmark_protocol_sniffing(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let mut group = c.benchmark_group("protocol_sniffing");
    
    group.bench_function("http_sniffing", |b| {
        b.to_async(&rt).iter(|| async {
            let engine = SniffingEngine::new();
            let data = b"GET / HTTP/1.1\r\nHost: example.com\r\n\r\n";
            let result = engine.sniff(data).await;
            black_box(result)
        })
    });
    
    group.bench_function("tls_sniffing", |b| {
        b.to_async(&rt).iter(|| async {
            let engine = SniffingEngine::new();
            let data = &[0x16, 0x03, 0x03, 0x00, 0x00, 0x00];
            let result = engine.sniff(data).await;
            black_box(result)
        })
    });
    
    group.bench_function("ssh_sniffing", |b| {
        b.to_async(&rt).iter(|| async {
            let engine = SniffingEngine::new();
            let data = b"SSH-2.0-OpenSSH_7.4";
            let result = engine.sniff(data).await;
            black_box(result)
        })
    });
    
    group.finish();
}

/// Benchmark rule set operations
fn benchmark_rule_sets(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let mut group = c.benchmark_group("rule_sets");
    
    group.bench_function("rule_set_loading", |b| {
        b.to_async(&rt).iter(|| async {
            let manager = singbox_rs::route::rule_set::RuleSetManager::new();
            
            let config = singbox_rs::route::rule_set::RuleSetConfig {
                tag: "test".to_string(),
                r#type: singbox_rs::route::rule_set::RuleSetType::Inline,
                format: singbox_rs::route::rule_set::RuleSetFormat::Source,
                path: None,
                url: None,
                download_detour: None,
                update_interval: None,
                rules: Some(vec![
                    singbox_rs::route::rule_set::RuleSetEntry::Domain {
                        value: "example.com".to_string(),
                    },
                ]),
            };
            
            let result = manager.load_rule_set(config).await;
            black_box(result)
        })
    });
    
    group.finish();
}

/// Benchmark performance monitoring
fn benchmark_performance_monitoring(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let mut group = c.benchmark_group("performance_monitoring");
    
    group.bench_function("metrics_collection", |b| {
        b.to_async(&rt).iter(|| async {
            let monitor = PerformanceMonitor::new();
            let metrics = monitor.get_current_metrics().await;
            black_box(metrics)
        })
    });
    
    group.bench_function("performance_verification", |b| {
        b.to_async(&rt).iter(|| async {
            let monitor = PerformanceMonitor::new();
            let targets = PerformanceTargets::default();
            let verification = monitor.verify_performance_targets(&targets).await;
            black_box(verification)
        })
    });
    
    group.bench_function("benchmark_execution", |b| {
        b.to_async(&rt).iter(|| async {
            let monitor = PerformanceMonitor::new();
            let result = monitor.run_benchmark("test", || async {
                tokio::time::sleep(Duration::from_micros(10)).await;
                Ok(())
            }, 10).await;
            black_box(result)
        })
    });
    
    group.finish();
}

/// Throughput benchmarks
fn benchmark_throughput(c: &mut Criterion) {
    let mut group = c.benchmark_group("throughput");
    
    // Test different data sizes
    for size in [1024, 4096, 16384, 65536].iter() {
        group.throughput(Throughput::Bytes(*size as u64));
        
        group.bench_with_input(BenchmarkId::new("data_processing", size), size, |b, &size| {
            let data = vec![0u8; size];
            
            b.iter(|| {
                // Simulate data processing
                let checksum: u32 = data.iter().map(|&x| x as u32).sum();
                black_box(checksum)
            })
        });
    }
    
    group.finish();
}

/// Memory usage benchmarks
fn benchmark_memory_usage(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let mut group = c.benchmark_group("memory_usage");
    
    group.bench_function("large_rule_set", |b| {
        b.to_async(&rt).iter(|| async {
            // Create a large rule set
            let mut rules = Vec::new();
            for i in 0..1000 {
                rules.push(singbox_rs::route::rule_set::RuleSetEntry::Domain {
                    value: format!("example{}.com", i),
                });
            }
            
            let config = singbox_rs::route::rule_set::RuleSetConfig {
                tag: "large_test".to_string(),
                r#type: singbox_rs::route::rule_set::RuleSetType::Inline,
                format: singbox_rs::route::rule_set::RuleSetFormat::Source,
                path: None,
                url: None,
                download_detour: None,
                update_interval: None,
                rules: Some(rules),
            };
            
            let manager = singbox_rs::route::rule_set::RuleSetManager::new();
            let result = manager.load_rule_set(config).await;
            black_box(result)
        })
    });
    
    group.finish();
}

criterion_group!(
    benches,
    benchmark_cli_commands,
    benchmark_mixed_protocol,
    benchmark_hysteria_protocol,
    benchmark_advanced_routing,
    benchmark_protocol_sniffing,
    benchmark_rule_sets,
    benchmark_performance_monitoring,
    benchmark_throughput,
    benchmark_memory_usage
);

criterion_main!(benches);
