//! Performance comparison benchmarks
//! 
//! Benchmarks to demonstrate Rust version performance advantages

use criterion::{black_box, criterion_group, criterion_main, Criterion};
use singbox_rs::*;
use std::time::Duration;

fn benchmark_box_creation(c: &mut Criterion) {
    c.bench_function("box_creation", |b| {
        b.iter(|| {
            let options = BoxOptions {
                context: Context::new(),
                options: Options::default(),
                disable_color: false,
            };
            
            let box_instance = Box::new(black_box(options));
            black_box(box_instance)
        })
    });
}

fn benchmark_config_parsing(c: &mut Criterion) {
    let config_json = r#"{
  "log": {
    "level": "info",
    "timestamp": true
  },
  "dns": {
    "servers": [
      {
        "tag": "cloudflare",
        "address": "*******",
        "address_strategy": 1,
        "strategy": 1
      }
    ],
    "strategy": 1
  },
  "inbounds": [
    {
      "inbound_type": "mixed",
      "type": "mixed",
      "tag": "mixed-in"
    }
  ],
  "outbounds": [
    {
      "outbound_type": "direct",
      "type": "direct",
      "tag": "direct"
    }
  ],
  "route": {
    "auto_detect_interface": true,
    "final_outbound": "direct"
  }
}"#;

    c.bench_function("config_parsing", |b| {
        b.iter(|| {
            let options: Options = serde_json::from_str(black_box(config_json)).unwrap();
            black_box(options)
        })
    });
}

fn benchmark_router_creation(c: &mut Criterion) {
    c.bench_function("router_creation", |b| {
        b.iter(|| {
            let router = Router::new(black_box("direct"));
            black_box(router)
        })
    });
}

fn benchmark_dns_client_creation(c: &mut Criterion) {
    c.bench_function("dns_client_creation", |b| {
        b.iter(|| {
            let options = ClientOptions::default();
            let client = DNSClient::new(black_box(options));
            black_box(client)
        })
    });
}

fn benchmark_network_manager_creation(c: &mut Criterion) {
    c.bench_function("network_manager_creation", |b| {
        b.iter(|| {
            let manager = NetworkManager::new();
            black_box(manager)
        })
    });
}

fn benchmark_protocol_registry(c: &mut Criterion) {
    c.bench_function("protocol_registry_creation", |b| {
        b.iter(|| {
            let inbound_registry = InboundRegistry::new();
            let outbound_registry = OutboundRegistry::new();
            register_builtin_protocols(&inbound_registry, &outbound_registry);
            black_box((inbound_registry, outbound_registry))
        })
    });
}

fn benchmark_full_service_lifecycle(c: &mut Criterion) {
    c.bench_function("full_service_lifecycle", |b| {
        b.iter_batched(
            || {
                BoxOptions {
                    context: Context::new(),
                    options: Options::default(),
                    disable_color: false,
                }
            },
            |options| {
                let rt = tokio::runtime::Runtime::new().unwrap();
                rt.block_on(async {
                    let mut box_instance = Box::new(black_box(options)).unwrap();
                    box_instance.start().await.unwrap();
                    box_instance.close().await.unwrap();
                    black_box(box_instance)
                })
            },
            criterion::BatchSize::SmallInput,
        )
    });
}

fn benchmark_concurrent_operations(c: &mut Criterion) {
    c.bench_function("concurrent_box_creation", |b| {
        b.iter(|| {
            let handles: Vec<_> = (0..10).map(|_| {
                std::thread::spawn(|| {
                    let options = BoxOptions {
                        context: Context::new(),
                        options: Options::default(),
                        disable_color: false,
                    };
                    Box::new(options)
                })
            }).collect();
            
            let results: Vec<_> = handles.into_iter()
                .map(|h| h.join().unwrap())
                .collect();
            
            black_box(results)
        })
    });
}

fn benchmark_memory_efficiency(c: &mut Criterion) {
    c.bench_function("memory_allocation", |b| {
        b.iter(|| {
            // Create multiple instances to test memory efficiency
            let instances: Vec<_> = (0..100).map(|_| {
                let options = BoxOptions {
                    context: Context::new(),
                    options: Options::default(),
                    disable_color: false,
                };
                Box::new(options).unwrap()
            }).collect();
            
            black_box(instances)
        })
    });
}

fn benchmark_startup_time(c: &mut Criterion) {
    c.bench_function("service_startup", |b| {
        b.iter_batched(
            || {
                let options = BoxOptions {
                    context: Context::new(),
                    options: Options::default(),
                    disable_color: false,
                };
                Box::new(options).unwrap()
            },
            |mut box_instance| {
                let rt = tokio::runtime::Runtime::new().unwrap();
                rt.block_on(async {
                    box_instance.start().await.unwrap();
                    black_box(box_instance)
                })
            },
            criterion::BatchSize::SmallInput,
        )
    });
}

criterion_group!(
    benches,
    benchmark_box_creation,
    benchmark_config_parsing,
    benchmark_router_creation,
    benchmark_dns_client_creation,
    benchmark_network_manager_creation,
    benchmark_protocol_registry,
    benchmark_full_service_lifecycle,
    benchmark_concurrent_operations,
    benchmark_memory_efficiency,
    benchmark_startup_time
);

criterion_main!(benches);
