warning: unused import: `Adapter`
  --> src/box/mod.rs:12:22
   |
12 | use crate::adapter::{Adapter, Inbound, Outbound, Lifecycle, StartStage};
   |                      ^^^^^^^
   |
   = note: `#[warn(unused_imports)]` on by default

warning: unused import: `LogOptions`
  --> src/cli.rs:15:30
   |
15 | use crate::option::{Options, LogOptions};
   |                              ^^^^^^^^^^

warning: unused import: `crate::common::interrupt::Context`
 --> src/protocol/http.rs:9:5
  |
9 | use crate::common::interrupt::Context;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `AsyncRead` and `AsyncWrite`
 --> src/protocol/socks.rs:3:17
  |
3 | use tokio::io::{AsyncRead, AsyncReadExt, AsyncWrite, AsyncWriteExt};
  |                 ^^^^^^^^^                ^^^^^^^^^^

warning: unused import: `md5::Digest`
 --> src/protocol/shadowsocks.rs:7:5
  |
7 | use md5::Digest;
  |     ^^^^^^^^^^^

warning: unused imports: `AeadInPlace`, `Aes128Gcm`, `Key`, and `Nonce`
 --> src/protocol/vmess.rs:6:15
  |
6 | use aes_gcm::{Aes128Gcm, Key, Nonce, AeadInPlace};
  |               ^^^^^^^^^  ^^^  ^^^^^  ^^^^^^^^^^^

warning: unused import: `chacha20poly1305::ChaCha20Poly1305`
 --> src/protocol/vmess.rs:8:5
  |
8 | use chacha20poly1305::ChaCha20Poly1305;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `md5`
  --> src/protocol/vmess.rs:12:5
   |
12 | use md5;
   |     ^^^

warning: unused import: `rand::RngCore`
 --> src/protocol/vless.rs:6:5
  |
6 | use rand::RngCore;
  |     ^^^^^^^^^^^^^

warning: unused imports: `Ipv4Addr` and `Ipv6Addr`
 --> src/protocol/wireguard/mod.rs:7:24
  |
7 | use std::net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddr};
  |                        ^^^^^^^^  ^^^^^^^^

warning: unused import: `crate::network::Connection`
  --> src/protocol/wireguard/mod.rs:17:5
   |
17 | use crate::network::Connection;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::network::Connection`
  --> src/protocol/tuic/mod.rs:17:5
   |
17 | use crate::network::Connection;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `webpki_roots`
  --> src/transport/tls.rs:15:5
   |
15 | use webpki_roots;
   |     ^^^^^^^^^^^^

warning: unused imports: `SinkExt` and `StreamExt`
  --> src/transport/websocket.rs:12:20
   |
12 | use futures_util::{SinkExt, StreamExt, Stream, Sink};
   |                    ^^^^^^^  ^^^^^^^^^

warning: unused import: `TransportListener`
  --> src/transport/websocket.rs:15:51
   |
15 | use super::{TransportConnection, TransportDialer, TransportListener, TransportError, Co...
   |                                                   ^^^^^^^^^^^^^^^^^

warning: unused import: `server`
  --> src/transport/http2.rs:10:18
   |
10 | use h2::{client, server, SendStream, RecvStream};
   |                  ^^^^^^

warning: unused imports: `Response` and `Uri`
  --> src/transport/http2.rs:11:21
   |
11 | use http::{Request, Response, Method, Uri};
   |                     ^^^^^^^^          ^^^

warning: unused import: `TransportListener`
  --> src/transport/http2.rs:14:51
   |
14 | use super::{TransportConnection, TransportDialer, TransportListener, TransportError, Co...
   |                                                   ^^^^^^^^^^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> src/transport/quic.rs:8:5
  |
8 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `ServerConfig`
  --> src/transport/quic.rs:11:73
   |
11 | use quinn::{Endpoint, Connection, SendStream, RecvStream, ClientConfig, ServerConfig};
   |                                                                         ^^^^^^^^^^^^

warning: unused import: `TransportListener`
  --> src/transport/quic.rs:14:51
   |
14 | use super::{TransportConnection, TransportDialer, TransportListener, TransportError, Co...
   |                                                   ^^^^^^^^^^^^^^^^^

warning: unused import: `rand::seq::SliceRandom`
 --> src/route/router.rs:6:5
  |
6 | use rand::seq::SliceRandom;
  |     ^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `Ipv4Addr` and `Ipv6Addr`
 --> src/route/geoip.rs:6:24
  |
6 | use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
  |                        ^^^^^^^^  ^^^^^^^^

warning: unused import: `HashSet`
 --> src/route/geosite.rs:5:33
  |
5 | use std::collections::{HashMap, HashSet};
  |                                 ^^^^^^^

warning: unused import: `std::path::PathBuf`
 --> src/route/rule/advanced.rs:8:5
  |
8 | use std::path::PathBuf;
  |     ^^^^^^^^^^^^^^^^^^

warning: unused import: `RuleCondition`
  --> src/route/rule/advanced.rs:15:37
   |
15 | use crate::route::rule::{RouteRule, RuleCondition, Destination};
   |                                     ^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src/route/sniff/mod.rs:6:5
  |
6 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Destination`
  --> src/route/rule_set/mod.rs:15:37
   |
15 | use crate::route::rule::{RouteRule, Destination};
   |                                     ^^^^^^^^^^^

warning: unused import: `SocketAddr`
 --> src/dns/mod.rs:7:24
  |
7 | use std::net::{IpAddr, SocketAddr};
  |                        ^^^^^^^^^^

warning: unused import: `UNIX_EPOCH`
 --> src/security/mod.rs:9:39
  |
9 | use std::time::{Duration, SystemTime, UNIX_EPOCH};
  |                                       ^^^^^^^^^^

warning: unused import: `UNIX_EPOCH`
 --> src/stats.rs:7:48
  |
7 | use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
  |                                                ^^^^^^^^^^

warning: unused import: `ServiceError`
  --> src/service/derp.rs:14:31
   |
14 | use crate::service::{Service, ServiceError};
   |                               ^^^^^^^^^^^^

warning: unused import: `ServiceError`
  --> src/service/resolved.rs:11:31
   |
11 | use crate::service::{Service, ServiceError};
   |                               ^^^^^^^^^^^^

warning: unused import: `ServiceError`
  --> src/service/ssm_api.rs:11:31
   |
11 | use crate::service::{Service, ServiceError};
   |                               ^^^^^^^^^^^^

warning: unused import: `Instant`
 --> src/performance/optimization.rs:8:27
  |
8 | use std::time::{Duration, Instant, SystemTime};
  |                           ^^^^^^^

warning: unused import: `Mutex`
  --> src/network/multiplex.rs:10:38
   |
10 | use tokio::sync::{RwLock, Semaphore, Mutex};
   |                                      ^^^^^

warning: unused import: `Mutex`
 --> src/common/taskmonitor/mod.rs:6:22
  |
6 | use std::sync::{Arc, Mutex};
  |                      ^^^^^

warning: unused import: `Receiver`
 --> src/common/taskmonitor/mod.rs:8:29
  |
8 | use std::sync::mpsc::{self, Receiver, Sender};
  |                             ^^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src/config/mod.rs:8:13
  |
8 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src/config/types/mod.rs:7:5
  |
7 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src/config/types/dns.rs:4:5
  |
4 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src/config/types/inbound.rs:4:5
  |
4 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `RecommendedWatcher`
  --> src/config/advanced.rs:14:38
   |
14 | use notify::{Watcher, RecursiveMode, RecommendedWatcher, Event};
   |                                      ^^^^^^^^^^^^^^^^^^

warning: unused import: `ConfigError`
  --> src/config/advanced.rs:16:29
   |
16 | use crate::config::{Config, ConfigError};
   |                             ^^^^^^^^^^^

warning: unused imports: `DnsConfig`, `ExperimentalConfig`, `InboundConfig`, `OutboundConfig`, and `RouteConfig`
  --> src/config/compatibility.rs:11:28
   |
11 | ...fig, InboundConfig, OutboundConfig, RouteConfig, DnsConfig, ExperimentalConfig};
   |         ^^^^^^^^^^^^^  ^^^^^^^^^^^^^^  ^^^^^^^^^^^  ^^^^^^^^^  ^^^^^^^^^^^^^^^^^^

warning: unused import: `std::fmt`
 --> src/error/mod.rs:6:5
  |
6 | use std::fmt;
  |     ^^^^^^^^

warning: unused import: `std::fmt`
 --> src/error/production.rs:7:5
  |
7 | use std::fmt;
  |     ^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/cli.rs:594:37
    |
594 |                 let mut rng = rand::thread_rng();
    |                                     ^^^^^^^^^^
    |
    = note: `#[warn(deprecated)]` on by default

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/cli.rs:903:15
    |
903 |         rand::thread_rng().fill_bytes(&mut private_key);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/cli.rs:912:15
    |
912 |         rand::thread_rng().fill_bytes(&mut public_key);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/cli.rs:926:15
    |
926 |         rand::thread_rng().fill_bytes(&mut private_key);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/cli.rs:942:15
    |
942 |         rand::thread_rng().fill_bytes(&mut private_key);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/cli.rs:943:15
    |
943 |         rand::thread_rng().fill_bytes(&mut public_key);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/cli.rs:958:15
    |
958 |         rand::thread_rng().fill_bytes(&mut private_key);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/cli.rs:959:15
    |
959 |         rand::thread_rng().fill_bytes(&mut public_key);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/cli.rs:973:15
    |
973 |         rand::thread_rng().fill_bytes(&mut private_key);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/cli.rs:974:15
    |
974 |         rand::thread_rng().fill_bytes(&mut public_key);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/protocol/shadowsocks.rs:308:19
    |
308 |             rand::thread_rng().fill_bytes(&mut nonce);
    |                   ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/protocol/vmess.rs:128:15
    |
128 |         rand::thread_rng().fill_bytes(&mut data_iv);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/protocol/vmess.rs:133:15
    |
133 |         rand::thread_rng().fill_bytes(&mut data_key);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/protocol/vmess.rs:151:19
    |
151 |             rand::thread_rng().next_u32() % 16
    |                   ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/protocol/vmess.rs:188:19
    |
188 |             rand::thread_rng().fill_bytes(&mut padding);
    |                   ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/protocol/wireguard/mod.rs:223:15
    |
223 |         rand::thread_rng().fill_bytes(&mut private_key);
    |               ^^^^^^^^^^

warning: use of deprecated function `rand::thread_rng`: Renamed to `rng`
   --> src/route/router.rs:687:41
    |
687 |                     let mut rng = rand::thread_rng();
    |                                         ^^^^^^^^^^

warning: unused variable: `component`
  --> src/box/lifecycle.rs:38:17
   |
38 |             for component in components.iter() {
   |                 ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_component`
   |
   = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `component`
  --> src/box/lifecycle.rs:53:13
   |
53 |         for component in components.iter().rev() {
   |             ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_component`

warning: unused variable: `stage`
  --> src/box/service.rs:75:13
   |
75 |         for stage in stages.iter() {
   |             ^^^^^ help: if this is intentional, prefix it with an underscore: `_stage`

warning: unused variable: `name`
  --> src/box/service.rs:76:18
   |
76 |             for (name, service) in services.iter() {
   |                  ^^^^ help: if this is intentional, prefix it with an underscore: `_name`

warning: unused variable: `route_config`
   --> src/box/mod.rs:170:21
    |
170 |         if let Some(route_config) = &self.config.route {
    |                     ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_route_config`

warning: unused variable: `inbounds`
   --> src/box/mod.rs:177:21
    |
177 |             let mut inbounds = self.inbounds.write().await;
    |                     ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_inbounds`

warning: unused variable: `inbound_config`
   --> src/box/mod.rs:178:17
    |
178 |             for inbound_config in inbounds_config {
    |                 ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_inbound_config`

warning: unused variable: `outbounds`
   --> src/box/mod.rs:187:21
    |
187 |             let mut outbounds = self.outbounds.write().await;
    |                     ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_outbounds`

warning: unused variable: `outbound_config`
   --> src/box/mod.rs:188:17
    |
188 |             for outbound_config in outbounds_config {
    |                 ^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_outbound_config`

warning: variable does not need to be mutable
   --> src/box/mod.rs:177:17
    |
177 |             let mut inbounds = self.inbounds.write().await;
    |                 ----^^^^^^^^
    |                 |
    |                 help: remove this `mut`
    |
    = note: `#[warn(unused_mut)]` on by default

warning: variable does not need to be mutable
   --> src/box/mod.rs:187:17
    |
187 |             let mut outbounds = self.outbounds.write().await;
    |                 ----^^^^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: unused variable: `dns_client`
   --> src/box/mod.rs:202:21
    |
202 |         if let Some(dns_client) = self.dns_client.read().await.as_ref() {
    |                     ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_dns_client`

warning: unused variable: `router`
   --> src/box/mod.rs:207:21
    |
207 |         if let Some(router) = self.router.read().await.as_ref() {
    |                     ^^^^^^ help: if this is intentional, prefix it with an underscore: `_router`

warning: variable does not need to be mutable
   --> src/box_service.rs:150:17
    |
150 |             let mut network_manager = self.network_manager.lock().unwrap();
    |                 ----^^^^^^^^^^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:156:17
    |
156 |             let mut dns_client = self.dns_client.lock().unwrap();
    |                 ----^^^^^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:162:17
    |
162 |             let mut router = self.router.lock().unwrap();
    |                 ----^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:181:17
    |
181 |             let mut network_manager = self.network_manager.lock().unwrap();
    |                 ----^^^^^^^^^^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:187:17
    |
187 |             let mut dns_client = self.dns_client.lock().unwrap();
    |                 ----^^^^^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:193:17
    |
193 |             let mut router = self.router.lock().unwrap();
    |                 ----^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:200:17
    |
200 |             let mut network_manager = self.network_manager.lock().unwrap();
    |                 ----^^^^^^^^^^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:206:17
    |
206 |             let mut dns_client = self.dns_client.lock().unwrap();
    |                 ----^^^^^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:212:17
    |
212 |             let mut router = self.router.lock().unwrap();
    |                 ----^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:219:17
    |
219 |             let mut network_manager = self.network_manager.lock().unwrap();
    |                 ----^^^^^^^^^^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:225:17
    |
225 |             let mut dns_client = self.dns_client.lock().unwrap();
    |                 ----^^^^^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:231:17
    |
231 |             let mut router = self.router.lock().unwrap();
    |                 ----^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:258:17
    |
258 |             let mut router = self.router.lock().unwrap();
    |                 ----^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:264:17
    |
264 |             let mut dns_client = self.dns_client.lock().unwrap();
    |                 ----^^^^^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: variable does not need to be mutable
   --> src/box_service.rs:270:17
    |
270 |             let mut network_manager = self.network_manager.lock().unwrap();
    |                 ----^^^^^^^^^^^^^^^
    |                 |
    |                 help: remove this `mut`

warning: unused variable: `output`
   --> src/cli.rs:700:46
    |
700 |             GeoipCommands::Export { country, output } => {
    |                                              ^^^^^^ help: try ignoring the field: `output: _`

warning: unused variable: `category`
   --> src/cli.rs:726:47
    |
726 |             GeositeCommands::Lookup { domain, category } => {
    |                                               ^^^^^^^^ help: try ignoring the field: `category: _`

warning: unused variable: `output`
   --> src/cli.rs:741:49
    |
741 |             GeositeCommands::Export { category, output } => {
    |                                                 ^^^^^^ help: try ignoring the field: `output: _`

warning: unused variable: `network`
   --> src/cli.rs:837:47
    |
837 |             ToolsCommands::Connect { address, network } => {
    |                                               ^^^^^^^ help: try ignoring the field: `network: _`

warning: unused variable: `format`
   --> src/cli.rs:882:47
    |
882 |             ToolsCommands::Synctime { server, format, write } => {
    |                                               ^^^^^^ help: try ignoring the field: `format: _`

warning: unused variable: `write`
   --> src/cli.rs:882:55
    |
882 |             ToolsCommands::Synctime { server, format, write } => {
    |                                                       ^^^^^ help: try ignoring the field: `write: _`

warning: unused variable: `bind_addr`
   --> src/protocol/socks.rs:289:9
    |
289 |         bind_addr: &str,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_bind_addr`

warning: unused variable: `e`
   --> src/protocol/shadowsocks.rs:312:27
    |
312 |                 .map_err(|e| ProtocolError::EncryptionFailed)?;
    |                           ^ help: if this is intentional, prefix it with an underscore: `_e`

warning: variable does not need to be mutable
   --> src/transport/http2.rs:201:14
    |
201 |         let (mut client, connection) = client::handshake(tcp_stream).await
    |              ----^^^^^^
    |              |
    |              help: remove this `mut`

warning: unused variable: `common_name`
   --> src/security/tls.rs:508:49
    |
508 |                 CertificateSource::SelfSigned { common_name, .. } => {
    |                                                 ^^^^^^^^^^^-
    |                                                 |
    |                                                 help: try removing the field

warning: unused variable: `source_ip`
   --> src/security/threat_detection.rs:478:29
    |
478 |                 if let Some(source_ip) = event.source_ip {
    |                             ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_source_ip`

warning: unused variable: `duration`
   --> src/security/threat_detection.rs:528:43
    |
528 |             ResponseActionType::BlockIp { duration } => {
    |                                           ^^^^^^^^ help: try ignoring the field: `duration: _`

warning: unused variable: `error_stats`
   --> src/experimental/debug_api.rs:335:13
    |
335 |         let error_stats = Arc::clone(&self.error_stats);
    |             ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_error_stats`

warning: unused variable: `endpoints`
   --> src/experimental/debug_api.rs:336:13
    |
336 |         let endpoints = self.config.endpoints.clone();
    |             ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_endpoints`

warning: unused variable: `state`
   --> src/performance/optimization.rs:691:9
    |
691 |         state: &mut OptimizationState,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `tuning_parameters`
   --> src/performance/optimization.rs:710:9
    |
710 |         tuning_parameters: &Arc<RwLock<HashMap<String, TuningParameter>>>,
    |         ^^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tuning_parameters`

warning: unused variable: `state`
   --> src/performance/optimization.rs:711:9
    |
711 |         state: &mut OptimizationState,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `tuning_parameters`
   --> src/performance/optimization.rs:719:9
    |
719 |         tuning_parameters: &Arc<RwLock<HashMap<String, TuningParameter>>>,
    |         ^^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tuning_parameters`

warning: unused variable: `state`
   --> src/performance/optimization.rs:720:9
    |
720 |         state: &mut OptimizationState,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `options`
  --> src/config/parser.rs:77:38
   |
77 |     async fn process_includes(&self, options: &mut Options) -> Result<(), ConfigError> {
   |                                      ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_options`

warning: unused variable: `options`
  --> src/config/mod.rs:87:13
   |
87 |         let options = self.parser.parse_file(path).await?;
   |             ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_options`

warning: unused variable: `error`
   --> src/error/production.rs:448:59
    |
448 | ...tr, error: &ProductionError) -> bool {
    |        ^^^^^ help: if this is intentional, prefix it with an underscore: `_error`

warning: unused variable: `error`
   --> src/error/production.rs:478:9
    |
478 |         error: &ProductionError,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_error`

warning: use of deprecated method `rand::Rng::gen_range`: Renamed to `random_range`
   --> src/route/router.rs:688:37
    |
688 |                     let index = rng.gen_range(0..healthy_outbounds.len());
    |                                     ^^^^^^^^^

warning: unused import: `Adapter`
 --> src/route/router.rs:9:22
  |
9 | use crate::adapter::{Adapter, Inbound, Outbound, InboundContext, Lifecycle, StartStage};
  |                      ^^^^^^^

warning: unused variable: `stage`
   --> src/box/mod.rs:257:27
    |
257 |     async fn start(&self, stage: StartStage) -> Result<(), String> {
    |                           ^^^^^ help: if this is intentional, prefix it with an underscore: `_stage`

warning: unused variable: `domain`
   --> src/protocol/tuic/mod.rs:603:21
    |
603 |                 let domain = String::from_utf8_lossy(&data[offset..offset + domain_len]);
    |                     ^^^^^^ help: if this is intentional, prefix it with an underscore: `_domain`

warning: variable does not need to be mutable
   --> src/transport/http2.rs:135:19
    |
135 |     fn poll_flush(mut self: Pin<&mut Self>, _cx: &mut Context<'_>) -> Poll<Result<(), ...
    |                   ----^^^^
    |                   |
    |                   help: remove this `mut`

warning: unused variable: `answers`
   --> src/route/sniff/mod.rs:332:13
    |
332 |         let answers = u16::from_be_bytes([data[6], data[7]]);
    |             ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_answers`

warning: unused variable: `authority`
   --> src/route/sniff/mod.rs:333:13
    |
333 |         let authority = u16::from_be_bytes([data[8], data[9]]);
    |             ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_authority`

warning: unused variable: `additional`
   --> src/route/sniff/mod.rs:334:13
    |
334 |         let additional = u16::from_be_bytes([data[10], data[11]]);
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_additional`

warning: unused variable: `ctx`
   --> src/route/rule_set/mod.rs:479:23
    |
479 |     fn matches(&self, ctx: &InboundContext) -> bool {
    |                       ^^^ help: if this is intentional, prefix it with an underscore: `_ctx`

warning: unused variable: `transaction_id`
   --> src/dns/resolver.rs:223:13
    |
223 |         let transaction_id = u16::from_be_bytes([packet[0], packet[1]]);
    |             ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_transaction_id`

warning: unused variable: `stage`
   --> src/dns/mod.rs:251:27
    |
251 |     async fn start(&self, stage: StartStage) -> Result<(), String> {
    |                           ^^^^^ help: if this is intentional, prefix it with an underscore: `_stage`

warning: unused variable: `outbound`
   --> src/config/parser.rs:320:13
    |
320 |         for outbound in &options.outbounds {
    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_outbound`

warning: unused variable: `clash_api`
   --> src/config/compatibility.rs:792:29
    |
792 |             if let Some(ref clash_api) = experimental.clash_api {
    |                             ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_clash_api`

warning: field `context` is never read
  --> src/box/mod.rs:69:5
   |
66 | pub struct SingBox {
   |            ------- field in this struct
...
69 |     context: Context,
   |     ^^^^^^^
   |
   = note: `#[warn(dead_code)]` on by default

warning: fields `inbound_registry`, `outbound_registry`, `options`, and `context` are never read
  --> src/box_service.rs:76:5
   |
70 | pub struct Box {
   |            --- fields in this struct
...
76 |     inbound_registry: Arc<InboundRegistry>,
   |     ^^^^^^^^^^^^^^^^
77 |     outbound_registry: Arc<OutboundRegistry>,
   |     ^^^^^^^^^^^^^^^^^
78 |     options: Options,
   |     ^^^^^^^
79 |     context: Context,
   |     ^^^^^^^

warning: fields `content` and `path` are never read
   --> src/cli.rs:282:5
    |
281 | struct OptionsEntry {
    |        ------------ fields in this struct
282 |     content: Vec<u8>,
    |     ^^^^^^^
283 |     path: String,
    |     ^^^^
    |
    = note: `OptionsEntry` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: field `listen_addr` is never read
  --> src/protocol/http.rs:15:5
   |
13 | pub struct HttpInbound {
   |            ----------- field in this struct
14 |     tag: String,
15 |     listen_addr: SocketAddr,
   |     ^^^^^^^^^^^

warning: field `listen_addr` is never read
  --> src/protocol/socks.rs:58:5
   |
56 | pub struct SocksInbound {
   |            ------------ field in this struct
57 |     tag: String,
58 |     listen_addr: SocketAddr,
   |     ^^^^^^^^^^^

warning: fields `listen_addr` and `users` are never read
   --> src/protocol/shadowsocks.rs:349:5
    |
347 | pub struct ShadowsocksInbound {
    |            ------------------ fields in this struct
348 |     tag: String,
349 |     listen_addr: SocketAddr,
    |     ^^^^^^^^^^^
350 |     cipher: ShadowsocksCipher,
351 |     users: HashMap<String, String>, // For multi-user support
    |     ^^^^^

warning: field `alter_id` is never read
  --> src/protocol/vmess.rs:93:5
   |
90 | pub struct VMessClient {
   |            ----------- field in this struct
...
93 |     alter_id: u16,
   |     ^^^^^^^^

warning: field `listen_addr` is never read
   --> src/protocol/vmess.rs:273:5
    |
271 | pub struct VMessInbound {
    |            ------------ field in this struct
272 |     tag: String,
273 |     listen_addr: SocketAddr,
    |     ^^^^^^^^^^^

warning: field `command` is never read
   --> src/protocol/vmess.rs:432:5
    |
431 | struct VMessRequest {
    |        ------------ field in this struct
432 |     command: u8,
    |     ^^^^^^^
    |
    = note: `VMessRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: field `packet_encoding` is never read
   --> src/protocol/vmess.rs:469:5
    |
465 | pub struct VMessOutbound {
    |            ------------- field in this struct
...
469 |     packet_encoding: PacketEncoding,
    |     ^^^^^^^^^^^^^^^

warning: field `listen_addr` is never read
   --> src/protocol/trojan.rs:195:5
    |
193 | pub struct TrojanInbound {
    |            ------------- field in this struct
194 |     tag: String,
195 |     listen_addr: SocketAddr,
    |     ^^^^^^^^^^^

warning: field `password` is never read
   --> src/protocol/trojan.rs:321:5
    |
318 | pub struct TrojanOutbound {
    |            -------------- field in this struct
...
321 |     password: String,
    |     ^^^^^^^^

warning: field `listen_addr` is never read
   --> src/protocol/vless.rs:265:5
    |
263 | pub struct VLessInbound {
    |            ------------ field in this struct
264 |     tag: String,
265 |     listen_addr: SocketAddr,
    |     ^^^^^^^^^^^

warning: field `flow` is never read
   --> src/protocol/vless.rs:395:5
    |
391 | pub struct VLessOutbound {
    |            ------------- field in this struct
...
395 |     flow: VLessFlow,
    |     ^^^^

warning: fields `key_pair` and `routing_table` are never read
   --> src/protocol/wireguard/mod.rs:313:5
    |
308 | pub struct WgTunnel {
    |            -------- fields in this struct
...
313 |     key_pair: WgKeyPair,
    |     ^^^^^^^^
...
319 |     routing_table: Arc<RwLock<HashMap<IpAddr, String>>>, // IP -> peer public key
    |     ^^^^^^^^^^^^^

warning: multiple associated items are never used
   --> src/protocol/wireguard/mod.rs:370:14
    |
328 | impl WgTunnel {
    | ------------- associated items in this implementation
...
370 |     async fn handshake(&self, peer_public_key: &str) -> Result<(), String> {
    |              ^^^^^^^^^
...
387 |     async fn send_keepalive(&self, peer_public_key: &str) -> Result<(), String> {
    |              ^^^^^^^^^^^^^^
...
402 |     async fn route_packet(&self, destination: IpAddr, _packet: &[u8]) -> Result<String...
    |              ^^^^^^^^^^^^
...
422 |     fn ip_in_same_subnet(ip1: &IpAddr, ip2: &IpAddr) -> bool {
    |        ^^^^^^^^^^^^^^^^^
...
441 |     fn encrypt_packet(&self, _peer_public_key: &str, packet: &[u8]) -> Result<Vec<u8>,...
    |        ^^^^^^^^^^^^^^
...
451 |     fn decrypt_packet(&self, _peer_public_key: &str, encrypted_packet: &[u8]) -> Resul...
    |        ^^^^^^^^^^^^^^
...
483 |     async fn start_keepalive_task(&self) {
    |              ^^^^^^^^^^^^^^^^^^^^

warning: field `stream_counter` is never read
   --> src/protocol/tuic/mod.rs:270:5
    |
259 | pub struct TuicAdapter {
    |            ----------- field in this struct
...
270 |     stream_counter: Arc<Mutex<u64>>,
    |     ^^^^^^^^^^^^^^

warning: multiple methods are never used
   --> src/protocol/tuic/mod.rs:312:14
    |
279 | impl TuicAdapter {
    | ---------------- methods in this implementation
...
312 |     async fn next_stream_id(&self) -> u64 {
    |              ^^^^^^^^^^^^^^
...
319 |     async fn authenticate(&self, uuid: &Uuid, password: &str) -> bool {
    |              ^^^^^^^^^^^^
...
329 |     async fn handle_command(
    |              ^^^^^^^^^^^^^^
...
352 |     async fn handle_authenticate(&self, connection_id: &str, payload: &[u8]) -> Result...
    |              ^^^^^^^^^^^^^^^^^^^
...
390 |     async fn handle_connect(
    |              ^^^^^^^^^^^^^^
...
435 |     async fn handle_udp_associate(
    |              ^^^^^^^^^^^^^^^^^^^^
...
478 |     async fn handle_heartbeat(&self, connection_id: &str) -> Result<Vec<u8>, String> {
    |              ^^^^^^^^^^^^^^^^
...
490 |     fn parse_header(&self, data: &[u8]) -> Result<(TuicHeader, usize), String> {
    |        ^^^^^^^^^^^^
...
553 |     fn parse_address(&self, data: &[u8]) -> Result<(SocketAddr, usize), String> {
    |        ^^^^^^^^^^^^^
...
621 |     async fn start_heartbeat_task(&self) {
    |              ^^^^^^^^^^^^^^^^^^^^

warning: methods `detect_protocol`, `is_http_request`, and `handle_with_protocol` are never used
   --> src/protocol/inbound/mixed.rs:63:8
    |
52  | impl MixedInbound {
    | ----------------- methods in this implementation
...
63  |     fn detect_protocol(&self, buffer: &[u8]) -> DetectedProtocol {
    |        ^^^^^^^^^^^^^^^
...
88  |     fn is_http_request(&self, buffer: &[u8]) -> bool {
    |        ^^^^^^^^^^^^^^^
...
107 |     fn handle_with_protocol(
    |        ^^^^^^^^^^^^^^^^^^^^

warning: methods `authenticate` and `handle_connection` are never used
   --> src/protocol/inbound/hysteria.rs:173:14
    |
136 | impl HysteriaInbound {
    | -------------------- methods in this implementation
...
173 |     async fn authenticate(&self, auth_data: &[u8]) -> Result<bool, String> {
    |              ^^^^^^^^^^^^
...
214 |     async fn handle_connection(&self, connection_id: String, remote_addr: SocketAddr) ...
    |              ^^^^^^^^^^^^^^^^^

warning: methods `authenticate`, `handle_masquerade`, and `handle_connection` are never used
   --> src/protocol/inbound/hysteria2.rs:258:14
    |
202 | impl Hysteria2Inbound {
    | --------------------- methods in this implementation
...
258 |     async fn authenticate(&self, auth_data: &[u8]) -> Result<bool, String> {
    |              ^^^^^^^^^^^^
...
303 |     async fn handle_masquerade(&self, _request: &[u8]) -> Result<Vec<u8>, String> {
    |              ^^^^^^^^^^^^^^^^^
...
339 |     async fn handle_connection(&self, connection_id: String, remote_addr: SocketAddr) ...
    |              ^^^^^^^^^^^^^^^^^

warning: field `config` is never read
   --> src/transport/tls.rs:295:5
    |
292 | pub struct TlsListener {
    |            ----------- field in this struct
...
295 |     config: TlsServerConfig,
    |     ^^^^^^

warning: multiple fields are never read
  --> src/route/mod.rs:61:5
   |
53 | pub struct BasicRule {
   |            --------- fields in this struct
...
61 |     domain_suffix: Vec<String>,
   |     ^^^^^^^^^^^^^
62 |     domain_keyword: Vec<String>,
   |     ^^^^^^^^^^^^^^
63 |     domain_regex: Vec<String>,
   |     ^^^^^^^^^^^^
64 |     source_ip_cidr: Vec<String>,
   |     ^^^^^^^^^^^^^^
65 |     ip_cidr: Vec<String>,
   |     ^^^^^^^
66 |     source_port: Vec<u16>,
   |     ^^^^^^^^^^^
67 |     source_port_range: Vec<String>,
   |     ^^^^^^^^^^^^^^^^^
68 |     port: Vec<u16>,
   |     ^^^^
69 |     port_range: Vec<String>,
   |     ^^^^^^^^^^
70 |     process_name: Vec<String>,
   |     ^^^^^^^^^^^^
71 |     process_path: Vec<String>,
   |     ^^^^^^^^^^^^
72 |     package_name: Vec<String>,
   |     ^^^^^^^^^^^^
73 |     user: Vec<String>,
   |     ^^^^
74 |     user_id: Vec<u32>,
   |     ^^^^^^^
75 |     protocol: Vec<String>,
   |     ^^^^^^^^
76 |     auth_user: Vec<String>,
   |     ^^^^^^^^^
77 |     clash_mode: String,
   |     ^^^^^^^^^^
   |
   = note: `BasicRule` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: fields `config`, `stats`, `rule_cache`, `load_balancer`, and `failover_manager` are never read
  --> src/route/router.rs:20:5
   |
13 | pub struct Router {
   |            ------ fields in this struct
...
20 |     config: RouterConfig,
   |     ^^^^^^
...
23 |     stats: Arc<RwLock<RouterStats>>,
   |     ^^^^^
...
26 |     rule_cache: Arc<RwLock<HashMap<String, CachedRouteResult>>>,
   |     ^^^^^^^^^^
...
29 |     load_balancer: Arc<RwLock<LoadBalancer>>,
   |     ^^^^^^^^^^^^^
...
32 |     failover_manager: Arc<RwLock<FailoverManager>>,
   |     ^^^^^^^^^^^^^^^^

warning: field `weights` is never read
   --> src/route/router.rs:547:5
    |
542 | pub struct LoadBalancer {
    |            ------------ field in this struct
...
547 |     weights: HashMap<String, u32>,
    |     ^^^^^^^

warning: multiple methods are never used
   --> src/route/rule/advanced.rs:776:8
    |
759 | impl ProtocolSniffRule {
    | ---------------------- methods in this implementation
...
776 |     fn sniff_protocol(&self, data: &[u8]) -> Option<String> {
    |        ^^^^^^^^^^^^^^
...
820 |     fn is_http_request(&self, data: &[u8]) -> bool {
    |        ^^^^^^^^^^^^^^^
...
838 |     fn is_tls_handshake(&self, data: &[u8]) -> bool {
    |        ^^^^^^^^^^^^^^^^
...
851 |     fn is_ssh_handshake(&self, data: &[u8]) -> bool {
    |        ^^^^^^^^^^^^^^^^
...
861 |     fn is_ftp_response(&self, data: &[u8]) -> bool {
    |        ^^^^^^^^^^^^^^^
...
877 |     fn is_smtp_response(&self, data: &[u8]) -> bool {
    |        ^^^^^^^^^^^^^^^^
...
887 |     fn is_pop3_response(&self, data: &[u8]) -> bool {
    |        ^^^^^^^^^^^^^^^^
...
897 |     fn is_imap_response(&self, data: &[u8]) -> bool {
    |        ^^^^^^^^^^^^^^^^

warning: field `timeout` is never read
  --> src/dns/resolver.rs:13:5
   |
10 | pub struct DNSResolver {
   |            ----------- field in this struct
...
13 |     timeout: Duration,
   |     ^^^^^^^

warning: fields `config` and `policies` are never read
   --> src/security/mod.rs:261:5
    |
259 | pub struct SecurityManager {
    |            --------------- fields in this struct
260 |     /// Configuration
261 |     config: SecurityConfig,
    |     ^^^^^^
...
282 |     policies: Arc<RwLock<Vec<SecurityPolicy>>>,
    |     ^^^^^^^^

warning: fields `server_handle`, `traffic_stats`, and `proxy_groups` are never read
   --> src/experimental/clash_api.rs:192:5
    |
187 | pub struct ClashApi {
    |            -------- fields in this struct
...
192 |     server_handle: std::sync::Mutex<Option<tokio::task::JoinHandle<()>>>,
    |     ^^^^^^^^^^^^^
...
198 |     traffic_stats: Arc<RwLock<ClashTraffic>>,
    |     ^^^^^^^^^^^^^
...
201 |     proxy_groups: Arc<RwLock<HashMap<String, ClashProxyGroup>>>,
    |     ^^^^^^^^^^^^

warning: methods `start_server` and `stop_server` are never used
   --> src/experimental/clash_api.rs:282:14
    |
207 | impl ClashApi {
    | ------------- methods in this implementation
...
282 |     async fn start_server(&self) -> Result<(), String> {
    |              ^^^^^^^^^^^^
...
289 |     async fn stop_server(&self) -> Result<(), String> {
    |              ^^^^^^^^^^^

warning: field `server_handle` is never read
  --> src/experimental/v2ray_api.rs:55:5
   |
52 | pub struct V2RayApi {
   |            -------- field in this struct
...
55 |     server_handle: Arc<RwLock<Option<tokio::task::JoinHandle<()>>>>,
   |     ^^^^^^^^^^^^^

warning: methods `start_server` and `stop_server` are never used
   --> src/experimental/v2ray_api.rs:106:14
    |
59  | impl V2RayApi {
    | ------------- methods in this implementation
...
106 |     async fn start_server(&self) -> Result<(), String> {
    |              ^^^^^^^^^^^^
...
113 |     async fn stop_server(&self) -> Result<(), String> {
    |              ^^^^^^^^^^^

warning: field `socket` is never read
   --> src/network/mod.rs:251:5
    |
250 | struct UdpAcceptLoop {
    |        ------------- field in this struct
251 |     socket: UdpSocket,
    |     ^^^^^^

warning: function `current_timestamp` is never used
   --> src/error/production.rs:634:4
    |
634 | fn current_timestamp() -> u64 {
    |    ^^^^^^^^^^^^^^^^^

warning: use of `async fn` in public traits is discouraged as auto trait bounds cannot be specified
   --> src/transport/mod.rs:115:5
    |
115 |     async fn dial(&self, addr: &str) -> Result<Self::Connection, TransportError>;
    |     ^^^^^
    |
    = note: you can suppress this lint if you plan to use the trait only in your own code, or do not care about auto traits like `Send` on the `Future`
    = note: `#[warn(async_fn_in_trait)]` on by default
help: you can alternatively desugar to a normal `fn` that returns `impl Future` and add any desired bounds such as `Send`, but these cannot be relaxed without a breaking API change
    |
115 -     async fn dial(&self, addr: &str) -> Result<Self::Connection, TransportError>;
115 +     fn dial(&self, addr: &str) -> impl std::future::Future<Output = Result<Self::Connection, TransportError>> + Send;
    |

warning: use of `async fn` in public traits is discouraged as auto trait bounds cannot be specified
   --> src/transport/mod.rs:118:5
    |
118 |     async fn dial_timeout(&self, addr: &str, timeout: std::time::Duration) -> Result<S...
    |     ^^^^^
    |
    = note: you can suppress this lint if you plan to use the trait only in your own code, or do not care about auto traits like `Send` on the `Future`
help: you can alternatively desugar to a normal `fn` that returns `impl Future` and add any desired bounds such as `Send`, but these cannot be relaxed without a breaking API change
    |
118 -     async fn dial_timeout(&self, addr: &str, timeout: std::time::Duration) -> Result<Self::Connection, TransportError>;
118 +     fn dial_timeout(&self, addr: &str, timeout: std::time::Duration) -> impl std::future::Future<Output = Result<Self::Connection, TransportError>> + Send;
    |

warning: use of `async fn` in public traits is discouraged as auto trait bounds cannot be specified
   --> src/transport/mod.rs:129:5
    |
129 |     async fn accept(&mut self) -> Result<Self::Connection, TransportError>;
    |     ^^^^^
    |
    = note: you can suppress this lint if you plan to use the trait only in your own code, or do not care about auto traits like `Send` on the `Future`
help: you can alternatively desugar to a normal `fn` that returns `impl Future` and add any desired bounds such as `Send`, but these cannot be relaxed without a breaking API change
    |
129 -     async fn accept(&mut self) -> Result<Self::Connection, TransportError>;
129 +     fn accept(&mut self) -> impl std::future::Future<Output = Result<Self::Connection, TransportError>> + Send;
    |

warning: `singbox-rs` (lib) generated 165 warnings (run `cargo fix --lib -p singbox-rs` to apply 67 suggestions)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 0.41s
